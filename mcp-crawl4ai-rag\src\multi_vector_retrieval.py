"""
🚀 ФАЗА 5.3: MULTI-VECTOR RETRIEVAL SYSTEM
Многомерна система за търсене която надхвърля семантичното търсене

Автор: Augment Agent (Автономно развитие)
Дата: 2025-07-04
Цел: Създаване на многомерни възможности за търсене за консултантски заявки
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from enum import Enum
import re
from datetime import datetime, timedelta
import numpy as np

from .utils import get_supabase_client
from openai import AsyncOpenAI
import os

# Настройка на логинг
logger = logging.getLogger(__name__)

class SearchDimension(Enum):
    """Измерения за многомерно търсене"""
    SEMANTIC = "semantic"           # Семантично сходство
    TOPIC = "topic"                # Тематично съответствие
    TEMPORAL = "temporal"          # Времево съответствие
    GEOGRAPHIC = "geographic"      # Географско съответствие
    BUDGET = "budget"              # Бюджетно съответствие
    PROGRAM_TYPE = "program_type"  # Тип програма
    DEADLINE = "deadline"          # Близост до крайни срокове

@dataclass
class MultiVectorQuery:
    """Многомерна заявка с тегла за различните измерения"""
    text: str
    semantic_weight: float = 0.4
    topic_weight: float = 0.2
    temporal_weight: float = 0.15
    geographic_weight: float = 0.1
    budget_weight: float = 0.1
    program_type_weight: float = 0.05
    
    # Филтри
    topics: List[str] = None
    time_range: Tuple[datetime, datetime] = None
    locations: List[str] = None
    budget_range: Tuple[float, float] = None
    program_types: List[str] = None
    deadline_urgency: str = None  # "urgent", "medium", "flexible"

@dataclass
class MultiVectorResult:
    """Резултат от многомерно търсене с детайлни скорове"""
    content: str
    url: str
    chunk_number: int
    metadata: Dict[str, Any]
    
    # Скорове по измерения
    semantic_score: float
    topic_score: float
    temporal_score: float
    geographic_score: float
    budget_score: float
    program_type_score: float
    deadline_score: float
    
    # Общ скор
    total_score: float
    
    # Обяснение на скора
    score_explanation: Dict[str, str]

class MultiVectorRetrievalEngine:
    """
    🧠 Multi-Vector Retrieval Engine за консултантски заявки
    
    Комбинира множество измерения за точно търсене:
    - Семантично сходство (BGE-M3)
    - Тематично съответствие (програми, области)
    - Времево съответствие (години, периоди)
    - Географско съответствие (региони, страни)
    - Бюджетно съответствие (размери на финансиране)
    - Тип програма (ОПРР, ОПТТИ, Interreg и др.)
    - Близост до крайни срокове
    """
    
    def __init__(self, supabase_client=None, openai_client=None):
        self.supabase_client = supabase_client or get_supabase_client()
        
        # Initialize OpenAI async client
        if openai_client:
            self.openai_client = openai_client
        else:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            self.openai_client = AsyncOpenAI(api_key=api_key)
        
        # Кеш за извлечени метаданни
        self._metadata_cache = {}
        
        # Предефинирани теми и програми
        self.known_topics = [
            "иновации", "дигитализация", "зелена енергия", "туризъм", 
            "образование", "здравеопазване", "инфраструктура", "МСП",
            "научни изследвания", "култура", "селско стопанство", "транспорт"
        ]
        
        self.known_programs = [
            "ОПРР", "ОПТТИ", "ОПОС", "Interreg", "Horizon Europe", 
            "LIFE", "Erasmus+", "Creative Europe", "COSME"
        ]
        
        self.bulgarian_regions = [
            "София", "Пловдив", "Варна", "Бургас", "Русе", "Стара Загора",
            "Плевен", "Сливен", "Добрич", "Шумен", "Перник", "Хасково",
            "Ямбол", "Пазарджик", "Благоевград", "Велико Търново",
            "Враца", "Габрово", "Кърджали", "Кюстендил", "Ловеч",
            "Монтана", "Разград", "Силистра", "Смолян", "Търговище",
            "Видин", "Севлиево"
        ]

    async def analyze_query_dimensions(self, query: str) -> Dict[str, Any]:
        """
        🔍 Анализира заявката и извлича информация за всички измерения
        """
        try:
            analysis_prompt = f"""Анализирай следната заявка за европейски програми и извлечи информация за различните измерения:

Заявка: "{query}"

Извлечи следната информация в JSON формат:
{{
    "topics": ["списък с теми/области"],
    "time_indicators": ["години, периоди, времеви индикатори"],
    "locations": ["региони, градове, страни"],
    "budget_indicators": ["бюджетни индикатори, суми"],
    "program_types": ["типове програми"],
    "deadline_urgency": "urgent/medium/flexible",
    "query_complexity": "simple/medium/complex"
}}

Познати теми: {', '.join(self.known_topics)}
Познати програми: {', '.join(self.known_programs)}
Български региони: {', '.join(self.bulgarian_regions)}

Отговори само с JSON, без допълнителен текст."""

            response = await self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": "Ти си експерт по европейски програми и анализираш заявки за многомерно търсене."
                    },
                    {"role": "user", "content": analysis_prompt}
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            analysis_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            try:
                analysis = json.loads(analysis_text)
                logger.info(f"Query analysis completed: {analysis}")
                return analysis
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON analysis: {analysis_text}")
                return self._fallback_analysis(query)
                
        except Exception as e:
            logger.error(f"Error in query analysis: {e}")
            return self._fallback_analysis(query)

    def _fallback_analysis(self, query: str) -> Dict[str, Any]:
        """Fallback анализ без LLM"""
        analysis = {
            "topics": [],
            "time_indicators": [],
            "locations": [],
            "budget_indicators": [],
            "program_types": [],
            "deadline_urgency": "medium",
            "query_complexity": "simple"
        }
        
        query_lower = query.lower()
        
        # Простo keyword matching
        for topic in self.known_topics:
            if topic.lower() in query_lower:
                analysis["topics"].append(topic)
        
        for program in self.known_programs:
            if program.lower() in query_lower:
                analysis["program_types"].append(program)
        
        for region in self.bulgarian_regions:
            if region.lower() in query_lower:
                analysis["locations"].append(region)
        
        # Времеви индикатори
        year_pattern = r'\b(20\d{2})\b'
        years = re.findall(year_pattern, query)
        analysis["time_indicators"] = years
        
        # Urgency keywords
        if any(word in query_lower for word in ["спешно", "бързо", "незабавно", "крайен срок"]):
            analysis["deadline_urgency"] = "urgent"
        elif any(word in query_lower for word in ["гъвкаво", "не бърза", "когато е възможно"]):
            analysis["deadline_urgency"] = "flexible"
        
        return analysis

    async def calculate_dimension_scores(
        self, 
        chunk: Dict[str, Any], 
        query_analysis: Dict[str, Any],
        semantic_score: float
    ) -> Dict[str, float]:
        """
        📊 Изчислява скорове за всички измерения
        """
        scores = {
            "semantic": semantic_score,
            "topic": 0.0,
            "temporal": 0.0,
            "geographic": 0.0,
            "budget": 0.0,
            "program_type": 0.0,
            "deadline": 0.0
        }
        
        chunk_content = chunk.get('content', '').lower()
        chunk_metadata = chunk.get('metadata', {})
        
        # Topic Score
        if query_analysis.get("topics"):
            topic_matches = sum(
                1 for topic in query_analysis["topics"]
                if topic.lower() in chunk_content
            )
            scores["topic"] = min(topic_matches / len(query_analysis["topics"]), 1.0)
        
        # Temporal Score
        if query_analysis.get("time_indicators"):
            temporal_matches = sum(
                1 for time_indicator in query_analysis["time_indicators"]
                if time_indicator in chunk_content
            )
            scores["temporal"] = min(temporal_matches / len(query_analysis["time_indicators"]), 1.0)
        
        # Geographic Score
        if query_analysis.get("locations"):
            geo_matches = sum(
                1 for location in query_analysis["locations"]
                if location.lower() in chunk_content
            )
            scores["geographic"] = min(geo_matches / len(query_analysis["locations"]), 1.0)
        
        # Program Type Score
        if query_analysis.get("program_types"):
            program_matches = sum(
                1 for program in query_analysis["program_types"]
                if program.lower() in chunk_content
            )
            scores["program_type"] = min(program_matches / len(query_analysis["program_types"]), 1.0)
        
        # Budget Score (simplified - based on budget keywords)
        budget_keywords = ["лева", "евро", "финансиране", "бюджет", "сума", "средства"]
        if query_analysis.get("budget_indicators"):
            budget_presence = any(keyword in chunk_content for keyword in budget_keywords)
            scores["budget"] = 0.8 if budget_presence else 0.2
        
        # Deadline Score (based on urgency and deadline keywords)
        deadline_keywords = ["крайен срок", "кандидатстване", "подаване", "дедлайн"]
        deadline_presence = any(keyword in chunk_content for keyword in deadline_keywords)
        
        urgency = query_analysis.get("deadline_urgency", "medium")
        if urgency == "urgent" and deadline_presence:
            scores["deadline"] = 1.0
        elif urgency == "medium" and deadline_presence:
            scores["deadline"] = 0.6
        elif deadline_presence:
            scores["deadline"] = 0.3
        else:
            scores["deadline"] = 0.1
        
        return scores

    async def multi_vector_search(
        self, 
        query: MultiVectorQuery,
        limit: int = 20
    ) -> List[MultiVectorResult]:
        """
        🎯 Извършва многомерно търсене
        """
        try:
            logger.info(f"Starting multi-vector search for: {query.text[:50]}...")
            
            # Стъпка 1: Анализ на заявката
            query_analysis = await self.analyze_query_dimensions(query.text)
            
            # Стъпка 2: Семантично търсене (базово)
            from .utils import enhanced_semantic_search
            
            semantic_results = await enhanced_semantic_search(
                query=query.text,
                supabase_client=self.supabase_client,
                async_openai_client=self.openai_client,
                final_top_k=limit * 2  # Получаваме повече кандидати
            )
            
            if not semantic_results:
                logger.warning("No semantic results found")
                return []
            
            # Стъпка 3: Изчисляване на многомерни скорове
            multi_vector_results = []
            
            for result in semantic_results:
                # Изчисляване на скорове за всички измерения
                dimension_scores = await self.calculate_dimension_scores(
                    chunk=result,
                    query_analysis=query_analysis,
                    semantic_score=result.get('similarity', 0.0)
                )
                
                # Изчисляване на общ скор с тегла
                total_score = (
                    dimension_scores["semantic"] * query.semantic_weight +
                    dimension_scores["topic"] * query.topic_weight +
                    dimension_scores["temporal"] * query.temporal_weight +
                    dimension_scores["geographic"] * query.geographic_weight +
                    dimension_scores["budget"] * query.budget_weight +
                    dimension_scores["program_type"] * query.program_type_weight
                )
                
                # Създаване на обяснение на скора
                score_explanation = {
                    "semantic": f"Семантично сходство: {dimension_scores['semantic']:.2f}",
                    "topic": f"Тематично съответствие: {dimension_scores['topic']:.2f}",
                    "temporal": f"Времево съответствие: {dimension_scores['temporal']:.2f}",
                    "geographic": f"Географско съответствие: {dimension_scores['geographic']:.2f}",
                    "budget": f"Бюджетно съответствие: {dimension_scores['budget']:.2f}",
                    "program_type": f"Тип програма: {dimension_scores['program_type']:.2f}",
                    "deadline": f"Близост до срокове: {dimension_scores['deadline']:.2f}"
                }
                
                # Създаване на MultiVectorResult
                mv_result = MultiVectorResult(
                    content=result.get('content', ''),
                    url=result.get('url', ''),
                    chunk_number=result.get('chunk_number', 0),
                    metadata=result.get('metadata', {}),
                    semantic_score=dimension_scores["semantic"],
                    topic_score=dimension_scores["topic"],
                    temporal_score=dimension_scores["temporal"],
                    geographic_score=dimension_scores["geographic"],
                    budget_score=dimension_scores["budget"],
                    program_type_score=dimension_scores["program_type"],
                    deadline_score=dimension_scores["deadline"],
                    total_score=total_score,
                    score_explanation=score_explanation
                )
                
                multi_vector_results.append(mv_result)
            
            # Стъпка 4: Сортиране по общ скор
            multi_vector_results.sort(key=lambda x: x.total_score, reverse=True)
            
            # Стъпка 5: Връщане на топ резултати
            final_results = multi_vector_results[:limit]
            
            logger.info(f"Multi-vector search completed: {len(final_results)} results")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Error in multi-vector search: {e}", exc_info=True)
            return []

# Utility функции за интеграция с MCP сървъра

async def perform_multi_vector_search(
    query_text: str,
    semantic_weight: float = 0.4,
    topic_weight: float = 0.2,
    temporal_weight: float = 0.15,
    geographic_weight: float = 0.1,
    budget_weight: float = 0.1,
    program_type_weight: float = 0.05,
    limit: int = 10
) -> Dict[str, Any]:
    """
    🎯 Извършва многомерно търсене с настройваеми тегла
    """
    try:
        engine = MultiVectorRetrievalEngine()
        
        query = MultiVectorQuery(
            text=query_text,
            semantic_weight=semantic_weight,
            topic_weight=topic_weight,
            temporal_weight=temporal_weight,
            geographic_weight=geographic_weight,
            budget_weight=budget_weight,
            program_type_weight=program_type_weight
        )
        
        results = await engine.multi_vector_search(query, limit=limit)
        
        # Конвертиране на резултатите за JSON сериализация
        serializable_results = []
        for result in results:
            serializable_results.append({
                "content": result.content[:500] + "..." if len(result.content) > 500 else result.content,
                "url": result.url,
                "chunk_number": result.chunk_number,
                "total_score": round(result.total_score, 3),
                "dimension_scores": {
                    "semantic": round(result.semantic_score, 3),
                    "topic": round(result.topic_score, 3),
                    "temporal": round(result.temporal_score, 3),
                    "geographic": round(result.geographic_score, 3),
                    "budget": round(result.budget_score, 3),
                    "program_type": round(result.program_type_score, 3),
                    "deadline": round(result.deadline_score, 3)
                },
                "score_explanation": result.score_explanation
            })
        
        return {
            "success": True,
            "query": query_text,
            "results_count": len(results),
            "results": serializable_results,
            "search_weights": {
                "semantic": semantic_weight,
                "topic": topic_weight,
                "temporal": temporal_weight,
                "geographic": geographic_weight,
                "budget": budget_weight,
                "program_type": program_type_weight
            }
        }
        
    except Exception as e:
        logger.error(f"Error in multi-vector search: {e}")
        return {
            "success": False,
            "error": str(e),
            "results_count": 0,
            "results": []
        }
