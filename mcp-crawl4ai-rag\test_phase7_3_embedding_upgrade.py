#!/usr/bin/env python3
"""
🎯 PHASE 7.3: EMBEDDING MODEL UPGRADE TEST
A/B тестване на BGE-M3 vs BGE-large-en-v1.5 за български език

Очаквано подобрение: +5-8% от текущите 70%
Цел: 75-78% Overall Success Index
"""

import asyncio
import os
import sys
from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import required modules
from supabase import create_client, Client
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv
from query_enhancement import BulgarianQueryEnhancer

# Load environment variables
load_dotenv()

def get_supabase_client() -> Client:
    """Get Supabase client"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    if not url or not key:
        raise ValueError(f"Missing Supabase credentials: URL={url is not None}, KEY={key is not None}")
    return create_client(url, key)

def get_embedding_models():
    """Get both embedding models for comparison"""
    print("📥 Loading embedding models...")
    
    # Current model
    current_model = SentenceTransformer('BAAI/bge-large-en-v1.5')
    print("✅ BGE-large-en-v1.5 loaded")
    
    # New multilingual model
    try:
        new_model = SentenceTransformer('BAAI/bge-m3')
        print("✅ BGE-M3 loaded")
        return current_model, new_model
    except Exception as e:
        print(f"❌ Failed to load BGE-M3: {e}")
        print("🔄 Falling back to current model only")
        return current_model, None

# Test queries - same as Phase 7.2 for comparison
TEST_QUERIES = [
    {
        "query": "процедури за финансиране на проекти",
        "difficulty": "EASY"
    },
    {
        "query": "образование и професионално обучение", 
        "difficulty": "EASY"
    },
    {
        "query": "европейски фондове за околна среда",
        "difficulty": "MEDIUM"
    },
    {
        "query": "програма за развитие на човешките ресурси",
        "difficulty": "MEDIUM"
    },
    {
        "query": "транспортна инфраструктура и мобилност",
        "difficulty": "MEDIUM"
    },
    {
        "query": "подкрепа за малки и средни предприятия",
        "difficulty": "MEDIUM"
    },
    {
        "query": "иновации и технологии за МСП",
        "difficulty": "HARD"
    },
    {
        "query": "регионално развитие и местни общности", 
        "difficulty": "HARD"
    },
    {
        "query": "цифрова трансформация и дигитализация",
        "difficulty": "HARD"
    },
    {
        "query": "енергийна ефективност и възобновяеми източници",
        "difficulty": "HARD"
    }
]

def calculate_content_relevance(query: str, content: str, threshold: float = 0.3) -> float:
    """Calculate content relevance based on keyword matching"""
    if not content:
        return 0.0
    
    query_words = set(query.lower().split())
    content_words = set(content.lower().split())
    
    # Remove common stopwords
    stopwords = {'и', 'на', 'за', 'в', 'с', 'от', 'до', 'по', 'при', 'към', 'или', 'че', 'да', 'се', 'не'}
    query_words = query_words - stopwords
    content_words = content_words - stopwords
    
    if not query_words:
        return 0.0
    
    matches = len(query_words.intersection(content_words))
    relevance = matches / len(query_words)
    
    return relevance

async def test_embedding_model(model, model_name: str, query_enhancer):
    """Test a specific embedding model"""
    print(f"\n🧪 Testing {model_name}")
    print("-" * 50)
    
    supabase = get_supabase_client()
    results = []
    
    for i, test_case in enumerate(TEST_QUERIES, 1):
        query = test_case["query"]
        difficulty = test_case["difficulty"]
        
        print(f"🔍 Test {i}/10: {query}")
        
        try:
            # Enhance query
            enhancement_result = query_enhancer.enhance_query_for_search(query)
            enhanced_query = enhancement_result['expanded_query']
            keywords = enhancement_result['keywords']
            
            # Generate embedding with current model
            start_time = time.time()
            query_embedding = model.encode([enhanced_query])[0].tolist()
            embedding_time = time.time() - start_time
            
            # Test with enhanced function
            response = supabase.rpc(
                'match_crawled_pages_v7_2_enhanced',
                {
                    'p_query_embedding': query_embedding,
                    'p_keywords': keywords,
                    'p_weight_similarity': 0.65,
                    'p_weight_program_name': 0.15,
                    'p_weight_year': 0.05,
                    'p_weight_metadata': 0.10,
                    'p_weight_keywords': 0.05,
                    'p_match_count': 5
                }
            ).execute()
            
            if response.data:
                # Analyze top 3 results
                top_3_results = response.data[:3]
                relevance_scores = []
                
                for j, result in enumerate(top_3_results, 1):
                    content_relevance = calculate_content_relevance(query, result.get('content', ''))
                    relevance_scores.append(content_relevance)
                
                # Calculate metrics
                success_at_3 = any(score >= 0.3 for score in relevance_scores)
                precision_at_3 = sum(1 for score in relevance_scores if score >= 0.3) / 3
                best_relevance = max(relevance_scores) if relevance_scores else 0
                
                # Calculate MRR
                mrr = 0
                for j, score in enumerate(relevance_scores, 1):
                    if score >= 0.3:
                        mrr = 1.0 / j
                        break
                
                results.append({
                    'query': query,
                    'difficulty': difficulty,
                    'success_at_3': success_at_3,
                    'precision_at_3': precision_at_3,
                    'best_relevance': best_relevance,
                    'mrr': mrr,
                    'embedding_time': embedding_time,
                    'top_scores': [r.get('final_score', 0) for r in top_3_results]
                })
                
                print(f"   ✅ Success: {success_at_3} | Precision: {precision_at_3:.1%} | Time: {embedding_time:.3f}s")
                
            else:
                print("   ❌ No results")
                results.append({
                    'query': query,
                    'difficulty': difficulty,
                    'success_at_3': False,
                    'precision_at_3': 0,
                    'best_relevance': 0,
                    'mrr': 0,
                    'embedding_time': embedding_time,
                    'top_scores': []
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({
                'query': query,
                'difficulty': difficulty,
                'success_at_3': False,
                'precision_at_3': 0,
                'best_relevance': 0,
                'mrr': 0,
                'embedding_time': 0,
                'top_scores': []
            })
    
    # Calculate overall metrics
    total_success_at_3 = sum(1 for r in results if r['success_at_3'])
    avg_precision_at_3 = sum(r['precision_at_3'] for r in results) / len(results)
    avg_mrr = sum(r['mrr'] for r in results) / len(results)
    overall_success_index = (total_success_at_3 / len(results)) * 100
    avg_embedding_time = sum(r['embedding_time'] for r in results) / len(results)
    
    print(f"\n📊 {model_name} Results:")
    print(f"   Overall Success Index: {overall_success_index:.1f}%")
    print(f"   Success@3 Rate: {total_success_at_3}/{len(results)}")
    print(f"   Average Precision@3: {avg_precision_at_3:.1%}")
    print(f"   Average MRR: {avg_mrr:.3f}")
    print(f"   Average Embedding Time: {avg_embedding_time:.3f}s")
    
    return {
        'model_name': model_name,
        'overall_success_index': overall_success_index,
        'success_at_3_rate': total_success_at_3/len(results),
        'avg_precision_at_3': avg_precision_at_3,
        'avg_mrr': avg_mrr,
        'avg_embedding_time': avg_embedding_time,
        'detailed_results': results
    }

async def test_embedding_upgrade():
    """Test embedding model upgrade"""
    print("🎯 PHASE 7.3: EMBEDDING MODEL UPGRADE TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 Testing: BGE-M3 vs BGE-large-en-v1.5 for Bulgarian")
    print(f"📊 Previous Performance (Phase 7.2): 70.0% Overall Success Index")
    print(f"🎯 Target: 75-78% Overall Success Index (+5-8%)")
    print("=" * 60)
    
    # Initialize
    query_enhancer = BulgarianQueryEnhancer()
    current_model, new_model = get_embedding_models()
    
    # Test current model (baseline)
    current_results = await test_embedding_model(current_model, "BGE-large-en-v1.5", query_enhancer)
    
    # Test new model if available
    if new_model:
        new_results = await test_embedding_model(new_model, "BGE-M3", query_enhancer)
        
        # Compare results
        print("\n" + "=" * 60)
        print("📊 EMBEDDING MODEL COMPARISON")
        print("=" * 60)
        
        current_success = current_results['overall_success_index']
        new_success = new_results['overall_success_index']
        improvement = new_success - current_success
        
        print(f"🔄 BGE-large-en-v1.5: {current_success:.1f}%")
        print(f"🆕 BGE-M3: {new_success:.1f}%")
        print(f"📈 Improvement: {improvement:+.1f}%")
        
        print(f"\n⏱️ Performance Comparison:")
        print(f"   BGE-large-en-v1.5: {current_results['avg_embedding_time']:.3f}s avg")
        print(f"   BGE-M3: {new_results['avg_embedding_time']:.3f}s avg")
        
        # Determine winner
        if improvement >= 5:
            print(f"✅ BGE-M3 WINS! Improvement: {improvement:+.1f}% (target: +5-8%)")
            winner = "BGE-M3"
            winner_results = new_results
        elif improvement > 0:
            print(f"🔄 MARGINAL IMPROVEMENT: {improvement:+.1f}% (target: +5-8%)")
            winner = "BGE-M3" if improvement >= 2 else "BGE-large-en-v1.5"
            winner_results = new_results if improvement >= 2 else current_results
        else:
            print(f"❌ NO IMPROVEMENT: {improvement:+.1f}% - keeping current model")
            winner = "BGE-large-en-v1.5"
            winner_results = current_results
        
        # Save comparison results
        comparison_data = {
            'test_date': datetime.now().isoformat(),
            'current_model': current_results,
            'new_model': new_results,
            'improvement': improvement,
            'winner': winner,
            'recommendation': f"Use {winner} for production"
        }
        
    else:
        print("\n❌ BGE-M3 not available - using current model results only")
        winner = "BGE-large-en-v1.5"
        winner_results = current_results
        comparison_data = {
            'test_date': datetime.now().isoformat(),
            'current_model': current_results,
            'new_model': None,
            'improvement': 0,
            'winner': winner,
            'recommendation': "BGE-M3 not available - continue with current model"
        }
    
    # Save detailed results
    with open('phase7_3_embedding_upgrade_results.json', 'w', encoding='utf-8') as f:
        json.dump(comparison_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 Detailed results saved to: phase7_3_embedding_upgrade_results.json")
    print(f"🏆 Recommended model: {winner}")
    
    return winner_results['overall_success_index'], winner

if __name__ == "__main__":
    asyncio.run(test_embedding_upgrade())
