#!/usr/bin/env python3
"""
РЕАЛЕН ТЕСТ НА СЕМАНТИЧНОТО РАЗДЕЛЯНЕ
Проверява дали функцията create_semantic_chunks работи правилно
"""

import sys
import os

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_semantic_chunking():
    """Тества семантичното разделяне с реален текст"""
    print("🧪 РЕАЛЕН ТЕСТ НА СЕМАНТИЧНО РАЗДЕЛЯНЕ")
    print("=" * 60)
    
    # Тестов текст на български
    test_text = """
    Програмата за подкрепа на малките и средните предприятия (МСП) предоставя финансиране за иновативни проекти. 
    Критериите за кандидатстване включват минимум 2 години дейност на предприятието.
    Предприятието трябва да има регистрация в България и да не е в процедура по несъстоятелност.
    
    Максималният размер на безвъзмездната помощ е 200,000 лева. Собственият принос трябва да бъде поне 15% от общия бюджет.
    Проектът трябва да се изпълни в срок от 18 месеца от подписването на договора.
    Срокът за кандидатстване е до 31 декември 2024 година.
    
    Документите за кандидатстване включват бизнес план, финансови отчети и декларация за де минимис помощи.
    Бизнес планът трябва да съдържа подробно описание на проекта и очакваните резултати.
    Финансовите отчети трябва да са за последните две години.
    
    Оценката на проектите се извършва от експертна комисия по критерии за иновативност, устойчивост и въздействие.
    Резултатите от оценката се обявяват в срок от 60 дни след крайния срок за кандидатстване.
    """
    
    try:
        # Опитваме се да импортираме функцията
        try:
            from utils import create_semantic_chunks
            print("✅ Функцията create_semantic_chunks е импортирана успешно")
        except ImportError as e:
            print(f"❌ Грешка при импорт: {e}")
            return False
        
        # Тестваме функцията
        print(f"\n📝 Тестов текст ({len(test_text)} символа):")
        print(f"   Първи 100 символа: {test_text[:100]}...")
        
        # Създаваме chunks
        chunks = create_semantic_chunks(test_text)
        
        print(f"\n📊 РЕЗУЛТАТИ:")
        print(f"   Създадени chunks: {len(chunks)}")
        
        if not chunks:
            print("❌ Няма създадени chunks!")
            return False
        
        # Анализираме всеки chunk
        total_chars = 0
        total_tokens = 0
        
        for i, chunk in enumerate(chunks):
            content = chunk.get('content', '')
            tokens = chunk.get('tokens', 0)
            sentences = chunk.get('sentences', 0)
            chunk_id = chunk.get('chunk_id', i)
            
            total_chars += len(content)
            total_tokens += tokens
            
            print(f"\n   Chunk {chunk_id + 1}:")
            print(f"     Символи: {len(content)}")
            print(f"     Tokens: {tokens}")
            print(f"     Изречения: {sentences}")
            print(f"     Съдържание: {content[:80]}...")
            
            # Проверяваме метаданните
            metadata = chunk.get('metadata', {})
            if metadata:
                print(f"     Метаданни: {list(metadata.keys())}")
        
        # Обобщение
        avg_chunk_size = total_chars / len(chunks) if chunks else 0
        avg_tokens = total_tokens / len(chunks) if chunks else 0
        
        print(f"\n📈 СТАТИСТИКИ:")
        print(f"   Общо символи: {total_chars}")
        print(f"   Общо tokens: {total_tokens}")
        print(f"   Средна дължина chunk: {avg_chunk_size:.0f} символа")
        print(f"   Средно tokens на chunk: {avg_tokens:.0f}")
        
        # Проверяваме качеството
        success_criteria = [
            (len(chunks) > 0, "Има създадени chunks"),
            (avg_tokens >= 50, f"Средно tokens >= 50 (текущо: {avg_tokens:.0f})"),
            (avg_tokens <= 600, f"Средно tokens <= 600 (текущо: {avg_tokens:.0f})"),
            (total_chars > len(test_text) * 0.8, "Запазено е поне 80% от текста")
        ]
        
        print(f"\n🔍 ПРОВЕРКА НА КАЧЕСТВОТО:")
        passed_checks = 0
        for condition, description in success_criteria:
            status = "✅" if condition else "❌"
            print(f"   {status} {description}")
            if condition:
                passed_checks += 1
        
        success_rate = passed_checks / len(success_criteria) * 100
        print(f"\n🎯 РЕЗУЛТАТ: {passed_checks}/{len(success_criteria)} проверки успешни ({success_rate:.0f}%)")
        
        if success_rate >= 75:
            print("🎉 Семантичното разделяне работи добре!")
            return True
        else:
            print("⚠️  Семантичното разделяне се нуждае от подобрения")
            return False
            
    except Exception as e:
        print(f"💥 ГРЕШКА: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Тества гранични случаи"""
    print("\n🧪 ТЕСТ НА ГРАНИЧНИ СЛУЧАИ")
    print("-" * 40)
    
    try:
        from utils import create_semantic_chunks
        
        test_cases = [
            ("", "Празен текст"),
            ("Кратко изречение.", "Много кратък текст"),
            ("А" * 1000, "Текст без пунктуация"),
            ("Първо. Второ! Трето? Четвърто:", "Различни пунктуации")
        ]
        
        for text, description in test_cases:
            print(f"\n   {description}:")
            chunks = create_semantic_chunks(text)
            print(f"     Chunks: {len(chunks)}")
            if chunks:
                print(f"     Първи chunk: {chunks[0].get('content', '')[:50]}...")
        
        print("✅ Всички гранични случаи преминаха без грешки")
        return True
        
    except Exception as e:
        print(f"❌ Грешка в граничните случаи: {e}")
        return False

def main():
    """Главна функция"""
    print("🚀 ТЕСТ НА СЕМАНТИЧНО РАЗДЕЛЯНЕ")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Това е реален тест!")
    print("=" * 60)
    
    # Основен тест
    basic_success = test_semantic_chunking()
    
    # Тест на гранични случаи
    edge_success = test_edge_cases()
    
    # Заключение
    print(f"\n{'='*60}")
    print("🎯 ЗАКЛЮЧЕНИЕ")
    print(f"{'='*60}")
    
    if basic_success and edge_success:
        print("🎉 Семантичното разделяне работи отлично!")
        print("✅ Всички тестове преминаха успешно")
        return True
    elif basic_success:
        print("⚠️  Семантичното разделяне работи основно, но има проблеми с граничните случаи")
        return False
    else:
        print("❌ Семантичното разделяне НЕ работи правилно!")
        print("🔧 Необходими са сериозни поправки")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
