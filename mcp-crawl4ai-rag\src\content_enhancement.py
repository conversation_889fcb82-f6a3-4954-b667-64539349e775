#!/usr/bin/env python3
"""
Phase 4.1: Content Enhancement & Quality Improvement
Advanced content processing for 99% RAG accuracy
"""

import asyncio
import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import statistics
import hashlib

from openai import AsyncOpenAI
from supabase import Client
import spacy
try:
    import textstat
    flesch_reading_ease = textstat.flesch_reading_ease
    flesch_kincaid_grade = textstat.flesch_kincaid_grade
except ImportError:
    # Fallback if textstat is not available
    def flesch_reading_ease(text):
        return 50.0  # Default readability score
    def flesch_kincaid_grade(text):
        return 10.0  # Default grade level
from collections import Counter

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ContentQualityMetrics:
    """Comprehensive content quality assessment"""
    readability_score: float  # 0-100 (higher = more readable)
    information_density: float  # 0-1 (higher = more informative)
    semantic_coherence: float  # 0-1 (higher = more coherent)
    entity_richness: float  # 0-1 (higher = more entities)
    structural_quality: float  # 0-1 (higher = better structure)
    overall_quality: float  # 0-1 (weighted average)
    
@dataclass
class EnhancedContent:
    """Enhanced content with quality metrics and enrichments"""
    original_content: str
    enhanced_content: str
    quality_metrics: ContentQualityMetrics
    extracted_entities: List[Dict[str, Any]]
    key_concepts: List[str]
    semantic_tags: List[str]
    content_type: str  # factual, procedural, comparative, etc.
    enhancement_metadata: Dict[str, Any]

class ContentEnhancer:
    """Advanced content enhancement system for RAG optimization"""
    
    def __init__(self, openai_client: Optional[AsyncOpenAI] = None):
        self.openai_client = openai_client
        self.nlp = None
        self._initialize_nlp()
        
    def _initialize_nlp(self):
        """Initialize spaCy NLP pipeline"""
        try:
            # Try to load Bulgarian model first, fallback to English
            try:
                self.nlp = spacy.load("bg_core_news_sm")
                logger.info("✅ Loaded Bulgarian spaCy model")
            except OSError:
                self.nlp = spacy.load("en_core_web_sm")
                logger.info("✅ Loaded English spaCy model (Bulgarian not available)")
        except Exception as e:
            logger.error(f"❌ Failed to load spaCy model: {e}")
            self.nlp = None
    
    async def enhance_content(
        self, 
        content: str, 
        url: str = "", 
        metadata: Optional[Dict[str, Any]] = None
    ) -> EnhancedContent:
        """
        Comprehensive content enhancement for improved RAG performance
        """
        logger.info(f"🔧 Enhancing content from: {url[:50]}...")
        
        try:
            # Step 1: Content Quality Assessment
            quality_metrics = await self._assess_content_quality(content)
            
            # Step 2: Entity Extraction and Enrichment
            extracted_entities = await self._extract_and_enrich_entities(content)
            
            # Step 3: Key Concept Identification
            key_concepts = await self._identify_key_concepts(content)
            
            # Step 4: Semantic Tagging
            semantic_tags = await self._generate_semantic_tags(content)
            
            # Step 5: Content Type Classification
            content_type = await self._classify_content_type(content)
            
            # Step 6: Content Enhancement
            enhanced_content = await self._enhance_content_text(content, quality_metrics)
            
            # Step 7: Create enhancement metadata
            enhancement_metadata = {
                "enhancement_timestamp": datetime.now().isoformat(),
                "source_url": url,
                "original_length": len(content),
                "enhanced_length": len(enhanced_content),
                "enhancement_ratio": len(enhanced_content) / len(content) if content else 1.0,
                "processing_version": "4.1.0"
            }
            
            return EnhancedContent(
                original_content=content,
                enhanced_content=enhanced_content,
                quality_metrics=quality_metrics,
                extracted_entities=extracted_entities,
                key_concepts=key_concepts,
                semantic_tags=semantic_tags,
                content_type=content_type,
                enhancement_metadata=enhancement_metadata
            )
            
        except Exception as e:
            logger.error(f"❌ Content enhancement failed: {e}")
            # Return minimal enhancement on error
            return EnhancedContent(
                original_content=content,
                enhanced_content=content,
                quality_metrics=ContentQualityMetrics(0.5, 0.5, 0.5, 0.5, 0.5, 0.5),
                extracted_entities=[],
                key_concepts=[],
                semantic_tags=[],
                content_type="unknown",
                enhancement_metadata={"error": str(e)}
            )
    
    async def _assess_content_quality(self, content: str) -> ContentQualityMetrics:
        """Assess content quality across multiple dimensions"""
        
        # Readability Assessment
        try:
            readability = flesch_reading_ease(content)
            readability_score = max(0, min(100, readability)) / 100
        except:
            readability_score = 0.5
        
        # Information Density (unique words / total words)
        words = content.lower().split()
        if words:
            unique_words = len(set(words))
            information_density = min(1.0, unique_words / len(words))
        else:
            information_density = 0.0
        
        # Semantic Coherence (sentence length variance)
        sentences = re.split(r'[.!?]+', content)
        sentence_lengths = [len(s.split()) for s in sentences if s.strip()]
        if sentence_lengths:
            length_variance = statistics.variance(sentence_lengths) if len(sentence_lengths) > 1 else 0
            semantic_coherence = max(0, 1 - (length_variance / 100))  # Normalize
        else:
            semantic_coherence = 0.0
        
        # Entity Richness (using spaCy if available)
        entity_richness = 0.5  # Default
        if self.nlp and content:
            try:
                doc = self.nlp(content[:1000])  # Limit for performance
                entities = [ent for ent in doc.ents]
                entity_richness = min(1.0, len(entities) / max(1, len(content.split()) / 10))
            except:
                pass
        
        # Structural Quality (presence of structure indicators)
        structure_indicators = [
            r'\n\n',  # Paragraphs
            r'^\d+\.',  # Numbered lists
            r'^[-*•]',  # Bullet points
            r'[A-Z][^.!?]*:',  # Headers/titles
        ]
        structure_score = 0
        for pattern in structure_indicators:
            if re.search(pattern, content, re.MULTILINE):
                structure_score += 0.25
        structural_quality = min(1.0, structure_score)
        
        # Overall Quality (weighted average)
        overall_quality = (
            readability_score * 0.2 +
            information_density * 0.25 +
            semantic_coherence * 0.2 +
            entity_richness * 0.2 +
            structural_quality * 0.15
        )
        
        return ContentQualityMetrics(
            readability_score=readability_score,
            information_density=information_density,
            semantic_coherence=semantic_coherence,
            entity_richness=entity_richness,
            structural_quality=structural_quality,
            overall_quality=overall_quality
        )
    
    async def _extract_and_enrich_entities(self, content: str) -> List[Dict[str, Any]]:
        """Extract and enrich entities with additional context"""
        entities = []
        
        if self.nlp:
            try:
                doc = self.nlp(content[:2000])  # Limit for performance
                for ent in doc.ents:
                    entity_info = {
                        "text": ent.text,
                        "label": ent.label_,
                        "start": ent.start_char,
                        "end": ent.end_char,
                        "confidence": getattr(ent, 'confidence', 0.8),
                        "context": content[max(0, ent.start_char-50):ent.end_char+50]
                    }
                    entities.append(entity_info)
            except Exception as e:
                logger.warning(f"Entity extraction failed: {e}")
        
        return entities
    
    async def _identify_key_concepts(self, content: str) -> List[str]:
        """Identify key concepts using frequency and importance analysis"""
        
        # Simple keyword extraction based on frequency and length
        words = re.findall(r'\b[а-яА-Я]{4,}\b|\b[a-zA-Z]{4,}\b', content.lower())
        
        # Filter out common stop words (basic list)
        stop_words = {
            'това', 'който', 'която', 'което', 'които', 'може', 'трябва', 'има', 'няма',
            'this', 'that', 'which', 'what', 'where', 'when', 'how', 'can', 'will', 'should'
        }
        
        filtered_words = [w for w in words if w not in stop_words and len(w) > 3]
        
        # Get most frequent words
        word_counts = Counter(filtered_words)
        key_concepts = [word for word, count in word_counts.most_common(10) if count > 1]
        
        return key_concepts
    
    async def _generate_semantic_tags(self, content: str) -> List[str]:
        """Generate semantic tags for content categorization"""
        
        # Domain-specific tag patterns for EU programs
        tag_patterns = {
            'финансиране': ['финансиране', 'бюджет', 'средства', 'евро', 'лева'],
            'образование': ['образование', 'обучение', 'студенти', 'университет', 'училище'],
            'иновации': ['иновации', 'технологии', 'изследвания', 'развитие', 'наука'],
            'предприемачество': ['предприятия', 'бизнес', 'стартъп', 'мсп', 'компании'],
            'селски_райони': ['селски', 'земеделие', 'фермери', 'село', 'развитие'],
            'дигитализация': ['дигитален', 'цифров', 'технологии', 'интернет', 'софтуер'],
            'околна_среда': ['околна среда', 'климат', 'енергия', 'възобновяеми', 'зелен'],
            'култура': ['култура', 'изкуство', 'творчество', 'наследство', 'музика']
        }
        
        content_lower = content.lower()
        detected_tags = []
        
        for tag, keywords in tag_patterns.items():
            matches = sum(1 for keyword in keywords if keyword in content_lower)
            if matches >= 2:  # Require at least 2 keyword matches
                detected_tags.append(tag)
        
        return detected_tags
    
    async def _classify_content_type(self, content: str) -> str:
        """Classify content type for optimal retrieval strategy"""
        
        content_lower = content.lower()
        
        # Pattern-based classification
        if any(word in content_lower for word in ['как', 'стъпки', 'процедура', 'кандидатстване']):
            return 'procedural'
        elif any(word in content_lower for word in ['сравни', 'разлика', 'различие', 'спрямо']):
            return 'comparative'
        elif any(word in content_lower for word in ['анализ', 'тенденции', 'защо', 'причини']):
            return 'analytical'
        elif any(word in content_lower for word in ['какво', 'кой', 'кога', 'къде', 'колко']):
            return 'factual'
        else:
            return 'informational'
    
    async def _enhance_content_text(self, content: str, quality_metrics: ContentQualityMetrics) -> str:
        """Enhance content text based on quality assessment"""
        
        enhanced = content
        
        # Add structure if missing
        if quality_metrics.structural_quality < 0.5:
            # Add paragraph breaks for long text blocks
            enhanced = re.sub(r'([.!?])\s+([А-ЯA-Z])', r'\1\n\n\2', enhanced)
        
        # Clean up excessive whitespace
        enhanced = re.sub(r'\n{3,}', '\n\n', enhanced)
        enhanced = re.sub(r' {2,}', ' ', enhanced)
        
        # Ensure proper sentence endings
        enhanced = re.sub(r'([а-яa-z])([А-ЯA-Z])', r'\1. \2', enhanced)
        
        return enhanced.strip()

# Utility functions for integration
async def enhance_document_batch(
    documents: List[Dict[str, Any]], 
    openai_client: Optional[AsyncOpenAI] = None
) -> List[Dict[str, Any]]:
    """Enhance a batch of documents with quality improvements"""
    
    enhancer = ContentEnhancer(openai_client)
    enhanced_documents = []
    
    for doc in documents:
        try:
            content = doc.get('content', '')
            url = doc.get('url', '')
            metadata = doc.get('metadata', {})
            
            enhanced = await enhancer.enhance_content(content, url, metadata)
            
            # Update document with enhancements
            enhanced_doc = doc.copy()
            enhanced_doc.update({
                'enhanced_content': enhanced.enhanced_content,
                'quality_score': enhanced.quality_metrics.overall_quality,
                'content_type': enhanced.content_type,
                'key_concepts': enhanced.key_concepts,
                'semantic_tags': enhanced.semantic_tags,
                'entities': enhanced.extracted_entities,
                'enhancement_metadata': enhanced.enhancement_metadata
            })
            
            enhanced_documents.append(enhanced_doc)
            
        except Exception as e:
            logger.error(f"❌ Failed to enhance document {doc.get('url', 'unknown')}: {e}")
            enhanced_documents.append(doc)  # Keep original on error
    
    return enhanced_documents

async def calculate_content_quality_score(content: str) -> float:
    """Quick content quality assessment for filtering"""
    enhancer = ContentEnhancer()
    quality_metrics = await enhancer._assess_content_quality(content)
    return quality_metrics.overall_quality
