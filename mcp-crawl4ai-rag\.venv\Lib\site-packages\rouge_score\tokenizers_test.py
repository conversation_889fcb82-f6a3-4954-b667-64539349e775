# Copyright 2022 The rouge_score Authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Tests for tokenizers."""

from absl.testing import absltest
from rouge_score import tokenizers


class TokenizersTest(absltest.TestCase):

  def test_default_tokenizer_no_stemmer_init(self):
    tokenizer = tokenizers.DefaultTokenizer(use_stemmer=False)
    self.assertIsInstance(tokenizer, tokenizers.Tokenizer)

    result = tokenizer.tokenize("this is a test")
    self.assertListEqual(["this", "is", "a", "test"], result)

  def test_default_tokenizer_with_stemmer_init(self):
    tokenizer = tokenizers.DefaultTokenizer(use_stemmer=True)
    self.assertIsInstance(tokenizer, tokenizers.Tokenizer)

    result = tokenizer.tokenize("the friends had a meeting")
    self.assertListEqual(["the", "friend", "had", "a", "meet"], result)


if __name__ == "__main__":
  absltest.main()
