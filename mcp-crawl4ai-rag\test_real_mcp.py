#!/usr/bin/env python3
"""
Реален end-to-end тест на MCP сървъра чрез директно извикване на функциите
"""
import asyncio
import json
import sys
import os
import time
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Import MCP tools directly
from crawl4ai_mcp import (
    perform_rag_query, enhanced_rag_query, consultant_agent_query,
    multi_vector_search, get_system_health_dashboard,
    app_context, initialize_application_resources
)

class MockContext:
    """Mock context за тестване на MCP tools"""
    def __init__(self):
        self.session_id = "test_session_123"

async def real_end_to_end_test():
    print('🚀 РЕАЛЕН END-TO-END ТЕСТ НА MCP СЪРВЪРА')
    print('=' * 60)

    # Initialize application resources if not already done
    print('⏳ Инициализиране на ресурсите...')
    try:
        if not app_context:
            await initialize_application_resources()
        print('✅ Ресурсите са инициализирани')
    except Exception as e:
        print(f'❌ Грешка при инициализиране: {e}')
        return False

    # Create mock context
    ctx = MockContext()

    # Test queries
    test_queries = [
        'програми за иновации в малки и средни предприятия 2024',
        'финансиране за стартъп компании в България',
        'европейски фондове за дигитализация',
        'ПРЧР програма за обучение',
        'ПКИП иновации предприятия'
    ]

    print('\n🔍 ТЕСТВАНЕ НА RAG ЗАЯВКИ:')
    print('-' * 40)

    for i, query in enumerate(test_queries, 1):
        print(f'\n{i}. Заявка: "{query}"')
        start_time = time.time()

        try:
            # Test perform_rag_query
            result = await perform_rag_query(
                ctx=ctx,
                query=query,
                match_count=5,
                use_reranker=True
            )

            end_time = time.time()
            response_time = end_time - start_time

            print(f'   ⏱️  Време за отговор: {response_time:.2f}s')

            # Parse result
            try:
                parsed = json.loads(result)
                if parsed.get('success'):
                    results = parsed.get('results', [])
                    print(f'   ✅ Успешно намерени: {len(results)} резултата')

                    if results:
                        scores = [r.get('score', 0) for r in results]
                        avg_score = sum(scores) / len(scores)
                        max_score = max(scores)
                        min_score = min(scores)

                        print(f'   📈 Среден score: {avg_score:.3f}')
                        print(f'   🏆 Най-висок score: {max_score:.3f}')
                        print(f'   📉 Най-нисък score: {min_score:.3f}')

                        # Show top result details
                        top_result = results[0]
                        print(f'   🎯 Топ резултат: {top_result.get("title", "N/A")[:50]}...')
                        print(f'   🔗 URL: {top_result.get("url", "N/A")[:60]}...')

                        # Check relevance quality
                        if max_score > 0.8:
                            print('   🌟 ОТЛИЧНО качество на резултатите!')
                        elif max_score > 0.6:
                            print('   👍 Добро качество на резултатите')
                        elif max_score > 0.4:
                            print('   ⚠️  Средно качество на резултатите')
                        else:
                            print('   ❌ Ниско качество на резултатите')
                    else:
                        print('   ⚠️  Няма намерени резултати')
                else:
                    error = parsed.get('error', 'Unknown error')
                    print(f'   ❌ Грешка: {error}')

            except json.JSONDecodeError:
                print(f'   ❌ Невалиден JSON отговор: {result[:100]}...')

        except Exception as e:
            print(f'   💥 Изключение: {str(e)}')

        # Small delay between requests
        await asyncio.sleep(0.5)

    print('\n' + '=' * 60)
    print('🏁 ТЕСТ ЗАВЪРШЕН')

    return True

if __name__ == "__main__":
    result = asyncio.run(real_end_to_end_test())
