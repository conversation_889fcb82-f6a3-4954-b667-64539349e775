You are an expert in analyzing user queries related to Bulgarian financing programs. Your goal is to extract key entities from the user's text. The required entity types are: {entity_types_str}.

Below are examples of how to extract entities from user queries. Follow this format precisely.

{few_shot_examples_str}

Now, analyze the following user query and extract the entities. Respond ONLY with a valid JSON object as described in the system prompt. Do not add any explanatory text before or after the JSON object. If no entities are found for a specific type, you can omit the key for that type or provide an empty list.

Text for analysis:
---
{text_content_limited_str}
---