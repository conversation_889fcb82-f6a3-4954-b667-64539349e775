#!/usr/bin/env python3
"""
Phase 4.2: Hybrid Search Implementation
Combining semantic and keyword-based search for 99% RAG accuracy
"""

import asyncio
import logging
import re
import json
import math
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import Counter, defaultdict

from supabase import Client
from openai import AsyncOpenAI
import numpy as np

from .utils import create_embedding

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class SearchResult:
    """Unified search result with multiple scoring components"""
    document_id: str
    content: str
    title: str
    url: str
    semantic_score: float  # Cosine similarity score
    keyword_score: float   # BM25/TF-IDF score
    hybrid_score: float    # Combined score
    metadata: Dict[str, Any]
    rank_position: int = 0

@dataclass
class HybridSearchConfig:
    """Configuration for hybrid search parameters optimized for Bulgarian content"""
    # 🎯 PHASE 8.1.2: External LLM recommendations - 60% semantic + 40% keyword
    semantic_weight: float = 0.6  # Dense vector weight (reduced from 0.7)
    keyword_weight: float = 0.4   # BM25 weight (increased from 0.3)
    bm25_k1: float = 1.2         # BM25 term frequency saturation
    bm25_b: float = 0.75         # BM25 field length normalization
    min_semantic_threshold: float = 0.1  # Lowered for better recall
    min_keyword_threshold: float = 0.05  # Lowered for better recall
    fusion_method: str = "weighted_sum"  # weighted_sum, rrf, comb_sum

class BM25Scorer:
    """BM25 scoring implementation for keyword-based search"""
    
    def __init__(self, documents: List[Dict[str, Any]], k1: float = 1.2, b: float = 0.75):
        self.k1 = k1
        self.b = b
        self.documents = documents
        self.doc_count = len(documents)
        self.avg_doc_length = 0
        self.doc_frequencies = {}
        self.doc_lengths = {}
        
        self._build_index()
    
    def _build_index(self):
        """Build BM25 index from documents"""
        logger.info(f"🔧 Building BM25 index for {self.doc_count} documents...")
        
        total_length = 0
        term_doc_counts = defaultdict(int)
        
        for i, doc in enumerate(self.documents):
            content = (doc.get('content', '') + ' ' + doc.get('title', '')).lower()
            terms = self._tokenize(content)
            
            self.doc_lengths[i] = len(terms)
            total_length += len(terms)
            
            # Count unique terms per document
            unique_terms = set(terms)
            for term in unique_terms:
                term_doc_counts[term] += 1
        
        self.avg_doc_length = total_length / max(1, self.doc_count)
        
        # Calculate IDF for each term
        for term, doc_count in term_doc_counts.items():
            idf = math.log((self.doc_count - doc_count + 0.5) / (doc_count + 0.5))
            self.doc_frequencies[term] = max(0, idf)
        
        logger.info(f"✅ BM25 index built: {len(self.doc_frequencies)} unique terms")
    
    def _tokenize(self, text: str) -> List[str]:
        """Tokenize text for BM25 scoring"""
        # Simple tokenization - can be enhanced with stemming/lemmatization
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        tokens = [token for token in text.split() if len(token) > 2]
        return tokens
    
    def score_query(self, query: str) -> List[Tuple[int, float]]:
        """Score all documents against query using BM25"""
        query_terms = self._tokenize(query)
        scores = []
        
        for doc_id in range(self.doc_count):
            score = self._calculate_bm25_score(doc_id, query_terms)
            scores.append((doc_id, score))
        
        # Sort by score descending
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores
    
    def _calculate_bm25_score(self, doc_id: int, query_terms: List[str]) -> float:
        """Calculate BM25 score for a document"""
        doc = self.documents[doc_id]
        content = (doc.get('content', '') + ' ' + doc.get('title', '')).lower()
        doc_terms = self._tokenize(content)
        doc_length = self.doc_lengths.get(doc_id, 0)
        
        score = 0.0
        term_counts = Counter(doc_terms)
        
        for term in query_terms:
            if term in term_counts:
                tf = term_counts[term]
                idf = self.doc_frequencies.get(term, 0)
                
                # BM25 formula
                numerator = tf * (self.k1 + 1)
                denominator = tf + self.k1 * (1 - self.b + self.b * (doc_length / self.avg_doc_length))
                
                score += idf * (numerator / denominator)
        
        return score

class HybridSearchEngine:
    """Advanced hybrid search combining semantic and keyword-based retrieval"""
    
    def __init__(self, supabase_client: Client, config: Optional[HybridSearchConfig] = None):
        self.supabase_client = supabase_client
        self.config = config or HybridSearchConfig()
        self.bm25_scorer = None
        self.documents_cache = []
        self.cache_timestamp = None
        
    async def initialize(self):
        """Initialize hybrid search engine with document corpus"""
        logger.info("🔧 Initializing Hybrid Search Engine...")
        
        # Load all documents for BM25 indexing
        await self._load_document_corpus()
        
        # Build BM25 index
        if self.documents_cache:
            self.bm25_scorer = BM25Scorer(
                self.documents_cache, 
                k1=self.config.bm25_k1, 
                b=self.config.bm25_b
            )
        
        logger.info("✅ Hybrid Search Engine initialized")
    
    async def _load_document_corpus(self):
        """Load all documents for keyword indexing"""
        try:
            response = self.supabase_client.table('crawled_pages').select(
                'id, content, url, metadata, created_at'
            ).execute()

            # Add empty title field for compatibility
            for doc in response.data:
                doc['title'] = doc.get('title', '')

            self.documents_cache = response.data
            logger.info(f"📚 Loaded {len(self.documents_cache)} documents for hybrid search")

        except Exception as e:
            logger.error(f"❌ Failed to load document corpus: {e}")
            self.documents_cache = []
    
    async def hybrid_search(
        self, 
        query: str, 
        limit: int = 10,
        semantic_threshold: Optional[float] = None,
        keyword_threshold: Optional[float] = None
    ) -> List[SearchResult]:
        """
        Perform hybrid search combining semantic and keyword-based retrieval
        """
        logger.info(f"🔍 Performing hybrid search: '{query[:50]}...'")
        
        if not self.bm25_scorer:
            await self.initialize()
        
        # Use config defaults if not specified
        sem_threshold = semantic_threshold or self.config.min_semantic_threshold
        kw_threshold = keyword_threshold or self.config.min_keyword_threshold
        
        try:
            # Step 1: Semantic Search
            semantic_results = await self._semantic_search(query, limit * 2)
            
            # Step 2: Keyword Search
            keyword_results = await self._keyword_search(query, limit * 2)
            
            # Step 3: Fusion and Ranking
            hybrid_results = await self._fuse_results(
                semantic_results, 
                keyword_results, 
                query,
                sem_threshold,
                kw_threshold
            )
            
            # Step 4: Final ranking and limiting
            final_results = hybrid_results[:limit]
            
            logger.info(f"✅ Hybrid search completed: {len(final_results)} results")
            return final_results
            
        except Exception as e:
            logger.error(f"❌ Hybrid search failed: {e}")
            return []
    
    async def _semantic_search(self, query: str, limit: int) -> List[SearchResult]:
        """Perform semantic search using embeddings"""
        try:
            query_embedding = create_embedding(query)
            
            response = self.supabase_client.rpc('match_crawled_pages_v4_debug', {
                'p_query_embedding': query_embedding,
                'p_match_count': limit,
                'p_min_similarity_threshold': 0.1,
                'p_min_program_containment_threshold': 0.0,
                'p_query_entities_filter': [],
                'p_query_program_canonical_names_jsonb': {},
                'p_source_filters_jsonb': {},
                'p_weight_similarity': 1.0,
                'p_weight_program_name': 0.0,
                'p_weight_year': 0.0
            }).execute()
            
            results = []
            for i, doc in enumerate(response.data):
                result = SearchResult(
                    document_id=str(doc.get('id', '')),
                    content=doc.get('content', ''),
                    title=doc.get('title', ''),
                    url=doc.get('url', ''),
                    semantic_score=doc.get('similarity', 0.0),
                    keyword_score=0.0,  # Will be filled in fusion
                    hybrid_score=0.0,   # Will be calculated in fusion
                    metadata=doc.get('metadata', {}),
                    rank_position=i + 1
                )
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Semantic search failed: {e}")
            return []
    
    async def _keyword_search(self, query: str, limit: int) -> List[SearchResult]:
        """Perform keyword-based search using BM25"""
        if not self.bm25_scorer:
            return []
        
        try:
            bm25_scores = self.bm25_scorer.score_query(query)
            
            results = []
            for i, (doc_idx, score) in enumerate(bm25_scores[:limit]):
                if doc_idx < len(self.documents_cache):
                    doc = self.documents_cache[doc_idx]
                    result = SearchResult(
                        document_id=str(doc.get('id', '')),
                        content=doc.get('content', ''),
                        title=doc.get('title', ''),
                        url=doc.get('url', ''),
                        semantic_score=0.0,  # Will be filled in fusion
                        keyword_score=score,
                        hybrid_score=0.0,    # Will be calculated in fusion
                        metadata=doc.get('metadata', {}),
                        rank_position=i + 1
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Keyword search failed: {e}")
            return []
    
    async def _fuse_results(
        self, 
        semantic_results: List[SearchResult],
        keyword_results: List[SearchResult],
        query: str,
        semantic_threshold: float,
        keyword_threshold: float
    ) -> List[SearchResult]:
        """Fuse semantic and keyword search results"""
        
        # Create lookup dictionaries
        semantic_lookup = {r.document_id: r for r in semantic_results}
        keyword_lookup = {r.document_id: r for r in keyword_results}
        
        # Get all unique document IDs
        all_doc_ids = set(semantic_lookup.keys()) | set(keyword_lookup.keys())
        
        fused_results = []
        
        for doc_id in all_doc_ids:
            semantic_result = semantic_lookup.get(doc_id)
            keyword_result = keyword_lookup.get(doc_id)
            
            # Get scores (default to 0 if not found)
            semantic_score = semantic_result.semantic_score if semantic_result else 0.0
            keyword_score = keyword_result.keyword_score if keyword_result else 0.0
            
            # Apply thresholds
            if semantic_score < semantic_threshold and keyword_score < keyword_threshold:
                continue
            
            # Use the result with more complete information
            base_result = semantic_result or keyword_result

            # Skip if no valid base result
            if not base_result:
                continue

            # Calculate hybrid score based on fusion method
            if self.config.fusion_method == "weighted_sum":
                hybrid_score = (
                    semantic_score * self.config.semantic_weight +
                    keyword_score * self.config.keyword_weight
                )
            elif self.config.fusion_method == "rrf":
                # Reciprocal Rank Fusion
                sem_rank = semantic_result.rank_position if semantic_result else 1000
                kw_rank = keyword_result.rank_position if keyword_result else 1000
                hybrid_score = 1 / (60 + sem_rank) + 1 / (60 + kw_rank)
            else:  # comb_sum
                hybrid_score = semantic_score + keyword_score

            # Create fused result with safe attribute access
            fused_result = SearchResult(
                document_id=doc_id,
                content=getattr(base_result, 'content', '') if base_result else '',
                title=getattr(base_result, 'title', '') if base_result else '',
                url=getattr(base_result, 'url', '') if base_result else '',
                semantic_score=semantic_score,
                keyword_score=keyword_score,
                hybrid_score=hybrid_score,
                metadata=getattr(base_result, 'metadata', {}) if base_result else {}
            )
            
            fused_results.append(fused_result)
        
        # Sort by hybrid score
        fused_results.sort(key=lambda x: x.hybrid_score, reverse=True)
        
        # Update rank positions
        for i, result in enumerate(fused_results):
            result.rank_position = i + 1
        
        logger.info(f"🔀 Fused {len(semantic_results)} semantic + {len(keyword_results)} keyword → {len(fused_results)} hybrid results")
        
        return fused_results

# Utility functions for integration
async def create_hybrid_search_engine(supabase_client: Client) -> HybridSearchEngine:
    """Create and initialize hybrid search engine"""
    engine = HybridSearchEngine(supabase_client)
    await engine.initialize()
    return engine

async def hybrid_rag_search(
    query: str,
    supabase_client: Client,
    limit: int = 5,
    semantic_weight: float = 0.6,  # 🎯 PHASE 8.1.2: Optimized for Bulgarian content
    keyword_weight: float = 0.4    # 🎯 PHASE 8.1.2: Increased BM25 weight
) -> List[Dict[str, Any]]:
    """Simplified hybrid search for RAG integration"""
    
    config = HybridSearchConfig(
        semantic_weight=semantic_weight,
        keyword_weight=keyword_weight
    )
    
    engine = HybridSearchEngine(supabase_client, config)
    await engine.initialize()
    
    results = await engine.hybrid_search(query, limit)
    
    # Convert to standard format
    formatted_results = []
    for result in results:
        formatted_result = {
            'id': result.document_id,
            'content': result.content,
            'title': result.title,
            'url': result.url,
            'similarity': result.semantic_score,
            'keyword_score': result.keyword_score,
            'hybrid_score': result.hybrid_score,
            'metadata': result.metadata
        }
        formatted_results.append(formatted_result)
    
    return formatted_results
