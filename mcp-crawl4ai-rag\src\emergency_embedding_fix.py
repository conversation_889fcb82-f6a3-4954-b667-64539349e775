#!/usr/bin/env python3
"""
Emergency Embedding Fix - Незабавна поправка на embedding проблема
Изчистване на старите embeddings и regeneration с правилния модел
"""

import asyncio
import time
import json
from typing import List, Dict, Any
from dotenv import load_dotenv
from utils import get_supabase_client, create_embedding, create_embeddings_batch

# Load environment variables
load_dotenv('../.env')

class EmergencyEmbeddingFixer:
    def __init__(self):
        self.supabase = get_supabase_client()
        self.processed_count = 0
        self.total_count = 0
        self.start_time = time.time()
    
    async def analyze_current_state(self):
        """Анализ на текущото състояние"""
        print("🔍 АНАЛИЗ НА ТЕКУЩО СЪСТОЯНИЕ...")
        
        try:
            # Общ брой записи
            count_response = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).execute()
            self.total_count = count_response.count if hasattr(count_response, 'count') else 0
            print(f"📊 Общо записи: {self.total_count}")
            
            # Проверка на embedding размерности
            sample_response = self.supabase.table('crawled_pages').select(
                'id, embedding'
            ).limit(5).execute()
            
            if sample_response.data:
                dimensions = []
                for record in sample_response.data:
                    if record.get('embedding'):
                        dimensions.append(len(record['embedding']))
                
                print(f"📏 Текущи размерности: {dimensions}")
                print(f"⚠️ Проблем: Несъвместими размерности с модела (очаквани: 1024)")
            
            # Проверка колко записи имат embeddings
            with_embeddings = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).not_.is_('embedding', 'null').execute()
            
            embeddings_count = with_embeddings.count if hasattr(with_embeddings, 'count') else 0
            print(f"🧠 Записи с embeddings: {embeddings_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ Грешка при анализ: {e}")
            return False
    
    async def clear_invalid_embeddings(self):
        """Изчистване на невалидните embeddings"""
        print("\n🧹 ИЗЧИСТВАНЕ НА НЕВАЛИДНИ EMBEDDINGS...")
        
        try:
            # Изчистване на всички embeddings (ще ги regenerate с правилния модел)
            response = self.supabase.table('crawled_pages').update({
                'embedding': None
            }).neq('id', 0).execute()  # Update всички записи
            
            print(f"✅ Изчистени embeddings за всички записи")
            
            # Проверка че са изчистени
            check_response = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).not_.is_('embedding', 'null').execute()
            
            remaining_count = check_response.count if hasattr(check_response, 'count') else 0
            print(f"📊 Оставащи embeddings: {remaining_count}")
            
            return True
            
        except Exception as e:
            print(f"❌ Грешка при изчистване: {e}")
            return False
    
    async def regenerate_embeddings_batch(self, batch_size: int = 10):
        """Regeneration на embeddings на batch-ове"""
        print(f"\n🔄 REGENERATION НА EMBEDDINGS (batch size: {batch_size})...")
        
        offset = 0
        total_processed = 0
        
        while True:
            try:
                # Вземане на batch записи без embeddings
                response = self.supabase.table('crawled_pages').select(
                    'id, content, url'
                ).is_('embedding', 'null').range(offset, offset + batch_size - 1).execute()
                
                if not response.data:
                    print("✅ Всички записи са обработени!")
                    break
                
                batch_data = response.data
                print(f"\n📦 Обработка на batch {offset//batch_size + 1}: {len(batch_data)} записа...")
                
                # Създаване на embeddings за batch-а
                texts = []
                record_ids = []
                
                for record in batch_data:
                    content = record.get('content', '')
                    if content and content.strip():
                        texts.append(content[:2000])  # Ограничаваме до 2000 символа
                        record_ids.append(record['id'])
                    else:
                        print(f"   ⚠️ Празно съдържание за запис {record['id']}")
                
                if texts:
                    # Създаване на embeddings
                    start_time = time.time()
                    embeddings = create_embeddings_batch(texts)
                    embedding_time = time.time() - start_time
                    
                    print(f"   🧠 Създадени {len(embeddings)} embeddings за {embedding_time:.2f}s")
                    
                    # Обновяване на записите в базата
                    update_start = time.time()
                    for i, (record_id, embedding) in enumerate(zip(record_ids, embeddings)):
                        try:
                            self.supabase.table('crawled_pages').update({
                                'embedding': embedding
                            }).eq('id', record_id).execute()
                            
                            total_processed += 1
                            
                            if (i + 1) % 5 == 0:
                                print(f"      ✅ Обновени {i + 1}/{len(embeddings)} записа...")
                                
                        except Exception as e:
                            print(f"      ❌ Грешка при обновяване на запис {record_id}: {e}")
                    
                    update_time = time.time() - update_start
                    print(f"   💾 Обновени {len(embeddings)} записа за {update_time:.2f}s")
                    
                    # Прогрес
                    progress = (total_processed / self.total_count) * 100
                    elapsed = time.time() - self.start_time
                    eta = (elapsed / total_processed) * (self.total_count - total_processed) if total_processed > 0 else 0
                    
                    print(f"   📊 Прогрес: {total_processed}/{self.total_count} ({progress:.1f}%)")
                    print(f"   ⏱️ ETA: {eta/60:.1f} минути")
                
                offset += batch_size
                
                # Малка пауза между batch-овете
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"❌ Грешка при обработка на batch: {e}")
                offset += batch_size
                continue
        
        print(f"\n✅ REGENERATION ЗАВЪРШЕН! Обработени {total_processed} записа")
        return total_processed
    
    async def test_regenerated_embeddings(self):
        """Тестване на regenerated embeddings"""
        print("\n🧪 ТЕСТВАНЕ НА REGENERATED EMBEDDINGS...")
        
        try:
            # Проверка на размерности
            sample_response = self.supabase.table('crawled_pages').select(
                'id, embedding'
            ).not_.is_('embedding', 'null').limit(5).execute()
            
            if sample_response.data:
                dimensions = []
                for record in sample_response.data:
                    if record.get('embedding'):
                        dimensions.append(len(record['embedding']))
                
                print(f"📏 Нови размерности: {dimensions}")
                
                if all(d == 1024 for d in dimensions):
                    print("✅ Всички embeddings имат правилна размерност (1024)")
                else:
                    print("⚠️ Има embeddings с неправилна размерност")
            
            # Тест на търсене
            test_query = "Програми за иновации в предприятията"
            test_embedding = create_embedding(test_query)
            
            search_start = time.time()
            response = self.supabase.rpc('match_crawled_pages_v4_debug', {
                'p_query_embedding': test_embedding,
                'p_match_count': 5,
                'p_min_similarity_threshold': 0.1,
                'p_weight_similarity': 1.0,
                'p_weight_program_name': 0.0,
                'p_weight_year': 0.0
            }).execute()
            search_time = time.time() - search_start
            
            if response.data:
                results_count = len(response.data)
                avg_similarity = sum(r.get('similarity_score', 0) for r in response.data) / results_count
                
                print(f"🔍 Тест търсене:")
                print(f"   ⏱️ Време: {search_time:.3f}s")
                print(f"   📊 Резултати: {results_count}")
                print(f"   🎯 Средна similarity: {avg_similarity:.3f}")
                
                # Показване на първия резултат
                if response.data:
                    first_result = response.data[0]
                    print(f"   📄 Първи резултат: {first_result.get('url', 'N/A')[:50]}...")
                    print(f"   📊 Similarity: {first_result.get('similarity_score', 0):.3f}")
                
                return True
            else:
                print("❌ Търсенето не връща резултати")
                return False
                
        except Exception as e:
            print(f"❌ Грешка при тестване: {e}")
            return False
    
    async def generate_performance_comparison(self):
        """Генериране на performance сравнение"""
        print("\n📊 PERFORMANCE СРАВНЕНИЕ...")
        
        # Тестване с няколко заявки
        test_queries = [
            "иновации предприятия",
            "малки средни предприятия",
            "европейски фондове",
            "научни изследвания",
            "дигитализация"
        ]
        
        results = []
        
        for query in test_queries:
            try:
                start_time = time.time()
                embedding = create_embedding(query)
                
                response = self.supabase.rpc('match_crawled_pages_v4_debug', {
                    'p_query_embedding': embedding,
                    'p_match_count': 10,
                    'p_min_similarity_threshold': 0.1,
                    'p_weight_similarity': 1.0,
                    'p_weight_program_name': 0.0,
                    'p_weight_year': 0.0
                }).execute()
                
                execution_time = time.time() - start_time
                results_count = len(response.data) if response.data else 0
                avg_similarity = sum(r.get('similarity_score', 0) for r in response.data) / results_count if results_count > 0 else 0
                
                results.append({
                    'query': query,
                    'time': execution_time,
                    'results': results_count,
                    'avg_similarity': avg_similarity
                })
                
                print(f"   🔍 '{query}': {execution_time:.3f}s, {results_count} резултата, {avg_similarity:.3f} similarity")
                
            except Exception as e:
                print(f"   ❌ Грешка при '{query}': {e}")
        
        # Общи статистики
        if results:
            avg_time = sum(r['time'] for r in results) / len(results)
            avg_results = sum(r['results'] for r in results) / len(results)
            avg_quality = sum(r['avg_similarity'] for r in results) / len(results)
            
            print(f"\n📈 ОБЩИ СТАТИСТИКИ:")
            print(f"   ⏱️ Средно време: {avg_time:.3f}s")
            print(f"   📊 Средно резултати: {avg_results:.1f}")
            print(f"   🎯 Средно качество: {avg_quality:.3f}")
            
            # Сравнение с предишните резултати (25.6% качество)
            quality_improvement = (avg_quality * 100) / 25.6 if avg_quality > 0 else 0
            print(f"   📈 Подобрение на качеството: {quality_improvement:.1f}x")

async def main():
    """Главна функция за emergency fix"""
    print("🚨 ЗАПОЧВАНЕ НА EMERGENCY EMBEDDING FIX")
    print("=" * 60)
    
    fixer = EmergencyEmbeddingFixer()
    
    # Стъпка 1: Анализ на текущото състояние
    if not await fixer.analyze_current_state():
        print("❌ Не мога да анализирам текущото състояние")
        return
    
    # Стъпка 2: Изчистване на невалидните embeddings
    if not await fixer.clear_invalid_embeddings():
        print("❌ Не мога да изчистя невалидните embeddings")
        return
    
    # Стъпка 3: Regeneration на embeddings
    processed_count = await fixer.regenerate_embeddings_batch(batch_size=5)
    
    if processed_count == 0:
        print("❌ Не са обработени записи")
        return
    
    # Стъпка 4: Тестване на резултатите
    if not await fixer.test_regenerated_embeddings():
        print("⚠️ Има проблеми с regenerated embeddings")
    
    # Стъпка 5: Performance сравнение
    await fixer.generate_performance_comparison()
    
    print("\n" + "=" * 60)
    print("✅ EMERGENCY FIX ЗАВЪРШЕН!")
    print("=" * 60)
    
    print("\n📋 РЕЗУЛТАТИ:")
    print(f"• Обработени записи: {processed_count}")
    print(f"• Нова embedding размерност: 1024")
    print(f"• Очаквано подобрение на качеството: 2-3x")
    print(f"• Очаквано подобрение на скоростта: 2x")

if __name__ == "__main__":
    asyncio.run(main())
