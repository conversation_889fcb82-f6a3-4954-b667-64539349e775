#!/usr/bin/env python3
"""
Бърз тест на Multi-Step Pipeline
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_multi_step_quick():
    """Бърз тест на Multi-Step RAG"""
    print("🚀 БЪРЗ ТЕСТ НА MULTI-STEP RAG")
    print("=" * 50)
    
    try:
        from src.utils import multi_step_rag_query, MULTI_STEP_PIPELINE_AVAILABLE
        
        print(f"✅ Multi-Step Pipeline Available: {MULTI_STEP_PIPELINE_AVAILABLE}")
        
        # Един бърз тест
        query = "ОПРР програми за транспорт"
        print(f"\n🔍 Тестване: {query}")
        
        result = multi_step_rag_query(
            query=query,
            max_results=3,
            enable_groq_reranking=True,
            enable_program_extraction=True,
            enable_bulgarian_nlp=False,  # Изключи за по-бърз тест
            debug=True  # Включи debug за повече информация
        )
        
        documents = result.get('documents', [])
        method = result.get('method', 'unknown')
        processing_time = result.get('processing_time', 0)
        
        print(f"\n📊 РЕЗУЛТАТ:")
        print(f"  📄 Документи: {len(documents)}")
        print(f"  🔧 Метод: {method}")
        print(f"  ⏱️ Време: {processing_time:.2f}s")
        
        # Покажи първия документ ако има
        if documents:
            top_doc = documents[0]
            title = top_doc.get('title', 'Без заглавие')[:50]
            score = top_doc.get('final_pipeline_score', top_doc.get('similarity_score', 0))
            print(f"  🏆 Топ документ: {title}... (score: {score:.3f})")
            
            # Покажи всички ключове в документа
            print(f"  🔑 Ключове в документа: {list(top_doc.keys())}")
        else:
            print("  ⚠️ Няма намерени документи!")
            
        # Покажи всички ключове в резултата
        print(f"  🔑 Ключове в резултата: {list(result.keys())}")
        
        return len(documents) > 0
        
    except Exception as e:
        print(f"❌ Грешка в Multi-Step RAG: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_multi_step_quick()
    
    if success:
        print(f"\n🎉 Multi-Step Pipeline работи успешно!")
    else:
        print(f"\n⚠️ Multi-Step Pipeline има проблеми")
