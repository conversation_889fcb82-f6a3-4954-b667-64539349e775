#!/usr/bin/env python3
"""
Phase 4.4: Query Understanding & Intent Detection
Advanced query analysis for 99% RAG accuracy
"""

import asyncio
import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import spacy
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Types of queries for different handling strategies"""
    FACTUAL = "factual"           # Simple fact-based questions
    PROCEDURAL = "procedural"     # How-to questions
    COMPARATIVE = "comparative"   # Comparison questions
    ANALYTICAL = "analytical"     # Complex analysis questions
    TEMPORAL = "temporal"         # Time-based questions
    DEFINITIONAL = "definitional" # What is X questions

class QueryComplexity(Enum):
    """Query complexity levels"""
    SIMPLE = "simple"       # Single concept, direct answer
    MODERATE = "moderate"   # Multiple concepts, some reasoning
    COMPLEX = "complex"     # Multiple concepts, complex reasoning

@dataclass
class QueryAnalysis:
    """Comprehensive query analysis result"""
    original_query: str
    query_type: QueryType
    complexity: QueryComplexity
    entities: List[Dict[str, Any]]
    keywords: List[str]
    programs: List[str]
    years: List[str]
    intent: str
    expanded_queries: List[str]
    confidence: float

class QueryUnderstandingEngine:
    """Advanced query understanding and intent detection"""
    
    def __init__(self):
        self.nlp = None
        self._load_spacy_model()
        
        # Query type patterns
        self.query_patterns = {
            QueryType.FACTUAL: [
                r'\b(what|who|where|when|which)\b',
                r'\b(is|are|was|were)\b.*\?',
                r'\b(tell me|explain)\b'
            ],
            QueryType.PROCEDURAL: [
                r'\b(how to|how do|how can)\b',
                r'\b(steps|process|procedure)\b',
                r'\b(apply|submit|register)\b'
            ],
            QueryType.COMPARATIVE: [
                r'\b(compare|versus|vs|difference|better)\b',
                r'\b(between|among)\b.*\band\b',
                r'\b(similar|different)\b'
            ],
            QueryType.ANALYTICAL: [
                r'\b(analyze|analysis|evaluate|assessment)\b',
                r'\b(why|because|reason|cause)\b',
                r'\b(impact|effect|consequence)\b'
            ],
            QueryType.TEMPORAL: [
                r'\b(when|until|before|after|during)\b',
                r'\b(deadline|period|timeline)\b',
                r'\b(2024|2025|2026)\b'
            ],
            QueryType.DEFINITIONAL: [
                r'\b(what is|what are|define|definition)\b',
                r'\b(meaning|means)\b'
            ]
        }
        
        # EU program patterns
        self.program_patterns = [
            r'\b(horizon|erasmus|interreg|life|cosme|creative)\b',
            r'\b(digital europe|connecting europe)\b',
            r'\b(just transition|recovery)\b'
        ]
        
        logger.info("🧠 Query Understanding Engine initialized")
    
    def _load_spacy_model(self):
        """Load spaCy model for NLP processing"""
        try:
            self.nlp = spacy.load("en_core_web_sm")
            logger.info("✅ spaCy model loaded successfully")
        except OSError:
            logger.warning("⚠️ spaCy model not found, using basic processing")
            self.nlp = None
    
    async def analyze_query(
        self,
        query: str,
        async_openai_client: Optional[AsyncOpenAI] = None
    ) -> QueryAnalysis:
        """
        Comprehensive query analysis
        
        Args:
            query: User query to analyze
            async_openai_client: OpenAI client for advanced analysis
        
        Returns:
            QueryAnalysis with comprehensive understanding
        """
        logger.info(f"🧠 Analyzing query: '{query[:50]}...'")
        
        # Step 1: Basic analysis
        query_type = self._classify_query_type(query)
        complexity = self._assess_complexity(query)
        
        # Step 2: Entity extraction
        entities = await self._extract_entities(query)
        
        # Step 3: Keyword extraction
        keywords = self._extract_keywords(query)
        
        # Step 4: Program and year extraction
        programs = self._extract_programs(query)
        years = self._extract_years(query)
        
        # Step 5: Intent detection
        intent = await self._detect_intent(query, async_openai_client)
        
        # Step 6: Query expansion
        expanded_queries = await self._expand_query(query, async_openai_client)
        
        # Step 7: Confidence calculation
        confidence = self._calculate_confidence(query, entities, keywords)
        
        analysis = QueryAnalysis(
            original_query=query,
            query_type=query_type,
            complexity=complexity,
            entities=entities,
            keywords=keywords,
            programs=programs,
            years=years,
            intent=intent,
            expanded_queries=expanded_queries,
            confidence=confidence
        )
        
        logger.info(f"✅ Query analysis completed: {query_type.value}, {complexity.value}, confidence: {confidence:.2f}")
        return analysis
    
    def _classify_query_type(self, query: str) -> QueryType:
        """Classify query type based on patterns"""
        query_lower = query.lower()
        
        # Score each query type
        type_scores = {}
        for query_type, patterns in self.query_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    score += 1
            type_scores[query_type] = score
        
        # Return type with highest score, default to FACTUAL
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            if type_scores[best_type] > 0:
                return best_type
        
        return QueryType.FACTUAL
    
    def _assess_complexity(self, query: str) -> QueryComplexity:
        """Assess query complexity"""
        
        # Simple heuristics for complexity
        word_count = len(query.split())
        question_marks = query.count('?')
        conjunctions = len(re.findall(r'\b(and|or|but|however|also)\b', query.lower()))
        
        complexity_score = 0
        
        # Word count factor
        if word_count > 20:
            complexity_score += 2
        elif word_count > 10:
            complexity_score += 1
        
        # Multiple questions
        if question_marks > 1:
            complexity_score += 1
        
        # Conjunctions indicate complexity
        complexity_score += conjunctions
        
        # Complex patterns
        complex_patterns = [
            r'\b(compare|analyze|evaluate)\b',
            r'\b(multiple|several|various)\b',
            r'\b(relationship|correlation|impact)\b'
        ]
        
        for pattern in complex_patterns:
            if re.search(pattern, query.lower()):
                complexity_score += 1
        
        # Classify based on score
        if complexity_score >= 4:
            return QueryComplexity.COMPLEX
        elif complexity_score >= 2:
            return QueryComplexity.MODERATE
        else:
            return QueryComplexity.SIMPLE
    
    async def _extract_entities(self, query: str) -> List[Dict[str, Any]]:
        """Extract named entities from query"""
        entities = []
        
        if self.nlp:
            doc = self.nlp(query)
            for ent in doc.ents:
                entities.append({
                    'text': ent.text,
                    'label': ent.label_,
                    'start': ent.start_char,
                    'end': ent.end_char
                })
        
        # Manual entity extraction for EU-specific terms
        eu_entities = self._extract_eu_entities(query)
        entities.extend(eu_entities)
        
        return entities
    
    def _extract_eu_entities(self, query: str) -> List[Dict[str, Any]]:
        """Extract EU-specific entities"""
        entities = []
        query_lower = query.lower()
        
        # EU programs
        programs = self._extract_programs(query)
        for program in programs:
            entities.append({
                'text': program,
                'label': 'EU_PROGRAM',
                'start': query_lower.find(program.lower()),
                'end': query_lower.find(program.lower()) + len(program)
            })
        
        # Years
        years = self._extract_years(query)
        for year in years:
            entities.append({
                'text': year,
                'label': 'YEAR',
                'start': query.find(year),
                'end': query.find(year) + len(year)
            })
        
        return entities
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Extract important keywords from query"""
        
        # Remove stop words and extract meaningful terms
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'what', 'when', 'where', 'who', 'why', 'how', 'which'
        }
        
        words = re.findall(r'\b\w+\b', query.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords
    
    def _extract_programs(self, query: str) -> List[str]:
        """Extract EU program names from query"""
        programs = []
        query_lower = query.lower()
        
        for pattern in self.program_patterns:
            matches = re.findall(pattern, query_lower)
            programs.extend(matches)
        
        # Manual program detection
        program_names = [
            'horizon 2020', 'horizon europe', 'erasmus+', 'erasmus plus',
            'interreg', 'life', 'cosme', 'creative europe', 'digital europe',
            'connecting europe facility', 'just transition fund'
        ]
        
        for program in program_names:
            if program in query_lower:
                programs.append(program)
        
        return list(set(programs))  # Remove duplicates
    
    def _extract_years(self, query: str) -> List[str]:
        """Extract years from query"""
        year_pattern = r'\b(20\d{2})\b'
        years = re.findall(year_pattern, query)
        return list(set(years))
    
    async def _detect_intent(
        self,
        query: str,
        async_openai_client: Optional[AsyncOpenAI] = None
    ) -> str:
        """Detect user intent from query"""
        
        # Basic intent detection based on query type
        query_lower = query.lower()
        
        if any(word in query_lower for word in ['apply', 'application', 'submit']):
            return "application_process"
        elif any(word in query_lower for word in ['deadline', 'when', 'timeline']):
            return "timeline_information"
        elif any(word in query_lower for word in ['eligibility', 'eligible', 'criteria']):
            return "eligibility_check"
        elif any(word in query_lower for word in ['funding', 'budget', 'amount']):
            return "funding_information"
        elif any(word in query_lower for word in ['compare', 'difference', 'versus']):
            return "comparison"
        else:
            return "general_information"
    
    async def _expand_query(
        self,
        query: str,
        async_openai_client: Optional[AsyncOpenAI] = None
    ) -> List[str]:
        """Generate expanded queries for better retrieval"""
        
        expanded = [query]  # Always include original
        
        # Simple expansion based on synonyms and related terms
        expansions = {
            'funding': ['grant', 'financial support', 'money'],
            'application': ['apply', 'submission', 'proposal'],
            'deadline': ['timeline', 'due date', 'closing date'],
            'eligibility': ['criteria', 'requirements', 'conditions']
        }
        
        query_lower = query.lower()
        for term, synonyms in expansions.items():
            if term in query_lower:
                for synonym in synonyms:
                    expanded_query = query_lower.replace(term, synonym)
                    expanded.append(expanded_query)
        
        return expanded[:5]  # Limit to 5 expansions
    
    def _calculate_confidence(
        self,
        query: str,
        entities: List[Dict[str, Any]],
        keywords: List[str]
    ) -> float:
        """Calculate confidence in query understanding"""
        
        confidence = 0.5  # Base confidence
        
        # Boost confidence based on entities found
        if entities:
            confidence += min(0.3, len(entities) * 0.1)
        
        # Boost confidence based on keywords
        if keywords:
            confidence += min(0.2, len(keywords) * 0.02)
        
        # Boost confidence for clear question structure
        if '?' in query:
            confidence += 0.1
        
        # Boost confidence for specific EU terms
        eu_terms = ['horizon', 'erasmus', 'interreg', 'europe', 'eu', 'funding']
        eu_term_count = sum(1 for term in eu_terms if term in query.lower())
        confidence += min(0.2, eu_term_count * 0.05)
        
        return min(1.0, confidence)

# Global instance
query_understanding_engine = QueryUnderstandingEngine()

async def analyze_user_query(
    query: str,
    async_openai_client: Optional[AsyncOpenAI] = None
) -> Dict[str, Any]:
    """
    Analyze user query for enhanced RAG retrieval
    
    Args:
        query: User query to analyze
        async_openai_client: OpenAI client for advanced analysis
    
    Returns:
        Query analysis as dictionary
    """
    
    analysis = await query_understanding_engine.analyze_query(
        query=query,
        async_openai_client=async_openai_client
    )
    
    # Convert to dictionary for easy use
    return {
        'original_query': analysis.original_query,
        'query_type': analysis.query_type.value,
        'complexity': analysis.complexity.value,
        'entities': analysis.entities,
        'keywords': analysis.keywords,
        'programs': analysis.programs,
        'years': analysis.years,
        'intent': analysis.intent,
        'expanded_queries': analysis.expanded_queries,
        'confidence': analysis.confidence
    }
