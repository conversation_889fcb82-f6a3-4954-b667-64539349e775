#!/usr/bin/env python3
"""
Debug script за проверка на embedding конверсия
"""

import numpy as np
from sentence_transformers import SentenceTransformer

def test_embedding_conversion():
    """Тестване на embedding конверсия"""
    
    print("=== EMBEDDING CONVERSION DEBUG ===")
    
    # Зареждане на модел
    model = SentenceTransformer("BAAI/bge-large-en-v1.5")
    
    # Тест текстове
    texts = ["Test text 1", "Test text 2", "Test text 3"]
    
    print(f"Тестване с {len(texts)} текста...")
    
    # Генериране на embeddings
    embeddings = model.encode(texts, normalize_embeddings=True)
    
    print(f"Embeddings shape: {embeddings.shape}")
    print(f"Embeddings type: {type(embeddings)}")
    print(f"Single embedding shape: {embeddings[0].shape}")
    print(f"Single embedding type: {type(embeddings[0])}")
    
    # Тестване на различни конверсии
    print("\n=== CONVERSION TESTS ===")
    
    # Метод 1: Правилен начин
    print("1. Правилен начин:")
    correct_embeddings = embeddings.tolist()
    print(f"   Type: {type(correct_embeddings)}")
    print(f"   Length: {len(correct_embeddings)}")
    print(f"   First embedding length: {len(correct_embeddings[0])}")
    
    # Метод 2: Грешен начин (текущ в кода)
    print("2. Грешен начин (текущ в кода):")
    wrong_embeddings = [embedding.tolist() for embedding in embeddings]
    print(f"   Type: {type(wrong_embeddings)}")
    print(f"   Length: {len(wrong_embeddings)}")
    print(f"   First embedding length: {len(wrong_embeddings[0])}")
    
    # Метод 3: Алтернативен начин
    print("3. Алтернативен начин:")
    alt_embeddings = [emb.tolist() for emb in embeddings]
    print(f"   Type: {type(alt_embeddings)}")
    print(f"   Length: {len(alt_embeddings)}")
    print(f"   First embedding length: {len(alt_embeddings[0])}")
    
    # Проверка дали са еднакви
    print("\n=== COMPARISON ===")
    print(f"Правилен == Грешен: {correct_embeddings == wrong_embeddings}")
    print(f"Правилен == Алтернативен: {correct_embeddings == alt_embeddings}")
    
    # Детайлна проверка на първия embedding
    print("\n=== DETAILED CHECK ===")
    print(f"Правилен първи embedding (първи 10): {correct_embeddings[0][:10]}")
    print(f"Грешен първи embedding (първи 10): {wrong_embeddings[0][:10]}")
    
    # Проверка на размерности
    print(f"\nПравилен размерност: {len(correct_embeddings[0])}")
    print(f"Грешен размерност: {len(wrong_embeddings[0])}")
    
    return correct_embeddings, wrong_embeddings

if __name__ == "__main__":
    correct, wrong = test_embedding_conversion()
    print("\n✅ Тест завърши")
