# Съдържание на файла manual_test.py - ВЕРСИЯ 3 (ФИНАЛНА)

import os
import sys
import logging
from dotenv import load_dotenv

# Добавяме основната директория към пътя, за да работят относителните импорти
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Сега импортираме, след като сме оправили пътя
from phase5_adaptive_fusion import adaptive_fusion_search
from supabase import create_client
from openai import OpenAI

# Конфигуриране на логване
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

QUESTIONS = [
    'Какви са възможностите за развитие на неземеделски дейности в селските райони?',
    'Има ли програми за подкрепа на производствени инвестиции в Стара Загора?',
    'Какви мерки за справедлив преход подкрепят индустриалните паркове?'
]

def manual_verification_test():
    print('--- 🕵️‍♂️ ЗАПОЧВАМ РЪЧНА ПРОВЕРКА (ФИНАЛНА ВЕРСИЯ) 🕵️‍♂️ ---')
    load_dotenv()
    try:
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_SERVICE_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ Грешка: SUPABASE_URL или SUPABASE_SERVICE_KEY не са намерени в .env файла.")
            return

        supabase_client = create_client(supabase_url, supabase_key)
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    except Exception as e:
        print(f'❌ Грешка при свързване! Проверете .env файла. Грешка: {e}')
        return

    for i, question in enumerate(QUESTIONS, 1):
        print(f'\n================== ТЕСТ #{i} ==================')
        print(f'❓ Въпрос: "{question}"')
        print('--------------------------------------------------')
        
        try:
            # --- КЛЮЧОВА ПОПРАВКА: Премахнат 'await' ---
            results = adaptive_fusion_search(
                supabase_client,
                question,
                openai_client,
                match_count=2
            )
            
            if not results:
                print('❌ Системата не върна резултати.')
            else:
                print(f'✅ Намерени {len(results)} резултата:')
                for j, result in enumerate(results, 1):
                    score = result.get('adaptive_score', 'N/A')
                    url = result.get('url', 'N/A')
                    print(f'  {j}. URL: {url} | Score: {score:.4f}')
        except Exception as e:
            print(f"🔥 Възникна грешка по време на търсенето: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    manual_verification_test()