#!/usr/bin/env python3
"""
Реален тест на унифицираната RAG архитектура
Тества дали системата работи с новите оптимизирани параметри
"""

import asyncio
import json
import sys
import os
import time
from typing import Dict, Any
from dotenv import load_dotenv

# Зареждаме environment variables от .env файла
load_dotenv()

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import (
    get_supabase_client,
    enhanced_semantic_search,
    ultra_smart_rag_query
)

async def test_unified_rag_architecture():
    """
    Реален тест на унифицираната RAG архитектура
    Тества и двата режима с реални заявки
    """
    print("🧪 ЗАПОЧВА РЕАЛЕН ТЕСТ НА УНИФИЦИРАНАТА RAG АРХИТЕКТУРА")
    print("=" * 70)
    
    # Инициализация на Supabase клиент
    try:
        supabase_client = get_supabase_client()
        print("✅ Supabase клиент инициализиран успешно")
    except Exception as e:
        print(f"❌ Грешка при инициализация на Supabase: {e}")
        return False
    
    # Тестови заявки на български
    test_queries = [
        "европейски фондове за малки предприятия",
        "програми за дигитализация на бизнеса",
        "финансиране на иновации в България"
    ]
    
    results = {
        "standard_mode": [],
        "advanced_mode": [],
        "performance_comparison": {}
    }
    
    print(f"\n📋 ТЕСТВАНЕ С {len(test_queries)} РЕАЛНИ ЗАЯВКИ")
    print("-" * 50)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 ЗАЯВКА {i}: '{query}'")
        print("-" * 30)
        
        # ТЕСТ 1: Standard режим (enhanced_semantic_search)
        print("📊 Тестване на STANDARD режим...")
        start_time = time.time()
        
        try:
            standard_results = await enhanced_semantic_search(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=None,  # Без OpenAI за по-бърз тест
                similarity_threshold=0.62,  # Новия оптимизиран праг
                final_top_k=5
            )
            standard_time = time.time() - start_time
            standard_count = len(standard_results) if isinstance(standard_results, list) else 0
            
            print(f"   ✅ Standard: {standard_count} резултата за {standard_time:.2f}s")
            
            results["standard_mode"].append({
                "query": query,
                "results_count": standard_count,
                "execution_time": standard_time,
                "success": True
            })
            
        except Exception as e:
            print(f"   ❌ Standard режим грешка: {e}")
            results["standard_mode"].append({
                "query": query,
                "results_count": 0,
                "execution_time": 0,
                "success": False,
                "error": str(e)
            })
        
        # ТЕСТ 2: Advanced режим (ultra_smart_rag_query)
        print("🚀 Тестване на ADVANCED режим...")
        start_time = time.time()
        
        try:
            advanced_results = await ultra_smart_rag_query(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=None,  # Без OpenAI за по-бърз тест
                similarity_threshold=0.62,  # Новия оптимизиран праг
                final_top_k=5,
                enable_all_optimizations=True
            )
            advanced_time = time.time() - start_time
            
            # Проверяваме дали резултатът е dict с results ключ
            if isinstance(advanced_results, dict) and "results" in advanced_results:
                advanced_count = len(advanced_results["results"])
            elif isinstance(advanced_results, list):
                advanced_count = len(advanced_results)
            else:
                advanced_count = 0
            
            print(f"   ✅ Advanced: {advanced_count} резултата за {advanced_time:.2f}s")
            
            results["advanced_mode"].append({
                "query": query,
                "results_count": advanced_count,
                "execution_time": advanced_time,
                "success": True
            })
            
        except Exception as e:
            print(f"   ❌ Advanced режим грешка: {e}")
            results["advanced_mode"].append({
                "query": query,
                "results_count": 0,
                "execution_time": 0,
                "success": False,
                "error": str(e)
            })
        
        # Кратка пауза между заявките
        await asyncio.sleep(0.5)
    
    # АНАЛИЗ НА РЕЗУЛТАТИТЕ
    print("\n" + "=" * 70)
    print("📊 АНАЛИЗ НА РЕЗУЛТАТИТЕ")
    print("=" * 70)
    
    # Standard режим статистики
    standard_success = sum(1 for r in results["standard_mode"] if r["success"])
    standard_avg_time = sum(r["execution_time"] for r in results["standard_mode"] if r["success"]) / max(standard_success, 1)
    standard_total_results = sum(r["results_count"] for r in results["standard_mode"] if r["success"])
    
    print(f"\n🔹 STANDARD РЕЖИМ:")
    print(f"   Успешни заявки: {standard_success}/{len(test_queries)}")
    print(f"   Средно време: {standard_avg_time:.2f}s")
    print(f"   Общо резултати: {standard_total_results}")
    
    # Advanced режим статистики
    advanced_success = sum(1 for r in results["advanced_mode"] if r["success"])
    advanced_avg_time = sum(r["execution_time"] for r in results["advanced_mode"] if r["success"]) / max(advanced_success, 1)
    advanced_total_results = sum(r["results_count"] for r in results["advanced_mode"] if r["success"])
    
    print(f"\n🔹 ADVANCED РЕЖИМ:")
    print(f"   Успешни заявки: {advanced_success}/{len(test_queries)}")
    print(f"   Средно време: {advanced_avg_time:.2f}s")
    print(f"   Общо резултати: {advanced_total_results}")
    
    # Сравнение на производителността
    results["performance_comparison"] = {
        "standard_success_rate": standard_success / len(test_queries),
        "advanced_success_rate": advanced_success / len(test_queries),
        "standard_avg_time": standard_avg_time,
        "advanced_avg_time": advanced_avg_time,
        "standard_total_results": standard_total_results,
        "advanced_total_results": advanced_total_results
    }
    
    # ЗАКЛЮЧЕНИЕ
    print(f"\n" + "=" * 70)
    print("🎯 ЗАКЛЮЧЕНИЕ")
    print("=" * 70)
    
    overall_success = (standard_success + advanced_success) >= (len(test_queries) * 1.5)  # Поне 75% успех
    
    if overall_success:
        print("✅ ТЕСТЪТ ПРЕМИНА УСПЕШНО!")
        print("   - Унифицираната архитектура работи коректно")
        print("   - Новите параметри (similarity_threshold=0.62) са ефективни")
        print("   - И двата режима (standard/advanced) функционират")
    else:
        print("❌ ТЕСТЪТ НЕ ПРЕМИНА!")
        print("   - Има проблеми с унифицираната архитектура")
        print("   - Необходими са допълнителни корекции")
    
    # Запазване на резултатите
    with open("test_results_unified_rag.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Детайлни резултати запазени в: test_results_unified_rag.json")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(test_unified_rag_architecture())
    sys.exit(0 if success else 1)
