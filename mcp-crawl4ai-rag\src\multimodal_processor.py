"""
🎯 Multi-modal Document Processing System for EU Programs
========================================================

Comprehensive system for processing:
- PDF documents with OCR and table extraction
- Images and diagrams with visual analysis
- Forms and structured documents
- Tabular data extraction and analysis
- Multi-language content processing (Bulgarian focus)

Author: Augment Agent
Date: 2025-01-05
"""

import os
import io
import logging
import asyncio
import base64
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import tempfile

# Core libraries
import fitz  # PyMuPDF for PDF processing
import cv2
import numpy as np
from PIL import Image, ImageEnhance
import pytesseract
import pandas as pd
import tabula

# Advanced document processing
try:
    from unstructured.partition.auto import partition
    from unstructured.partition.pdf import partition_pdf
    from unstructured.partition.image import partition_image
    UNSTRUCTURED_AVAILABLE = True
except ImportError:
    UNSTRUCTURED_AVAILABLE = False

# Table extraction
try:
    import camelot
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False

# Vision models (optional)
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Bulgarian language processing
try:
    from .bulgarian_nlp import BulgarianNLP
    BULGARIAN_NLP_AVAILABLE = True
except ImportError:
    try:
        from bulgarian_nlp import BulgarianNLP
        BULGARIAN_NLP_AVAILABLE = True
    except ImportError:
        BULGARIAN_NLP_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class ProcessingResult:
    """Result of multi-modal document processing"""
    success: bool
    content_type: str
    extracted_text: str = ""
    tables: List[Dict[str, Any]] = field(default_factory=list)
    images: List[Dict[str, Any]] = field(default_factory=list)
    forms: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    processing_time: float = 0.0
    confidence_score: float = 0.0

@dataclass
class MultiModalConfig:
    """Configuration for multi-modal processing"""
    # OCR settings
    ocr_language: str = "bul+eng"  # Bulgarian + English
    ocr_confidence_threshold: float = 0.6
    
    # Image processing
    enhance_images: bool = True
    image_dpi: int = 300
    
    # Table extraction
    extract_tables: bool = True
    table_confidence_threshold: float = 0.7
    
    # Vision model settings
    use_vision_model: bool = False
    vision_model: str = "gpt-4-vision-preview"
    
    # Bulgarian processing
    use_bulgarian_nlp: bool = True
    
    # Performance settings
    max_file_size_mb: int = 50
    timeout_seconds: int = 120

class MultiModalProcessor:
    """
    🎯 Advanced Multi-Modal Document Processor
    
    Capabilities:
    1. PDF processing with OCR and table extraction
    2. Image analysis with Bulgarian text recognition
    3. Form detection and field extraction
    4. Table extraction and structuring
    5. Visual content analysis
    6. Multi-language content processing
    """
    
    def __init__(self, config: Optional[MultiModalConfig] = None):
        self.config = config or MultiModalConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.bulgarian_nlp = None
        self.openai_client = None
        
        self._initialize_components()
        
        self.logger.info("🎯 Multi-Modal Processor initialized")
    
    def _initialize_components(self):
        """Initialize processing components"""
        
        # Initialize Bulgarian NLP
        if self.config.use_bulgarian_nlp and BULGARIAN_NLP_AVAILABLE:
            try:
                self.bulgarian_nlp = BulgarianNLP()
                self.logger.info("✅ Bulgarian NLP initialized")
            except Exception as e:
                self.logger.warning(f"⚠️ Bulgarian NLP initialization failed: {e}")
        
        # Initialize OpenAI client for vision
        if self.config.use_vision_model and OPENAI_AVAILABLE:
            try:
                api_key = os.getenv("OPENAI_API_KEY")
                if api_key:
                    self.openai_client = openai.OpenAI(api_key=api_key)
                    self.logger.info("✅ OpenAI Vision client initialized")
                else:
                    self.logger.warning("⚠️ OPENAI_API_KEY not found")
            except Exception as e:
                self.logger.warning(f"⚠️ OpenAI client initialization failed: {e}")
    
    async def process_document(
        self, 
        content: Union[bytes, str], 
        content_type: str,
        source_url: str = "unknown"
    ) -> ProcessingResult:
        """
        🎯 Main entry point for multi-modal document processing
        
        Args:
            content: Document content (bytes for binary, str for text)
            content_type: MIME type or file extension
            source_url: Source URL for logging and metadata
            
        Returns:
            ProcessingResult with extracted content and metadata
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            self.logger.info(f"🔍 Processing {content_type} document from {source_url}")
            
            # Check file size
            if isinstance(content, bytes):
                size_mb = len(content) / (1024 * 1024)
                if size_mb > self.config.max_file_size_mb:
                    return ProcessingResult(
                        success=False,
                        content_type=content_type,
                        error_message=f"File too large: {size_mb:.1f}MB > {self.config.max_file_size_mb}MB"
                    )
            
            # Route to appropriate processor
            if content_type.lower() in ['pdf', 'application/pdf'] or source_url.lower().endswith('.pdf'):
                result = await self._process_pdf(content, source_url)
            elif content_type.lower().startswith('image/') or source_url.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff')):
                result = await self._process_image(content, source_url)
            elif content_type.lower() in ['text/html', 'html_markdown']:
                result = await self._process_html(content, source_url)
            else:
                result = await self._process_generic(content, content_type, source_url)
            
            # Calculate processing time
            result.processing_time = asyncio.get_event_loop().time() - start_time
            
            self.logger.info(f"✅ Processing completed in {result.processing_time:.2f}s")
            return result
            
        except Exception as e:
            processing_time = asyncio.get_event_loop().time() - start_time
            self.logger.error(f"❌ Processing failed: {e}", exc_info=True)
            return ProcessingResult(
                success=False,
                content_type=content_type,
                error_message=str(e),
                processing_time=processing_time
            )
    
    async def _process_pdf(self, content: bytes, source_url: str) -> ProcessingResult:
        """Process PDF documents with advanced extraction"""
        self.logger.info(f"📄 Processing PDF: {source_url}")
        
        result = ProcessingResult(success=True, content_type="pdf")
        
        try:
            # Use PyMuPDF for basic text extraction
            with fitz.open(stream=content, filetype="pdf") as pdf_doc:
                text_content = []
                images = []
                tables = []
                
                for page_num in range(pdf_doc.page_count):
                    page = pdf_doc[page_num]
                    
                    # Extract text
                    page_text = page.get_text()
                    if page_text.strip():
                        text_content.append(page_text)
                    
                    # Extract images for OCR if text is sparse
                    if len(page_text.strip()) < 100:  # Likely image-based page
                        page_images = await self._extract_images_from_pdf_page(page, page_num)
                        images.extend(page_images)
                    
                    # Extract tables
                    if self.config.extract_tables:
                        page_tables = await self._extract_tables_from_pdf_page(content, page_num)
                        tables.extend(page_tables)
                
                result.extracted_text = "\n\n".join(text_content)
                result.images = images
                result.tables = tables
                
                # If no text extracted, try OCR on images
                if not result.extracted_text.strip() and images:
                    ocr_text = await self._perform_ocr_on_images(images)
                    result.extracted_text = ocr_text
                
                result.metadata = {
                    "page_count": pdf_doc.page_count,
                    "has_images": len(images) > 0,
                    "has_tables": len(tables) > 0,
                    "extraction_method": "pymupdf"
                }
                
        except Exception as e:
            result.success = False
            result.error_message = f"PDF processing failed: {e}"
            self.logger.error(f"PDF processing error: {e}", exc_info=True)
        
        return result

    async def _process_image(self, content: bytes, source_url: str) -> ProcessingResult:
        """Process image files with OCR and visual analysis"""
        self.logger.info(f"🖼️ Processing image: {source_url}")

        result = ProcessingResult(success=True, content_type="image")

        try:
            # Load image
            image = Image.open(io.BytesIO(content))

            # Enhance image for better OCR
            if self.config.enhance_images:
                image = self._enhance_image_for_ocr(image)

            # Perform OCR
            ocr_text = await self._perform_ocr_on_image(image)
            result.extracted_text = ocr_text

            # Analyze with vision model if available
            if self.openai_client:
                vision_analysis = await self._analyze_with_vision_model(content)
                result.metadata["vision_analysis"] = vision_analysis

            # Detect forms and tables in image
            forms = await self._detect_forms_in_image(image)
            tables = await self._detect_tables_in_image(image)

            result.forms = forms
            result.tables = tables

            result.metadata.update({
                "image_size": image.size,
                "image_mode": image.mode,
                "has_forms": len(forms) > 0,
                "has_tables": len(tables) > 0
            })

        except Exception as e:
            result.success = False
            result.error_message = f"Image processing failed: {e}"
            self.logger.error(f"Image processing error: {e}", exc_info=True)

        return result

    async def _process_html(self, content: Union[str, bytes], source_url: str) -> ProcessingResult:
        """Process HTML content with form and table detection"""
        self.logger.info(f"🌐 Processing HTML: {source_url}")

        result = ProcessingResult(success=True, content_type="html")

        try:
            if isinstance(content, bytes):
                content = content.decode('utf-8', errors='ignore')

            # Use unstructured for HTML processing if available
            if UNSTRUCTURED_AVAILABLE:
                elements = await asyncio.to_thread(
                    partition,
                    text=content,
                    content_type='text/html'
                )

                text_parts = []
                tables = []
                forms = []

                for element in elements:
                    if hasattr(element, 'text'):
                        text_parts.append(element.text)

                    # Extract tables
                    if 'table' in str(type(element)).lower():
                        table_data = self._extract_table_from_element(element)
                        if table_data:
                            tables.append(table_data)

                result.extracted_text = "\n".join(text_parts)
                result.tables = tables
                result.forms = forms
            else:
                # Fallback to simple text extraction
                result.extracted_text = content

            result.metadata = {
                "extraction_method": "unstructured" if UNSTRUCTURED_AVAILABLE else "simple",
                "content_length": len(content)
            }

        except Exception as e:
            result.success = False
            result.error_message = f"HTML processing failed: {e}"
            self.logger.error(f"HTML processing error: {e}", exc_info=True)

        return result

    async def _process_generic(self, content: Union[str, bytes], content_type: str, source_url: str) -> ProcessingResult:
        """Process generic documents"""
        self.logger.info(f"📄 Processing generic document: {content_type} from {source_url}")

        result = ProcessingResult(success=True, content_type=content_type)

        try:
            if isinstance(content, bytes):
                content = content.decode('utf-8', errors='ignore')

            result.extracted_text = content
            result.metadata = {
                "extraction_method": "direct",
                "content_length": len(content)
            }

        except Exception as e:
            result.success = False
            result.error_message = f"Generic processing failed: {e}"
            self.logger.error(f"Generic processing error: {e}", exc_info=True)

        return result

    # ========== HELPER METHODS ==========

    def _enhance_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """Enhance image quality for better OCR results"""
        try:
            # Convert to grayscale if needed
            if image.mode != 'L':
                image = image.convert('L')

            # Enhance contrast
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)

            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(2.0)

            # Resize if too small
            if image.size[0] < 300 or image.size[1] < 300:
                scale_factor = max(300 / image.size[0], 300 / image.size[1])
                new_size = (int(image.size[0] * scale_factor), int(image.size[1] * scale_factor))
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            return image

        except Exception as e:
            self.logger.warning(f"Image enhancement failed: {e}")
            return image

    async def _perform_ocr_on_image(self, image: Image.Image) -> str:
        """Perform OCR on a single image"""
        try:
            # Configure Tesseract for Bulgarian + English
            custom_config = f'--oem 3 --psm 6 -l {self.config.ocr_language}'

            # Perform OCR
            text = await asyncio.to_thread(
                pytesseract.image_to_string,
                image,
                config=custom_config
            )

            return text.strip()

        except Exception as e:
            self.logger.error(f"OCR failed: {e}")
            return ""

    async def _perform_ocr_on_images(self, images: List[Dict[str, Any]]) -> str:
        """Perform OCR on multiple images and combine results"""
        ocr_texts = []

        for img_data in images:
            try:
                if 'image' in img_data:
                    text = await self._perform_ocr_on_image(img_data['image'])
                    if text:
                        ocr_texts.append(text)
            except Exception as e:
                self.logger.warning(f"OCR failed for image: {e}")

        return "\n\n".join(ocr_texts)

    async def _extract_images_from_pdf_page(self, page, page_num: int) -> List[Dict[str, Any]]:
        """Extract images from a PDF page"""
        images = []

        try:
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)

                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")
                        image = Image.open(io.BytesIO(img_data))

                        images.append({
                            "page": page_num,
                            "index": img_index,
                            "image": image,
                            "format": "png",
                            "size": image.size
                        })

                    pix = None  # Free memory

                except Exception as e:
                    self.logger.warning(f"Failed to extract image {img_index} from page {page_num}: {e}")

        except Exception as e:
            self.logger.error(f"Failed to extract images from page {page_num}: {e}")

        return images

    async def _extract_tables_from_pdf_page(self, pdf_content: bytes, page_num: int) -> List[Dict[str, Any]]:
        """Extract tables from a PDF page using multiple methods"""
        tables = []

        try:
            # Method 1: Try Camelot (if available)
            if CAMELOT_AVAILABLE:
                try:
                    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
                        tmp_file.write(pdf_content)
                        tmp_file.flush()

                        camelot_tables = await asyncio.to_thread(
                            camelot.read_pdf,
                            tmp_file.name,
                            pages=str(page_num + 1),  # Camelot uses 1-based indexing
                            flavor='lattice'
                        )

                        for i, table in enumerate(camelot_tables):
                            if table.accuracy > self.config.table_confidence_threshold:
                                tables.append({
                                    "page": page_num,
                                    "index": i,
                                    "method": "camelot_lattice",
                                    "accuracy": table.accuracy,
                                    "data": table.df.to_dict('records'),
                                    "shape": table.df.shape
                                })

                        os.unlink(tmp_file.name)

                except Exception as e:
                    self.logger.warning(f"Camelot table extraction failed for page {page_num}: {e}")

            # Method 2: Try Tabula (fallback)
            try:
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
                    tmp_file.write(pdf_content)
                    tmp_file.flush()

                    tabula_tables = await asyncio.to_thread(
                        tabula.read_pdf,
                        tmp_file.name,
                        pages=page_num + 1,
                        multiple_tables=True
                    )

                    for i, df in enumerate(tabula_tables):
                        if not df.empty:
                            tables.append({
                                "page": page_num,
                                "index": len(tables),
                                "method": "tabula",
                                "accuracy": 0.8,  # Default confidence
                                "data": df.to_dict('records'),
                                "shape": df.shape
                            })

                    os.unlink(tmp_file.name)

            except Exception as e:
                self.logger.warning(f"Tabula table extraction failed for page {page_num}: {e}")

        except Exception as e:
            self.logger.error(f"Table extraction failed for page {page_num}: {e}")

        return tables

    async def _detect_forms_in_image(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Detect forms and form fields in images"""
        forms = []

        try:
            # Convert PIL image to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

            # Detect rectangular regions (potential form fields)
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            form_fields = []
            for contour in contours:
                # Filter by area and aspect ratio
                area = cv2.contourArea(contour)
                if area > 100:  # Minimum area threshold
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h

                    # Typical form field characteristics
                    if 0.2 < aspect_ratio < 10 and w > 20 and h > 10:
                        form_fields.append({
                            "x": int(x),
                            "y": int(y),
                            "width": int(w),
                            "height": int(h),
                            "area": int(area),
                            "aspect_ratio": float(aspect_ratio)
                        })

            if form_fields:
                forms.append({
                    "type": "detected_form",
                    "fields": form_fields,
                    "field_count": len(form_fields),
                    "detection_method": "opencv_contours"
                })

        except Exception as e:
            self.logger.warning(f"Form detection failed: {e}")

        return forms

    async def _detect_tables_in_image(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Detect tables in images using computer vision"""
        tables = []

        try:
            # Convert PIL image to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)

            # Detect horizontal and vertical lines
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))

            horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)
            vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)

            # Combine lines to detect table structure
            table_mask = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)

            # Find contours of potential tables
            contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                if area > 1000:  # Minimum table area
                    x, y, w, h = cv2.boundingRect(contour)

                    # Extract table region for OCR
                    table_region = image.crop((x, y, x + w, y + h))
                    table_text = await self._perform_ocr_on_image(table_region)

                    tables.append({
                        "index": i,
                        "x": int(x),
                        "y": int(y),
                        "width": int(w),
                        "height": int(h),
                        "area": int(area),
                        "text": table_text,
                        "detection_method": "opencv_lines"
                    })

        except Exception as e:
            self.logger.warning(f"Table detection failed: {e}")

        return tables

    async def _analyze_with_vision_model(self, image_content: bytes) -> Dict[str, Any]:
        """Analyze image with OpenAI Vision model"""
        if not self.openai_client:
            return {}

        try:
            # Encode image to base64
            image_b64 = base64.b64encode(image_content).decode('utf-8')

            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model=self.config.vision_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "Analyze this document image. Identify: 1) Document type, 2) Key information, 3) Tables/forms present, 4) Language(s), 5) Quality assessment. Focus on EU funding program documents."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_b64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500
            )

            return {
                "analysis": response.choices[0].message.content,
                "model": self.config.vision_model,
                "confidence": "high"
            }

        except Exception as e:
            self.logger.error(f"Vision model analysis failed: {e}")
            return {"error": str(e)}

    def _extract_table_from_element(self, element) -> Optional[Dict[str, Any]]:
        """Extract table data from unstructured element"""
        try:
            if hasattr(element, 'text') and element.text:
                # Simple table parsing - can be enhanced
                lines = element.text.strip().split('\n')
                if len(lines) > 1:
                    return {
                        "type": "html_table",
                        "rows": len(lines),
                        "data": [line.split('\t') for line in lines if line.strip()],
                        "raw_text": element.text
                    }
        except Exception as e:
            self.logger.warning(f"Table extraction from element failed: {e}")

        return None

# ========== FACTORY FUNCTION ==========

def create_multimodal_processor(config: Optional[MultiModalConfig] = None) -> MultiModalProcessor:
    """Factory function to create a MultiModalProcessor instance"""
    return MultiModalProcessor(config)

# ========== INTEGRATION HELPER ==========

async def process_document_multimodal(
    content: Union[bytes, str],
    content_type: str,
    source_url: str = "unknown",
    config: Optional[MultiModalConfig] = None
) -> ProcessingResult:
    """
    Convenience function for processing documents with multi-modal capabilities

    Args:
        content: Document content
        content_type: MIME type or file extension
        source_url: Source URL for logging
        config: Processing configuration

    Returns:
        ProcessingResult with extracted content and metadata
    """
    processor = create_multimodal_processor(config)
    return await processor.process_document(content, content_type, source_url)
