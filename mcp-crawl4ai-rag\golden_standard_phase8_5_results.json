{"timestamp": "2025-07-05T08:04:31.332542", "duration_seconds": 307.270118, "phase": "8.5_PERFORMANCE_OPTIMIZATION", "metrics": {"hybrid_reranked": {"success_rate": 100.0, "avg_execution_time": 2.075628423690796, "avg_result_count": 10.0, "avg_quality_score": 17.833333333333336, "avg_relevance_score": 0.17833333333333332, "relevant_content_rate": 90.0, "successful_tests": 10, "total_tests": 10}, "advanced_query": {"success_rate": 100.0, "avg_execution_time": 16.6414644241333, "avg_result_count": 10.0, "avg_quality_score": 16.666666666666664, "avg_relevance_score": 0.16666666666666669, "relevant_content_rate": 80.0, "successful_tests": 10, "total_tests": 10}, "cascaded": {"success_rate": 100.0, "avg_execution_time": 12.008275079727174, "avg_result_count": 10.0, "avg_quality_score": 16.666666666666664, "avg_relevance_score": 0.16666666666666669, "relevant_content_rate": 80.0, "successful_tests": 10, "total_tests": 10}}, "test_details": [{"test_case": {"id": 1, "query": "програми за малки и средни предприятия в България", "expected_keywords": ["МСП", "малки", "средни", "предприятия", "бизнес"], "category": "SME_PROGRAMS"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 5.350792169570923, "quality_metrics": {"relevance_score": 0.19999999999999998, "keyword_matches": 3, "content_quality": 20.0, "has_relevant_content": true, "relevant_results_count": 1}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 21.88388419151306, "quality_metrics": {"relevance_score": 0.0, "keyword_matches": 0, "content_quality": 0.0, "has_relevant_content": false, "relevant_results_count": 0}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 14.442555904388428, "quality_metrics": {"relevance_score": 0.0, "keyword_matches": 0, "content_quality": 0.0, "has_relevant_content": false, "relevant_results_count": 0}}}}, {"test_case": {"id": 2, "query": "европейски фондове за иновации и развитие", "expected_keywords": ["иновации", "развитие", "технологии", "изследвания"], "category": "INNOVATION_FUNDS"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.590517282485962, "quality_metrics": {"relevance_score": 0.16666666666666666, "keyword_matches": 2, "content_quality": 16.666666666666664, "has_relevant_content": true, "relevant_results_count": 2}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 21.758289337158203, "quality_metrics": {"relevance_score": 0.25, "keyword_matches": 3, "content_quality": 25.0, "has_relevant_content": true, "relevant_results_count": 3}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 14.087098121643066, "quality_metrics": {"relevance_score": 0.25, "keyword_matches": 3, "content_quality": 25.0, "has_relevant_content": true, "relevant_results_count": 3}}}}, {"test_case": {"id": 3, "query": "финансиране на стартъп компании в България", "expected_keywords": ["стартъп", "финансиране", "нови", "компании"], "category": "STARTUP_FUNDING"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.5869524478912354, "quality_metrics": {"relevance_score": 0.4166666666666667, "keyword_matches": 5, "content_quality": 41.66666666666667, "has_relevant_content": true, "relevant_results_count": 3}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 14.939740896224976, "quality_metrics": {"relevance_score": 0.25, "keyword_matches": 3, "content_quality": 25.0, "has_relevant_content": true, "relevant_results_count": 3}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 10.159641742706299, "quality_metrics": {"relevance_score": 0.25, "keyword_matches": 3, "content_quality": 25.0, "has_relevant_content": true, "relevant_results_count": 3}}}}, {"test_case": {"id": 4, "query": "процедури за кандидатстване по оперативни програми", "expected_keywords": ["процедури", "кандидатстване", "оперативни", "програми"], "category": "APPLICATION_PROCEDURES"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.7114691734313965, "quality_metrics": {"relevance_score": 0.25, "keyword_matches": 3, "content_quality": 25.0, "has_relevant_content": true, "relevant_results_count": 2}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 15.72680926322937, "quality_metrics": {"relevance_score": 0.16666666666666666, "keyword_matches": 2, "content_quality": 16.666666666666664, "has_relevant_content": true, "relevant_results_count": 2}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 9.84002423286438, "quality_metrics": {"relevance_score": 0.16666666666666666, "keyword_matches": 2, "content_quality": 16.666666666666664, "has_relevant_content": true, "relevant_results_count": 2}}}}, {"test_case": {"id": 5, "query": "подкрепа за дигитализация на предприятията", "expected_keywords": ["дигитализация", "цифрови", "технологии", "трансформация"], "category": "DIGITALIZATION"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.5749642848968506, "quality_metrics": {"relevance_score": 0.0, "keyword_matches": 0, "content_quality": 0.0, "has_relevant_content": false, "relevant_results_count": 0}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 13.553410530090332, "quality_metrics": {"relevance_score": 0.0, "keyword_matches": 0, "content_quality": 0.0, "has_relevant_content": false, "relevant_results_count": 0}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 12.547749519348145, "quality_metrics": {"relevance_score": 0.0, "keyword_matches": 0, "content_quality": 0.0, "has_relevant_content": false, "relevant_results_count": 0}}}}, {"test_case": {"id": 6, "query": "програми за околна среда и климат", "expected_keywords": ["околна среда", "климат", "екология", "зелени"], "category": "ENVIRONMENT"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 2.468266487121582, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 14.604066133499146, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 9.914489507675171, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}}}, {"test_case": {"id": 7, "query": "европейски средства за образование и обучение", "expected_keywords": ["образование", "обучение", "квалификация", "умения"], "category": "EDUCATION"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.5669176578521729, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 18.91986393928528, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 14.442352056503296, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}}}, {"test_case": {"id": 8, "query": "инфраструктурни проекти с европейско финансиране", "expected_keywords": ["инфраструктура", "строителство", "пътища", "транспорт"], "category": "INFRASTRUCTURE"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.6399314403533936, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 12.936307668685913, "quality_metrics": {"relevance_score": 0.16666666666666666, "keyword_matches": 2, "content_quality": 16.666666666666664, "has_relevant_content": true, "relevant_results_count": 2}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 9.701477766036987, "quality_metrics": {"relevance_score": 0.16666666666666666, "keyword_matches": 2, "content_quality": 16.666666666666664, "has_relevant_content": true, "relevant_results_count": 2}}}}, {"test_case": {"id": 9, "query": "програми за развитие на селските райони", "expected_keywords": ["селски", "райони", "земеделие", "развитие"], "category": "RURAL_DEVELOPMENT"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.5938656330108643, "quality_metrics": {"relevance_score": 0.4166666666666667, "keyword_matches": 5, "content_quality": 41.66666666666667, "has_relevant_content": true, "relevant_results_count": 3}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 18.51694965362549, "quality_metrics": {"relevance_score": 0.4166666666666667, "keyword_matches": 5, "content_quality": 41.66666666666667, "has_relevant_content": true, "relevant_results_count": 3}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 14.830939054489136, "quality_metrics": {"relevance_score": 0.4166666666666667, "keyword_matches": 5, "content_quality": 41.66666666666667, "has_relevant_content": true, "relevant_results_count": 3}}}}, {"test_case": {"id": 10, "query": "подкрепа за енергийна ефективност и възобновяеми източници", "expected_keywords": ["енергийна", "ефективност", "възобновяеми", "енергия"], "category": "ENERGY_EFFICIENCY"}, "results": {"hybrid_reranked": {"method": "hybrid_reranked", "success": true, "result_count": 10, "execution_time": 1.672607660293579, "quality_metrics": {"relevance_score": 0.08333333333333333, "keyword_matches": 1, "content_quality": 8.333333333333332, "has_relevant_content": true, "relevant_results_count": 1}}, "advanced_query": {"method": "advanced_query", "success": true, "result_count": 10, "execution_time": 13.57532262802124, "quality_metrics": {"relevance_score": 0.25, "keyword_matches": 3, "content_quality": 25.0, "has_relevant_content": true, "relevant_results_count": 3}}, "cascaded": {"method": "cascaded", "success": true, "result_count": 10, "execution_time": 10.116422891616821, "quality_metrics": {"relevance_score": 0.25, "keyword_matches": 3, "content_quality": 25.0, "has_relevant_content": true, "relevant_results_count": 3}}}}]}