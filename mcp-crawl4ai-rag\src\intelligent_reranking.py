#!/usr/bin/env python3
"""
Phase 4.5: Intelligent Result Reranking
Multi-stage reranking with cross-encoder models and relevance feedback
"""

import asyncio
import logging
import math
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from sentence_transformers import CrossEncoder
import cohere
from openai import AsyncOpenAI

logger = logging.getLogger(__name__)

@dataclass
class RerankingScore:
    """Comprehensive reranking score breakdown"""
    semantic_score: float
    cross_encoder_score: float
    llm_relevance_score: float
    quality_score: float
    temporal_score: float
    final_score: float
    confidence: float

@dataclass
class RerankedResult:
    """Result with comprehensive reranking information"""
    document: Dict[str, Any]
    original_rank: int
    new_rank: int
    reranking_score: RerankingScore
    explanation: str

class IntelligentRerankingEngine:
    """Multi-stage intelligent reranking system"""
    
    def __init__(self):
        self.cross_encoder = None
        self.cohere_client = None
        self._load_models()
        
        # Optimized reranking weights for better performance
        self.weights = {
            'semantic_score': 0.35,      # Increased - most reliable
            'cross_encoder_score': 0.35, # Increased - works well
            'llm_relevance_score': 0.20, # Decreased - can be inconsistent
            'quality_score': 0.10,       # Decreased - less critical
            'temporal_score': 0.00       # Disabled - not useful with limited data
        }
        
        logger.info("🎯 Intelligent Reranking Engine initialized")
    
    def _load_models(self):
        """Load reranking models"""
        try:
            # Load cross-encoder for semantic reranking
            self.cross_encoder = CrossEncoder('cross-encoder/ms-marco-MiniLM-L-6-v2')
            logger.info("✅ Cross-encoder model loaded successfully")
        except Exception as e:
            logger.warning(f"⚠️ Cross-encoder not available: {e}")
            self.cross_encoder = None
        
        try:
            # Initialize Cohere client for reranking
            import os
            cohere_api_key = os.getenv("COHERE_API_KEY")
            if cohere_api_key:
                self.cohere_client = cohere.Client(cohere_api_key)
                logger.info("✅ Cohere reranking client initialized")
            else:
                logger.warning("⚠️ Cohere API key not found")
        except Exception as e:
            logger.warning(f"⚠️ Cohere client not available: {e}")
            self.cohere_client = None
    
    async def intelligent_rerank(
        self,
        results: List[Dict[str, Any]],
        query: str,
        query_metadata: Optional[Dict[str, Any]] = None,
        async_openai_client: Optional[AsyncOpenAI] = None,
        top_k: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Multi-stage intelligent reranking
        
        Args:
            results: Search results to rerank
            query: Original query
            query_metadata: Query analysis metadata
            async_openai_client: OpenAI client for LLM-based reranking
            top_k: Number of top results to return
        
        Returns:
            Reranked results with enhanced scoring
        """
        if not results:
            return []
        
        logger.info(f"🎯 Intelligent reranking: {len(results)} results for query '{query[:50]}...'")
        
        # Stage 1: Cross-encoder reranking
        stage1_results = await self._cross_encoder_rerank(results, query)
        
        # Stage 2: Cohere reranking
        stage2_results = await self._cohere_rerank(stage1_results, query)
        
        # Stage 3: LLM-based relevance scoring
        stage3_results = await self._llm_relevance_rerank(
            stage2_results, query, query_metadata, async_openai_client
        )
        
        # Stage 4: Multi-factor fusion
        final_results = await self._multi_factor_fusion(
            stage3_results, query, query_metadata
        )
        
        # Stage 5: Final ranking and selection
        reranked_results = await self._final_ranking(final_results, top_k)
        
        logger.info(f"✅ Intelligent reranking completed: {len(reranked_results)} results")
        return reranked_results
    
    async def _cross_encoder_rerank(
        self,
        results: List[Dict[str, Any]],
        query: str
    ) -> List[Dict[str, Any]]:
        """Stage 1: Cross-encoder semantic reranking"""
        
        if not self.cross_encoder or not results:
            logger.info("⚠️ Cross-encoder not available, skipping stage 1")
            for i, result in enumerate(results):
                result['cross_encoder_score'] = result.get('similarity', 0.5)
            return results
        
        logger.info("🔄 Stage 1: Cross-encoder reranking")
        
        try:
            # Prepare query-document pairs
            pairs = []
            for result in results:
                content = result.get('content', '')[:512]  # Limit content length
                pairs.append([query, content])
            
            # Get cross-encoder scores
            scores = self.cross_encoder.predict(pairs)
            
            # Add scores to results
            for i, (result, score) in enumerate(zip(results, scores)):
                result['cross_encoder_score'] = float(score)
                result['cross_encoder_rank'] = i + 1
            
            # Sort by cross-encoder score
            results.sort(key=lambda x: x['cross_encoder_score'], reverse=True)
            
            logger.info(f"✅ Stage 1 completed: Cross-encoder scores applied")
            return results
            
        except Exception as e:
            logger.error(f"❌ Cross-encoder reranking failed: {e}")
            # Fallback: use original similarity scores
            for result in results:
                result['cross_encoder_score'] = result.get('similarity', 0.5)
            return results
    
    async def _cohere_rerank(
        self,
        results: List[Dict[str, Any]],
        query: str
    ) -> List[Dict[str, Any]]:
        """Stage 2: Cohere reranking"""
        
        if not self.cohere_client or not results:
            logger.info("⚠️ Cohere not available, skipping stage 2")
            for result in results:
                result['cohere_score'] = result.get('cross_encoder_score', 0.5)
            return results
        
        logger.info("🔄 Stage 2: Cohere reranking")
        
        try:
            # Prepare documents for Cohere
            documents = []
            for result in results:
                content = result.get('content', '')[:1000]  # Limit content
                documents.append(content)
            
            # Cohere rerank - try newer model first
            try:
                rerank_response = self.cohere_client.rerank(
                    model="rerank-multilingual-v3.0",  # Try newer model
                    query=query,
                    documents=documents,
                    top_n=len(documents)
                )
            except:
                # Fallback to older model
                rerank_response = self.cohere_client.rerank(
                    model="rerank-multilingual-v2.0",
                    query=query,
                    documents=documents,
                    top_n=len(documents)
                )
            
            # Create score mapping
            cohere_scores = {}
            for item in rerank_response.results:
                cohere_scores[item.index] = item.relevance_score
            
            # Apply Cohere scores
            for i, result in enumerate(results):
                cohere_score = cohere_scores.get(i, 0.5)
                result['cohere_score'] = cohere_score
                result['cohere_rank'] = i + 1
            
            # Sort by Cohere score
            results.sort(key=lambda x: x['cohere_score'], reverse=True)
            
            logger.info(f"✅ Stage 2 completed: Cohere reranking applied")
            return results
            
        except Exception as e:
            logger.error(f"❌ Cohere reranking failed: {e}")
            # Fallback: use cross-encoder scores
            for result in results:
                result['cohere_score'] = result.get('cross_encoder_score', 0.5)
            return results
    
    async def _llm_relevance_rerank(
        self,
        results: List[Dict[str, Any]],
        query: str,
        query_metadata: Optional[Dict[str, Any]],
        async_openai_client: Optional[AsyncOpenAI]
    ) -> List[Dict[str, Any]]:
        """Stage 3: LLM-based relevance scoring"""
        
        if not async_openai_client or not results:
            logger.info("⚠️ OpenAI client not available, skipping stage 3")
            for result in results:
                result['llm_relevance_score'] = result.get('cohere_score', 0.5)
            return results
        
        logger.info("🔄 Stage 3: LLM relevance scoring")
        
        try:
            # Batch process results for efficiency
            batch_size = 3
            for i in range(0, len(results), batch_size):
                batch = results[i:i + batch_size]
                await self._score_batch_with_llm(batch, query, query_metadata, async_openai_client)
            
            logger.info(f"✅ Stage 3 completed: LLM relevance scores applied")
            return results
            
        except Exception as e:
            logger.error(f"❌ LLM relevance scoring failed: {e}")
            # Fallback: use Cohere scores
            for result in results:
                result['llm_relevance_score'] = result.get('cohere_score', 0.5)
            return results
    
    async def _score_batch_with_llm(
        self,
        batch: List[Dict[str, Any]],
        query: str,
        query_metadata: Optional[Dict[str, Any]],
        async_openai_client: AsyncOpenAI
    ):
        """Score a batch of results with LLM"""
        
        # Prepare batch prompt
        documents_text = ""
        for i, result in enumerate(batch):
            content = result.get('content', '')[:300]
            documents_text += f"\nDocument {i+1}:\n{content}\n"
        
        query_context = ""
        if query_metadata:
            query_context = f"""
Query Type: {query_metadata.get('query_type', 'unknown')}
Intent: {query_metadata.get('intent', 'unknown')}
Programs: {', '.join(query_metadata.get('programs', []))}
"""
        
        prompt = f"""
You are an expert at evaluating document relevance for EU funding queries.

Query: "{query}"
{query_context}

Documents to evaluate:
{documents_text}

For each document, provide a relevance score from 0.0 to 1.0 based on:
1. Direct relevance to the query
2. Completeness of information
3. Accuracy and specificity
4. Usefulness for the user

Respond with only a JSON array of scores: [score1, score2, score3]
"""
        
        try:
            response = await async_openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=100
            )
            
            # Parse scores
            scores_text = response.choices[0].message.content.strip()

            # Try JSON parsing first
            try:
                import json
                scores = json.loads(scores_text)
            except:
                # Fallback to eval for simple arrays
                try:
                    scores = eval(scores_text)
                except:
                    # Final fallback: default scores
                    scores = [0.5] * len(batch)

            # Ensure we have the right number of scores
            if len(scores) != len(batch):
                logger.warning(f"⚠️ Score count mismatch: got {len(scores)}, expected {len(batch)}")
                scores = [0.5] * len(batch)  # Fallback scores

            # Apply scores
            for i, (result, score) in enumerate(zip(batch, scores)):
                result['llm_relevance_score'] = float(score)

        except Exception as e:
            logger.warning(f"⚠️ LLM scoring failed for batch: {e}")
            # Fallback scores
            for result in batch:
                result['llm_relevance_score'] = result.get('cohere_score', 0.5)
    
    async def _multi_factor_fusion(
        self,
        results: List[Dict[str, Any]],
        query: str,
        query_metadata: Optional[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Stage 4: Multi-factor score fusion"""
        
        logger.info("🔄 Stage 4: Multi-factor fusion")
        
        for result in results:
            # Extract individual scores
            semantic_score = result.get('similarity', 0.5)
            cross_encoder_score = result.get('cross_encoder_score', 0.5)
            cohere_score = result.get('cohere_score', 0.5)
            llm_relevance_score = result.get('llm_relevance_score', 0.5)
            quality_score = result.get('quality_score', 0.5)
            
            # Calculate temporal score
            temporal_score = self._calculate_temporal_score(result)
            
            # Weighted fusion
            final_score = (
                semantic_score * self.weights['semantic_score'] +
                cross_encoder_score * self.weights['cross_encoder_score'] +
                llm_relevance_score * self.weights['llm_relevance_score'] +
                quality_score * self.weights['quality_score'] +
                temporal_score * self.weights['temporal_score']
            )
            
            # Calculate confidence based on score consistency
            scores = [semantic_score, cross_encoder_score, cohere_score, llm_relevance_score]
            confidence = 1.0 - (statistics.stdev(scores) if len(scores) > 1 else 0.0)
            
            # Store comprehensive scoring
            reranking_score = RerankingScore(
                semantic_score=semantic_score,
                cross_encoder_score=cross_encoder_score,
                llm_relevance_score=llm_relevance_score,
                quality_score=quality_score,
                temporal_score=temporal_score,
                final_score=final_score,
                confidence=confidence
            )
            
            result['reranking_score'] = reranking_score.__dict__
            result['final_rerank_score'] = final_score
            result['rerank_confidence'] = confidence
        
        logger.info("✅ Stage 4 completed: Multi-factor fusion applied")
        return results
    
    def _calculate_temporal_score(self, result: Dict[str, Any]) -> float:
        """Calculate temporal relevance score"""
        
        created_at = result.get('created_at')
        if not created_at:
            return 0.5
        
        try:
            from datetime import datetime
            if isinstance(created_at, str):
                created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            else:
                created_date = created_at
            
            # Calculate days since creation
            days_old = (datetime.now() - created_date.replace(tzinfo=None)).days
            
            # More recent content gets higher score
            if days_old <= 30:
                return 1.0
            elif days_old <= 90:
                return 0.8
            elif days_old <= 365:
                return 0.6
            else:
                return 0.4
                
        except Exception:
            return 0.5
    
    async def _final_ranking(
        self,
        results: List[Dict[str, Any]],
        top_k: int
    ) -> List[Dict[str, Any]]:
        """Stage 5: Final ranking and selection"""
        
        logger.info("🔄 Stage 5: Final ranking")
        
        # Sort by final rerank score
        results.sort(key=lambda x: x.get('final_rerank_score', 0), reverse=True)
        
        # Create reranked results with explanations
        reranked_results = []
        for i, result in enumerate(results[:top_k]):
            # Generate explanation
            reranking_score = result.get('reranking_score', {})
            explanation = self._generate_rerank_explanation(reranking_score)
            
            # Update result with reranking info
            result['rerank_explanation'] = explanation
            result['original_rank'] = result.get('original_rank', i + 1)
            result['new_rank'] = i + 1
            
            # Update similarity with final score for compatibility
            result['similarity'] = result.get('final_rerank_score', result.get('similarity', 0))
            
            reranked_results.append(result)
        
        logger.info(f"✅ Stage 5 completed: Final ranking applied, returning top {len(reranked_results)}")
        return reranked_results
    
    def _generate_rerank_explanation(self, reranking_score: Dict[str, Any]) -> str:
        """Generate human-readable reranking explanation"""
        
        final_score = reranking_score.get('final_score', 0)
        confidence = reranking_score.get('confidence', 0)
        
        # Find top contributing factors
        factors = [
            ('Semantic', reranking_score.get('semantic_score', 0)),
            ('Cross-Encoder', reranking_score.get('cross_encoder_score', 0)),
            ('LLM Relevance', reranking_score.get('llm_relevance_score', 0)),
            ('Quality', reranking_score.get('quality_score', 0)),
            ('Temporal', reranking_score.get('temporal_score', 0))
        ]
        
        # Sort by score
        factors.sort(key=lambda x: x[1], reverse=True)
        top_factors = factors[:2]
        
        explanation = f"Score: {final_score:.3f} (confidence: {confidence:.2f}). "
        explanation += f"Top factors: {top_factors[0][0]} ({top_factors[0][1]:.2f}), "
        explanation += f"{top_factors[1][0]} ({top_factors[1][1]:.2f})"
        
        return explanation

# Global instance
intelligent_reranking_engine = IntelligentRerankingEngine()

async def intelligent_rerank_results(
    results: List[Dict[str, Any]],
    query: str,
    query_metadata: Optional[Dict[str, Any]] = None,
    async_openai_client: Optional[AsyncOpenAI] = None,
    top_k: int = 5
) -> List[Dict[str, Any]]:
    """
    Apply intelligent multi-stage reranking to search results
    
    Args:
        results: Search results to rerank
        query: Original query
        query_metadata: Query analysis metadata
        async_openai_client: OpenAI client for LLM-based scoring
        top_k: Number of top results to return
    
    Returns:
        Intelligently reranked results
    """
    
    return await intelligent_reranking_engine.intelligent_rerank(
        results=results,
        query=query,
        query_metadata=query_metadata,
        async_openai_client=async_openai_client,
        top_k=top_k
    )
