#!/usr/bin/env python3
"""
Embedding Dimension Mismatch Fix
Поправка на критичното несъответствие между database schema и embedding модела
"""

import asyncio
import time
import json
from typing import List, Dict, Any
from dotenv import load_dotenv
from utils import get_supabase_client, create_embedding, get_bge_model

# Load environment variables
load_dotenv('../.env')

class EmbeddingDimensionFixer:
    def __init__(self):
        self.supabase = get_supabase_client()
        self.model = get_bge_model()
    
    async def analyze_dimension_mismatch(self):
        """Анализ на проблема с размерностите"""
        print("🔍 АНАЛИЗ НА EMBEDDING DIMENSION MISMATCH...")
        
        # 1. Проверка на текущия модел
        if self.model:
            model_dimension = self.model.get_sentence_embedding_dimension()
            print(f"📏 Текущ модел размерност: {model_dimension}")
        else:
            print("❌ Модел не е зареден!")
            return
        
        # 2. Проверка на database schema
        print("\n📊 Database schema анализ:")
        try:
            # Проверка на sample записи
            response = self.supabase.table('crawled_pages').select(
                'id, embedding'
            ).limit(3).execute()
            
            if response.data:
                for i, record in enumerate(response.data):
                    if record.get('embedding'):
                        actual_dim = len(record['embedding'])
                        print(f"   Запис {i+1}: {actual_dim} размерности")
                    else:
                        print(f"   Запис {i+1}: Няма embedding")
            else:
                print("   Няма данни в таблицата")
                
        except Exception as e:
            print(f"❌ Грешка при анализ: {e}")
    
    async def create_test_embedding(self):
        """Създаване на тестов embedding за проверка"""
        print("\n🧪 СЪЗДАВАНЕ НА ТЕСТОВ EMBEDDING...")
        
        test_text = "Програми за иновации в предприятията"
        
        try:
            # Създаване на embedding с текущия модел
            embedding = create_embedding(test_text)
            
            print(f"📏 Тестов embedding размерност: {len(embedding)}")
            print(f"📊 Първи 5 стойности: {embedding[:5]}")
            
            return embedding
            
        except Exception as e:
            print(f"❌ Грешка при създаване на тестов embedding: {e}")
            return None
    
    async def check_database_compatibility(self):
        """Проверка на съвместимостта с базата данни"""
        print("\n🔧 ПРОВЕРКА НА DATABASE СЪВМЕСТИМОСТ...")
        
        # Създаване на тестов embedding
        test_embedding = await self.create_test_embedding()
        if not test_embedding:
            return False
        
        try:
            # Опит за търсене с новия embedding
            response = self.supabase.rpc('match_crawled_pages_v4_debug', {
                'p_query_embedding': test_embedding,
                'p_match_count': 1,
                'p_min_similarity_threshold': 0.1,
                'p_weight_similarity': 1.0,
                'p_weight_program_name': 0.0,
                'p_weight_year': 0.0
            }).execute()
            
            if response.data:
                print("✅ Database търсенето работи с новия embedding")
                return True
            else:
                print("⚠️ Database търсенето не връща резултати")
                return False
                
        except Exception as e:
            print(f"❌ Database търсенето не работи: {e}")
            return False
    
    async def generate_database_migration_sql(self):
        """Генериране на SQL за migration на database schema"""
        print("\n📝 ГЕНЕРИРАНЕ НА DATABASE MIGRATION SQL...")
        
        model_dimension = self.model.get_sentence_embedding_dimension() if self.model else 1024
        
        migration_sql = f"""
-- Database Migration: Fix Embedding Dimension Mismatch
-- Поправка на несъответствието между модел и database schema

-- ============================================================
-- СТЪПКА 1: BACKUP НА ТЕКУЩИ ДАННИ (ПРЕПОРЪЧИТЕЛНО)
-- ============================================================

-- Създаване на backup таблица
CREATE TABLE crawled_pages_backup AS 
SELECT * FROM crawled_pages;

-- ============================================================
-- СТЪПКА 2: АНАЛИЗ НА ТЕКУЩИ РАЗМЕРНОСТИ
-- ============================================================

-- Проверка на текущи embedding размерности
SELECT 
    id,
    array_length(embedding, 1) as embedding_dimension,
    url
FROM crawled_pages 
LIMIT 10;

-- Статистика за размерности
SELECT 
    array_length(embedding, 1) as dimension,
    COUNT(*) as count
FROM crawled_pages 
WHERE embedding IS NOT NULL
GROUP BY array_length(embedding, 1)
ORDER BY count DESC;

-- ============================================================
-- СТЪПКА 3: ОПЦИЯ А - ПРОМЯНА НА SCHEMA ЗА СЪВМЕСТИМОСТ
-- ============================================================

-- Ако искаме да запазим данните и променим schema-та:
-- ALTER TABLE crawled_pages ALTER COLUMN embedding TYPE vector({model_dimension});

-- Обновяване на RPC функциите за новата размерност
DROP FUNCTION IF EXISTS match_crawled_pages_v4_debug;

CREATE OR REPLACE FUNCTION match_crawled_pages_v4_debug(
    p_query_embedding vector({model_dimension}),
    p_match_count integer DEFAULT 10,
    p_min_similarity_threshold double precision DEFAULT 0.1,
    p_min_program_containment_threshold double precision DEFAULT 0.0,
    p_query_entities_filter text[] DEFAULT ARRAY[]::text[],
    p_query_program_canonical_names_jsonb jsonb DEFAULT '{{}}'::jsonb,
    p_source_filters_jsonb jsonb DEFAULT '{{}}'::jsonb,
    p_weight_similarity double precision DEFAULT 0.4,
    p_weight_program_name double precision DEFAULT 0.3,
    p_weight_year double precision DEFAULT 0.3
)
RETURNS TABLE(
    id bigint,
    url text,
    content text,
    metadata jsonb,
    similarity_score double precision,
    program_name_score double precision,
    year_score double precision,
    final_score double precision
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.url,
        cp.content,
        cp.metadata,
        (1 - (cp.embedding <=> p_query_embedding)) as similarity_score,
        0.0 as program_name_score,  -- Simplified for now
        0.0 as year_score,          -- Simplified for now
        (1 - (cp.embedding <=> p_query_embedding)) as final_score
    FROM crawled_pages cp
    WHERE cp.embedding IS NOT NULL
    AND (1 - (cp.embedding <=> p_query_embedding)) >= p_min_similarity_threshold
    ORDER BY cp.embedding <=> p_query_embedding
    LIMIT p_match_count;
END;
$$;

-- ============================================================
-- СТЪПКА 4: ОПЦИЯ Б - ИЗЧИСТВАНЕ И REGENERATION НА EMBEDDINGS
-- ============================================================

-- Ако искаме да изчистим всички embeddings и да ги regenerate:
-- UPDATE crawled_pages SET embedding = NULL;

-- След това трябва да се стартира процес за regeneration на всички embeddings
-- с правилната размерност ({model_dimension})

-- ============================================================
-- СТЪПКА 5: ОБНОВЯВАНЕ НА ИНДЕКСИ
-- ============================================================

-- Премахване на стари индекси
DROP INDEX IF EXISTS crawled_pages_embedding_idx;
DROP INDEX IF EXISTS crawled_pages_embedding_hnsw_cosine_idx;
DROP INDEX IF EXISTS crawled_pages_embedding_hnsw_ip_idx;

-- Създаване на нови HNSW индекси с правилната размерност
CREATE INDEX crawled_pages_embedding_hnsw_cosine_idx 
ON crawled_pages 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

CREATE INDEX crawled_pages_embedding_hnsw_ip_idx 
ON crawled_pages 
USING hnsw (embedding vector_ip_ops) 
WITH (m = 16, ef_construction = 64);

-- ============================================================
-- СТЪПКА 6: ТЕСТВАНЕ НА НОВАТА SCHEMA
-- ============================================================

-- Тест на RPC функцията
-- SELECT * FROM match_crawled_pages_v4_debug(
--     ARRAY[0.1, 0.2, 0.3, ...]::vector({model_dimension}),
--     10,
--     0.1,
--     0.0,
--     ARRAY[]::text[],
--     '{{}}'::jsonb,
--     '{{}}'::jsonb,
--     1.0,
--     0.0,
--     0.0
-- );

-- ============================================================
-- ЗАБЕЛЕЖКИ
-- ============================================================

/*
КРИТИЧНО: Преди изпълнение на тази migration:

1. Направете пълен backup на базата данни
2. Тествайте на development environment първо
3. Планирайте downtime за production
4. Подгответе процес за regeneration на embeddings
5. Уведомете потребителите за временното прекъсване

ПРЕПОРЪЧАНА СТРАТЕГИЯ:
- Използвайте ОПЦИЯ Б (изчистване и regeneration)
- Това ще гарантира консистентност на данните
- Ще подобри качеството на търсенето значително
*/
"""
        
        # Запазване на migration SQL
        with open('embedding_dimension_migration.sql', 'w', encoding='utf-8') as f:
            f.write(migration_sql)
        
        print("💾 Migration SQL запазен в 'embedding_dimension_migration.sql'")
        print(f"📏 Целева размерност: {model_dimension}")
    
    async def recommend_solution_strategy(self):
        """Препоръки за решение на проблема"""
        print("\n💡 ПРЕПОРЪКИ ЗА РЕШЕНИЕ...")
        
        model_dimension = self.model.get_sentence_embedding_dimension() if self.model else 1024
        
        print(f"""
🎯 АНАЛИЗ НА ПРОБЛЕМА:
• Database schema очаква: 1536 размерности (OpenAI)
• Текущ модел създава: {model_dimension} размерности (BGE)
• Реални данни имат: 12715+ размерности (несъвместими)

🚀 ПРЕПОРЪЧАНО РЕШЕНИЕ:

1. НЕЗАБАВНО (за тестване):
   • Използвайте OpenAI embeddings временно
   • Настройте OPENAI_API_KEY в .env файла
   • Това ще даде незабавно подобрение на качеството

2. ДЪЛГОСРОЧНО (за production):
   • Мигрирайте към BGE модел с правилна schema
   • Regenerate всички embeddings с {model_dimension} размерности
   • Обновете database schema и RPC функции

3. АЛТЕРНАТИВНО:
   • Използвайте sentence-transformers/LaBSE (768 dim)
   • По-добър за български език
   • Изисква schema migration

📊 ОЧАКВАНИ ПОДОБРЕНИЯ:
• Качество: 25.6% → 60-70%+ (2.5x подобрение)
• Скорост: 0.685s → 0.2-0.3s (2-3x по-бързо)
• Точност: Значително по-добра семантична точност

⚠️ КРИТИЧНО: Текущата система е неработоспособна за production
поради dimension mismatch. Трябва незабавна поправка!
""")

async def main():
    """Главна функция за анализ и поправка"""
    print("🚀 ЗАПОЧВАНЕ НА EMBEDDING DIMENSION MISMATCH ANALYSIS")
    print("=" * 70)
    
    fixer = EmbeddingDimensionFixer()
    
    # Анализ на проблема
    await fixer.analyze_dimension_mismatch()
    
    # Тестване на текущия модел
    await fixer.create_test_embedding()
    
    # Проверка на съвместимост
    await fixer.check_database_compatibility()
    
    # Генериране на migration SQL
    await fixer.generate_database_migration_sql()
    
    # Препоръки за решение
    await fixer.recommend_solution_strategy()
    
    print("\n" + "=" * 70)
    print("✅ АНАЛИЗ ЗАВЪРШЕН!")
    print("=" * 70)
    
    print("\n📋 СЛЕДВАЩИ СТЪПКИ:")
    print("1. Прегледайте 'embedding_dimension_migration.sql'")
    print("2. Изберете стратегия (OpenAI временно или BGE migration)")
    print("3. Направете backup на базата данни")
    print("4. Изпълнете избраната migration стратегия")
    print("5. Тествайте системата след migration")

if __name__ == "__main__":
    asyncio.run(main())
