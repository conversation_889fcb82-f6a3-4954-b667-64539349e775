#!/usr/bin/env python3
"""
Phase 8.1.4: Testing and Validation
Comprehensive testing of hybrid search system and performance measurement
"""

import asyncio
import json
import os
import time
from typing import List, Dict, Any, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Test queries for comprehensive evaluation
TEST_QUERIES = [
    # Simple queries
    "европейски фондове",
    "програми за малки предприятия", 
    "финансиране на иновации",
    
    # Complex queries
    "програма за конкурентоспособност на малки и средни предприятия в България",
    "европейски структурни фондове за регионално развитие и кохезия",
    "финансиране на научни изследвания и технологично развитие",
    
    # Specific queries
    "ОПИК програма за дигитализация",
    "Хоризонт Европа за стартъп компании",
    "ПРСР мерки за селско стопанство",
    
    # Domain-specific queries
    "зелена сделка европейски фондове",
    "дигитална трансформация финансиране ЕС",
    "устойчиво развитие програми България"
]

def test_hybrid_vs_semantic_comparison():
    """Compare hybrid search vs pure semantic search"""
    print("🧪 Testing Hybrid vs Semantic Search Comparison...")
    
    try:
        from supabase import create_client
        from src.utils import search_documents_with_text_hybrid, search_documents_with_text_legacy
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test on sample queries
        test_queries = TEST_QUERIES[:5]  # Use first 5 queries
        
        hybrid_scores = []
        semantic_scores = []
        hybrid_times = []
        semantic_times = []
        
        for query in test_queries:
            print(f"\n🔍 Testing query: '{query[:40]}...'")
            
            # Test hybrid search
            start_time = time.time()
            hybrid_results = search_documents_with_text_hybrid(
                supabase, query, match_count=5, 
                weight_dense=0.6, weight_sparse=0.4
            )
            hybrid_time = time.time() - start_time
            
            # Test semantic search (legacy) with same weights for fair comparison
            start_time = time.time()
            semantic_results = search_documents_with_text_legacy(
                supabase, query, match_count=5,
                weight_similarity=0.6, weight_program_name=0.3, weight_year=0.1
            )
            semantic_time = time.time() - start_time
            
            # Calculate average scores
            if hybrid_results:
                avg_hybrid_score = sum(r.get('hybrid_score', 0) for r in hybrid_results) / len(hybrid_results)
                hybrid_scores.append(avg_hybrid_score)
                hybrid_times.append(hybrid_time)
                print(f"   Hybrid: {len(hybrid_results)} results, avg score: {avg_hybrid_score:.3f}, time: {hybrid_time:.3f}s")
            
            if semantic_results:
                avg_semantic_score = sum(r.get('similarity', 0) for r in semantic_results) / len(semantic_results)
                semantic_scores.append(avg_semantic_score)
                semantic_times.append(semantic_time)
                print(f"   Semantic: {len(semantic_results)} results, avg score: {avg_semantic_score:.3f}, time: {semantic_time:.3f}s")
        
        # Calculate overall performance
        if hybrid_scores and semantic_scores:
            avg_hybrid = sum(hybrid_scores) / len(hybrid_scores)
            avg_semantic = sum(semantic_scores) / len(semantic_scores)
            avg_hybrid_time = sum(hybrid_times) / len(hybrid_times)
            avg_semantic_time = sum(semantic_times) / len(semantic_times)
            
            improvement = ((avg_hybrid - avg_semantic) / avg_semantic) * 100 if avg_semantic > 0 else 0
            
            print(f"\n📊 PERFORMANCE COMPARISON:")
            print(f"   Hybrid Search:   {avg_hybrid:.3f} avg score, {avg_hybrid_time:.3f}s avg time")
            print(f"   Semantic Search: {avg_semantic:.3f} avg score, {avg_semantic_time:.3f}s avg time")
            print(f"   Improvement:     {improvement:+.1f}% score, {((avg_semantic_time - avg_hybrid_time) / avg_semantic_time * 100):+.1f}% time")
            
            return improvement > 0  # Success if hybrid is better
        
        return False
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        return False

async def test_hybrid_search_engine():
    """Test the HybridSearchEngine class directly"""
    print("\n🧪 Testing HybridSearchEngine class...")
    
    try:
        from src.hybrid_search import HybridSearchEngine, HybridSearchConfig
        from supabase import create_client
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test with optimized config
        config = HybridSearchConfig()  # Uses 60/40 weights
        engine = HybridSearchEngine(supabase, config)
        
        # Test multiple queries
        test_queries = TEST_QUERIES[5:8]  # Use middle queries
        
        total_results = 0
        total_time = 0
        
        for query in test_queries:
            print(f"🔍 Testing: '{query[:40]}...'")
            
            start_time = time.time()
            results = await engine.hybrid_search(query, limit=5)
            end_time = time.time()
            
            query_time = end_time - start_time
            total_time += query_time
            total_results += len(results)
            
            if results:
                avg_score = sum(r.hybrid_score for r in results) / len(results)
                print(f"   ✅ {len(results)} results, avg score: {avg_score:.3f}, time: {query_time:.3f}s")
                
                # Check score components
                semantic_avg = sum(r.semantic_score for r in results) / len(results)
                keyword_avg = sum(r.keyword_score for r in results) / len(results)
                print(f"   📊 Semantic: {semantic_avg:.3f}, Keyword: {keyword_avg:.3f}")
            else:
                print(f"   ❌ No results")
                return False
        
        avg_time = total_time / len(test_queries)
        avg_results = total_results / len(test_queries)
        
        print(f"\n📊 HYBRID ENGINE PERFORMANCE:")
        print(f"   Average results per query: {avg_results:.1f}")
        print(f"   Average time per query: {avg_time:.3f}s")
        
        return avg_results >= 3 and avg_time < 2.0  # Success criteria
        
    except Exception as e:
        print(f"❌ HybridSearchEngine test failed: {e}")
        return False

def test_weight_optimization():
    """Test different weight combinations to validate 60/40 is optimal"""
    print("\n🧪 Testing Weight Optimization...")
    
    try:
        from supabase import create_client
        from src.utils import search_documents_with_text_hybrid
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test different weight combinations
        weight_configs = [
            (0.7, 0.3, "Old Config (70/30)"),
            (0.6, 0.4, "New Config (60/40)"),
            (0.5, 0.5, "Balanced (50/50)"),
            (0.8, 0.2, "Semantic Heavy (80/20)")
        ]
        
        test_query = "програма за иновации и конкурентоспособност"
        
        results_summary = []
        
        for dense_weight, sparse_weight, config_name in weight_configs:
            print(f"🔧 Testing {config_name}...")
            
            results = search_documents_with_text_hybrid(
                supabase, test_query, match_count=5,
                weight_dense=dense_weight, weight_sparse=sparse_weight
            )
            
            if results:
                avg_score = sum(r.get('hybrid_score', 0) for r in results) / len(results)
                avg_semantic = sum(r.get('similarity', 0) for r in results) / len(results)
                avg_keyword = sum(r.get('keyword_score', 0) for r in results) / len(results)
                
                results_summary.append((config_name, avg_score, avg_semantic, avg_keyword))
                print(f"   📊 Avg hybrid: {avg_score:.3f}, semantic: {avg_semantic:.3f}, keyword: {avg_keyword:.3f}")
            else:
                print(f"   ❌ No results")
        
        # Find best configuration
        if results_summary:
            best_config = max(results_summary, key=lambda x: x[1])
            print(f"\n🏆 BEST CONFIGURATION: {best_config[0]} with score {best_config[1]:.3f}")
            
            # Check if 60/40 is among top performers
            new_config_score = next((score for name, score, _, _ in results_summary if "60/40" in name), 0)
            is_optimal = new_config_score >= best_config[1] * 0.95  # Within 5% of best
            
            return is_optimal
        
        return False
        
    except Exception as e:
        print(f"❌ Weight optimization test failed: {e}")
        return False

def test_query_complexity_handling():
    """Test how hybrid search handles different query complexities"""
    print("\n🧪 Testing Query Complexity Handling...")
    
    try:
        from supabase import create_client
        from src.utils import search_documents_with_text_hybrid
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Categorize queries by complexity
        query_categories = {
            "Simple": TEST_QUERIES[:3],
            "Complex": TEST_QUERIES[3:6],
            "Specific": TEST_QUERIES[6:9],
            "Domain": TEST_QUERIES[9:12]
        }
        
        category_performance = {}
        
        for category, queries in query_categories.items():
            print(f"📂 Testing {category} queries...")
            
            category_scores = []
            category_times = []
            
            for query in queries:
                start_time = time.time()
                results = search_documents_with_text_hybrid(supabase, query, match_count=5)
                end_time = time.time()
                
                if results:
                    avg_score = sum(r.get('hybrid_score', 0) for r in results) / len(results)
                    category_scores.append(avg_score)
                    category_times.append(end_time - start_time)
                    print(f"   ✅ '{query[:30]}...': {avg_score:.3f} score, {len(results)} results")
                else:
                    print(f"   ❌ '{query[:30]}...': No results")
            
            if category_scores:
                avg_score = sum(category_scores) / len(category_scores)
                avg_time = sum(category_times) / len(category_times)
                category_performance[category] = (avg_score, avg_time, len(category_scores))
                print(f"   📊 {category} average: {avg_score:.3f} score, {avg_time:.3f}s time")
        
        # Analyze performance across categories
        if category_performance:
            print(f"\n📊 COMPLEXITY ANALYSIS:")
            for category, (score, query_time, count) in category_performance.items():
                print(f"   {category:8}: {score:.3f} score, {query_time:.3f}s time, {count} successful queries")

            # Success if all categories have reasonable performance
            all_good = all(score > 0.3 and query_time < 2.0 for score, query_time, _ in category_performance.values())
            return all_good
        
        return False
        
    except Exception as e:
        print(f"❌ Query complexity test failed: {e}")
        return False

def calculate_baseline_improvement():
    """Calculate improvement over 80% baseline"""
    print("\n🧪 Calculating Baseline Improvement...")
    
    try:
        from supabase import create_client
        from src.utils import search_documents_with_text_hybrid
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Use comprehensive test set
        successful_queries = 0
        total_queries = len(TEST_QUERIES)
        total_score = 0
        
        for i, query in enumerate(TEST_QUERIES):
            print(f"🔍 Query {i+1}/{total_queries}: '{query[:40]}...'")
            
            results = search_documents_with_text_hybrid(supabase, query, match_count=5)
            
            if results and len(results) > 0:
                # Consider successful if we get results with reasonable scores
                avg_score = sum(r.get('hybrid_score', 0) for r in results) / len(results)
                if avg_score > 0.2:  # Minimum threshold for relevance
                    successful_queries += 1
                    total_score += avg_score
                    print(f"   ✅ Success: {len(results)} results, avg score: {avg_score:.3f}")
                else:
                    print(f"   ⚠️ Low relevance: avg score {avg_score:.3f}")
            else:
                print(f"   ❌ No results")
        
        # Calculate metrics
        success_rate = (successful_queries / total_queries) * 100
        avg_score = total_score / successful_queries if successful_queries > 0 else 0
        
        # Baseline comparison (80% from previous phase)
        baseline_success_rate = 80.0
        improvement = success_rate - baseline_success_rate
        
        print(f"\n📊 BASELINE IMPROVEMENT ANALYSIS:")
        print(f"   Current Success Rate: {success_rate:.1f}%")
        print(f"   Baseline (Phase 7.4): {baseline_success_rate:.1f}%")
        print(f"   Improvement: {improvement:+.1f} percentage points")
        print(f"   Average Score: {avg_score:.3f}")
        print(f"   Successful Queries: {successful_queries}/{total_queries}")
        
        # Success criteria: maintain or improve upon 80% baseline
        return success_rate >= baseline_success_rate
        
    except Exception as e:
        print(f"❌ Baseline improvement calculation failed: {e}")
        return False

async def main():
    """Run all Phase 8.1.4 tests"""
    print("🚀 Phase 8.1.4: Testing and Validation")
    print("=" * 60)
    
    # Test results
    results = []
    
    # Test 1: Hybrid vs Semantic Comparison
    results.append(("Hybrid vs Semantic", test_hybrid_vs_semantic_comparison()))
    
    # Test 2: HybridSearchEngine Class (async)
    results.append(("HybridSearchEngine", await test_hybrid_search_engine()))
    
    # Test 3: Weight Optimization
    results.append(("Weight Optimization", test_weight_optimization()))
    
    # Test 4: Query Complexity Handling
    results.append(("Query Complexity", test_query_complexity_handling()))
    
    # Test 5: Baseline Improvement
    results.append(("Baseline Improvement", calculate_baseline_improvement()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PHASE 8.1.4 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 8.1.4 tests PASSED!")
        print("🚀 Phase 8.1: Hybrid Search Implementation COMPLETE!")
        print("📈 Ready for Phase 8.2: Cross-encoder Reranking")
        return True
    elif passed >= total * 0.8:  # 80% pass rate acceptable
        print("⚠️ Most tests passed. Phase 8.1 substantially complete.")
        print("📈 Ready to proceed to Phase 8.2 with minor optimizations needed")
        return True
    else:
        print("❌ Too many tests failed. Review issues before proceeding.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
