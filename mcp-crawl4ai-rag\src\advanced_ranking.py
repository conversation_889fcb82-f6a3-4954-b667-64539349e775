#!/usr/bin/env python3
"""
Phase 4.3: Advanced Ranking & Scoring System
Sophisticated ranking algorithms for 99% RAG accuracy
"""

import asyncio
import logging
import math
import statistics
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class RankingFeatures:
    """Features used for advanced ranking"""
    semantic_similarity: float
    keyword_relevance: float
    content_quality: float
    entity_relevance: float
    temporal_relevance: float
    program_match: float
    structural_quality: float
    information_density: float
    readability_score: float
    
    def to_vector(self) -> List[float]:
        """Convert to feature vector for ML ranking"""
        return [
            self.semantic_similarity,
            self.keyword_relevance,
            self.content_quality,
            self.entity_relevance,
            self.temporal_relevance,
            self.program_match,
            self.structural_quality,
            self.information_density,
            self.readability_score
        ]

@dataclass
class RankedResult:
    """Result with advanced ranking score"""
    document: Dict[str, Any]
    ranking_features: RankingFeatures
    final_score: float
    ranking_explanation: str

class AdvancedRankingEngine:
    """Advanced ranking system with multiple scoring algorithms"""
    
    def __init__(self):
        # Configurable weights for different ranking factors
        self.weights = {
            'semantic_similarity': 0.25,
            'keyword_relevance': 0.15,
            'content_quality': 0.20,
            'entity_relevance': 0.15,
            'temporal_relevance': 0.05,
            'program_match': 0.10,
            'structural_quality': 0.05,
            'information_density': 0.03,
            'readability_score': 0.02
        }
        
        # Learning-to-rank parameters
        self.ltr_enabled = True
        self.ltr_weights = None
        
        logger.info("🎯 Advanced Ranking Engine initialized")
    
    async def rank_results(
        self,
        results: List[Dict[str, Any]],
        query: str,
        query_metadata: Optional[Dict[str, Any]] = None,
        ranking_strategy: str = "weighted_ensemble"
    ) -> List[RankedResult]:
        """
        Advanced ranking with multiple algorithms
        
        Args:
            results: List of search results
            query: Original query
            query_metadata: Extracted query metadata
            ranking_strategy: 'weighted_ensemble', 'learning_to_rank', 'context_aware'
        """
        if not results:
            return []
        
        logger.info(f"🎯 Ranking {len(results)} results using {ranking_strategy} strategy")
        
        # Step 1: Extract ranking features for each result
        ranked_results = []
        for result in results:
            features = await self._extract_ranking_features(result, query, query_metadata)
            ranked_results.append((result, features))
        
        # Step 2: Apply ranking strategy
        if ranking_strategy == "weighted_ensemble":
            final_results = await self._weighted_ensemble_ranking(ranked_results, query)
        elif ranking_strategy == "learning_to_rank":
            final_results = await self._learning_to_rank(ranked_results, query)
        elif ranking_strategy == "context_aware":
            final_results = await self._context_aware_ranking(ranked_results, query, query_metadata)
        else:
            # Fallback to weighted ensemble
            final_results = await self._weighted_ensemble_ranking(ranked_results, query)
        
        # Step 3: Sort by final score
        final_results.sort(key=lambda x: x.final_score, reverse=True)
        
        logger.info(f"✅ Advanced ranking completed: {len(final_results)} results ranked")
        return final_results
    
    async def _extract_ranking_features(
        self,
        result: Dict[str, Any],
        query: str,
        query_metadata: Optional[Dict[str, Any]] = None
    ) -> RankingFeatures:
        """Extract comprehensive ranking features"""
        
        # Basic features from result
        semantic_similarity = result.get('similarity', 0.0)
        keyword_relevance = result.get('keyword_score', 0.0)
        content_quality = result.get('quality_score', 0.5)
        
        # Entity relevance
        entity_relevance = await self._calculate_entity_relevance(result, query, query_metadata)
        
        # Temporal relevance
        temporal_relevance = await self._calculate_temporal_relevance(result, query_metadata)
        
        # Program match
        program_match = await self._calculate_program_match(result, query_metadata)
        
        # Content analysis features
        content = result.get('content', '')
        structural_quality = self._calculate_structural_quality(content)
        information_density = self._calculate_information_density(content)
        readability_score = self._calculate_readability_score(content)
        
        return RankingFeatures(
            semantic_similarity=semantic_similarity,
            keyword_relevance=keyword_relevance,
            content_quality=content_quality,
            entity_relevance=entity_relevance,
            temporal_relevance=temporal_relevance,
            program_match=program_match,
            structural_quality=structural_quality,
            information_density=information_density,
            readability_score=readability_score
        )
    
    async def _weighted_ensemble_ranking(
        self,
        ranked_results: List[Tuple[Dict[str, Any], RankingFeatures]],
        query: str
    ) -> List[RankedResult]:
        """Weighted ensemble ranking algorithm"""
        
        final_results = []
        
        for result, features in ranked_results:
            # Calculate weighted score
            feature_vector = features.to_vector()
            weight_vector = list(self.weights.values())
            
            final_score = sum(f * w for f, w in zip(feature_vector, weight_vector))
            
            # Normalize to 0-1 range
            final_score = max(0.0, min(1.0, final_score))
            
            # Generate explanation
            explanation = self._generate_ranking_explanation(features, self.weights)
            
            final_results.append(RankedResult(
                document=result,
                ranking_features=features,
                final_score=final_score,
                ranking_explanation=explanation
            ))
        
        return final_results
    
    async def _learning_to_rank(
        self,
        ranked_results: List[Tuple[Dict[str, Any], RankingFeatures]],
        query: str
    ) -> List[RankedResult]:
        """Learning-to-rank algorithm (simplified implementation)"""
        
        if not self.ltr_enabled or self.ltr_weights is None:
            # Fallback to weighted ensemble if LTR not trained
            return await self._weighted_ensemble_ranking(ranked_results, query)
        
        final_results = []
        
        for result, features in ranked_results:
            # Apply learned weights
            feature_vector = np.array(features.to_vector())
            final_score = np.dot(feature_vector, self.ltr_weights)
            
            # Apply sigmoid normalization
            final_score = 1 / (1 + math.exp(-final_score))
            
            explanation = f"LTR Score: {final_score:.3f} (learned weights applied)"
            
            final_results.append(RankedResult(
                document=result,
                ranking_features=features,
                final_score=final_score,
                ranking_explanation=explanation
            ))
        
        return final_results
    
    async def _context_aware_ranking(
        self,
        ranked_results: List[Tuple[Dict[str, Any], RankingFeatures]],
        query: str,
        query_metadata: Optional[Dict[str, Any]] = None
    ) -> List[RankedResult]:
        """Context-aware ranking that adapts weights based on query type"""
        
        # Adapt weights based on query characteristics
        adapted_weights = self.weights.copy()
        
        if query_metadata:
            query_type = query_metadata.get('query_type', 'factual')
            complexity = query_metadata.get('complexity', 'simple')
            
            # Adjust weights based on query type
            if query_type == 'procedural':
                adapted_weights['structural_quality'] *= 1.5
                adapted_weights['information_density'] *= 1.3
            elif query_type == 'comparative':
                adapted_weights['entity_relevance'] *= 1.4
                adapted_weights['content_quality'] *= 1.2
            elif query_type == 'analytical':
                adapted_weights['content_quality'] *= 1.5
                adapted_weights['information_density'] *= 1.4
            
            # Adjust weights based on complexity
            if complexity == 'complex':
                adapted_weights['semantic_similarity'] *= 1.2
                adapted_weights['content_quality'] *= 1.3
        
        # Normalize weights
        total_weight = sum(adapted_weights.values())
        adapted_weights = {k: v/total_weight for k, v in adapted_weights.items()}
        
        final_results = []
        
        for result, features in ranked_results:
            # Calculate context-aware score
            feature_vector = features.to_vector()
            weight_vector = list(adapted_weights.values())
            
            final_score = sum(f * w for f, w in zip(feature_vector, weight_vector))
            final_score = max(0.0, min(1.0, final_score))
            
            explanation = self._generate_ranking_explanation(features, adapted_weights)
            
            final_results.append(RankedResult(
                document=result,
                ranking_features=features,
                final_score=final_score,
                ranking_explanation=explanation
            ))
        
        return final_results
    
    async def _calculate_entity_relevance(
        self,
        result: Dict[str, Any],
        query: str,
        query_metadata: Optional[Dict[str, Any]] = None
    ) -> float:
        """Calculate entity relevance score"""
        
        if not query_metadata:
            return 0.5
        
        # Extract entities from query metadata
        query_programs = query_metadata.get('programs', [])
        query_keywords = query_metadata.get('keywords', [])
        
        # Extract entities from result
        result_metadata = result.get('metadata', {})
        result_content = result.get('content', '').lower()
        
        relevance_score = 0.0
        total_entities = 0
        
        # Check program matches
        if query_programs:
            for program in query_programs:
                if program.lower() in result_content:
                    relevance_score += 1.0
                total_entities += 1
        
        # Check keyword matches
        if query_keywords:
            for keyword in query_keywords:
                if keyword.lower() in result_content:
                    relevance_score += 0.5
                total_entities += 1
        
        return relevance_score / max(1, total_entities)
    
    async def _calculate_temporal_relevance(
        self,
        result: Dict[str, Any],
        query_metadata: Optional[Dict[str, Any]] = None
    ) -> float:
        """Calculate temporal relevance score"""
        
        # Simple temporal relevance based on recency
        created_at = result.get('created_at')
        if not created_at:
            return 0.5
        
        try:
            if isinstance(created_at, str):
                created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            else:
                created_date = created_at
            
            # Calculate days since creation
            days_old = (datetime.now() - created_date.replace(tzinfo=None)).days
            
            # More recent content gets higher score
            if days_old <= 30:
                return 1.0
            elif days_old <= 90:
                return 0.8
            elif days_old <= 365:
                return 0.6
            else:
                return 0.4
                
        except Exception:
            return 0.5
    
    async def _calculate_program_match(
        self,
        result: Dict[str, Any],
        query_metadata: Optional[Dict[str, Any]] = None
    ) -> float:
        """Calculate program-specific match score"""
        
        if not query_metadata:
            return 0.5
        
        query_programs = query_metadata.get('programs', [])
        if not query_programs:
            return 0.5
        
        result_content = result.get('content', '').lower()
        result_metadata = result.get('metadata', {})
        
        matches = 0
        for program in query_programs:
            if program.lower() in result_content:
                matches += 1
        
        return matches / len(query_programs)
    
    def _calculate_structural_quality(self, content: str) -> float:
        """Calculate structural quality of content"""
        
        if not content:
            return 0.0
        
        score = 0.0
        
        # Check for proper structure indicators
        if any(marker in content for marker in ['•', '-', '1.', '2.', 'а)', 'б)']):
            score += 0.3
        
        # Check for headings/sections
        if any(marker in content for marker in [':', '?', '!']):
            score += 0.2
        
        # Check for proper length
        if 100 <= len(content) <= 2000:
            score += 0.3
        elif len(content) > 50:
            score += 0.1
        
        # Check for balanced sentences
        sentences = content.split('.')
        if 3 <= len(sentences) <= 20:
            score += 0.2
        
        return min(1.0, score)
    
    def _calculate_information_density(self, content: str) -> float:
        """Calculate information density score"""
        
        if not content:
            return 0.0
        
        # Simple heuristic: ratio of meaningful words to total words
        words = content.split()
        if not words:
            return 0.0
        
        # Count meaningful words (longer than 3 characters)
        meaningful_words = [w for w in words if len(w) > 3]
        
        density = len(meaningful_words) / len(words)
        return min(1.0, density)
    
    def _calculate_readability_score(self, content: str) -> float:
        """Calculate readability score"""
        
        if not content:
            return 0.0
        
        # Simple readability heuristic
        sentences = content.split('.')
        words = content.split()
        
        if not sentences or not words:
            return 0.0
        
        avg_sentence_length = len(words) / len(sentences)
        
        # Optimal sentence length is around 15-20 words
        if 10 <= avg_sentence_length <= 25:
            return 1.0
        elif 5 <= avg_sentence_length <= 35:
            return 0.7
        else:
            return 0.4
    
    def _generate_ranking_explanation(
        self,
        features: RankingFeatures,
        weights: Dict[str, float]
    ) -> str:
        """Generate human-readable ranking explanation"""
        
        # Find top contributing factors
        feature_contributions = []
        feature_names = [
            'semantic_similarity', 'keyword_relevance', 'content_quality',
            'entity_relevance', 'temporal_relevance', 'program_match',
            'structural_quality', 'information_density', 'readability_score'
        ]
        
        feature_values = features.to_vector()
        
        for name, value, weight in zip(feature_names, feature_values, weights.values()):
            contribution = value * weight
            feature_contributions.append((name, contribution, value))
        
        # Sort by contribution
        feature_contributions.sort(key=lambda x: x[1], reverse=True)
        
        # Generate explanation
        top_factors = feature_contributions[:3]
        explanation_parts = []
        
        for name, contribution, value in top_factors:
            readable_name = name.replace('_', ' ').title()
            explanation_parts.append(f"{readable_name}: {value:.2f}")
        
        return f"Top factors: {', '.join(explanation_parts)}"

# Global instance
advanced_ranking_engine = AdvancedRankingEngine()

async def advanced_rank_results(
    results: List[Dict[str, Any]],
    query: str,
    query_metadata: Optional[Dict[str, Any]] = None,
    ranking_strategy: str = "context_aware"
) -> List[Dict[str, Any]]:
    """
    Apply advanced ranking to search results
    
    Args:
        results: Search results to rank
        query: Original query
        query_metadata: Query metadata for context-aware ranking
        ranking_strategy: Ranking algorithm to use
    
    Returns:
        Ranked results with enhanced scoring
    """
    
    ranked_results = await advanced_ranking_engine.rank_results(
        results=results,
        query=query,
        query_metadata=query_metadata,
        ranking_strategy=ranking_strategy
    )
    
    # Convert back to standard format with enhanced scores
    enhanced_results = []
    for ranked_result in ranked_results:
        result = ranked_result.document.copy()
        result['advanced_score'] = ranked_result.final_score
        result['ranking_explanation'] = ranked_result.ranking_explanation
        result['ranking_features'] = ranked_result.ranking_features.__dict__
        
        # Update similarity with advanced score for compatibility
        result['similarity'] = ranked_result.final_score
        
        enhanced_results.append(result)
    
    return enhanced_results
