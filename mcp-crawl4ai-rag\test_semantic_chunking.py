#!/usr/bin/env python3
"""
Тест за проверка на semantic chunking оптимизацията в Phase 2
Проверява дали новият semantic chunking работи правилно
"""

import asyncio
import logging
from src.utils import create_semantic_chunks

# Настройка на logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_semantic_chunking():
    """Тест на semantic chunking функцията"""
    
    print("🧪 Тестване на Semantic Chunking...")
    print("=" * 60)
    
    # Тестов текст с български съдържание
    test_text = """
    Програма за развитие на селските райони 2014-2020 г. е една от най-важните програми за финансиране в България. 
    Тя предоставя възможности за подкрепа на земеделските стопани и развитие на селските общности.
    
    Основните цели на програмата включват: подобряване на конкурентоспособността на земеделския сектор; 
    устойчиво управление на природните ресурси и действия по климата; постигане на балансирано 
    териториално развитие на селските икономики и общности.
    
    Бюджетът на програмата възлиза на 2.37 милиарда евро за периода 2014-2020 г. От тях 1.59 милиарда 
    евро са от Европейския земеделски фонд за развитие на селските райони (ЕЗФРСР), а 780 милиона евро 
    представляват национално съфинансиране.
    
    Програмата се изпълнява чрез различни мерки, като всяка мерка има специфични цели и критерии за подбор. 
    Кандидатите могат да бъдат земеделски стопани, млади фермери, групи производители, местни общности и други.
    """
    
    print(f"📝 Тестов текст: {len(test_text)} символа")
    print(f"📊 Брой думи: {len(test_text.split())}")
    
    # Тест с различни параметри
    test_configs = [
        {"max_tokens": 256, "min_tokens": 64, "overlap_ratio": 0.1},
        {"max_tokens": 512, "min_tokens": 128, "overlap_ratio": 0.15},
        {"max_tokens": 128, "min_tokens": 32, "overlap_ratio": 0.2}
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n🔍 Тест {i}: max_tokens={config['max_tokens']}, min_tokens={config['min_tokens']}, overlap={config['overlap_ratio']}")
        print("-" * 50)
        
        try:
            chunks = create_semantic_chunks(
                text=test_text,
                max_tokens=config['max_tokens'],
                min_tokens=config['min_tokens'],
                overlap_ratio=config['overlap_ratio']
            )
            
            print(f"📊 Брой chunks: {len(chunks)}")
            
            if chunks:
                total_tokens = sum(chunk.get('tokens', 0) for chunk in chunks)
                avg_tokens = total_tokens / len(chunks) if chunks else 0
                
                print(f"📈 Общо tokens: {total_tokens}")
                print(f"📈 Средно tokens на chunk: {avg_tokens:.1f}")
                
                # Показване на първия chunk
                first_chunk = chunks[0]
                print(f"🏆 Първи chunk:")
                print(f"   Tokens: {first_chunk.get('tokens', 0)}")
                print(f"   Sentences: {first_chunk.get('sentences', 0)}")
                print(f"   Content: {first_chunk.get('content', '')[:100]}...")
                
                # Проверка за metadata
                metadata = first_chunk.get('metadata', {})
                if metadata:
                    print(f"   Metadata: {list(metadata.keys())}")
                
                # Проверка за качество
                if avg_tokens >= config['min_tokens'] and avg_tokens <= config['max_tokens']:
                    print("✅ УСПЕХ: Chunks са в правилния размер!")
                else:
                    print("⚠️  ВНИМАНИЕ: Chunks не са в очаквания размер")
            else:
                print("❌ ГРЕШКА: Няма генерирани chunks")
                
        except Exception as e:
            print(f"❌ ГРЕШКА: {e}")
            logger.error(f"Грешка при тест {i}: {e}", exc_info=True)
    
    print("\n" + "=" * 60)
    print("🏁 Тестването на semantic chunking завърши!")

if __name__ == "__main__":
    test_semantic_chunking()
