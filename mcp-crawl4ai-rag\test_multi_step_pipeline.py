#!/usr/bin/env python3
"""
Test Multi-Step Pipeline - Тества всички 6 етапа на pipeline-а
"""

import asyncio
import sys
import os
import time
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append('src')

from multi_step_pipeline import create_multi_step_pipeline, MultiStepRetrievalPipeline
from supabase import create_client, Client

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_multi_step_pipeline():
    """Тества всички 6 етапа на Multi-Step Pipeline"""
    
    # Initialize Supabase
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        logger.error("SUPABASE_URL или SUPABASE_SERVICE_KEY не са зададени")
        return
    
    supabase: Client = create_client(url, key)
    
    test_query = "Какви са условията за кандидатстване по програма за регионално развитие?"
    
    logger.info("🚀 ЗАПОЧВАМ MULTI-STEP PIPELINE ТЕСТ")
    logger.info("=" * 60)
    
    try:
        # Create Multi-Step Pipeline
        logger.info("📊 СЪЗДАВАМ MULTI-STEP PIPELINE")
        pipeline = create_multi_step_pipeline()
        logger.info(f"✅ Pipeline създаден: {type(pipeline)}")
        
        # Test pipeline execution
        logger.info("📊 ИЗПЪЛНЯВАМ PIPELINE")
        start_time = time.time()
        
        results = pipeline.process_query(
            query=test_query,
            supabase_client=supabase,
            async_openai_client=None,
            final_top_k=5
        )
        
        execution_time = time.time() - start_time
        
        logger.info(f"✅ Pipeline завърши: {execution_time:.2f}s")
        logger.info(f"📊 Резултати: {len(results)} documents")
        
        # Display results
        for i, result in enumerate(results[:3], 1):
            logger.info(f"📄 Резултат {i}:")
            logger.info(f"   URL: {result.get('url', 'N/A')[:80]}...")
            logger.info(f"   Similarity: {result.get('similarity', 0):.3f}")
            logger.info(f"   Content: {result.get('content', '')[:100]}...")
            logger.info("-" * 40)
        
        logger.info("=" * 60)
        logger.info("🎉 MULTI-STEP PIPELINE ТЕСТ ЗАВЪРШИ УСПЕШНО!")
        
    except Exception as e:
        logger.error(f"❌ Multi-Step Pipeline failed: {e}", exc_info=True)
        logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_multi_step_pipeline())
