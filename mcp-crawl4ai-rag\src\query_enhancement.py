#!/usr/bin/env python3
"""
🔍 QUERY ENHANCEMENT MODULE
Имплементира query expansion, normalization и keyword boosting за български език
"""

import re
from typing import List, Dict, Set, Tuple
import logging

logger = logging.getLogger(__name__)

class BulgarianQueryEnhancer:
    """Enhanced query processing for Bulgarian EU funding terminology"""
    
    def __init__(self):
        """Initialize with Bulgarian synonyms and term mappings"""
        
        # Core financing terms
        self.FINANCING_SYNONYMS = {
            "финансиране": ["подкрепа", "средства", "субсидии", "грантове", "помощи", "фондове"],
            "процедури": ["програми", "схеми", "мерки", "възможности", "покани", "конкурси"],
            "проекти": ["инициативи", "дейности", "предложения", "заявки"],
            "подкрепа": ["финансиране", "помощ", "съдействие", "асистенция"],
            "средства": ["финансиране", "ресурси", "капитал", "бюджет"],
            "грантове": ["субсидии", "безвъзмездна помощ", "дарения"]
        }
        
        # Domain-specific terms
        self.DOMAIN_SYNONYMS = {
            "образование": ["обучение", "квалификация", "развитие", "капацитет", "умения", "компетенции"],
            "околна среда": ["екология", "зелена", "климат", "устойчивост", "природа"],
            "транспорт": ["мобилност", "инфраструктура", "пътища", "железопътен", "логистика"],
            "иновации": ["технологии", "изследвания", "развитие", "модернизация", "дигитални"],
            "предприятия": ["бизнес", "компании", "фирми", "стопанство", "икономика"],
            "регионално развитие": ["местно развитие", "общности", "територии", "градове", "села"],
            "цифрова трансформация": ["дигитализация", "цифровизация", "ИКТ", "технологии", "автоматизация"],
            "енергийна ефективност": ["енергоспестяване", "възобновяема енергия", "зелена енергия", "климат"]
        }
        
        # Program name mappings
        self.PROGRAM_MAPPINGS = {
            "европейски фондове": ["ЕС фондове", "структурни фондове", "кохезионни фондове", "европейски програми"],
            "човешки ресурси": ["развитие на човешките ресурси", "заетост", "социално включване"],
            "МСП": ["малки и средни предприятия", "микро предприятия", "стартъпи"]
        }
        
        # Stopwords to remove
        self.STOPWORDS = {
            'и', 'на', 'за', 'в', 'с', 'от', 'до', 'по', 'при', 'към', 'или', 'че', 'да', 'се', 'не',
            'като', 'това', 'тази', 'този', 'тези', 'един', 'една', 'едно', 'два', 'три', 'много'
        }
        
        # All synonyms combined
        self.ALL_SYNONYMS = {**self.FINANCING_SYNONYMS, **self.DOMAIN_SYNONYMS, **self.PROGRAM_MAPPINGS}
        
    def normalize_query(self, query: str) -> str:
        """Normalize Bulgarian query text"""
        if not query:
            return ""
            
        # Convert to lowercase
        normalized = query.lower().strip()
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Handle common variations
        replacements = {
            "процедури за финансиране": "финансиране проекти програми",
            "европейски фондове": "ЕС фондове структурни програми",
            "малки и средни предприятия": "МСП бизнес компании",
            "околна среда": "екология зелена климат",
            "човешки ресурси": "развитие заетост обучение"
        }
        
        for original, replacement in replacements.items():
            if original in normalized:
                normalized = normalized.replace(original, replacement)
        
        return normalized
    
    def expand_query(self, query: str, max_expansions: int = 3) -> str:
        """Expand query with relevant synonyms"""
        if not query:
            return query
            
        normalized = self.normalize_query(query)
        words = set(normalized.split())
        
        # Remove stopwords
        meaningful_words = words - self.STOPWORDS
        
        expanded_terms = set(meaningful_words)
        
        # Add synonyms for each meaningful word
        for word in meaningful_words:
            # Direct synonym lookup
            if word in self.ALL_SYNONYMS:
                synonyms = self.ALL_SYNONYMS[word][:max_expansions]
                expanded_terms.update(synonyms)
            
            # Partial matching for compound terms
            for key, synonyms in self.ALL_SYNONYMS.items():
                if word in key or key in word:
                    expanded_terms.update(synonyms[:max_expansions])
        
        # Combine original and expanded terms
        final_query = ' '.join(expanded_terms)
        
        logger.debug(f"Query expansion: '{query}' -> '{final_query}'")
        return final_query
    
    def extract_keywords(self, query: str) -> List[str]:
        """Extract meaningful keywords from query"""
        normalized = self.normalize_query(query)
        words = normalized.split()
        
        # Remove stopwords and short words
        keywords = [w for w in words if w not in self.STOPWORDS and len(w) > 2]
        
        return keywords
    
    def calculate_keyword_boost(self, query: str, content: str, boost_factor: float = 0.2) -> float:
        """Calculate keyword matching boost score"""
        if not query or not content:
            return 0.0
            
        keywords = self.extract_keywords(query)
        if not keywords:
            return 0.0
            
        content_lower = content.lower()
        matches = sum(1 for keyword in keywords if keyword in content_lower)
        
        keyword_score = matches / len(keywords)
        boost_score = keyword_score * boost_factor
        
        logger.debug(f"Keyword boost: {matches}/{len(keywords)} keywords matched = {boost_score:.3f}")
        return boost_score
    
    def enhance_query_for_search(self, query: str) -> Dict[str, any]:
        """Complete query enhancement pipeline"""
        if not query:
            return {
                'original_query': query,
                'normalized_query': '',
                'expanded_query': '',
                'keywords': [],
                'enhancement_applied': False
            }
        
        # Step 1: Normalize
        normalized = self.normalize_query(query)
        
        # Step 2: Expand with synonyms
        expanded = self.expand_query(normalized)
        
        # Step 3: Extract keywords
        keywords = self.extract_keywords(query)
        
        # Step 4: Determine if enhancement was applied
        enhancement_applied = (
            len(expanded.split()) > len(query.split()) or
            normalized != query.lower().strip()
        )
        
        result = {
            'original_query': query,
            'normalized_query': normalized,
            'expanded_query': expanded,
            'keywords': keywords,
            'enhancement_applied': enhancement_applied
        }
        
        logger.info(f"Query enhancement completed: {enhancement_applied}")
        return result

# Global instance
query_enhancer = BulgarianQueryEnhancer()

def enhance_query(query: str) -> Dict[str, any]:
    """Convenience function for query enhancement"""
    return query_enhancer.enhance_query_for_search(query)

def calculate_keyword_boost(query: str, content: str) -> float:
    """Convenience function for keyword boost calculation"""
    return query_enhancer.calculate_keyword_boost(query, content)

if __name__ == "__main__":
    # Test the enhancer
    test_queries = [
        "процедури за финансиране на проекти",
        "образование и професионално обучение",
        "европейски фондове за околна среда",
        "иновации и технологии за МСП"
    ]
    
    enhancer = BulgarianQueryEnhancer()
    
    for query in test_queries:
        print(f"\n🔍 Original: {query}")
        result = enhancer.enhance_query_for_search(query)
        print(f"📝 Normalized: {result['normalized_query']}")
        print(f"🚀 Expanded: {result['expanded_query']}")
        print(f"🔑 Keywords: {result['keywords']}")
        print(f"✅ Enhanced: {result['enhancement_applied']}")
