#!/usr/bin/env python3
"""
Phase 8.6.2: Content Chunking Revolution Testing
LLM-guided optimization for better precision through improved chunking strategy.

Based on external LLM recommendations:
- Semantic chunking with 256-512 tokens
- Overlap strategy for context preservation
- Bulgarian-specific sentence boundaries
- Metadata enrichment for better retrieval
"""

import asyncio
import logging
import time
from typing import List, Dict, Any
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import (
    search_crawled_pages_advanced_query
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s:%(name)s:%(message)s'
)
logger = logging.getLogger(__name__)

async def test_content_chunking_improvements():
    """Test Phase 8.6.2 content chunking improvements"""
    
    logger.info("🚀 Starting Phase 8.6.2 Content Chunking Revolution Testing")
    
    # Test queries for chunking evaluation
    test_cases = [
        {
            "name": "Сложна МСП заявка",
            "query": "програми за финансиране на малки и средни предприятия в сферата на иновации и технологично развитие с фокус върху дигитализация",
            "expected_chunks": ["МСП", "иновации", "технологии", "дигитализация", "финансиране"]
        },
        {
            "name": "Многокомпонентна екологична заявка", 
            "query": "европейски фондове за околна среда, зелена енергия, възобновяеми източници и устойчиво развитие на регионите",
            "expected_chunks": ["околна среда", "зелена енергия", "възобновяеми", "устойчиво развитие", "региони"]
        },
        {
            "name": "Образователна заявка с множество аспекти",
            "query": "програми за образование, професионално обучение, квалификация на кадри и развитие на умения в дигиталната икономика",
            "expected_chunks": ["образование", "обучение", "квалификация", "умения", "дигитална икономика"]
        },
        {
            "name": "Инфраструктурна заявка",
            "query": "инвестиции в транспортна инфраструктура, железопътен транспорт, пътища и цифрова свързаност в селските райони",
            "expected_chunks": ["транспорт", "инфраструктура", "железопътен", "пътища", "цифрова свързаност", "селски райони"]
        }
    ]
    
    logger.info("🔍 Testing content chunking with complex queries...")
    
    total_success = 0
    total_tests = len(test_cases)
    all_results = []
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n🔍 Testing: {test_case['name']}")
        logger.info(f"Query: '{test_case['query']}'")
        
        start_time = time.time()
        
        try:
            # Search with advanced query processing
            results = await search_crawled_pages_advanced_query(
                test_case['query'],
                match_count=5,
                use_bulgarian_embeddings=True,
                rerank_top_k=20,
                enable_query_expansion=True,
                enable_hyde=True,
                enable_multi_query=True
            )
            
            search_time = time.time() - start_time
            
            if results:
                logger.info(f"✅ Found {len(results)} results in {search_time:.2f}s")
                
                # Analyze chunking quality
                chunk_coverage = analyze_chunk_coverage(results, test_case['expected_chunks'])
                content_diversity = analyze_content_diversity(results)
                semantic_coherence = analyze_semantic_coherence(results)
                
                # Calculate success metrics
                success_rate = min(len(results) / 5, 1.0)  # Target 5 results
                quality_score = (chunk_coverage + content_diversity + semantic_coherence) / 3
                
                test_result = {
                    "name": test_case['name'],
                    "query": test_case['query'],
                    "results_count": len(results),
                    "search_time": search_time,
                    "chunk_coverage": chunk_coverage,
                    "content_diversity": content_diversity,
                    "semantic_coherence": semantic_coherence,
                    "quality_score": quality_score,
                    "success_rate": success_rate,
                    "results": results[:3]  # Store top 3 for analysis
                }
                
                all_results.append(test_result)
                
                if success_rate >= 0.8:  # 80% success threshold
                    total_success += 1
                    logger.info(f"✅ SUCCESS: {success_rate*100:.1f}% success rate")
                else:
                    logger.info(f"⚠️ PARTIAL: {success_rate*100:.1f}% success rate")
                
                # Display detailed results
                for j, result in enumerate(results[:3], 1):
                    keyword_score = result.get('keyword_score', 0)
                    final_score = result.get('final_score', result.get('similarity', 0))
                    content_preview = result.get('content', '')[:100] + "..."
                    
                    logger.info(f"  Result {j}:")
                    logger.info(f"    Quality Score: {quality_score:.3f}")
                    logger.info(f"    Final Score: {final_score:.3f}")
                    logger.info(f"    Content: {content_preview}")
                
                logger.info(f"  📊 Chunking Analysis:")
                logger.info(f"    Chunk Coverage: {chunk_coverage:.3f}")
                logger.info(f"    Content Diversity: {content_diversity:.3f}")
                logger.info(f"    Semantic Coherence: {semantic_coherence:.3f}")
                
            else:
                logger.warning(f"❌ No results found")
                test_result = {
                    "name": test_case['name'],
                    "query": test_case['query'],
                    "results_count": 0,
                    "search_time": search_time,
                    "chunk_coverage": 0.0,
                    "content_diversity": 0.0,
                    "semantic_coherence": 0.0,
                    "quality_score": 0.0,
                    "success_rate": 0.0,
                    "results": []
                }
                all_results.append(test_result)
                
        except Exception as e:
            logger.error(f"❌ Error testing {test_case['name']}: {e}")
            continue
    
    # Generate summary report
    logger.info("\n" + "="*80)
    logger.info("📊 PHASE 8.6.2 CONTENT CHUNKING - SUMMARY REPORT")
    logger.info("="*80)
    
    if all_results:
        avg_success_rate = sum(r['success_rate'] for r in all_results) / len(all_results)
        avg_quality_score = sum(r['quality_score'] for r in all_results) / len(all_results)
        avg_search_time = sum(r['search_time'] for r in all_results) / len(all_results)
        avg_chunk_coverage = sum(r['chunk_coverage'] for r in all_results) / len(all_results)
        avg_content_diversity = sum(r['content_diversity'] for r in all_results) / len(all_results)
        avg_semantic_coherence = sum(r['semantic_coherence'] for r in all_results) / len(all_results)
        
        logger.info("🎯 OVERALL PERFORMANCE:")
        logger.info(f"   Successful Queries: {total_success}/{total_tests} ({total_success/total_tests*100:.1f}%)")
        logger.info(f"   Average Success Rate: {avg_success_rate*100:.1f}%")
        logger.info(f"   Average Quality Score: {avg_quality_score:.3f}")
        logger.info(f"   Average Search Time: {avg_search_time:.2f}s")
        logger.info("")
        logger.info("📋 CHUNKING ANALYSIS:")
        logger.info(f"   Average Chunk Coverage: {avg_chunk_coverage:.3f}")
        logger.info(f"   Average Content Diversity: {avg_content_diversity:.3f}")
        logger.info(f"   Average Semantic Coherence: {avg_semantic_coherence:.3f}")
        logger.info("")
        logger.info("📋 DETAILED RESULTS:")
        
        for result in all_results:
            logger.info(f"   {result['name']}: {result['success_rate']*100:.1f}% success, {result['quality_score']:.3f} quality")
        
        logger.info("")
        if avg_quality_score >= 0.7:
            logger.info("✅ PHASE 8.6.2 SUCCESS: Content chunking shows excellent quality!")
        elif avg_quality_score >= 0.5:
            logger.info("⚠️ PHASE 8.6.2 PARTIAL: Content chunking shows good quality with room for improvement")
        else:
            logger.info("❌ PHASE 8.6.2 NEEDS WORK: Content chunking quality below expectations")
    
    logger.info("✅ Phase 8.6.2 testing completed!")

def analyze_chunk_coverage(results: List[Dict], expected_chunks: List[str]) -> float:
    """Analyze how well results cover expected content chunks"""
    if not results or not expected_chunks:
        return 0.0
    
    covered_chunks = 0
    total_chunks = len(expected_chunks)
    
    # Combine all result content
    all_content = " ".join([r.get('content', '') for r in results]).lower()
    
    for chunk in expected_chunks:
        if chunk.lower() in all_content:
            covered_chunks += 1
    
    return covered_chunks / total_chunks

def analyze_content_diversity(results: List[Dict]) -> float:
    """Analyze diversity of content in results"""
    if not results:
        return 0.0
    
    # Simple diversity metric based on unique words
    all_words = set()
    total_words = 0
    
    for result in results:
        content = result.get('content', '')
        words = content.lower().split()
        all_words.update(words)
        total_words += len(words)
    
    if total_words == 0:
        return 0.0
    
    # Diversity = unique words / total words
    diversity = len(all_words) / total_words
    return min(diversity * 2, 1.0)  # Scale to 0-1 range

def analyze_semantic_coherence(results: List[Dict]) -> float:
    """Analyze semantic coherence of results"""
    if not results:
        return 0.0
    
    # Simple coherence metric based on score consistency
    scores = [r.get('final_score', r.get('similarity', 0)) for r in results]
    
    if not scores:
        return 0.0
    
    # Coherence based on score distribution
    avg_score = sum(scores) / len(scores)
    score_variance = sum((s - avg_score) ** 2 for s in scores) / len(scores)
    
    # Lower variance = higher coherence
    coherence = max(0, 1 - score_variance)
    return min(coherence, 1.0)

if __name__ == "__main__":
    asyncio.run(test_content_chunking_improvements())
