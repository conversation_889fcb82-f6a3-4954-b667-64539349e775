#!/usr/bin/env python3
"""
🚀 PHASE 2: QUERY EXPANSION WITH HyDE
====================================

Имплементация на Query Expansion с HyDE (Hypothetical Document Embeddings):
1. Генериране на хипотетични документи от заявката
2. Създаване на множество query variations
3. Комбиниране на резултати с Reciprocal Rank Fusion
4. Интеграция с Phase 1 hybrid search

Очакван резултат: 20% подобрение (0.541 → ~0.65)
"""

import asyncio
import json
import time
import sys
import os
from typing import Dict, List, Any, Optional

# Add src to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Import after adding to path
import utils

# Use the functions
search_documents_with_text_hybrid = utils.search_documents_with_text_hybrid
get_supabase_client = utils.get_supabase_client
get_openai_client_sync = utils.get_openai_client_sync


def generate_hypothetical_document(query: str, openai_client) -> str:
    """
    🧠 HyDE: Generate hypothetical document that would answer the query.
    
    This creates a synthetic document that represents what an ideal answer
    would look like, which often improves retrieval accuracy.
    """
    try:
        prompt = f"""Като експерт по европейски фондове и програми за финансиране, напиши кратък хипотетичен документ (2-3 изречения) който би отговорил на следната заявка:

Заявка: {query}

Документът трябва да съдържа:
- Конкретна информация за програми или фондове
- Специфични условия или критерии
- Релевантни дати или периоди
- Български контекст където е приложимо

Хипотетичен документ:"""

        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=150,
            temperature=0.3
        )
        
        hypothetical_doc = response.choices[0].message.content.strip()
        print(f"🧠 HyDE generated: {hypothetical_doc[:100]}...")
        return hypothetical_doc
        
    except Exception as e:
        print(f"❌ HyDE generation error: {str(e)}")
        return query  # Fallback to original query


def generate_query_variations(query: str, openai_client) -> List[str]:
    """
    🔄 Generate multiple variations of the query for better coverage.
    """
    try:
        prompt = f"""Генерирай 3 различни вариации на следната заявка за търсене в база данни с европейски фондове. Всяка вариация трябва да:
- Запази основния смисъл
- Използва различни ключови думи
- Покрива различни аспекти на заявката

Оригинална заявка: {query}

Върни само 3 вариации, всяка на нов ред:"""

        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.7
        )
        
        variations_text = response.choices[0].message.content.strip()
        variations = [v.strip() for v in variations_text.split('\n') if v.strip()]
        
        # Ensure we have exactly 3 variations
        while len(variations) < 3:
            variations.append(query)
        variations = variations[:3]
        
        print(f"🔄 Generated {len(variations)} query variations")
        return variations
        
    except Exception as e:
        print(f"❌ Query variation error: {str(e)}")
        return [query, query, query]  # Fallback to original query


def reciprocal_rank_fusion(results_lists: List[List[Dict]], k: int = 60) -> List[Dict]:
    """
    🔗 Combine multiple result lists using Enhanced RRF with score preservation.

    Enhanced RRF that preserves original scores and normalizes properly.
    """
    # Collect all unique documents by ID
    doc_scores = {}

    for results in results_lists:
        for rank, doc in enumerate(results, 1):
            doc_id = doc.get('id', str(hash(doc.get('content', '')[:100])))

            # Enhanced RRF: Combine RRF with original score
            rrf_score = 1.0 / (k + rank)
            original_score = doc.get('hybrid_score', doc.get('similarity', 0.0))

            # Weighted combination: 70% original score + 30% RRF boost
            enhanced_score = (0.7 * original_score) + (0.3 * rrf_score * 10)  # Scale RRF up

            if doc_id in doc_scores:
                doc_scores[doc_id]['enhanced_score'] += enhanced_score
                doc_scores[doc_id]['rrf_score'] += rrf_score
                doc_scores[doc_id]['appearances'] += 1
                # Keep the best original score
                if original_score > doc_scores[doc_id]['best_original_score']:
                    doc_scores[doc_id]['best_original_score'] = original_score
                    doc_scores[doc_id]['doc'] = doc  # Update to best version
            else:
                doc_scores[doc_id] = {
                    'doc': doc,
                    'enhanced_score': enhanced_score,
                    'rrf_score': rrf_score,
                    'best_original_score': original_score,
                    'appearances': 1
                }

    # Sort by enhanced score and return documents
    sorted_docs = sorted(doc_scores.values(), key=lambda x: x['enhanced_score'], reverse=True)

    # Add enhanced metadata to documents
    final_results = []
    for item in sorted_docs:
        doc = item['doc'].copy()
        doc['enhanced_score'] = item['enhanced_score']
        doc['rrf_score'] = item['rrf_score']
        doc['rrf_appearances'] = item['appearances']
        doc['best_original_score'] = item['best_original_score']
        final_results.append(doc)

    return final_results


def search_with_query_expansion(
    client,
    query: str,
    openai_client,
    match_count: int = 10,
    use_hyde: bool = True,
    use_variations: bool = True
) -> List[Dict[str, Any]]:
    """
    🚀 PHASE 2: Enhanced search with query expansion and HyDE.
    """
    print(f"\n🚀 PHASE 2: Query Expansion for: {query}")
    
    all_results = []
    search_queries = [query]  # Start with original query
    
    # Step 1: Generate HyDE document
    if use_hyde:
        print("🧠 Generating HyDE document...")
        hypothetical_doc = generate_hypothetical_document(query, openai_client)
        search_queries.append(hypothetical_doc)
    
    # Step 2: Generate query variations
    if use_variations:
        print("🔄 Generating query variations...")
        variations = generate_query_variations(query, openai_client)
        search_queries.extend(variations)
    
    print(f"📝 Total search queries: {len(search_queries)}")
    
    # Step 3: Search with each query using Phase 1 hybrid search
    for i, search_query in enumerate(search_queries, 1):
        print(f"\n🔍 Search {i}/{len(search_queries)}: {search_query[:50]}...")
        
        try:
            results = search_documents_with_text_hybrid(
                client=client,
                query_text=search_query,
                match_count=match_count,
                min_similarity_threshold=0.1
            )
            
            if results:
                print(f"✅ Found {len(results)} results")
                all_results.append(results)
            else:
                print("❌ No results found")
                
        except Exception as e:
            print(f"❌ Search error: {str(e)}")
    
    # Step 4: Combine results using RRF
    if all_results:
        print(f"\n🔗 Combining {len(all_results)} result sets with RRF...")
        combined_results = reciprocal_rank_fusion(all_results)
        
        # Limit to requested count
        final_results = combined_results[:match_count]
        
        print(f"✅ Final combined results: {len(final_results)}")
        return final_results
    else:
        print("❌ No results from any query")
        return []


def test_phase2_query_expansion():
    """🧪 Test Phase 2 query expansion vs Phase 1 hybrid search."""
    print("\n" + "="*80)
    print("🚀 PHASE 2: QUERY EXPANSION TESTING")
    print("="*80)
    
    # Initialize clients
    client = get_supabase_client()
    openai_client = get_openai_client_sync()
    
    if not client:
        print("❌ Failed to initialize Supabase client")
        return False
        
    if not openai_client:
        print("❌ Failed to initialize OpenAI client")
        return False
    
    # Test queries
    test_queries = [
        "Какви са условията за кандидатстване по програма Еразъм+ за 2024 година?",
        "Финансиране за малки и средни предприятия в България", 
        "Програми за дигитализация и иновации в образованието",
        "Подкрепа за зелени технологии и устойчиво развитие",
        "Възможности за финансиране на научни изследвания"
    ]
    
    total_phase2_score = 0.0
    total_phase1_score = 0.0
    successful_queries = 0
    
    print(f"\n🧪 Testing {len(test_queries)} queries...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"📝 Query {i}: {query}")
        print('='*60)
        
        # Test Phase 2 (Query Expansion)
        print("\n🚀 PHASE 2 (Query Expansion + HyDE):")
        start_time = time.time()
        try:
            phase2_results = search_with_query_expansion(
                client=client,
                query=query,
                openai_client=openai_client,
                match_count=5,
                use_hyde=True,
                use_variations=True
            )
            phase2_time = time.time() - start_time
            
            if phase2_results:
                # Calculate average enhanced score
                phase2_scores = [r.get('enhanced_score', r.get('hybrid_score', 0.0)) for r in phase2_results]
                avg_phase2_score = sum(phase2_scores) / len(phase2_scores)
                total_phase2_score += avg_phase2_score

                print(f"✅ Found {len(phase2_results)} results in {phase2_time:.3f}s")
                print(f"📊 Average enhanced score: {avg_phase2_score:.3f}")
                print(f"🔗 RRF appearances: {[r.get('rrf_appearances', 1) for r in phase2_results[:3]]}")
                print(f"🎯 Best original scores: {[r.get('best_original_score', 0.0) for r in phase2_results[:3]]}")

                # Show top result
                top_result = phase2_results[0]
                print(f"🏆 Top result enhanced score: {top_result.get('enhanced_score', 0.0):.3f}")
                print(f"🔥 Top result original score: {top_result.get('best_original_score', 0.0):.3f}")
                print(f"📄 Content preview: {top_result.get('content', '')[:100]}...")
            else:
                print("❌ No Phase 2 results found")
                avg_phase2_score = 0.0
                
        except Exception as e:
            print(f"❌ Phase 2 error: {str(e)}")
            phase2_time = 0
            avg_phase2_score = 0.0
        
        # Test Phase 1 (Hybrid Search only)
        print("\n🔥 PHASE 1 (Hybrid Search only):")
        start_time = time.time()
        try:
            phase1_results = search_documents_with_text_hybrid(
                client=client,
                query_text=query,
                match_count=5,
                min_similarity_threshold=0.1
            )
            phase1_time = time.time() - start_time
            
            if phase1_results:
                # Calculate average score
                phase1_scores = [r.get('hybrid_score', 0.0) for r in phase1_results]
                avg_phase1_score = sum(phase1_scores) / len(phase1_scores)
                total_phase1_score += avg_phase1_score
                
                print(f"✅ Found {len(phase1_results)} results in {phase1_time:.3f}s")
                print(f"📊 Average hybrid score: {avg_phase1_score:.3f}")
                
                # Show top result
                top_result = phase1_results[0]
                print(f"🏆 Top result score: {top_result.get('hybrid_score', 0.0):.3f}")
                print(f"📄 Content preview: {top_result.get('content', '')[:100]}...")
            else:
                print("❌ No Phase 1 results found")
                avg_phase1_score = 0.0
                
        except Exception as e:
            print(f"❌ Phase 1 error: {str(e)}")
            phase1_time = 0
            avg_phase1_score = 0.0
        
        # Compare performance
        if phase2_results and phase1_results:
            successful_queries += 1
            improvement = ((avg_phase2_score - avg_phase1_score) / avg_phase1_score * 100) if avg_phase1_score > 0 else 0
            print(f"\n📈 PERFORMANCE COMPARISON:")
            print(f"   Phase 2: {avg_phase2_score:.3f} | Phase 1: {avg_phase1_score:.3f}")
            print(f"   Improvement: {improvement:+.1f}%")
            print(f"   Speed: Phase 2 {phase2_time:.3f}s | Phase 1 {phase1_time:.3f}s")
    
    # Final summary
    print(f"\n{'='*80}")
    print("🏁 FINAL PHASE 2 RESULTS")
    print('='*80)
    
    if successful_queries > 0:
        avg_phase2_final = total_phase2_score / successful_queries
        avg_phase1_final = total_phase1_score / successful_queries
        overall_improvement = ((avg_phase2_final - avg_phase1_final) / avg_phase1_final * 100) if avg_phase1_final > 0 else 0
        
        print(f"📊 Successful queries: {successful_queries}/{len(test_queries)}")
        print(f"🚀 Average PHASE 2 score: {avg_phase2_final:.3f}")
        print(f"🔥 Average PHASE 1 score: {avg_phase1_final:.3f}")
        print(f"📈 Overall improvement: {overall_improvement:+.1f}%")
        
        # Check if we achieved Phase 2 target
        target_improvement = 20.0  # 20% improvement target
        if overall_improvement >= target_improvement:
            print(f"🎯 ✅ PHASE 2 TARGET ACHIEVED! ({overall_improvement:.1f}% >= {target_improvement}%)")
            print("🚀 Ready to proceed to Phase 3: Contextual Retrieval")
        else:
            print(f"⚠️ PHASE 2 TARGET NOT MET ({overall_improvement:.1f}% < {target_improvement}%)")
            print("🔧 Need to optimize query expansion parameters")
    else:
        print("❌ No successful queries - need to debug query expansion")
    
    return successful_queries > 0


if __name__ == "__main__":
    print("🚀 STARTING PHASE 2: QUERY EXPANSION WITH HyDE")
    
    # Run Phase 2 testing
    success = test_phase2_query_expansion()
    
    if success:
        print(f"\n🎯 PHASE 2 COMPLETE!")
        print(f"✅ Query expansion with HyDE implemented and tested")
        print(f"🚀 Ready for Phase 3: Contextual Retrieval")
    else:
        print(f"\n❌ PHASE 2 FAILED - Need to debug query expansion")
