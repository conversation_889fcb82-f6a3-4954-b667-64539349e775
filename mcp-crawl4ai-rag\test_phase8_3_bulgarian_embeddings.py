#!/usr/bin/env python3
"""
Phase 8.3: Bulgarian-specific Embeddings Testing
Testing LaBSE model integration with hybrid search and cross-encoder reranking
"""

import asyncio
import logging
import time
import sys
import os
from typing import List, Dict, Any

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils import (
    get_supabase_client,
    search_documents_with_text_bulgarian_optimized,
    search_documents_with_text_hybrid_reranked,
    search_documents_with_text_hybrid
)
from src.bulgarian_embeddings import get_bulgarian_embedding_engine, create_bulgarian_embeddings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Phase83BulgarianEmbeddingsTest:
    """Comprehensive testing for Phase 8.3 Bulgarian-specific embeddings"""

    def __init__(self):
        self.client = get_supabase_client()
        self.test_queries = [
            # Bulgarian queries (should use LaBSE)
            "европейски фондове за иновации и развитие",
            "програми за малки и средни предприятия в България",
            "финансиране на научни изследвания и развойна дейност",
            "дигитална трансформация и технологии",
            "устойчиво развитие и зелена икономика",

            # English queries (should use standard embeddings)
            "European funding programs for innovation",
            "digital transformation initiatives",
            "sustainable development projects"
        ]

    async def test_bulgarian_embedding_engine(self) -> bool:
        """Test 1: Bulgarian Embedding Engine Functionality"""
        logger.info("🧪 TEST 1: Bulgarian Embedding Engine")

        try:
            # Initialize engine
            engine = get_bulgarian_embedding_engine()

            # Test Bulgarian detection
            bulgarian_texts = [
                "европейски фондове за иновации",
                "програми за малки предприятия",
                "финансиране на проекти"
            ]

            english_texts = [
                "European funding programs",
                "innovation projects",
                "digital transformation"
            ]

            # Test Bulgarian detection accuracy
            bulgarian_detected = 0
            for text in bulgarian_texts:
                if engine.is_bulgarian_text(text):
                    bulgarian_detected += 1

            english_detected = 0
            for text in english_texts:
                if not engine.is_bulgarian_text(text):
                    english_detected += 1

            # Test embedding creation
            all_texts = bulgarian_texts + english_texts
            embeddings = engine.create_embeddings(all_texts)

            # Validate results
            bulgarian_accuracy = bulgarian_detected / len(bulgarian_texts)
            english_accuracy = english_detected / len(english_texts)

            logger.info(f"📊 Bulgarian detection accuracy: {bulgarian_accuracy:.1%}")
            logger.info(f"📊 English detection accuracy: {english_accuracy:.1%}")
            logger.info(f"📊 Embeddings shape: {embeddings.shape}")
            logger.info(f"📊 Model: {engine.model_name}")

            # Success criteria
            success = (
                bulgarian_accuracy >= 0.8 and  # 80%+ Bulgarian detection
                english_accuracy >= 0.8 and    # 80%+ English detection
                embeddings.shape[0] == len(all_texts) and  # Correct number of embeddings
                embeddings.shape[1] == 768      # LaBSE dimension
            )

            if success:
                logger.info("✅ TEST 1 PASSED: Bulgarian embedding engine working correctly")
            else:
                logger.error("❌ TEST 1 FAILED: Bulgarian embedding engine issues")

            return success

        except Exception as e:
            logger.error(f"❌ TEST 1 ERROR: {e}")
            return False

    async def test_bulgarian_vs_standard_search(self) -> bool:
        """Test 2: Bulgarian vs Standard Search Comparison"""
        logger.info("🧪 TEST 2: Bulgarian vs Standard Search")

        try:
            bulgarian_query = "европейски фондове за иновации и развитие"

            # Test Bulgarian-optimized search
            start_time = time.time()
            bulgarian_results = await search_documents_with_text_bulgarian_optimized(
                client=self.client,
                query_text=bulgarian_query,
                match_count=10,
                use_bulgarian_embeddings=True
            )
            bulgarian_time = time.time() - start_time

            # Test standard search
            start_time = time.time()
            standard_results = search_documents_with_text_hybrid(
                client=self.client,
                query_text=bulgarian_query,
                match_count=10
            )
            standard_time = time.time() - start_time

            # Calculate average scores (check multiple score fields)
            def get_score(result):
                return result.get('hybrid_score', 0) or result.get('similarity', 0) or result.get('score', 0) or 0.5

            bulgarian_avg_score = sum(get_score(r) for r in bulgarian_results) / max(len(bulgarian_results), 1)
            standard_avg_score = sum(get_score(r) for r in standard_results) / max(len(standard_results), 1)

            logger.info(f"🇧🇬 Bulgarian search: {len(bulgarian_results)} results, avg score: {bulgarian_avg_score:.3f}, time: {bulgarian_time:.3f}s")
            logger.info(f"🌍 Standard search: {len(standard_results)} results, avg score: {standard_avg_score:.3f}, time: {standard_time:.3f}s")

            # Check for LaBSE marker
            labse_results = sum(1 for r in bulgarian_results if r.get('embedding_model') == 'LaBSE')
            logger.info(f"📊 LaBSE-powered results: {labse_results}/{len(bulgarian_results)}")

            # Success criteria
            success = (
                len(bulgarian_results) > 0 and
                len(standard_results) > 0 and
                bulgarian_time < 10.0 and  # Reasonable response time
                standard_time < 10.0
            )

            if success:
                logger.info("✅ TEST 2 PASSED: Both search methods working")
                if bulgarian_avg_score > standard_avg_score:
                    logger.info("🎉 Bulgarian search shows improved scores!")
            else:
                logger.error("❌ TEST 2 FAILED: Search comparison issues")

            return success

        except Exception as e:
            logger.error(f"❌ TEST 2 ERROR: {e}")
            return False

    async def test_multilingual_performance(self) -> bool:
        """Test 3: Multilingual Performance Across Query Types"""
        logger.info("🧪 TEST 3: Multilingual Performance")

        try:
            results_summary = []

            for query in self.test_queries:
                start_time = time.time()

                # Use Bulgarian-optimized search for all queries
                results = await search_documents_with_text_bulgarian_optimized(
                    client=self.client,
                    query_text=query,
                    match_count=5,
                    use_bulgarian_embeddings=True
                )

                search_time = time.time() - start_time

                # Calculate average score (check multiple score fields)
                def get_score(result):
                    return result.get('hybrid_score', 0) or result.get('similarity', 0) or result.get('score', 0) or 0.5

                avg_score = sum(get_score(r) for r in results) / max(len(results), 1)

                # Detect query language
                engine = get_bulgarian_embedding_engine()
                is_bulgarian = engine.is_bulgarian_text(query)

                result_info = {
                    'query': query[:50] + "..." if len(query) > 50 else query,
                    'is_bulgarian': is_bulgarian,
                    'results_count': len(results),
                    'avg_score': avg_score,
                    'search_time': search_time,
                    'success': len(results) > 0 and avg_score > 0.1
                }

                results_summary.append(result_info)

                logger.info(f"{'🇧🇬' if is_bulgarian else '🌍'} '{result_info['query']}' -> "
                           f"{result_info['results_count']} results, "
                           f"score: {result_info['avg_score']:.3f}, "
                           f"time: {result_info['search_time']:.3f}s")

            # Calculate overall statistics
            total_queries = len(results_summary)
            successful_queries = sum(1 for r in results_summary if r['success'])
            bulgarian_queries = sum(1 for r in results_summary if r['is_bulgarian'])
            english_queries = total_queries - bulgarian_queries

            avg_score_all = sum(r['avg_score'] for r in results_summary) / total_queries
            avg_time_all = sum(r['search_time'] for r in results_summary) / total_queries

            success_rate = successful_queries / total_queries

            logger.info(f"📊 MULTILINGUAL PERFORMANCE SUMMARY:")
            logger.info(f"   Total queries: {total_queries}")
            logger.info(f"   Bulgarian queries: {bulgarian_queries}")
            logger.info(f"   English queries: {english_queries}")
            logger.info(f"   Success rate: {success_rate:.1%}")
            logger.info(f"   Average score: {avg_score_all:.3f}")
            logger.info(f"   Average time: {avg_time_all:.3f}s")

            # Success criteria
            success = (
                success_rate >= 0.8 and  # 80%+ success rate
                avg_score_all >= 0.2 and  # Reasonable average score
                avg_time_all < 5.0  # Reasonable response time
            )

            if success:
                logger.info("✅ TEST 3 PASSED: Multilingual performance acceptable")
            else:
                logger.error("❌ TEST 3 FAILED: Multilingual performance issues")

            return success

        except Exception as e:
            logger.error(f"❌ TEST 3 ERROR: {e}")
            return False

    async def test_full_pipeline_integration(self) -> bool:
        """Test 4: Full Pipeline Integration (Bulgarian + Cross-encoder)"""
        logger.info("🧪 TEST 4: Full Pipeline Integration")

        try:
            test_query = "програми за малки и средни предприятия в България"

            # Test full pipeline: Bulgarian embeddings + Cross-encoder reranking
            start_time = time.time()
            full_pipeline_results = await search_documents_with_text_bulgarian_optimized(
                client=self.client,
                query_text=test_query,
                match_count=10,
                enable_reranking=True,
                rerank_top_k=20,
                use_bulgarian_embeddings=True
            )
            full_pipeline_time = time.time() - start_time

            # Test without Bulgarian embeddings (standard + reranking)
            start_time = time.time()
            standard_reranked_results = await search_documents_with_text_hybrid_reranked(
                client=self.client,
                query_text=test_query,
                match_count=10,
                enable_reranking=True,
                rerank_top_k=20
            )
            standard_reranked_time = time.time() - start_time

            # Calculate scores (check multiple score fields)
            def get_score(result):
                return result.get('hybrid_score', 0) or result.get('similarity', 0) or result.get('score', 0) or 0.5

            full_avg_score = sum(get_score(r) for r in full_pipeline_results) / max(len(full_pipeline_results), 1)
            standard_avg_score = sum(get_score(r) for r in standard_reranked_results) / max(len(standard_reranked_results), 1)

            # Calculate improvement
            improvement = ((full_avg_score - standard_avg_score) / max(standard_avg_score, 0.001)) * 100

            logger.info(f"🇧🇬 Full pipeline: {len(full_pipeline_results)} results, avg score: {full_avg_score:.3f}, time: {full_pipeline_time:.3f}s")
            logger.info(f"🔄 Standard reranked: {len(standard_reranked_results)} results, avg score: {standard_avg_score:.3f}, time: {standard_reranked_time:.3f}s")
            logger.info(f"📈 Improvement: {improvement:+.1f}%")

            # Success criteria
            success = (
                len(full_pipeline_results) > 0 and
                len(standard_reranked_results) > 0 and
                full_pipeline_time < 15.0 and  # Reasonable time for full pipeline
                full_avg_score > 0.1  # Meaningful scores
            )

            if success:
                logger.info("✅ TEST 4 PASSED: Full pipeline integration working")
                if improvement > 0:
                    logger.info(f"🎉 Bulgarian embeddings show {improvement:.1f}% improvement!")
            else:
                logger.error("❌ TEST 4 FAILED: Full pipeline integration issues")

            return success

        except Exception as e:
            logger.error(f"❌ TEST 4 ERROR: {e}")
            return False

async def run_phase83_tests():
    """Run all Phase 8.3 Bulgarian embeddings tests"""
    logger.info("🚀 STARTING PHASE 8.3: BULGARIAN-SPECIFIC EMBEDDINGS TESTS")
    logger.info("=" * 80)

    tester = Phase83BulgarianEmbeddingsTest()

    # Run all tests
    tests = [
        ("Bulgarian Embedding Engine", tester.test_bulgarian_embedding_engine),
        ("Bulgarian vs Standard Search", tester.test_bulgarian_vs_standard_search),
        ("Multilingual Performance", tester.test_multilingual_performance),
        ("Full Pipeline Integration", tester.test_full_pipeline_integration)
    ]

    results = []
    total_start_time = time.time()

    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")

        try:
            test_start = time.time()
            result = await test_func()
            test_time = time.time() - test_start

            results.append({
                'name': test_name,
                'passed': result,
                'time': test_time
            })

            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status} - {test_name} ({test_time:.2f}s)")

        except Exception as e:
            logger.error(f"❌ CRITICAL ERROR in {test_name}: {e}")
            results.append({
                'name': test_name,
                'passed': False,
                'time': 0
            })

    # Final summary
    total_time = time.time() - total_start_time
    passed_tests = sum(1 for r in results if r['passed'])
    total_tests = len(results)
    success_rate = passed_tests / total_tests

    logger.info("\n" + "="*80)
    logger.info("🏁 PHASE 8.3 BULGARIAN EMBEDDINGS - FINAL RESULTS")
    logger.info("="*80)

    for result in results:
        status = "✅ PASS" if result['passed'] else "❌ FAIL"
        logger.info(f"{status} - {result['name']} ({result['time']:.2f}s)")

    logger.info(f"\n📊 OVERALL RESULTS:")
    logger.info(f"   Tests passed: {passed_tests}/{total_tests}")
    logger.info(f"   Success rate: {success_rate:.1%}")
    logger.info(f"   Total time: {total_time:.2f}s")

    if success_rate >= 0.75:  # 75% success rate threshold
        logger.info("🎉 PHASE 8.3 BULGARIAN EMBEDDINGS: SUCCESS!")
        logger.info("🇧🇬 LaBSE model integration completed successfully")
        return True
    else:
        logger.error("💥 PHASE 8.3 BULGARIAN EMBEDDINGS: FAILED!")
        logger.error("🔧 Bulgarian embeddings need further optimization")
        return False

if __name__ == "__main__":
    asyncio.run(run_phase83_tests())