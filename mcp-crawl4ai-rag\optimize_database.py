#!/usr/bin/env python3
"""
Автоматична оптимизация на базата данни
Проверява и подобрява pgvector индекси за по-бързо търсене
"""

import os
import sys
import time
from typing import List, Dict, Any

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import get_supabase_client

def check_current_indexes():
    """Проверява текущите индекси в базата данни"""
    print("🔍 ПРОВЕРКА НА ТЕКУЩИ ИНДЕКСИ")
    print("=" * 50)
    
    supabase = get_supabase_client()
    if not supabase:
        print("❌ Не може да се свърже с базата данни!")
        return []
    
    try:
        # Проверка на индекси за chunks таблицата
        result = supabase.rpc('get_table_indexes', {'table_name': 'chunks'}).execute()
        
        if result.data:
            print("📊 Намерени индекси:")
            for idx in result.data:
                print(f"   - {idx.get('indexname', 'Unknown')}: {idx.get('indexdef', 'No definition')}")
        else:
            print("⚠️  Няма намерени индекси или грешка при заявката")
            
        return result.data if result.data else []
        
    except Exception as e:
        print(f"❌ Грешка при проверка на индекси: {e}")
        return []

def optimize_vector_indexes():
    """Оптимизира pgvector индексите"""
    print("\n🚀 ОПТИМИЗАЦИЯ НА VECTOR ИНДЕКСИ")
    print("=" * 50)
    
    supabase = get_supabase_client()
    if not supabase:
        print("❌ Не може да се свърже с базата данни!")
        return False
    
    optimizations = [
        {
            'name': 'HNSW Index for Embeddings',
            'sql': '''
                CREATE INDEX IF NOT EXISTS chunks_embedding_hnsw_idx 
                ON chunks USING hnsw (embedding vector_cosine_ops)
                WITH (m = 16, ef_construction = 64);
            ''',
            'description': 'HNSW индекс за бързо cosine similarity търсене'
        },
        {
            'name': 'IVFFlat Index for Embeddings (Backup)',
            'sql': '''
                CREATE INDEX IF NOT EXISTS chunks_embedding_ivfflat_idx 
                ON chunks USING ivfflat (embedding vector_cosine_ops)
                WITH (lists = 100);
            ''',
            'description': 'IVFFlat индекс като резерва за по-големи datasets'
        },
        {
            'name': 'Composite Index for Filtering',
            'sql': '''
                CREATE INDEX IF NOT EXISTS chunks_url_created_idx 
                ON chunks (url, created_at DESC);
            ''',
            'description': 'Композитен индекс за филтриране по URL и дата'
        },
        {
            'name': 'Text Search Index',
            'sql': '''
                CREATE INDEX IF NOT EXISTS chunks_content_gin_idx 
                ON chunks USING gin(to_tsvector('bulgarian', content));
            ''',
            'description': 'GIN индекс за full-text search на български'
        }
    ]
    
    success_count = 0
    
    for opt in optimizations:
        print(f"\n📝 Създаване: {opt['name']}")
        print(f"   {opt['description']}")
        
        try:
            start_time = time.time()
            
            # Изпълняване на SQL заявката
            result = supabase.rpc('execute_sql', {'sql_query': opt['sql']}).execute()
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.data is not None:
                print(f"   ✅ Успешно създаден за {duration:.2f}s")
                success_count += 1
            else:
                print(f"   ⚠️  Възможно е вече да съществува")
                success_count += 1
                
        except Exception as e:
            print(f"   ❌ Грешка: {e}")
    
    print(f"\n📊 Резултат: {success_count}/{len(optimizations)} оптимизации успешни")
    return success_count == len(optimizations)

def analyze_query_performance():
    """Анализира производителността на заявките"""
    print("\n📈 АНАЛИЗ НА ПРОИЗВОДИТЕЛНОСТ")
    print("=" * 50)
    
    supabase = get_supabase_client()
    if not supabase:
        print("❌ Не може да се свърже с базата данни!")
        return {}
    
    test_queries = [
        {
            'name': 'Count Total Chunks',
            'sql': 'SELECT COUNT(*) as total_chunks FROM chunks;'
        },
        {
            'name': 'Recent Chunks',
            'sql': 'SELECT COUNT(*) as recent_chunks FROM chunks WHERE created_at > NOW() - INTERVAL \'7 days\';'
        },
        {
            'name': 'Unique URLs',
            'sql': 'SELECT COUNT(DISTINCT url) as unique_urls FROM chunks;'
        },
        {
            'name': 'Average Content Length',
            'sql': 'SELECT AVG(LENGTH(content)) as avg_content_length FROM chunks;'
        }
    ]
    
    results = {}
    
    for query in test_queries:
        print(f"\n🔍 {query['name']}:")
        
        try:
            start_time = time.time()
            result = supabase.rpc('execute_sql', {'sql_query': query['sql']}).execute()
            end_time = time.time()
            
            duration = end_time - start_time
            
            if result.data:
                value = list(result.data[0].values())[0] if result.data[0] else 'N/A'
                print(f"   Резултат: {value}")
                print(f"   Време: {duration:.3f}s")
                results[query['name']] = {'value': value, 'time': duration}
            else:
                print(f"   ❌ Няма данни")
                
        except Exception as e:
            print(f"   ❌ Грешка: {e}")
    
    return results

def vacuum_and_analyze():
    """Изпълнява VACUUM и ANALYZE за оптимизация"""
    print("\n🧹 ПОЧИСТВАНЕ И АНАЛИЗ НА БАЗАТА")
    print("=" * 50)
    
    supabase = get_supabase_client()
    if not supabase:
        print("❌ Не може да се свърже с базата данни!")
        return False
    
    maintenance_queries = [
        {
            'name': 'VACUUM chunks table',
            'sql': 'VACUUM chunks;',
            'description': 'Почистване на мъртви редове'
        },
        {
            'name': 'ANALYZE chunks table',
            'sql': 'ANALYZE chunks;',
            'description': 'Обновяване на статистики за query planner'
        },
        {
            'name': 'REINDEX vector indexes',
            'sql': 'REINDEX INDEX CONCURRENTLY chunks_embedding_hnsw_idx;',
            'description': 'Преиндексиране на vector индекси'
        }
    ]
    
    success_count = 0
    
    for query in maintenance_queries:
        print(f"\n🔧 {query['name']}")
        print(f"   {query['description']}")
        
        try:
            start_time = time.time()
            result = supabase.rpc('execute_sql', {'sql_query': query['sql']}).execute()
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"   ✅ Завършено за {duration:.2f}s")
            success_count += 1
            
        except Exception as e:
            print(f"   ⚠️  Пропуснато (възможно е да не е нужно): {e}")
    
    return success_count > 0

def main():
    """Главна функция за оптимизация на базата данни"""
    print("🚀 АВТОМАТИЧНА ОПТИМИЗАЦИЯ НА БАЗАТА ДАННИ")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Това ще направи реални промени в базата данни!")
    print("=" * 60)
    
    # Стъпка 1: Проверка на текущи индекси
    current_indexes = check_current_indexes()
    
    # Стъпка 2: Анализ на производителност преди оптимизация
    print("\n📊 ПРОИЗВОДИТЕЛНОСТ ПРЕДИ ОПТИМИЗАЦИЯ:")
    before_stats = analyze_query_performance()
    
    # Стъпка 3: Оптимизация на индекси
    index_success = optimize_vector_indexes()
    
    # Стъпка 4: Поддръжка на базата данни
    maintenance_success = vacuum_and_analyze()
    
    # Стъпка 5: Анализ на производителност след оптимизация
    print("\n📊 ПРОИЗВОДИТЕЛНОСТ СЛЕД ОПТИМИЗАЦИЯ:")
    after_stats = analyze_query_performance()
    
    # Заключение
    print("\n" + "=" * 60)
    print("🎯 ЗАКЛЮЧЕНИЕ")
    print("=" * 60)
    
    if index_success:
        print("✅ Индексите са оптимизирани успешно!")
    else:
        print("⚠️  Някои индекси не са създадени правилно")
    
    if maintenance_success:
        print("✅ Поддръжката на базата е завършена!")
    else:
        print("⚠️  Поддръжката на базата е частично завършена")
    
    # Сравнение на производителността
    if before_stats and after_stats:
        print("\n📈 СРАВНЕНИЕ НА ПРОИЗВОДИТЕЛНОСТТА:")
        for query_name in before_stats:
            if query_name in after_stats:
                before_time = before_stats[query_name]['time']
                after_time = after_stats[query_name]['time']
                improvement = (before_time - after_time) / before_time * 100
                print(f"   {query_name}: {improvement:+.1f}% промяна")
    
    print("\n🎉 Оптимизацията на базата данни е завършена!")
    return index_success and maintenance_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
