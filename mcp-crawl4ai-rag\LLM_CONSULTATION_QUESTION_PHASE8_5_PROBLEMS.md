# 🤖 LLM CONSULTATION: РЕШАВАНЕ НА ПРОБЛЕМИ В RAG СИСТЕМАТА - PHASE 8.5

## 📋 КОНТЕКСТ НА ПРОБЛЕМА

Имаме напреднала RAG система за европейски фондове в България с отлична техническа производителност, но ниско качество на съдържанието. Системата постига 100% успеваемост в намирането на резултати, но само 17.8% средно качество на релевантността.

## 🏗️ ТЕКУЩА АРХИТЕКТУРА

### Технически компоненти:
- **Hybrid Search**: BM25 + Den<PERSON> векторно търсене (BGE-M3 1024-dim + LaBSE 768-dim)
- **Cross-encoder Reranking**: ms-marco-MiniLM-L6-v2 за joint query-document scoring
- **Advanced Query Processing**: Query expansion (13 български синонима), HyDE pattern, Multi-query transformation
- **Bulgarian-specific Embeddings**: LaBSE модел оптимизиран за български език
- **Performance Optimization**: Cascaded execution, caching (33.5% improvement), batch processing

### База данни:
- **Supabase PostgreSQL** с 100 страници от eufunds.bg
- **Dual embedding architecture**: BGE-M3 (1024-dim) + LaBSE (768-dim)
- **RPC функции**: match_crawled_pages_hybrid + match_crawled_pages_labse_hybrid

## 📊 ТЕКУЩИ РЕЗУЛТАТИ (Golden Standard Test)

### Производителност по методи:
1. **HYBRID_RERANKED** (най-добър):
   - ✅ Успеваемост: 100.0% (10/10)
   - ⏱️ Време: 2.08s (най-бързо)
   - 🎯 Качество: 17.8%
   - 💯 Релевантно съдържание: 90.0%

2. **ADVANCED_QUERY**:
   - ✅ Успеваемост: 100.0% (10/10)
   - ⏱️ Време: 16.64s
   - 🎯 Качество: 16.7%
   - 💯 Релевантно съдържание: 80.0%

3. **CASCADED**:
   - ✅ Успеваемост: 100.0% (10/10)
   - ⏱️ Време: 12.01s
   - 🎯 Качество: 16.7%
   - 💯 Релевантно съдържание: 80.0%

## ❗ ОСНОВНИ ПРОБЛЕМИ

### 1. НИСКО КАЧЕСТВО НА СЪДЪРЖАНИЕТО (17.8%)
- Системата намира документи, но те не съдържат точните ключови думи
- Несъответствие между заявката и върнатото съдържание
- Слаба семантична релевантност въпреки техническия успех

### 2. НЕСЪОТВЕТСТВИЕ НА КЛЮЧОВИ ДУМИ
**Примери от тестовете:**
- Заявка: "програми за малки и средни предприятия" → Ключови думи: МСП, малки, средни, предприятия
- Заявка: "европейски фондове за иновации" → Ключови думи: иновации, развитие, технологии
- Заявка: "енергийна ефективност" → Ключови думи: енергийна, ефективност, възобновяеми

### 3. HYDE ДОКУМЕНТИ НЕ ПОДОБРЯВАТ РЕЗУЛТАТИТЕ
- HyDE pattern генерира хипотетични документи, но те не водят до по-добри резултати
- 132% overhead без значително подобрение в качеството
- Възможно несъответствие между генерираните документи и реалното съдържание

### 4. SCORING ALGORITHM ПРОБЛЕМИ
- Cross-encoder reranking дава високи технически скорове (8.5+), но ниска релевантност
- Възможно несъответствие между scoring метриките и реалната полезност
- Нужда от по-добро балансиране на scoring weights

## 🎯 ТЕСТОВИ СЛУЧАИ (Golden Standard)

```
1. "програми за малки и средни предприятия в България" → МСП, малки, средни, предприятия, бизнес
2. "европейски фондове за иновации и развитие" → иновации, развитие, технологии, изследвания  
3. "финансиране на стартъп компании в България" → стартъп, финансиране, нови, компании
4. "процедури за кандидатстване по оперативни програми" → процедури, кандидатстване, оперативни, програми
5. "подкрепа за дигитализация на предприятията" → дигитализация, цифрови, технологии, трансформация
6. "програми за околна среда и климат" → околна среда, климат, екология, зелени
7. "европейски средства за образование и обучение" → образование, обучение, квалификация, умения
8. "инфраструктурни проекти с европейско финансиране" → инфраструктура, строителство, пътища, транспорт
9. "програми за развитие на селските райони" → селски, райони, земеделие, развитие
10. "подкрепа за енергийна ефективност и възобновяеми източници" → енергийна, ефективност, възобновяеми, енергия
```

## 🤔 ВЪПРОСИ КЪМ LLM ЕКСПЕРТИТЕ

### 1. SCORING ALGORITHM OPTIMIZATION
Как да подобрим scoring алгоритъма, за да постигнем по-висока семантична релевантност? Трябва ли да:
- Променим weights в hybrid search (текущо: BM25 + Dense fusion)
- Използваме различен cross-encoder модел за български език
- Добавим keyword-based scoring component
- Внедрим semantic similarity threshold

### 2. QUERY-DOCUMENT MATCHING IMPROVEMENT  
Как да подобрим съответствието между заявки и документи? Възможни подходи:
- Query preprocessing и normalization за български език
- Document preprocessing и keyword extraction
- Semantic query expansion с domain-specific терминология
- Multi-level matching (exact + semantic + fuzzy)

### 3. HYDE PATTERN OPTIMIZATION
Как да оптимизираме HyDE pattern за по-добри резултати?
- По-специфични prompts за европейски фондове в България
- Различни HyDE стратегии за различни типове заявки
- Комбиниране на HyDE с други query augmentation техники
- Conditional HyDE usage based on query characteristics

### 4. CONTENT QUALITY ENHANCEMENT
Как да подобрим качеството на съдържанието в базата данни?
- Better content extraction и cleaning от eufunds.bg
- Metadata enrichment с keywords и categories
- Document summarization за по-релевантни excerpts
- Content validation и quality scoring

### 5. EVALUATION METRICS REDESIGN
Как да преработим evaluation метриките за по-точна оценка?
- Weighted keyword matching вместо binary presence
- Semantic similarity scoring между query и content
- User intent classification и category-specific evaluation
- Multi-dimensional quality assessment

### 6. BULGARIAN LANGUAGE OPTIMIZATION
Какви специфични оптимизации за български език могат да подобрят резултатите?
- Bulgarian-specific preprocessing (stemming, lemmatization)
- Domain-specific vocabulary expansion
- Morphological analysis за по-добро keyword matching
- Cultural и linguistic context consideration

## 📈 ЖЕЛАНИ РЕЗУЛТАТИ

**Цел**: Подобряване на качеството от 17.8% на поне 40-50% при запазване на 100% успеваемост

**Приоритети**:
1. 🎯 **Семантична релевантност** - по-точно съответствие query-document
2. ⚡ **Производителност** - запазване на бързината на HYBRID_RERANKED (2.08s)
3. 🇧🇬 **Български език** - оптимизация за специфичните нужди на българския език
4. 📊 **Измерими подобрения** - ясни метрики за прогреса

## 🔧 ТЕХНИЧЕСКИ ОГРАНИЧЕНИЯ

- **Supabase free plan** - ограничени ресурси
- **OpenAI 4o-mini** - budget-friendly модел
- **100 страници** в базата данни
- **Real-time performance** изисквания
- **Bulgarian language focus** - специфични за България европейски фондове

---

**Моля, предоставете конкретни, практически препоръки за решаване на тези проблеми, с фокус върху подобряване на качеството на съдържанието при запазване на техническата производителност.**
