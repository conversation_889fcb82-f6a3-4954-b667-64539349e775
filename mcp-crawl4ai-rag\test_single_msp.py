#!/usr/bin/env python3
"""
Тест само за МСП програми (test_6) за да проверим подобренията
"""

import asyncio
import json
from datetime import datetime
from supabase import create_client, Client
from src.utils import ultra_smart_rag_query
import os
from dotenv import load_dotenv

# Зареди environment variables
load_dotenv()

# Supabase настройки
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_KEY")

# Тест случай за МСП
TEST_CASE = {
    "id": "test_6",
    "query": "Кои са програмите за малки и средни предприятия?",
    "expected_programs": ["Оперативна програма Иновации и конкурентоспособност", "Подкрепа за малки и средни предприятия"],
    "expected_content": ["малки и средни предприятия", "МСП", "иновации", "конкурентоспособност"],
    "expected_urls": ["https://www.eufunds.bg/bg/page/2"],
    "description": "Търси МСП програми - имаме данни в новините"
}

async def test_msp_programs():
    """Тестване на МСП програми с Phase 4.7"""
    
    # Инициализирай Supabase клиент
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    print(f"🎯 ТЕСТ ЗА МСП ПРОГРАМИ")
    print(f"❓ ВЪПРОС: {TEST_CASE['query']}")
    print(f"🎯 ОЧАКВАНИ ПРОГРАМИ: {TEST_CASE['expected_programs']}")
    print(f"🎯 ОЧАКВАНИ URL-И: {TEST_CASE['expected_urls']}")
    print("=" * 60)
    
    try:
        # Изпълни RAG заявката с Phase 4.7 подобрения
        result = await ultra_smart_rag_query(
            query=TEST_CASE['query'],
            supabase_client=supabase,
            final_top_k=5,
            similarity_threshold=0.3,
            adaptive_threshold=True,
            multi_pass_search=True,
            query_expansion=True,
            # Phase 4.7 optimizations enabled
            intelligent_clustering=True,
            dynamic_learning=True,
            performance_optimization=True
        )
        
        # Анализирай резултата
        found_programs = []
        found_urls = []
        found_content = []

        # Използвай правилния ключ за резултатите
        documents = result.get('documents', []) or result.get('results', [])

        for doc in documents:
            content = doc.get('content', '').lower()
            url = doc.get('url', '')
            metadata = doc.get('metadata', {})
            title = metadata.get('title', '') if metadata else ''
            
            # Търси програми в съдържанието и заглавието
            full_text = f"{content} {title}".lower()
            
            for expected_program in TEST_CASE['expected_programs']:
                program_lower = expected_program.lower()
                if program_lower in full_text:
                    found_programs.append(expected_program)
            
            # Търси очаквано съдържание
            for expected_content in TEST_CASE['expected_content']:
                if expected_content.lower() in content:
                    found_content.append(expected_content)
            
            # Търси очаквани URL-и
            for expected_url in TEST_CASE.get('expected_urls', []):
                if expected_url in url:
                    found_urls.append(url)
        
        # Премахни дубликати
        found_programs = list(set(found_programs))
        found_urls = list(set(found_urls))
        found_content = list(set(found_content))
        
        # Изчисли успех
        program_success = len(found_programs) / len(TEST_CASE['expected_programs']) if TEST_CASE['expected_programs'] else 0
        url_success = len(found_urls) / len(TEST_CASE.get('expected_urls', [])) if TEST_CASE.get('expected_urls') else 0
        content_success = len(found_content) / len(TEST_CASE['expected_content']) if TEST_CASE['expected_content'] else 0
        
        # Общ успех (средно аритметично)
        weights = []
        scores = []
        
        if TEST_CASE['expected_programs']:
            weights.append(0.5)  # 50% тежест за програми
            scores.append(program_success)
        
        if TEST_CASE.get('expected_urls'):
            weights.append(0.3)  # 30% тежест за URL-и
            scores.append(url_success)
        
        if TEST_CASE['expected_content']:
            weights.append(0.2)  # 20% тежест за съдържание
            scores.append(content_success)
        
        total_success = sum(w * s for w, s in zip(weights, scores)) / sum(weights) * 100
        
        # Покажи резултатите
        print(f"\n📊 РЕЗУЛТАТИ:")
        print(f"✅ НАМЕРЕНИ ПРОГРАМИ: {found_programs}")
        print(f"✅ НАМЕРЕНИ URL-И: {found_urls}")
        print(f"✅ НАМЕРЕНО СЪДЪРЖАНИЕ: {found_content}")
        print(f"\n📈 УСПЕХ ПО КАТЕГОРИИ:")
        print(f"   Програми: {program_success*100:.1f}% ({len(found_programs)}/{len(TEST_CASE['expected_programs'])})")
        print(f"   URL-и: {url_success*100:.1f}% ({len(found_urls)}/{len(TEST_CASE.get('expected_urls', []))})")
        print(f"   Съдържание: {content_success*100:.1f}% ({len(found_content)}/{len(TEST_CASE['expected_content'])})")
        print(f"\n🎉 ОБЩ УСПЕХ: {total_success:.1f}%")
        
        # Покажи намерените документи за анализ
        documents = result.get('documents', []) or result.get('results', [])
        print(f"\n📄 НАМЕРЕНИ ДОКУМЕНТИ ({len(documents)}):")
        if documents:
            for i, doc in enumerate(documents[:5], 1):
                print(f"{i}. URL: {doc.get('url', 'N/A')}")
                print(f"   Similarity: {doc.get('similarity', 'N/A')}")
                print(f"   Content preview: {doc.get('content', '')[:200]}...")
                print()
        else:
            print("❌ НЯМА НАМЕРЕНИ ДОКУМЕНТИ!")
            print(f"🔍 RAG резултат структура: {list(result.keys())}")
            print(f"🔍 Общо резултати: {result.get('total_results', 'N/A')}")
            print(f"🔍 Стратегия: {result.get('strategy', 'N/A')}")
            print(f"🔍 Време: {result.get('execution_time', 'N/A')}")
            if 'results' in result:
                print(f"🔍 Results ключ съществува с {len(result['results'])} елемента")
                for i, res in enumerate(result['results'][:3], 1):
                    print(f"   {i}. {list(res.keys())}")
            if 'documents' in result:
                print(f"🔍 Documents ключ съществува но е празен")
        
        return total_success >= 90.0
        
    except Exception as e:
        print(f"❌ ГРЕШКА: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_msp_programs())
    if success:
        print("🎉 ТЕСТЪТ ПРЕМИНА УСПЕШНО!")
    else:
        print("❌ ТЕСТЪТ НЕ ПРЕМИНА!")
