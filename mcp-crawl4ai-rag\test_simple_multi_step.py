#!/usr/bin/env python3
"""
Прост тест за multi-step pipeline без Bulgarian NLP
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Тестване на импортите"""
    print("🧪 ТЕСТВАНЕ НА ИМПОРТИ")
    print("=" * 50)
    
    try:
        print("📦 Тестване на Groq Reranker...")
        from src.groq_reranker import create_groq_reranker
        reranker = create_groq_reranker()
        print(f"  ✅ Groq Reranker: {type(reranker).__name__}")
        
        print("📦 Тестване на Program Extractor...")
        from src.program_name_extractor import BulgarianProgramExtractor
        extractor = BulgarianProgramExtractor()
        print(f"  ✅ Program Extractor: {type(extractor).__name__}")
        
        print("📦 Тестване на Multi-Step Pipeline...")
        from src.multi_step_pipeline import MultiStepRetrievalPipeline, PipelineConfig, create_multi_step_pipeline
        config = PipelineConfig(use_bulgarian_nlp=False)  # Изключи Bulgarian NLP
        pipeline = create_multi_step_pipeline(config)
        print(f"  ✅ Multi-Step Pipeline: {type(pipeline).__name__}")
        
        print("📦 Тестване на utils импорт...")
        from src.utils import MULTI_STEP_PIPELINE_AVAILABLE, multi_step_rag_query
        print(f"  ✅ Multi-Step Pipeline Available: {MULTI_STEP_PIPELINE_AVAILABLE}")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в импортите: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_functionality():
    """Тестване на основна функционалност"""
    print(f"\n🧪 ТЕСТВАНЕ НА ОСНОВНА ФУНКЦИОНАЛНОСТ")
    print("=" * 50)
    
    try:
        from src.utils import multi_step_rag_query
        
        # Прост тест
        result = multi_step_rag_query(
            query="ОПРР програми",
            max_results=3,
            enable_bulgarian_nlp=False,  # Изключи Bulgarian NLP
            debug=True
        )
        
        print(f"✅ Резултат получен:")
        print(f"  📄 Документи: {len(result.get('documents', []))}")
        print(f"  🔧 Метод: {result.get('method', 'unknown')}")
        print(f"  ⏱️ Време: {result.get('processing_time', 0):.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в функционалността: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_program_extractor():
    """Тестване на program extractor"""
    print(f"\n🧪 ТЕСТВАНЕ НА PROGRAM EXTRACTOR")
    print("=" * 50)
    
    try:
        from src.program_name_extractor import BulgarianProgramExtractor
        
        extractor = BulgarianProgramExtractor()
        
        test_text = "ОПРР 2021-2027 финансира транспортни проекти. Interreg програмата подкрепя трансгранично сътрудничество."
        matches = extractor.extract_program_names(test_text)
        
        print(f"✅ Извлечени програми: {len(matches)}")
        for match in matches[:3]:
            print(f"  🏷️ {match.exact_name} (confidence: {match.confidence:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в program extractor: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_groq_reranker():
    """Тестване на Groq reranker"""
    print(f"\n🧪 ТЕСТВАНЕ НА GROQ RERANKER")
    print("=" * 50)
    
    try:
        from src.groq_reranker import create_groq_reranker
        
        reranker = create_groq_reranker()
        
        # Тест на query complexity
        complexity = reranker.analyze_query_complexity("програми за развитие")
        print(f"✅ Query complexity: {complexity.get('complexity_score', 'N/A')}")
        
        # Тест на fallback scoring
        if hasattr(reranker, '_fallback_score_document'):
            score = reranker._fallback_score_document("тест документ", "тест заявка")
            print(f"✅ Fallback scoring: {score:.3f}")
        else:
            print(f"✅ Fallback scoring: Не е наличен")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в Groq reranker: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Започвам прости тестове за Phase 1...")
    
    success = True
    
    # Тествай импортите
    if not test_imports():
        success = False
    
    # Тествай program extractor
    if not test_program_extractor():
        success = False
    
    # Тествай Groq reranker
    if not test_groq_reranker():
        success = False
    
    # Тествай основната функционалност
    if not test_basic_functionality():
        success = False
    
    if success:
        print(f"\n🎉 Всички прости тестове завършени успешно!")
    else:
        print(f"\n⚠️ Някои тестове имат проблеми")
