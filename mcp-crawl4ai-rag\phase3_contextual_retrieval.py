#!/usr/bin/env python3
"""
🎯 PHASE 3: CONTEXTUAL RETRIEVAL
===============================

Имплементация на Contextual Retrieval с:
1. Small-to-Big retrieval (малки chunks → големи контексти)
2. Parent document expansion
3. Surrounding chunks context
4. Intelligent context merging
5. Интеграция с Phase 2 query expansion

Очакван резултат: 25% подобрение (1.183 → ~1.5)
"""

import asyncio
import json
import time
import sys
import os
from typing import Dict, List, Any, Optional

# Add src to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Import after adding to path
import utils

# Use the functions
search_documents_with_text_hybrid = utils.search_documents_with_text_hybrid
get_supabase_client = utils.get_supabase_client
get_openai_client_sync = utils.get_openai_client_sync

# Import Phase 2 functions
sys.path.insert(0, os.path.dirname(__file__))
from phase2_query_expansion import search_with_query_expansion


def expand_chunks_to_context(
    client,
    chunks: List[Dict[str, Any]],
    expansion_strategy: str = "surrounding_chunks",
    context_window: int = 2
) -> List[Dict[str, Any]]:
    """
    🔍 Expand small chunks to larger contexts for better understanding.
    
    Strategies:
    - surrounding_chunks: Get ±N chunks around each result
    - parent_document: Get full document for each chunk
    - hybrid: Combine both approaches
    """
    print(f"\n🔍 Expanding {len(chunks)} chunks using '{expansion_strategy}' strategy...")
    
    expanded_contexts = []
    processed_urls = set()
    
    try:
        if expansion_strategy == "surrounding_chunks":
            for chunk in chunks:
                url = chunk.get('url', '')
                chunk_number = chunk.get('chunk_number', 0)
                
                if not url:
                    expanded_contexts.append(chunk)
                    continue
                
                # Get surrounding chunks
                start_chunk = max(0, chunk_number - context_window)
                end_chunk = chunk_number + context_window
                
                print(f"📄 Getting chunks {start_chunk}-{end_chunk} for URL: {url[:50]}...")
                
                response = client.table('crawled_pages').select('*').eq('url', url).gte('chunk_number', start_chunk).lte('chunk_number', end_chunk).order('chunk_number').execute()
                
                if response.data:
                    # Combine surrounding chunks
                    combined_content = ' '.join([chunk_data['content'] for chunk_data in response.data])
                    
                    expanded_chunk = chunk.copy()
                    expanded_chunk['expanded_content'] = combined_content
                    expanded_chunk['expansion_method'] = 'surrounding_chunks'
                    expanded_chunk['context_chunks'] = len(response.data)
                    expanded_chunk['context_window'] = context_window
                    
                    expanded_contexts.append(expanded_chunk)
                    print(f"✅ Expanded to {len(response.data)} chunks")
                else:
                    expanded_contexts.append(chunk)
                    print("❌ No surrounding chunks found")
        
        elif expansion_strategy == "parent_document":
            # Group chunks by URL to avoid duplicate processing
            url_to_chunks = {}
            for chunk in chunks:
                url = chunk.get('url', '')
                if url:
                    if url not in url_to_chunks:
                        url_to_chunks[url] = []
                    url_to_chunks[url].append(chunk)
            
            for url, url_chunks in url_to_chunks.items():
                if url in processed_urls:
                    continue
                    
                print(f"📄 Getting full document for URL: {url[:50]}...")
                
                # Get all chunks for this URL
                response = client.table('crawled_pages').select('*').eq('url', url).order('chunk_number').execute()
                
                if response.data:
                    # Combine all chunks into full document
                    combined_content = ' '.join([chunk_data['content'] for chunk_data in response.data])
                    
                    # Create expanded version for each original chunk
                    for chunk in url_chunks:
                        expanded_chunk = chunk.copy()
                        expanded_chunk['expanded_content'] = combined_content
                        expanded_chunk['expansion_method'] = 'parent_document'
                        expanded_chunk['context_chunks'] = len(response.data)
                        
                        expanded_contexts.append(expanded_chunk)
                    
                    processed_urls.add(url)
                    print(f"✅ Expanded to full document ({len(response.data)} chunks)")
                else:
                    # Fallback to original chunks
                    expanded_contexts.extend(url_chunks)
                    print("❌ No parent document found")
        
        elif expansion_strategy == "hybrid":
            # First try surrounding chunks, then parent document for low-scoring results
            for chunk in chunks:
                original_score = chunk.get('enhanced_score', chunk.get('hybrid_score', 0.0))
                
                if original_score > 0.8:  # High-scoring chunks get surrounding context
                    expanded = expand_chunks_to_context(client, [chunk], "surrounding_chunks", context_window)
                    expanded_contexts.extend(expanded)
                else:  # Low-scoring chunks get full document context
                    expanded = expand_chunks_to_context(client, [chunk], "parent_document")
                    expanded_contexts.extend(expanded)
        
        print(f"✅ Context expansion complete: {len(expanded_contexts)} expanded chunks")
        return expanded_contexts
        
    except Exception as e:
        print(f"❌ Context expansion error: {str(e)}")
        return chunks  # Fallback to original chunks


def intelligent_context_merging(
    expanded_chunks: List[Dict[str, Any]],
    max_context_length: int = 2000
) -> List[Dict[str, Any]]:
    """
    🧠 Intelligently merge and truncate expanded contexts to optimal length.
    """
    print(f"\n🧠 Merging contexts for {len(expanded_chunks)} chunks...")
    
    merged_chunks = []
    
    for chunk in expanded_chunks:
        expanded_content = chunk.get('expanded_content', chunk.get('content', ''))
        original_content = chunk.get('content', '')
        
        # If expanded content is too long, intelligently truncate
        if len(expanded_content) > max_context_length:
            # Strategy: Keep original chunk in center, add context around it
            original_pos = expanded_content.find(original_content[:100])  # Find original chunk position
            
            if original_pos >= 0:
                # Calculate optimal window around original content
                start_pos = max(0, original_pos - max_context_length // 3)
                end_pos = min(len(expanded_content), original_pos + len(original_content) + max_context_length // 3)
                
                truncated_content = expanded_content[start_pos:end_pos]
                
                chunk['merged_content'] = truncated_content
                chunk['truncation_applied'] = True
                chunk['original_length'] = len(expanded_content)
                chunk['merged_length'] = len(truncated_content)
            else:
                # Fallback: Simple truncation from start
                chunk['merged_content'] = expanded_content[:max_context_length]
                chunk['truncation_applied'] = True
                chunk['original_length'] = len(expanded_content)
                chunk['merged_length'] = max_context_length
        else:
            chunk['merged_content'] = expanded_content
            chunk['truncation_applied'] = False
            chunk['merged_length'] = len(expanded_content)
        
        merged_chunks.append(chunk)
    
    print(f"✅ Context merging complete")
    return merged_chunks


def contextual_retrieval_search(
    client,
    query: str,
    openai_client,
    match_count: int = 10,
    expansion_strategy: str = "surrounding_chunks",
    context_window: int = 2,
    max_context_length: int = 2000,
    use_phase2: bool = True
) -> List[Dict[str, Any]]:
    """
    🎯 PHASE 3: Contextual retrieval with small-to-big expansion.
    """
    print(f"\n🎯 PHASE 3: Contextual Retrieval for: {query}")
    
    # Step 1: Get initial results using Phase 2 (or Phase 1 fallback)
    if use_phase2:
        print("🚀 Step 1: Using Phase 2 Query Expansion...")
        initial_results = search_with_query_expansion(
            client=client,
            query=query,
            openai_client=openai_client,
            match_count=match_count * 2,  # Get more candidates for expansion
            use_hyde=True,
            use_variations=True
        )
    else:
        print("🔥 Step 1: Using Phase 1 Hybrid Search...")
        initial_results = search_documents_with_text_hybrid(
            client=client,
            query_text=query,
            match_count=match_count * 2,
            min_similarity_threshold=0.1
        )
    
    if not initial_results:
        print("❌ No initial results found")
        return []
    
    print(f"✅ Step 1 complete: Found {len(initial_results)} initial results")
    
    # Step 2: Expand chunks to larger contexts
    print(f"🔍 Step 2: Expanding contexts using '{expansion_strategy}'...")
    expanded_results = expand_chunks_to_context(
        client=client,
        chunks=initial_results,
        expansion_strategy=expansion_strategy,
        context_window=context_window
    )
    
    print(f"✅ Step 2 complete: Expanded {len(expanded_results)} contexts")
    
    # Step 3: Intelligent context merging
    print("🧠 Step 3: Intelligent context merging...")
    merged_results = intelligent_context_merging(
        expanded_chunks=expanded_results,
        max_context_length=max_context_length
    )
    
    print(f"✅ Step 3 complete: Merged {len(merged_results)} contexts")
    
    # Step 4: Re-score based on expanded context (simple approach)
    print("📊 Step 4: Re-scoring with expanded context...")
    for result in merged_results:
        original_score = result.get('enhanced_score', result.get('hybrid_score', 0.0))
        context_boost = 0.0
        
        # Context quality indicators
        if result.get('expansion_method') == 'surrounding_chunks':
            context_boost += 0.1 * result.get('context_chunks', 1)
        elif result.get('expansion_method') == 'parent_document':
            context_boost += 0.05 * min(result.get('context_chunks', 1), 10)
        
        # Length bonus (more context = potentially better)
        length_bonus = min(0.2, result.get('merged_length', 0) / max_context_length * 0.2)
        
        # Calculate contextual score
        contextual_score = original_score + context_boost + length_bonus
        result['contextual_score'] = contextual_score
        result['context_boost'] = context_boost
        result['length_bonus'] = length_bonus
    
    # Sort by contextual score and limit results
    final_results = sorted(merged_results, key=lambda x: x.get('contextual_score', 0), reverse=True)[:match_count]
    
    print(f"✅ Step 4 complete: Final {len(final_results)} results with contextual scoring")
    
    return final_results


def test_phase3_contextual_retrieval():
    """🧪 Test Phase 3 contextual retrieval vs Phase 2 query expansion."""
    print("\n" + "="*80)
    print("🎯 PHASE 3: CONTEXTUAL RETRIEVAL TESTING")
    print("="*80)
    
    # Initialize clients
    client = get_supabase_client()
    openai_client = get_openai_client_sync()
    
    if not client:
        print("❌ Failed to initialize Supabase client")
        return False
        
    if not openai_client:
        print("❌ Failed to initialize OpenAI client")
        return False
    
    # Test queries
    test_queries = [
        "Какви са условията за кандидатстване по програма Еразъм+ за 2024 година?",
        "Финансиране за малки и средни предприятия в България", 
        "Програми за дигитализация и иновации в образованието",
        "Подкрепа за зелени технологии и устойчиво развитие",
        "Възможности за финансиране на научни изследвания"
    ]
    
    total_phase3_score = 0.0
    total_phase2_score = 0.0
    successful_queries = 0
    
    print(f"\n🧪 Testing {len(test_queries)} queries...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"📝 Query {i}: {query}")
        print('='*60)
        
        # Test Phase 3 (Contextual Retrieval)
        print("\n🎯 PHASE 3 (Contextual Retrieval):")
        start_time = time.time()
        try:
            phase3_results = contextual_retrieval_search(
                client=client,
                query=query,
                openai_client=openai_client,
                match_count=5,
                expansion_strategy="surrounding_chunks",
                context_window=2,
                max_context_length=2000,
                use_phase2=True
            )
            phase3_time = time.time() - start_time
            
            if phase3_results:
                # Calculate average contextual score
                phase3_scores = [r.get('contextual_score', 0.0) for r in phase3_results]
                avg_phase3_score = sum(phase3_scores) / len(phase3_scores)
                total_phase3_score += avg_phase3_score
                
                print(f"✅ Found {len(phase3_results)} results in {phase3_time:.3f}s")
                print(f"📊 Average contextual score: {avg_phase3_score:.3f}")
                print(f"🔍 Expansion methods: {[r.get('expansion_method', 'none') for r in phase3_results[:3]]}")
                print(f"📏 Context lengths: {[r.get('merged_length', 0) for r in phase3_results[:3]]}")
                
                # Show top result
                top_result = phase3_results[0]
                print(f"🏆 Top result contextual score: {top_result.get('contextual_score', 0.0):.3f}")
                print(f"🔥 Original score: {top_result.get('enhanced_score', top_result.get('hybrid_score', 0.0)):.3f}")
                print(f"📈 Context boost: {top_result.get('context_boost', 0.0):.3f}")
                print(f"📄 Content preview: {top_result.get('content', '')[:100]}...")
            else:
                print("❌ No Phase 3 results found")
                avg_phase3_score = 0.0
                
        except Exception as e:
            print(f"❌ Phase 3 error: {str(e)}")
            phase3_time = 0
            avg_phase3_score = 0.0
        
        # Test Phase 2 (Query Expansion only)
        print("\n🚀 PHASE 2 (Query Expansion only):")
        start_time = time.time()
        try:
            phase2_results = search_with_query_expansion(
                client=client,
                query=query,
                openai_client=openai_client,
                match_count=5,
                use_hyde=True,
                use_variations=True
            )
            phase2_time = time.time() - start_time
            
            if phase2_results:
                # Calculate average enhanced score
                phase2_scores = [r.get('enhanced_score', 0.0) for r in phase2_results]
                avg_phase2_score = sum(phase2_scores) / len(phase2_scores)
                total_phase2_score += avg_phase2_score
                
                print(f"✅ Found {len(phase2_results)} results in {phase2_time:.3f}s")
                print(f"📊 Average enhanced score: {avg_phase2_score:.3f}")
                
                # Show top result
                top_result = phase2_results[0]
                print(f"🏆 Top result score: {top_result.get('enhanced_score', 0.0):.3f}")
                print(f"📄 Content preview: {top_result.get('content', '')[:100]}...")
            else:
                print("❌ No Phase 2 results found")
                avg_phase2_score = 0.0
                
        except Exception as e:
            print(f"❌ Phase 2 error: {str(e)}")
            phase2_time = 0
            avg_phase2_score = 0.0
        
        # Compare performance
        if phase3_results and phase2_results:
            successful_queries += 1
            improvement = ((avg_phase3_score - avg_phase2_score) / avg_phase2_score * 100) if avg_phase2_score > 0 else 0
            print(f"\n📈 PERFORMANCE COMPARISON:")
            print(f"   Phase 3: {avg_phase3_score:.3f} | Phase 2: {avg_phase2_score:.3f}")
            print(f"   Improvement: {improvement:+.1f}%")
            print(f"   Speed: Phase 3 {phase3_time:.3f}s | Phase 2 {phase2_time:.3f}s")
    
    # Final summary
    print(f"\n{'='*80}")
    print("🏁 FINAL PHASE 3 RESULTS")
    print('='*80)
    
    if successful_queries > 0:
        avg_phase3_final = total_phase3_score / successful_queries
        avg_phase2_final = total_phase2_score / successful_queries
        overall_improvement = ((avg_phase3_final - avg_phase2_final) / avg_phase2_final * 100) if avg_phase2_final > 0 else 0
        
        print(f"📊 Successful queries: {successful_queries}/{len(test_queries)}")
        print(f"🎯 Average PHASE 3 score: {avg_phase3_final:.3f}")
        print(f"🚀 Average PHASE 2 score: {avg_phase2_final:.3f}")
        print(f"📈 Overall improvement: {overall_improvement:+.1f}%")
        
        # Check if we achieved Phase 3 target
        target_improvement = 25.0  # 25% improvement target
        if overall_improvement >= target_improvement:
            print(f"🎯 ✅ PHASE 3 TARGET ACHIEVED! ({overall_improvement:.1f}% >= {target_improvement}%)")
            print("🚀 Ready to proceed to Phase 4: Advanced Reranking")
        else:
            print(f"⚠️ PHASE 3 TARGET NOT MET ({overall_improvement:.1f}% < {target_improvement}%)")
            print("🔧 Need to optimize contextual retrieval parameters")
    else:
        print("❌ No successful queries - need to debug contextual retrieval")
    
    return successful_queries > 0


if __name__ == "__main__":
    print("🎯 STARTING PHASE 3: CONTEXTUAL RETRIEVAL")
    
    # Run Phase 3 testing
    success = test_phase3_contextual_retrieval()
    
    if success:
        print(f"\n🎯 PHASE 3 COMPLETE!")
        print(f"✅ Contextual retrieval implemented and tested")
        print(f"🚀 Ready for Phase 4: Advanced Reranking")
    else:
        print(f"\n❌ PHASE 3 FAILED - Need to debug contextual retrieval")
