# src/gazetteer_utils.py
import csv
import logging
from typing import List, Dict, Any, Optional # Добавен Optional
from pathlib import Path

# ProgramNameNormalizer ще се подава като аргумент, така че не го импортираме тук директно.

logger = logging.getLogger(__name__)

DEFAULT_GAZETTEER_FILE_NAME = "gazetteer_programs.csv"
GAZETTEER_DEFAULT_PATH = Path(__file__).parent.parent / DEFAULT_GAZETTEER_FILE_NAME

def load_program_gazetteer(
    normalizer_instance: Optional[Any], # Очакваме инстанция на ProgramNameNormalizer
    file_path: Path = GAZETTEER_DEFAULT_PATH
) -> List[Dict[str, Any]]:
    programs_data = []
    logger.info(f"Attempting to load program gazetteer from: {file_path}")

    if not normalizer_instance:
        logger.warning("ProgramNameNormalizer instance not provided to load_program_gazetteer. Gazetteer forms will not be fully normalized/lemmatized.")

    try:
        with open(file_path, mode='r', encoding='utf-8-sig') as csvfile:
            reader = csv.reader(csvfile, delimiter=';')
            header = next(reader)
            logger.debug(f"Gazetteer header: {header}")

            try:
                canonical_idx = header.index("CanonicalName")
                abbr_idx = header.index("Abbreviation")
                other_variants_indices = [
                    i for i, col_name in enumerate(header) 
                    if col_name.strip().startswith("OtherVariant")
                ]
                if not other_variants_indices: # Fallback за твоя случай
                    try:
                        first_ov_idx = header.index("OtherVariants")
                        other_variants_indices = list(range(first_ov_idx, len(header)))
                    except ValueError:
                        other_variants_indices = []
            except ValueError as ve:
                logger.error(f"Gazetteer CSV file at {file_path} must contain 'CanonicalName' and 'Abbreviation' columns. Error: {ve}")
                return []

            for i, row in enumerate(reader):
                if not any(field.strip() for field in row):
                    logger.debug(f"Skipping empty row {i+2} in gazetteer.")
                    continue
                
                # Проверка дали редът има достатъчно колони за основните полета
                min_required_cols = 0
                if header: # Ако има хедър, взимаме максималния индекс от него
                     min_required_cols = max(canonical_idx, abbr_idx if abbr_idx < len(header) else -1) + 1
                
                if len(row) < min_required_cols :
                    logger.warning(f"Skipping malformed row {i+2} in gazetteer (not enough columns for CanonicalName/Abbreviation): {row}")
                    continue

                canonical_name = row[canonical_idx].strip()
                abbreviation = row[abbr_idx].strip() if abbr_idx < len(row) and abbr_idx < len(header) else "" # По-сигурна проверка
                
                current_program_forms_raw = {canonical_name}
                if abbreviation:
                    current_program_forms_raw.add(abbreviation)

                for v_idx in other_variants_indices:
                    if v_idx < len(row) and row[v_idx] and row[v_idx].strip():
                        current_program_forms_raw.add(row[v_idx].strip())
                
                all_known_forms_lower_list = sorted([form.lower() for form in current_program_forms_raw if form])
                
                normalized_lemmatized_forms: List[List[str]] = []
                if normalizer_instance:
                    for form_text in current_program_forms_raw:
                        if form_text:
                            # Подаваме has_year_entity=False, за да не се опитва да маха години от газетърните форми
                            norm_result = normalizer_instance.normalize(form_text, has_year_entity=False)
                            if norm_result and norm_result.get("lemmatized_tokens_sorted_unique"):
                                normalized_lemmatized_forms.append(norm_result["lemmatized_tokens_sorted_unique"])
                                logger.debug(f"Gazetteer form '{form_text}' -> lemmas: {norm_result['lemmatized_tokens_sorted_unique']}")
                            else:
                                logger.warning(f"Could not get lemmas for gazetteer form: '{form_text}' (Normalizer result: {norm_result})")
                else:
                    logger.debug("Normalizer instance not available, skipping lemmatization of gazetteer forms.")

                program_entry = {
                    "canonical_name": canonical_name,
                    "abbreviations": [abbr for abbr in [abbreviation] if abbr],
                    "all_known_forms_lower": all_known_forms_lower_list,
                    "normalized_lemmatized_forms": normalized_lemmatized_forms
                }
                programs_data.append(program_entry)
                logger.debug(f"Loaded gazetteer entry: {program_entry['canonical_name']}, with {len(normalized_lemmatized_forms)} lemmatized forms.")

        logger.info(f"Successfully loaded {len(programs_data)} programs from gazetteer: {file_path}")
    except FileNotFoundError:
        logger.error(f"Gazetteer file not found: {file_path}")
    except Exception as e:
        logger.error(f"Error loading program gazetteer from {file_path}: {e}", exc_info=True)
    
    return programs_data

if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s')
    
    # За тестване на този файл директно, трябва да импортираме и инициализираме ProgramNameNormalizer тук
    normalizer_for_test = None
    try:
        # Опитваме се да импортираме и инициализираме тук, само за целите на __main__
        from entity_utils import ProgramNameNormalizer

        # Fallback function definition
        def get_program_name_normalizer():
            return ProgramNameNormalizer()

        logger.info("Main test: Attempting to get ProgramNameNormalizer instance...")
        normalizer_for_test = get_program_name_normalizer() # Използваме fallback функцията
        if normalizer_for_test:
            logger.info("Main test: ProgramNameNormalizer instance obtained.")
        else:
            logger.error("Main test: Failed to get ProgramNameNormalizer instance.")
    except ImportError:
        logger.error("Main test: Could not import ProgramNameNormalizer from entity_utils.")
    except Exception as e_init:
        logger.error(f"Main test: Error initializing ProgramNameNormalizer for test: {e_init}")

    logger.info(f"--- Testing gazetteer loading from: {GAZETTEER_DEFAULT_PATH} ---")
    # Подаваме инстанцията на нормализатора
    gazetteer_data = load_program_gazetteer(normalizer_instance=normalizer_for_test)

    if gazetteer_data:
        logger.info("\n--- Loaded Gazetteer Data (with lemmatized forms if available): ---")
        for program_idx, program in enumerate(gazetteer_data):
            logger.info(f"Program {program_idx + 1}:")
            logger.info(f"  Canonical Name: {program['canonical_name']}")
            logger.info(f"  Abbreviations: {program['abbreviations']}")
            logger.info(f"  All Known Forms (lower): {program['all_known_forms_lower']}")
            logger.info(f"  Normalized Lemmatized Forms: {program['normalized_lemmatized_forms']}")
            logger.info("-" * 20)
    else:
        logger.info("Gazetteer data could not be loaded.")