#!/usr/bin/env python3
"""
Database Performance Testing - Тестване на database performance
Сравнение преди и след index optimization
"""

import asyncio
import time
import json
from typing import List, Dict, Any
from dotenv import load_dotenv
from utils import get_supabase_client, create_embedding

# Load environment variables
load_dotenv('../.env')

class DatabasePerformanceTester:
    def __init__(self):
        self.supabase = get_supabase_client()
        self.test_queries = [
            "Програми за иновации в предприятията",
            "Финансиране на малки и средни предприятия",
            "Европейски фондове за развитие на региони",
            "Подкрепа за стартиращи предприятия",
            "Програми за дигитализация",
            "Фондове за научни изследвания",
            "Подкрепа за земеделски производители",
            "Програми за енергийна ефективност",
            "Финансиране на образователни проекти",
            "Подкрепа за туристически проекти"
        ]
    
    async def test_vector_search_performance(self) -> Dict[str, Any]:
        """Тестване на vector search performance"""
        print("🔬 ТЕСТВАНЕ НА VECTOR SEARCH PERFORMANCE...")
        
        results = {
            'total_queries': len(self.test_queries),
            'successful_queries': 0,
            'failed_queries': 0,
            'total_time': 0,
            'average_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'results_per_second': 0,
            'query_details': []
        }
        
        for i, query in enumerate(self.test_queries):
            print(f"   📝 Тест {i+1}/10: {query[:50]}...")
            
            try:
                # Създаване на embedding
                embedding_start = time.time()
                query_embedding = create_embedding(query)
                embedding_time = time.time() - embedding_start
                
                # Vector search
                search_start = time.time()
                response = self.supabase.rpc('match_crawled_pages_v4_debug', {
                    'p_query_embedding': query_embedding,
                    'p_match_count': 10,
                    'p_min_similarity_threshold': 0.1,
                    'p_weight_similarity': 1.0,
                    'p_weight_program_name': 0.0,
                    'p_weight_year': 0.0
                }).execute()
                search_time = time.time() - search_start
                
                total_time = embedding_time + search_time
                results_count = len(response.data) if response.data else 0
                
                # Статистики
                results['successful_queries'] += 1
                results['total_time'] += total_time
                results['min_time'] = min(results['min_time'], total_time)
                results['max_time'] = max(results['max_time'], total_time)
                
                # Детайли за заявката
                query_detail = {
                    'query': query,
                    'embedding_time': embedding_time,
                    'search_time': search_time,
                    'total_time': total_time,
                    'results_count': results_count,
                    'success': True
                }
                results['query_details'].append(query_detail)
                
                print(f"      ✅ {total_time:.3f}s ({results_count} резултата)")
                
            except Exception as e:
                results['failed_queries'] += 1
                query_detail = {
                    'query': query,
                    'error': str(e),
                    'success': False
                }
                results['query_details'].append(query_detail)
                print(f"      ❌ Грешка: {e}")
        
        # Изчисляване на средни стойности
        if results['successful_queries'] > 0:
            results['average_time'] = results['total_time'] / results['successful_queries']
            results['results_per_second'] = results['successful_queries'] / results['total_time']
        
        return results
    
    async def test_metadata_filtering_performance(self) -> Dict[str, Any]:
        """Тестване на metadata filtering performance"""
        print("\n🔬 ТЕСТВАНЕ НА METADATA FILTERING PERFORMANCE...")
        
        metadata_tests = [
            {'filter': {'source': 'eufunds'}, 'name': 'Source filter'},
            {'filter': {'program_type': 'innovation'}, 'name': 'Program type filter'},
            {'filter': {'title': {'$exists': True}}, 'name': 'Title exists filter'},
        ]
        
        results = {
            'total_tests': len(metadata_tests),
            'successful_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
        
        for i, test in enumerate(metadata_tests):
            print(f"   📝 Тест {i+1}/{len(metadata_tests)}: {test['name']}...")
            
            try:
                start_time = time.time()
                
                # Metadata filtering заявка
                response = self.supabase.table('crawled_pages').select(
                    'id, url, metadata'
                ).contains('metadata', test['filter']).limit(20).execute()
                
                execution_time = time.time() - start_time
                results_count = len(response.data) if response.data else 0
                
                results['successful_tests'] += 1
                
                test_detail = {
                    'name': test['name'],
                    'filter': test['filter'],
                    'execution_time': execution_time,
                    'results_count': results_count,
                    'success': True
                }
                results['test_details'].append(test_detail)
                
                print(f"      ✅ {execution_time:.3f}s ({results_count} резултата)")
                
            except Exception as e:
                results['failed_tests'] += 1
                test_detail = {
                    'name': test['name'],
                    'filter': test['filter'],
                    'error': str(e),
                    'success': False
                }
                results['test_details'].append(test_detail)
                print(f"      ❌ Грешка: {e}")
        
        return results
    
    async def test_hybrid_search_performance(self) -> Dict[str, Any]:
        """Тестване на hybrid search performance"""
        print("\n🔬 ТЕСТВАНЕ НА HYBRID SEARCH PERFORMANCE...")
        
        results = {
            'total_queries': 3,
            'successful_queries': 0,
            'failed_queries': 0,
            'query_details': []
        }
        
        hybrid_queries = [
            "иновации предприятия",
            "малки средни предприятия финансиране",
            "европейски фондове региони"
        ]
        
        for i, query in enumerate(hybrid_queries):
            print(f"   📝 Тест {i+1}/3: {query}...")
            
            try:
                start_time = time.time()
                
                # Hybrid search чрез RPC функция
                query_embedding = create_embedding(query)
                response = self.supabase.rpc('match_crawled_pages_v4_debug', {
                    'p_query_embedding': query_embedding,
                    'p_match_count': 10,
                    'p_min_similarity_threshold': 0.1,
                    'p_weight_similarity': 0.7,
                    'p_weight_program_name': 0.2,
                    'p_weight_year': 0.1
                }).execute()
                
                execution_time = time.time() - start_time
                results_count = len(response.data) if response.data else 0
                
                results['successful_queries'] += 1
                
                query_detail = {
                    'query': query,
                    'execution_time': execution_time,
                    'results_count': results_count,
                    'success': True
                }
                results['query_details'].append(query_detail)
                
                print(f"      ✅ {execution_time:.3f}s ({results_count} резултата)")
                
            except Exception as e:
                results['failed_queries'] += 1
                query_detail = {
                    'query': query,
                    'error': str(e),
                    'success': False
                }
                results['query_details'].append(query_detail)
                print(f"      ❌ Грешка: {e}")
        
        return results
    
    async def analyze_database_statistics(self) -> Dict[str, Any]:
        """Анализ на database статистики"""
        print("\n📊 АНАЛИЗ НА DATABASE СТАТИСТИКИ...")
        
        stats = {}
        
        try:
            # Общ брой записи
            count_response = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).execute()
            stats['total_records'] = count_response.count if hasattr(count_response, 'count') else 0
            print(f"   📊 Общо записи: {stats['total_records']}")
            
            # Проверка на embedding качество
            sample_response = self.supabase.table('crawled_pages').select(
                'id, embedding, metadata'
            ).limit(5).execute()
            
            if sample_response.data:
                embedding_dimensions = []
                for record in sample_response.data:
                    if record.get('embedding'):
                        embedding_dimensions.append(len(record['embedding']))
                
                if embedding_dimensions:
                    stats['embedding_dimension'] = embedding_dimensions[0]
                    stats['consistent_dimensions'] = all(d == embedding_dimensions[0] for d in embedding_dimensions)
                    print(f"   🧠 Embedding размерност: {stats['embedding_dimension']}")
                    print(f"   ✅ Консистентни размерности: {stats['consistent_dimensions']}")
            
            # Проверка на metadata структура
            metadata_sample = sample_response.data[0].get('metadata', {}) if sample_response.data else {}
            stats['metadata_keys'] = list(metadata_sample.keys())
            print(f"   🔑 Metadata ключове: {stats['metadata_keys']}")
            
        except Exception as e:
            print(f"   ❌ Грешка при анализ: {e}")
            stats['error'] = str(e)
        
        return stats
    
    def generate_performance_report(self, vector_results: Dict, metadata_results: Dict, 
                                  hybrid_results: Dict, db_stats: Dict) -> str:
        """Генериране на performance отчет"""
        
        report = f"""
🚀 DATABASE PERFORMANCE ОТЧЕТ
{'='*60}

📊 ОБЩИ СТАТИСТИКИ:
• Общо записи в базата: {db_stats.get('total_records', 'N/A')}
• Embedding размерност: {db_stats.get('embedding_dimension', 'N/A')}
• Консистентни размерности: {db_stats.get('consistent_dimensions', 'N/A')}

⚡ VECTOR SEARCH PERFORMANCE:
• Общо заявки: {vector_results['total_queries']}
• Успешни заявки: {vector_results['successful_queries']}
• Неуспешни заявки: {vector_results['failed_queries']}
• Средно време: {vector_results['average_time']:.3f}s
• Минимално време: {vector_results['min_time']:.3f}s
• Максимално време: {vector_results['max_time']:.3f}s
• Заявки в секунда: {vector_results['results_per_second']:.1f}

🔍 METADATA FILTERING PERFORMANCE:
• Общо тестове: {metadata_results['total_tests']}
• Успешни тестове: {metadata_results['successful_tests']}
• Неуспешни тестове: {metadata_results['failed_tests']}

🔀 HYBRID SEARCH PERFORMANCE:
• Общо заявки: {hybrid_results['total_queries']}
• Успешни заявки: {hybrid_results['successful_queries']}
• Неуспешни заявки: {hybrid_results['failed_queries']}

📈 ПРЕПОРЪКИ ЗА ОПТИМИЗАЦИЯ:
"""
        
        # Препоръки въз основа на резултатите
        if vector_results['average_time'] > 0.5:
            report += "• Vector search е бавен - препоръчва се HNSW индекс оптимизация\n"
        
        if vector_results['failed_queries'] > 0:
            report += "• Има неуспешни vector заявки - проверете embedding модела\n"
        
        if metadata_results['failed_tests'] > 0:
            report += "• Има проблеми с metadata filtering - проверете индексите\n"
        
        if vector_results['average_time'] < 0.2:
            report += "• Vector search performance е добър\n"
        
        report += f"\n{'='*60}\n"
        
        return report

async def main():
    """Главна функция за database performance testing"""
    print("🚀 ЗАПОЧВАНЕ НА DATABASE PERFORMANCE TESTING")
    print("=" * 60)
    
    tester = DatabasePerformanceTester()
    
    # Тестване на различни аспекти
    vector_results = await tester.test_vector_search_performance()
    metadata_results = await tester.test_metadata_filtering_performance()
    hybrid_results = await tester.test_hybrid_search_performance()
    db_stats = await tester.analyze_database_statistics()
    
    # Генериране на отчет
    report = tester.generate_performance_report(
        vector_results, metadata_results, hybrid_results, db_stats
    )
    
    print(report)
    
    # Запазване на резултатите
    results_data = {
        'timestamp': time.time(),
        'vector_search': vector_results,
        'metadata_filtering': metadata_results,
        'hybrid_search': hybrid_results,
        'database_stats': db_stats,
        'report': report
    }
    
    with open('database_performance_results.json', 'w', encoding='utf-8') as f:
        json.dump(results_data, f, ensure_ascii=False, indent=2, default=str)
    
    print("💾 Резултатите са запазени в 'database_performance_results.json'")
    
    print("\n📋 СЛЕДВАЩИ СТЪПКИ:")
    print("1. Изпълнете SQL заявките от 'database_index_optimization.sql' в Supabase")
    print("2. Стартирайте отново този тест за сравнение")
    print("3. Мониторирайте performance подобренията")

if __name__ == "__main__":
    asyncio.run(main())
