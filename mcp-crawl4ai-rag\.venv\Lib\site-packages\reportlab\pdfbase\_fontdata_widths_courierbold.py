widths = {'A': 600,
 'AE': 600,
 'Aacute': 600,
 'Acircumflex': 600,
 'Adieresis': 600,
 'Agrave': 600,
 'Aring': 600,
 'Atilde': 600,
 'B': 600,
 'C': 600,
 '<PERSON><PERSON><PERSON>': 600,
 'D': 600,
 'E': 600,
 'Eacute': 600,
 'Ecircumflex': 600,
 'Edieresis': 600,
 'Egrave': 600,
 'Eth': 600,
 'Euro': 600,
 'F': 600,
 'G': 600,
 'H': 600,
 'I': 600,
 'Iacute': 600,
 'Icircumflex': 600,
 'Idieresis': 600,
 'Igrave': 600,
 'J': 600,
 'K': 600,
 'L': 600,
 'Lslash': 600,
 'M': 600,
 'N': 600,
 'Ntilde': 600,
 'O': 600,
 'OE': 600,
 'Oacute': 600,
 'Ocircumflex': 600,
 'Odieresis': 600,
 'Ograve': 600,
 'Oslash': 600,
 'Otilde': 600,
 'P': 600,
 'Q': 600,
 'R': 600,
 'S': 600,
 '<PERSON>aron': 600,
 'T': 600,
 '<PERSON>': 600,
 'U': 600,
 'Uacute': 600,
 'Ucircumflex': 600,
 'Udieresis': 600,
 'Ugrave': 600,
 'V': 600,
 'W': 600,
 'X': 600,
 'Y': 600,
 'Yacute': 600,
 'Ydieresis': 600,
 'Z': 600,
 'Zcaron': 600,
 'a': 600,
 'aacute': 600,
 'acircumflex': 600,
 'acute': 600,
 'adieresis': 600,
 'ae': 600,
 'agrave': 600,
 'ampersand': 600,
 'aring': 600,
 'asciicircum': 600,
 'asciitilde': 600,
 'asterisk': 600,
 'at': 600,
 'atilde': 600,
 'b': 600,
 'backslash': 600,
 'bar': 600,
 'braceleft': 600,
 'braceright': 600,
 'bracketleft': 600,
 'bracketright': 600,
 'breve': 600,
 'brokenbar': 600,
 'bullet': 600,
 'c': 600,
 'caron': 600,
 'ccedilla': 600,
 'cedilla': 600,
 'cent': 600,
 'circumflex': 600,
 'colon': 600,
 'comma': 600,
 'copyright': 600,
 'currency': 600,
 'd': 600,
 'dagger': 600,
 'daggerdbl': 600,
 'degree': 600,
 'dieresis': 600,
 'divide': 600,
 'dollar': 600,
 'dotaccent': 600,
 'dotlessi': 600,
 'e': 600,
 'eacute': 600,
 'ecircumflex': 600,
 'edieresis': 600,
 'egrave': 600,
 'eight': 600,
 'ellipsis': 600,
 'emdash': 600,
 'endash': 600,
 'equal': 600,
 'eth': 600,
 'exclam': 600,
 'exclamdown': 600,
 'f': 600,
 'fi': 600,
 'five': 600,
 'fl': 600,
 'florin': 600,
 'four': 600,
 'fraction': 600,
 'g': 600,
 'germandbls': 600,
 'grave': 600,
 'greater': 600,
 'guillemotleft': 600,
 'guillemotright': 600,
 'guilsinglleft': 600,
 'guilsinglright': 600,
 'h': 600,
 'hungarumlaut': 600,
 'hyphen': 600,
 'i': 600,
 'iacute': 600,
 'icircumflex': 600,
 'idieresis': 600,
 'igrave': 600,
 'j': 600,
 'k': 600,
 'l': 600,
 'less': 600,
 'logicalnot': 600,
 'lslash': 600,
 'm': 600,
 'macron': 600,
 'minus': 600,
 'mu': 600,
 'multiply': 600,
 'n': 600,
 'nine': 600,
 'ntilde': 600,
 'numbersign': 600,
 'o': 600,
 'oacute': 600,
 'ocircumflex': 600,
 'odieresis': 600,
 'oe': 600,
 'ogonek': 600,
 'ograve': 600,
 'one': 600,
 'onehalf': 600,
 'onequarter': 600,
 'onesuperior': 600,
 'ordfeminine': 600,
 'ordmasculine': 600,
 'oslash': 600,
 'otilde': 600,
 'p': 600,
 'paragraph': 600,
 'parenleft': 600,
 'parenright': 600,
 'percent': 600,
 'period': 600,
 'periodcentered': 600,
 'perthousand': 600,
 'plus': 600,
 'plusminus': 600,
 'q': 600,
 'question': 600,
 'questiondown': 600,
 'quotedbl': 600,
 'quotedblbase': 600,
 'quotedblleft': 600,
 'quotedblright': 600,
 'quoteleft': 600,
 'quoteright': 600,
 'quotesinglbase': 600,
 'quotesingle': 600,
 'r': 600,
 'registered': 600,
 'ring': 600,
 's': 600,
 'scaron': 600,
 'section': 600,
 'semicolon': 600,
 'seven': 600,
 'six': 600,
 'slash': 600,
 'space': 600,
 'sterling': 600,
 't': 600,
 'thorn': 600,
 'three': 600,
 'threequarters': 600,
 'threesuperior': 600,
 'tilde': 600,
 'trademark': 600,
 'two': 600,
 'twosuperior': 600,
 'u': 600,
 'uacute': 600,
 'ucircumflex': 600,
 'udieresis': 600,
 'ugrave': 600,
 'underscore': 600,
 'v': 600,
 'w': 600,
 'x': 600,
 'y': 600,
 'yacute': 600,
 'ydieresis': 600,
 'yen': 600,
 'z': 600,
 'zcaron': 600,
 'zero': 600}
