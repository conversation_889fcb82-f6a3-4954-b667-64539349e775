#!/usr/bin/env python3
"""
ТЕСТ НА СЕМАНТИЧНО РАЗДЕЛЯНЕ С РЕАЛНИ ПАРАМЕТРИ
Тества с по-разумни параметри за min_tokens
"""

import sys
import os

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_chunking_with_realistic_params():
    """Тества семантичното разделяне с реални параметри"""
    print("🧪 ТЕСТ С РЕАЛНИ ПАРАМЕТРИ")
    print("=" * 60)
    
    # По-дълъг тестов текст
    test_text = """
    Програмата за подкрепа на малките и средните предприятия (МСП) предоставя финансиране за иновативни проекти. 
    Критериите за кандидатстване включват минимум 2 години дейност на предприятието.
    Предприятието трябва да има регистрация в България и да не е в процедура по несъстоятелност.
    
    Максималният размер на безвъзмездната помощ е 200,000 лева. Собственият принос трябва да бъде поне 15% от общия бюджет.
    Проектът трябва да се изпълни в срок от 18 месеца от подписването на договора.
    Срокът за кандидатстване е до 31 декември 2024 година.
    
    Документите за кандидатстване включват бизнес план, финансови отчети и декларация за де минимис помощи.
    Бизнес планът трябва да съдържа подробно описание на проекта и очакваните резултати.
    Финансовите отчети трябва да са за последните две години.
    
    Оценката на проектите се извършва от експертна комисия по критерии за иновативност, устойчивост и въздействие.
    Резултатите от оценката се обявяват в срок от 60 дни след крайния срок за кандидатстване.
    Успешните кандидати получават уведомление за одобрение на проекта.
    """
    
    try:
        from utils import create_semantic_chunks
        print("✅ Функцията create_semantic_chunks е импортирана")
        
        # Тестваме с различни реални параметри
        test_cases = [
            (100, 20, "Малки chunks (20-100 tokens)"),
            (200, 50, "Средни chunks (50-200 tokens)"),
            (300, 80, "По-големи chunks (80-300 tokens)"),
            (150, 30, "Оптимални chunks (30-150 tokens)")
        ]
        
        print(f"\n📝 Тестов текст ({len(test_text)} символа)")
        total_tokens = len(test_text.split())
        print(f"   Общо tokens: {total_tokens}")
        print()
        
        best_result = None
        best_score = 0
        
        for max_tokens, min_tokens, description in test_cases:
            print(f"🔧 {description}")
            print(f"   Параметри: max_tokens={max_tokens}, min_tokens={min_tokens}")
            
            chunks = create_semantic_chunks(test_text, max_tokens=max_tokens, min_tokens=min_tokens)
            
            print(f"   Резултат: {len(chunks)} chunks")
            
            if chunks:
                total_chunk_tokens = 0
                for i, chunk in enumerate(chunks):
                    tokens = chunk.get('tokens', 0)
                    content = chunk.get('content', '')
                    total_chunk_tokens += tokens
                    print(f"     Chunk {i+1}: {tokens} tokens")
                    print(f"       Съдържание: {content[:80]}...")
                
                # Оценяваме качеството
                coverage = total_chunk_tokens / total_tokens if total_tokens > 0 else 0
                avg_chunk_size = total_chunk_tokens / len(chunks)
                
                # Скор базиран на покритие и брой chunks
                score = coverage * len(chunks) * (1 if 30 <= avg_chunk_size <= 200 else 0.5)
                
                print(f"   📊 Статистики:")
                print(f"     Покритие: {coverage:.1%}")
                print(f"     Средна дължина: {avg_chunk_size:.0f} tokens")
                print(f"     Скор: {score:.2f}")
                
                if score > best_score:
                    best_score = score
                    best_result = (max_tokens, min_tokens, description, len(chunks))
            else:
                print(f"   ❌ Няма създадени chunks")
            
            print()
        
        # Резултат
        if best_result:
            max_t, min_t, desc, chunk_count = best_result
            print(f"🏆 НАЙ-ДОБЪР РЕЗУЛТАТ:")
            print(f"   {desc}")
            print(f"   Параметри: max_tokens={max_t}, min_tokens={min_t}")
            print(f"   Chunks: {chunk_count}")
            print(f"   Скор: {best_score:.2f}")
            return True
        else:
            print("❌ Няма успешни резултати")
            return False
            
    except Exception as e:
        print(f"💥 ГРЕШКА: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases_realistic():
    """Тества гранични случаи с реални параметри"""
    print("\n🧪 ГРАНИЧНИ СЛУЧАИ С РЕАЛНИ ПАРАМЕТРИ")
    print("-" * 50)
    
    try:
        from utils import create_semantic_chunks
        
        test_cases = [
            ("", "Празен текст"),
            ("Кратко изречение.", "Много кратък текст"),
            ("Първо изречение. Второ изречение! Трето изречение? Четвърто изречение: Пето изречение; Шесто изречение.", "Средна дължина"),
            ("А" * 500, "Дълъг текст без пунктуация"),
        ]
        
        # Използваме реални параметри
        max_tokens = 150
        min_tokens = 30
        
        print(f"Параметри: max_tokens={max_tokens}, min_tokens={min_tokens}")
        print()
        
        for text, description in test_cases:
            print(f"   {description}:")
            print(f"     Дължина: {len(text)} символа, {len(text.split())} tokens")
            
            chunks = create_semantic_chunks(text, max_tokens=max_tokens, min_tokens=min_tokens)
            print(f"     Chunks: {len(chunks)}")
            
            if chunks:
                for i, chunk in enumerate(chunks):
                    tokens = chunk.get('tokens', 0)
                    content = chunk.get('content', '')
                    print(f"       Chunk {i+1}: {tokens} tokens - {content[:50]}...")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка: {e}")
        return False

def main():
    """Главна функция"""
    print("🚀 ТЕСТ НА СЕМАНТИЧНО РАЗДЕЛЯНЕ С РЕАЛНИ ПАРАМЕТРИ")
    print("=" * 70)
    print("💡 Проблемът: min_tokens=256 е твърде голям за обикновени текстове!")
    print("=" * 70)
    
    # Основен тест
    basic_success = test_chunking_with_realistic_params()
    
    # Гранични случаи
    edge_success = test_edge_cases_realistic()
    
    # Заключение
    print(f"\n{'='*70}")
    print("🎯 ЗАКЛЮЧЕНИЕ")
    print(f"{'='*70}")
    
    if basic_success:
        print("✅ Семантичното разделяне работи с реални параметри!")
        print("🔧 Препоръка: Използвайте min_tokens=30-50 вместо 256")
        print("📝 Причина: 256 tokens = ~1000+ символа, твърде голямо за обикновени chunks")
        return True
    else:
        print("❌ Семантичното разделяне все още не работи!")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
