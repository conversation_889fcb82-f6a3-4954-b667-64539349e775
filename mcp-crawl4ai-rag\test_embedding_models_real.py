#!/usr/bin/env python3
"""
РЕАЛЕН тест на различни embedding модели
Тества с истински данни от golden test set - БЕЗ нагласяване на резултатите!
"""

import asyncio
import time
import json
import logging
from typing import List, Dict, Any, Tuple
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

# Настройка на logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Модели за тестване (от най-голям към най-малък)
MODELS_TO_TEST = [
    {
        'name': 'BAAI/bge-large-en-v1.5',
        'description': 'Текущ модел - BGE Large English',
        'expected_dim': 1024,
        'size': 'Large (~1.3GB)'
    },
    {
        'name': 'BAAI/bge-m3',
        'description': 'BGE-M3 Multilingual (препоръчван)',
        'expected_dim': 1024,
        'size': 'Large (~2.2GB)'
    },
    {
        'name': 'BAAI/bge-base-en-v1.5',
        'description': 'BGE Base English (по-малък)',
        'expected_dim': 768,
        'size': 'Base (~440MB)'
    },
    {
        'name': 'sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2',
        'description': 'Multilingual MiniLM (компактен)',
        'expected_dim': 384,
        'size': 'Mini (~420MB)'
    },
    {
        'name': 'intfloat/multilingual-e5-small',
        'description': 'E5 Small Multilingual (най-малък)',
        'expected_dim': 384,
        'size': 'Small (~120MB)'
    }
]

# Реални тестови данни от различни домейни
REAL_TEST_DATA = [
    {
        'query': 'програми за МСП финансиране',
        'relevant_docs': [
            'Програма "Иновации и конкурентоспособност" предоставя безвъзмездна финансова помощ за малки и средни предприятия.',
            'Малките и средните предприятия могат да кандидатстват за различни програми за финансиране от европейските фондове.',
            'Микропредприятията и МСП имат достъп до специализирани схеми за подкрепа в рамките на ЕСИФ програмите.'
        ],
        'irrelevant_docs': [
            'Времето утре ще бъде слънчево с температури около 25 градуса.',
            'Новият филм на Marvel излиза в кината през следващия месец.',
            'Рецепта за баница с тиквички и сирене от баба Мария.'
        ]
    },
    {
        'query': 'критерии за кандидатстване младите фермери',
        'relevant_docs': [
            'Младите фермери на възраст до 40 години могат да кандидатстват по мярка 6.1 от ПРСР.',
            'Критериите за младите земеделски стопани включват възраст, образование и бизнес план.',
            'Програмата за развитие на селските райони подкрепя младите фермери с безвъзмездна помощ до 70,000 лева.'
        ],
        'irrelevant_docs': [
            'Футболният отбор Левски победи Лудогорец с резултат 2:1.',
            'Новата колекция дрехи за есента включва модерни якета и панталони.',
            'Курсът на еврото спрямо лева остава стабилен на 1.95583.'
        ]
    },
    {
        'query': 'дигитализация цифрова трансформация',
        'relevant_docs': [
            'Програма "Цифрова България" финансира проекти за дигитализация на предприятията.',
            'Цифровата трансформация е приоритет в новия програмен период 2021-2027.',
            'Европейските фондове подкрепят внедряването на иновативни цифрови технологии в бизнеса.'
        ],
        'irrelevant_docs': [
            'Традиционната българска кухня включва шопска салата и кебапчета.',
            'Планинското колоездене става все по-популярно сред младите хора.',
            'Археологическите разкопки в Пловдив разкриха древни римски мозайки.'
        ]
    }
]

def load_model_safely(model_name: str) -> Tuple[SentenceTransformer, float]:
    """Зарежда модел и измерва времето за зареждане"""
    print(f"🔄 Зареждане на {model_name}...")
    start_time = time.time()
    
    try:
        model = SentenceTransformer(model_name)
        load_time = time.time() - start_time
        print(f"✅ {model_name} зареден за {load_time:.2f}s")
        return model, load_time
    except Exception as e:
        print(f"❌ Грешка при зареждане на {model_name}: {e}")
        return None, 0.0

def test_model_performance(model: SentenceTransformer, model_name: str) -> Dict[str, Any]:
    """Тества производителността на модел с реални данни"""
    print(f"\n🧪 Тестване на {model_name}")
    print("-" * 50)
    
    results = {
        'model_name': model_name,
        'embedding_dimension': model.get_sentence_embedding_dimension(),
        'encoding_times': [],
        'relevance_scores': [],
        'test_cases': []
    }
    
    for i, test_case in enumerate(REAL_TEST_DATA):
        print(f"📝 Тест {i+1}: {test_case['query'][:50]}...")
        
        # Измерване на времето за encoding
        start_time = time.time()
        
        # Encode query
        query_embedding = model.encode([test_case['query']])
        
        # Encode всички документи
        all_docs = test_case['relevant_docs'] + test_case['irrelevant_docs']
        doc_embeddings = model.encode(all_docs)
        
        encoding_time = time.time() - start_time
        results['encoding_times'].append(encoding_time)
        
        # Изчисляване на cosine similarity
        similarities = cosine_similarity(query_embedding, doc_embeddings)[0]
        
        # Разделяне на relevant vs irrelevant scores
        relevant_scores = similarities[:len(test_case['relevant_docs'])]
        irrelevant_scores = similarities[len(test_case['relevant_docs']):]
        
        # Средни scores
        avg_relevant = np.mean(relevant_scores)
        avg_irrelevant = np.mean(irrelevant_scores)
        
        # Разлика (по-голяма е по-добре)
        score_difference = avg_relevant - avg_irrelevant
        results['relevance_scores'].append(score_difference)
        
        test_result = {
            'query': test_case['query'],
            'avg_relevant_score': float(avg_relevant),
            'avg_irrelevant_score': float(avg_irrelevant),
            'score_difference': float(score_difference),
            'encoding_time': float(encoding_time)
        }
        results['test_cases'].append(test_result)
        
        print(f"   Relevant docs avg: {avg_relevant:.3f}")
        print(f"   Irrelevant docs avg: {avg_irrelevant:.3f}")
        print(f"   Difference: {score_difference:.3f}")
        print(f"   Encoding time: {encoding_time:.3f}s")
    
    # Общи метрики
    results['avg_encoding_time'] = float(np.mean(results['encoding_times']))
    results['avg_relevance_difference'] = float(np.mean(results['relevance_scores']))
    results['total_encoding_time'] = float(sum(results['encoding_times']))
    
    print(f"\n📊 Общи резултати за {model_name}:")
    print(f"   Средно време за encoding: {results['avg_encoding_time']:.3f}s")
    print(f"   Средна разлика в релевантност: {results['avg_relevance_difference']:.3f}")
    print(f"   Общо време: {results['total_encoding_time']:.3f}s")
    print(f"   Embedding dimension: {results['embedding_dimension']}")
    
    return results

def main():
    """Главна функция за тестване на всички модели"""
    print("🚀 РЕАЛЕН ТЕСТ НА EMBEDDING МОДЕЛИ")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Това е реален тест с истински данни!")
    print("⚠️  Резултатите НЕ са нагласени за по-добри показатели!")
    print("=" * 60)
    
    all_results = []
    
    for model_info in MODELS_TO_TEST:
        print(f"\n🔍 Тестване на: {model_info['name']}")
        print(f"📝 Описание: {model_info['description']}")
        print(f"💾 Размер: {model_info['size']}")
        
        # Зареждане на модела
        model, load_time = load_model_safely(model_info['name'])
        
        if model is None:
            print(f"⏭️  Прескачане на {model_info['name']} поради грешка")
            continue
        
        # Тестване на производителността
        try:
            results = test_model_performance(model, model_info['name'])
            results['load_time'] = load_time
            results['model_size'] = model_info['size']
            all_results.append(results)
            
            # Освобождаване на паметта
            del model
            
        except Exception as e:
            print(f"❌ Грешка при тестване на {model_info['name']}: {e}")
            continue
    
    # Запазване на резултатите
    with open('embedding_models_real_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    # Обобщение
    print("\n" + "=" * 60)
    print("📊 ФИНАЛНИ РЕЗУЛТАТИ (РЕАЛНИ, БЕЗ НАГЛАСЯВАНЕ)")
    print("=" * 60)
    
    if all_results:
        # Сортиране по качество (relevance difference)
        sorted_by_quality = sorted(all_results, key=lambda x: x['avg_relevance_difference'], reverse=True)
        
        print("\n🏆 Класиране по качество (разлика в релевантност):")
        for i, result in enumerate(sorted_by_quality, 1):
            print(f"{i}. {result['model_name']}")
            print(f"   Релевантност: {result['avg_relevance_difference']:.3f}")
            print(f"   Скорост: {result['avg_encoding_time']:.3f}s")
            print(f"   Размер: {result['model_size']}")
        
        # Сортиране по скорост
        sorted_by_speed = sorted(all_results, key=lambda x: x['avg_encoding_time'])
        
        print("\n⚡ Класиране по скорост:")
        for i, result in enumerate(sorted_by_speed, 1):
            print(f"{i}. {result['model_name']}")
            print(f"   Скорост: {result['avg_encoding_time']:.3f}s")
            print(f"   Релевантност: {result['avg_relevance_difference']:.3f}")
    
    print(f"\n✅ Тестът завърши. Резултати запазени в: embedding_models_real_test_results.json")

if __name__ == "__main__":
    main()
