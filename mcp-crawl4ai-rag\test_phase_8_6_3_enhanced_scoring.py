#!/usr/bin/env python3
"""
🎯 Phase 8.6.3: Enhanced Scoring Algorithm Test
Тестване на подобрения алгоритъм за оценяване с многокомпонентни тегла
"""

import asyncio
import sys
import os
import time
from typing import List, Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import (
    search_crawled_pages_advanced_query,
    calculate_enhanced_scoring,
    calculate_document_type_boost,
    evaluate_with_flexible_metrics,
    calculate_success_rate_at_k,
    calculate_mrr
)

# Test queries with expected content for evaluation
TEST_QUERIES = [
    {
        "query": "МСП програми за финансиране на малки и средни предприятия",
        "expected_content": [
            "малки и средни предприятия",
            "М<PERSON><PERSON>",
            "финансиране",
            "програма",
            "безвъзмездна помощ"
        ],
        "description": "МСП финансиране"
    },
    {
        "query": "екологични проекти зелена енергия възобновяеми източници",
        "expected_content": [
            "екологични",
            "зелена енергия",
            "възобновяеми източници",
            "околна среда",
            "устойчиво развитие"
        ],
        "description": "Екологични проекти"
    },
    {
        "query": "образователни програми обучение квалификация",
        "expected_content": [
            "образование",
            "обучение",
            "квалификация",
            "професионално развитие",
            "умения"
        ],
        "description": "Образователни програми"
    },
    {
        "query": "инфраструктурни проекти пътища транспорт",
        "expected_content": [
            "инфраструктура",
            "пътища",
            "транспорт",
            "строителство",
            "модернизация"
        ],
        "description": "Инфраструктурни проекти"
    }
]

async def test_enhanced_scoring_algorithm():
    """Test the enhanced scoring algorithm with multi-component weights"""
    print("🎯 ЗАПОЧВА ТЕСТВАНЕ НА ФАЗА 8.6.3: ПОДОБРЕН АЛГОРИТЪМ ЗА ОЦЕНЯВАНЕ")
    print("=" * 80)
    
    total_tests = len(TEST_QUERIES)
    successful_tests = 0
    all_metrics = []
    
    for i, test_case in enumerate(TEST_QUERIES, 1):
        query = test_case["query"]
        expected_content = test_case["expected_content"]
        description = test_case["description"]
        
        print(f"\n🔍 Тест {i}/{total_tests}: {description}")
        print(f"   Заявка: {query}")
        print(f"   Очаквано съдържание: {len(expected_content)} елемента")
        
        start_time = time.time()
        
        try:
            # Search with enhanced scoring
            results = await search_crawled_pages_advanced_query(
                query_text=query,
                match_count=10,
                use_bulgarian_embeddings=True,
                rerank_top_k=20,
                enable_query_expansion=True,
                enable_hyde=True,
                enable_multi_query=True
            )
            
            search_time = time.time() - start_time
            
            if not results:
                print(f"   ❌ Няма резултати за заявката")
                continue
            
            # Evaluate with flexible metrics
            metrics = evaluate_with_flexible_metrics(results, expected_content, query)
            all_metrics.append(metrics)
            
            # Test document type boost calculation
            doc_boosts = []
            for result in results[:3]:
                boost = calculate_document_type_boost(result)
                doc_boosts.append(boost)
            
            avg_doc_boost = sum(doc_boosts) / len(doc_boosts) if doc_boosts else 1.0
            
            # Check success criteria
            precision_at_1 = metrics['precision_at_1']
            success_rate_at_3 = metrics['success_rate_at_3']
            mrr = metrics['mrr']
            avg_enhanced_score = metrics['avg_enhanced_score_top3']
            
            # Success if any metric shows good performance
            is_successful = (
                precision_at_1 >= 0.8 or  # 80% precision@1
                success_rate_at_3 >= 0.6 or  # 60% success rate@3
                mrr >= 0.5 or  # 50% MRR
                avg_enhanced_score >= 0.4  # 40% average enhanced score
            )
            
            if is_successful:
                successful_tests += 1
                print(f"   ✅ УСПЕШЕН тест")
            else:
                print(f"   ⚠️ Частичен успех")
            
            print(f"   📊 Резултати ({len(results)} намерени за {search_time:.2f}s):")
            print(f"      🎯 Precision@1: {precision_at_1:.1%}")
            print(f"      📈 SuccessRate@3: {success_rate_at_3:.1%}")
            print(f"      🔄 MRR: {mrr:.3f}")
            print(f"      ⭐ Avg Enhanced Score: {avg_enhanced_score:.3f}")
            print(f"      🚀 Avg Doc Boost: {avg_doc_boost:.2f}x")
            
            # Show top results with enhanced scoring details
            print(f"   🏆 Топ 3 резултата:")
            for j, result in enumerate(results[:3], 1):
                enhanced_score = result.get('enhanced_score', 0.0)
                doc_boost = result.get('doc_type_boost', 1.0)
                scoring_components = result.get('scoring_components', {})
                title = result.get('title', 'Без заглавие')[:60]
                
                print(f"      {j}. {title}...")
                print(f"         Enhanced Score: {enhanced_score:.3f} (boost: {doc_boost:.2f}x)")
                print(f"         Components: {scoring_components}")
                
        except Exception as e:
            print(f"   ❌ Грешка при тестване: {str(e)}")
            continue
    
    # Calculate overall statistics
    if all_metrics:
        avg_precision_at_1 = sum(m['precision_at_1'] for m in all_metrics) / len(all_metrics)
        avg_success_rate_at_3 = sum(m['success_rate_at_3'] for m in all_metrics) / len(all_metrics)
        avg_mrr = sum(m['mrr'] for m in all_metrics) / len(all_metrics)
        avg_enhanced_score = sum(m['avg_enhanced_score_top3'] for m in all_metrics) / len(all_metrics)
        
        success_rate = successful_tests / total_tests
        
        print(f"\n🎯 ОБОБЩЕНИЕ НА ФАЗА 8.6.3:")
        print("=" * 50)
        print(f"📊 Общ успех: {successful_tests}/{total_tests} ({success_rate:.1%})")
        print(f"🎯 Средна Precision@1: {avg_precision_at_1:.1%}")
        print(f"📈 Средна SuccessRate@3: {avg_success_rate_at_3:.1%}")
        print(f"🔄 Средна MRR: {avg_mrr:.3f}")
        print(f"⭐ Средна Enhanced Score: {avg_enhanced_score:.3f}")
        
        # Performance evaluation
        if success_rate >= 0.75:
            print(f"🏆 ОТЛИЧЕН резултат! Фаза 8.6.3 работи превъзходно!")
        elif success_rate >= 0.5:
            print(f"✅ ДОБЪР резултат! Фаза 8.6.3 работи добре!")
        else:
            print(f"⚠️ Необходими подобрения в алгоритъма за оценяване")
        
        return success_rate >= 0.5
    else:
        print(f"❌ Няма валидни резултати за анализ")
        return False

async def test_document_type_boost():
    """Test document type boost calculation"""
    print(f"\n🚀 ТЕСТВАНЕ НА DOCUMENT TYPE BOOST:")
    print("-" * 40)
    
    # Test cases for document type boost
    test_documents = [
        {
            "title": "Оперативна програма Иновации и конкурентоспособност",
            "content": "Програмата подкрепя малки и средни предприятия чрез безвъзмездна помощ",
            "url": "https://eufunds.bg/op-innovation",
            "expected_boost": "> 1.5x"
        },
        {
            "title": "Министерство на икономиката - схема за подпомагане",
            "content": "Официална информация за кандидатстване за европейски фондове",
            "url": "https://mi.government.bg/grants",
            "expected_boost": "> 1.3x"
        },
        {
            "title": "Покана за предложения за финансиране на проекти",
            "content": "Грантове за екологични проекти и зелена енергия",
            "url": "https://example.bg/call-proposals",
            "expected_boost": "> 1.2x"
        },
        {
            "title": "Обща информация за бизнес",
            "content": "Статия за развитие на бизнеса в България",
            "url": "https://business.bg/article",
            "expected_boost": "~1.0x"
        }
    ]
    
    for i, doc in enumerate(test_documents, 1):
        boost = calculate_document_type_boost(doc)
        expected = doc["expected_boost"]
        
        print(f"{i}. {doc['title'][:50]}...")
        print(f"   URL: {doc['url']}")
        print(f"   Boost: {boost:.2f}x (очаквано: {expected})")
        print()

if __name__ == "__main__":
    async def main():
        print("🎯 СТАРТИРАНЕ НА ТЕСТОВЕ ЗА ФАЗА 8.6.3")
        print("=" * 60)
        
        # Test document type boost
        await test_document_type_boost()
        
        # Test enhanced scoring algorithm
        success = await test_enhanced_scoring_algorithm()
        
        if success:
            print(f"\n🎉 ФАЗА 8.6.3 ЗАВЪРШЕНА УСПЕШНО!")
            print(f"   Подобреният алгоритъм за оценяване работи отлично!")
        else:
            print(f"\n⚠️ Фаза 8.6.3 се нуждае от допълнителни подобрения")
    
    asyncio.run(main())
