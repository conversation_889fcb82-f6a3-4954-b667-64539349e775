#!/usr/bin/env python3
"""
РЕАЛЕН END-TO-END ТЕСТ НА MCP СЪРВЪРА
Тества цялата система от начало до край - точно както би я използвал потребител
"""

import asyncio
import json
import sys
import os
import time
import requests
from typing import Dict, Any
from dotenv import load_dotenv

# Зареждаме environment variables
load_dotenv()

async def test_mcp_server_end_to_end():
    """
    РЕАЛЕН END-TO-END ТЕСТ - тества сървъра като истински потребител
    """
    print("🔥 ЗАПОЧВА РЕАЛЕН END-TO-END ТЕСТ НА MCP СЪРВЪРА")
    print("=" * 80)
    
    # Проверка на environment variables
    required_vars = ["SUPABASE_URL", "SUPABASE_SERVICE_KEY", "OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ ЛИПСВАТ ENVIRONMENT VARIABLES: {missing_vars}")
        return False
    
    print("✅ Environment variables са налични")
    
    # ТЕСТ 1: Стартиране на MCP сървъра
    print("\n" + "="*50)
    print("🚀 ТЕСТ 1: СТАРТИРАНЕ НА MCP СЪРВЪРА")
    print("="*50)
    
    try:
        # Стартираме сървъра в background
        import subprocess
        server_process = subprocess.Popen(
            [sys.executable, "src/crawl4ai_mcp.py"],
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Чакаме малко да се стартира
        await asyncio.sleep(3)
        
        if server_process.poll() is None:
            print("✅ MCP сървър стартиран успешно")
        else:
            stdout, stderr = server_process.communicate()
            print(f"❌ MCP сървър не се стартира:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Грешка при стартиране на сървъра: {e}")
        return False
    
    # ТЕСТ 2: Тестване на RAG функционалност директно
    print("\n" + "="*50)
    print("🔍 ТЕСТ 2: RAG ФУНКЦИОНАЛНОСТ - ДИРЕКТЕН ТЕСТ")
    print("="*50)

    try:
        # Импортираме директно utils функциите
        sys.path.append(os.path.join(os.getcwd(), 'src'))
        from utils import enhanced_semantic_search, get_supabase_client

        # Тестваме enhanced_semantic_search директно
        test_query = "европейски фондове за малки предприятия в България"
        print(f"📝 Заявка: '{test_query}'")

        # Получаваме supabase client
        supabase = get_supabase_client()

        start_time = time.time()
        results = await enhanced_semantic_search(
            query=test_query,
            supabase_client=supabase,
            similarity_threshold=0.62,
            final_top_k=5
        )
        execution_time = time.time() - start_time

        print(f"⏱️  Време за изпълнение: {execution_time:.2f}s")
        print(f"📊 Брой резултати: {len(results)}")

        if len(results) > 0:
            print("✅ RAG ФУНКЦИОНАЛНОСТ РАБОТИ!")

            # Показваме първия резултат
            first_result = results[0]
            print(f"🎯 Първи резултат:")
            print(f"   Заглавие: {first_result.get('title', 'N/A')[:100]}...")
            print(f"   Similarity: {first_result.get('similarity', 0):.3f}")
            print(f"   URL: {first_result.get('url', 'N/A')[:80]}...")
        else:
            print("❌ RAG ФУНКЦИОНАЛНОСТ НЕ РАБОТИ!")
            print("Няма резултати от заявката")

    except Exception as e:
        print(f"❌ Грешка в RAG функционалност: {e}")
        import traceback
        traceback.print_exc()
    
    # ТЕСТ 3: Тестване на advanced RAG функционалност
    print("\n" + "="*50)
    print("🚀 ТЕСТ 3: ADVANCED RAG ФУНКЦИОНАЛНОСТ")
    print("="*50)

    try:
        from utils import ultra_smart_rag_query

        test_query = "програми за дигитализация и иновации"
        print(f"📝 Заявка: '{test_query}'")

        # Получаваме supabase client
        supabase = get_supabase_client()

        start_time = time.time()
        results = await ultra_smart_rag_query(
            query=test_query,
            supabase_client=supabase,
            similarity_threshold=0.62,
            final_top_k=5
        )
        execution_time = time.time() - start_time

        print(f"⏱️  Време за изпълнение: {execution_time:.2f}s")
        print(f"📊 Брой резултати: {len(results)}")

        if len(results) > 0:
            print("✅ ADVANCED RAG ФУНКЦИОНАЛНОСТ РАБОТИ!")

            # Показваме първия резултат
            if isinstance(results, list) and len(results) > 0:
                first_result = results[0]
                print(f"🎯 Първи резултат:")
                print(f"   Заглавие: {first_result.get('title', 'N/A')[:100]}...")
                print(f"   Similarity: {first_result.get('similarity', 0):.3f}")
                print(f"   URL: {first_result.get('url', 'N/A')[:80]}...")
            else:
                print(f"🎯 Резултати: {type(results)} с дължина {len(results)}")
        else:
            print("❌ ADVANCED RAG ФУНКЦИОНАЛНОСТ НЕ РАБОТИ!")
            print("Няма резултати от заявката")

    except Exception as e:
        print(f"❌ Грешка в ADVANCED RAG функционалност: {e}")
        import traceback
        traceback.print_exc()
    
    # ТЕСТ 4: Тестване на MCP unified_rag_query tool
    print("\n" + "="*50)
    print("🎯 ТЕСТ 4: MCP UNIFIED RAG TOOL")
    print("="*50)

    try:
        # Тестваме директно unified_rag_query от MCP сървъра
        from crawl4ai_mcp import unified_rag_query

        test_query = "програми за стартъпи и иновации"
        print(f"📝 Заявка: '{test_query}'")

        start_time = time.time()
        # Симулираме MCP Context
        class MockContext:
            pass

        result = await unified_rag_query(
            ctx=MockContext(),
            query=test_query,
            mode="standard",
            match_count=3
        )
        execution_time = time.time() - start_time

        print(f"⏱️  Време за изпълнение: {execution_time:.2f}s")

        # Парсираме JSON резултата
        result_data = json.loads(result)

        if result_data.get('success') and len(result_data.get('results', [])) > 0:
            print("✅ MCP UNIFIED RAG TOOL РАБОТИ!")
            print(f"📊 Брой резултати: {len(result_data['results'])}")

            first_result = result_data['results'][0]
            print(f"🎯 Първи резултат:")
            print(f"   Заглавие: {first_result.get('title', 'N/A')[:80]}...")
            print(f"   Similarity: {first_result.get('similarity', 0):.3f}")
        else:
            print("❌ MCP UNIFIED RAG TOOL НЕ РАБОТИ!")
            print(f"Грешка: {result_data.get('error', 'Неизвестна грешка')}")

    except Exception as e:
        print(f"❌ Грешка в MCP tool: {e}")
        import traceback
        traceback.print_exc()
    
    # ТЕСТ 5: Проверка на базата данни
    print("\n" + "="*50)
    print("🗄️  ТЕСТ 5: ПРОВЕРКА НА БАЗАТА ДАННИ")
    print("="*50)
    
    try:
        from utils import get_supabase_client
        
        supabase = get_supabase_client()
        
        # Проверяваме броя документи
        result = supabase.table('crawled_pages').select('id', count='exact').execute()
        doc_count = result.count
        
        print(f"📊 Документи в базата: {doc_count}")
        
        if doc_count > 0:
            print("✅ БАЗАТА ДАННИ СЪДЪРЖА ДОКУМЕНТИ!")
            
            # Проверяваме последно добавен документ
            latest = supabase.table('crawled_pages').select('url', 'created_at').order('created_at', desc=True).limit(1).execute()
            if latest.data:
                latest_doc = latest.data[0]
                print(f"📅 Последен документ: {latest_doc['created_at']}")
                print(f"🔗 URL: {latest_doc['url'][:80]}...")
        else:
            print("⚠️  БАЗАТА ДАННИ Е ПРАЗНА!")
            
    except Exception as e:
        print(f"❌ Грешка при проверка на базата: {e}")
    
    # Спираме сървъра
    try:
        server_process.terminate()
        server_process.wait(timeout=5)
    except:
        server_process.kill()
    
    # ФИНАЛЕН АНАЛИЗ
    print("\n" + "="*80)
    print("🎯 ФИНАЛЕН АНАЛИЗ НА END-TO-END ТЕСТА")
    print("="*80)
    
    print("✅ Тестът завърши - проверете резултатите по-горе")
    print("📋 Ако всички тестове са ✅ - сървърът работи реално!")
    print("📋 Ако има ❌ - има проблеми, които трябва да се поправят")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_mcp_server_end_to_end())
    sys.exit(0 if success else 1)
