#!/usr/bin/env python3
"""
Production Embedding Regeneration - Най-добри практики 2024-2025
Регенериране на всички embeddings с BGE модел (1024-dim) за пълна консистентност
"""

import os
import sys
import asyncio
import logging
from typing import List, Dict, Any, Optional
from supabase import create_client, Client
from sentence_transformers import SentenceTransformer
import numpy as np
from datetime import datetime
from dotenv import load_dotenv

# Зареждане на environment variables
load_dotenv('../.env')

# Настройка на логинг
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('production_regeneration.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ProductionEmbeddingRegenerator:
    def __init__(self):
        """Инициализация с най-добри практики за BGE модели"""
        # Supabase клиент
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")

        if not supabase_url or not supabase_key:
            raise ValueError("Липсват SUPABASE_URL или SUPABASE_SERVICE_KEY")
        
        self.supabase: Client = create_client(supabase_url, supabase_key)
        
        # BGE модел - най-добра практика за 2024-2025
        model_name = os.getenv("EMBEDDING_MODEL_NAME", 'BAAI/bge-large-en-v1.5')
        logger.info(f"Зареждане на BGE модел: {model_name}")
        self.model = SentenceTransformer(model_name)
        
        # Проверка на размерността
        test_embedding = self.model.encode(["test"])
        self.embedding_dim = len(test_embedding[0])
        logger.info(f"BGE модел размерност: {self.embedding_dim}")
        
        if self.embedding_dim != 1024:
            raise ValueError(f"Неочаквана размерност: {self.embedding_dim}, очаквана: 1024")

    def create_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """Създаване на embeddings с BGE модел"""
        try:
            embeddings = self.model.encode(texts, normalize_embeddings=True)
            return [embedding.tolist() for embedding in embeddings]
        except Exception as e:
            logger.error(f"Грешка при създаване на embeddings: {e}")
            raise

    def clear_all_embeddings(self) -> bool:
        """Изчистване на всички стари embeddings"""
        try:
            logger.info("Изчистване на всички стари embeddings...")

            response = self.supabase.table('crawled_pages').update({
                'embedding': None
            }).gte('id', 0).execute()

            logger.info(f"Изчистени embeddings за {len(response.data)} записа")
            return True
            
        except Exception as e:
            logger.error(f"Грешка при изчистване: {e}")
            return False

    def get_all_records(self) -> List[Dict[str, Any]]:
        """Извличане на всички записи за обработка"""
        try:
            response = self.supabase.table('crawled_pages').select(
                'id, content'
            ).order('id').execute()

            logger.info(f"Извлечени {len(response.data)} записа за обработка")
            return response.data

        except Exception as e:
            logger.error(f"Грешка при извличане на записи: {e}")
            return []

    def process_batch(self, records: List[Dict[str, Any]]) -> bool:
        """Обработка на партида записи с BGE модел"""
        try:
            # Подготовка на текстове за embedding
            texts = []
            record_ids = []

            for record in records:
                content = record.get('content', '') or ''

                if content.strip():
                    texts.append(content.strip())
                    record_ids.append(record['id'])
            
            if not texts:
                logger.warning("Няма текстове за обработка в тази партида")
                return True
            
            # Създаване на embeddings с BGE модел
            logger.info(f"Създаване на {len(texts)} embeddings с BGE модел...")
            embeddings = self.create_embeddings_batch(texts)

            # Проверка на размерността
            for i, embedding in enumerate(embeddings):
                if len(embedding) != 1024:
                    raise ValueError(f"Неправилна размерност {len(embedding)} за запис {record_ids[i]}")

            # Обновяване в базата данни
            updates_successful = 0
            for record_id, embedding in zip(record_ids, embeddings):
                try:
                    response = self.supabase.table('crawled_pages').update({
                        'embedding': embedding
                    }).eq('id', record_id).execute()

                    if response.data:
                        updates_successful += 1
                    else:
                        logger.warning(f"Неуспешно обновяване на запис {record_id}")

                except Exception as e:
                    logger.error(f"Грешка при обновяване на запис {record_id}: {e}")

            logger.info(f"Успешно обновени {updates_successful}/{len(embeddings)} записа")
            return updates_successful == len(embeddings)

        except Exception as e:
            logger.error(f"Грешка при обработка на партида: {e}")
            return False

    def verify_consistency(self) -> Dict[str, Any]:
        """Проверка на консистентността на embeddings"""
        try:
            import json
            logger.info("Проверка на консистентността...")

            response = self.supabase.table('crawled_pages').select(
                'id, embedding'
            ).not_.is_('embedding', 'null').execute()

            total_records = len(response.data)
            dimension_counts = {}

            for record in response.data:
                embedding = record.get('embedding')
                if embedding:
                    try:
                        # Парсиране на JSON string към list
                        if isinstance(embedding, str):
                            embedding_list = json.loads(embedding)
                        else:
                            embedding_list = embedding

                        dim = len(embedding_list)
                        dimension_counts[dim] = dimension_counts.get(dim, 0) + 1
                    except (json.JSONDecodeError, TypeError) as e:
                        logger.warning(f"Грешка при парсиране на embedding за запис {record.get('id')}: {e}")
                        # Fallback към string дължина за debug
                        dim = len(embedding) if isinstance(embedding, str) else 0
                        dimension_counts[f"error_{dim}"] = dimension_counts.get(f"error_{dim}", 0) + 1

            result = {
                'total_records': total_records,
                'dimension_distribution': dimension_counts,
                'is_consistent': len(dimension_counts) == 1 and 1024 in dimension_counts
            }

            logger.info(f"Резултат от проверката: {result}")
            return result

        except Exception as e:
            logger.error(f"Грешка при проверка: {e}")
            return {'error': str(e)}

    async def regenerate_all_embeddings(self, batch_size: int = 8) -> bool:
        """Пълно регенериране на всички embeddings с най-добри практики"""
        try:
            start_time = datetime.now()
            logger.info(f"Започване на production регенериране в {start_time}")

            # Стъпка 1: Изчистване на стари embeddings
            if not self.clear_all_embeddings():
                return False

            # Стъпка 2: Извличане на всички записи
            all_records = self.get_all_records()
            if not all_records:
                logger.error("Няма записи за обработка")
                return False

            # Стъпка 3: Обработка по партиди
            total_batches = (len(all_records) + batch_size - 1) // batch_size
            successful_batches = 0

            for i in range(0, len(all_records), batch_size):
                batch = all_records[i:i + batch_size]
                batch_num = (i // batch_size) + 1

                logger.info(f"Обработка на партида {batch_num}/{total_batches} ({len(batch)} записа)")

                if self.process_batch(batch):
                    successful_batches += 1
                    logger.info(f"Партида {batch_num} завършена успешно")
                else:
                    logger.error(f"Партида {batch_num} неуспешна")

            # Стъпка 4: Финална проверка
            verification = self.verify_consistency()

            end_time = datetime.now()
            duration = end_time - start_time

            logger.info(f"Регенериране завършено за {duration}")
            logger.info(f"Успешни партиди: {successful_batches}/{total_batches}")
            logger.info(f"Финална проверка: {verification}")

            return verification.get('is_consistent', False)

        except Exception as e:
            logger.error(f"Критична грешка при регенериране: {e}")
            return False

async def main():
    """Главна функция за production регенериране"""
    try:
        regenerator = ProductionEmbeddingRegenerator()
        
        logger.info("=" * 60)
        logger.info("PRODUCTION EMBEDDING REGENERATION - BGE 2024-2025")
        logger.info("=" * 60)

        success = await regenerator.regenerate_all_embeddings()

        if success:
            logger.info("УСПЕШНО ЗАВЪРШВАНЕ! Всички embeddings са консистентни.")
            return 0
        else:
            logger.error("НЕУСПЕШНО ЗАВЪРШВАНЕ! Проверете логовете.")
            return 1

    except Exception as e:
        logger.error(f"Критична грешка: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
