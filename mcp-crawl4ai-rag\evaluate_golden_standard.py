# evaluate_golden_standard.py

import os
import sys
import logging
import json
from dotenv import load_dotenv

# --- Настройка на пътя за импортиране ---
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from phase5_adaptive_fusion import adaptive_fusion_search
from supabase import create_client
from openai import OpenAI

logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Нашият "Златен стандарт" ---
GOLDEN_DATASET = [
  {"id": 1, "question": "Какви мерки има за справяне с последиците от Брекзит?", "expected_url": "https://www.eufunds.bg/bg/brexit"},
  {"id": 2, "question": "Как се подкрепят инвестиции в технологии за лесовъдство?", "expected_url": "https://www.eufunds.bg/bg/home"},
  {"id": 3, "question": "Кои са програмите по фонд 'Вътрешна сигурност'?", "expected_url": "https://www.eufunds.bg/bg"},
  {"id": 4, "question": "Какви са отворените процедури по програма 'Околна среда'?", "expected_url": "https://www.eufunds.bg/bg/opos/term/1380"},
  {"id": 5, "question": "Как община Елхово ще изгражда инсталация за компостиране?", "expected_url": "https://www.eufunds.bg/bg/opos/node/18327"},
  {"id": 6, "question": "Какви са мерките за превенция на свлачища, срутища и ерозии?", "expected_url": "https://www.eufunds.bg/bg/opos/node/18358"},
  {"id": 7, "question": "Каква е индикативната работна програма по 'Конкурентоспособност и иновации в предприятията'?", "expected_url": "https://www.eufunds.bg/bg/indicative-annual-work-programmes"},
  {"id": 8, "question": "Има ли подкрепа за индустриални паркове по програма 'Развитие на регионите'?", "expected_url": "https://www.eufunds.bg/bg/news-eip"},
  {"id": 9, "question": "Каква е инициативата за малки и средни предприятия (МСП) по ОПИК?", "expected_url": "https://www.eufunds.bg/bg/opic/node/562"},
  {"id": 10, "question": "Какво представлява проектът за разделно събиране в общините Суворово, Девня и Ветрино?", "expected_url": "https://www.eufunds.bg/bg/opos/node/18328"}
]

def evaluate_rag_system():
    print("--- 📊 ЗАПОЧВАМ ОЦЕНКА ПО 'ЗЛАТЕН СТАНДАРТ' 📊 ---")
    load_dotenv()
    try:
        supabase_client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY'))
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    except Exception as e:
        print(f"❌ Грешка при свързване! Проверете .env файла. Грешка: {e}")
        return

    successful_tests = 0
    total_tests = len(GOLDEN_DATASET)

    for test_case in GOLDEN_DATASET:
        question = test_case["question"]
        expected_url = test_case["expected_url"]
        
        print(f"\n--- Тест #{test_case['id']}: {question} ---")
        
        results = adaptive_fusion_search(
            supabase_client,
            question,
            openai_client,
            match_count=3  # Търсим в топ 3 резултата
        )
        
        is_successful = False
        if results:
            top_urls = [res.get("url") for res in results]
            print(f"  -> Върнати URL-и: {top_urls}")
            if expected_url in top_urls:
                is_successful = True
                successful_tests += 1
                print(f"  ✅ УСПЕХ! Очакваният URL '{expected_url}' е намерен.")
            else:
                print(f"  ❌ ПРОВАЛ! Очакваният URL '{expected_url}' НЕ е намерен в резултатите.")
        else:
            print("  ❌ ПРОВАЛ! Системата не върна резултати.")

    print("\n\n================== ФИНАЛЕН РЕЗУЛТАТ ==================")
    success_rate = (successful_tests / total_tests) * 100
    print(f"  Успешни тестове: {successful_tests} от {total_tests}")
    print(f"  Успеваемост: {success_rate:.2f}%")
    print("======================================================")

if __name__ == "__main__":
    evaluate_rag_system()