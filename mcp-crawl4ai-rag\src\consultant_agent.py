"""
Phase 5.0: Smart Consultant Agent for European Funding Programs

This module implements advanced agentic capabilities that mimic the behavior
of professional European funding consultants, providing intelligent query
decomposition, multi-step analysis, and comprehensive recommendations.

Key Features:
- Complex query decomposition for consultant-level analysis
- Multi-dimensional search (topic + time + location + budget)
- Comparative analysis between programs
- Deadline and eligibility monitoring
- Contextual recommendations based on client profile
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from openai import AsyncOpenAI
from supabase import Client

# Import existing RAG functions
from .utils import (
    ultra_smart_rag_query,
    enhanced_semantic_search,
    create_embeddings_batch
)

logger = logging.getLogger(__name__)

class ConsultantQueryType(Enum):
    """Types of consultant queries"""
    PROGRAM_SEARCH = "program_search"           # "Кои програми за МСП?"
    COMPARATIVE_ANALYSIS = "comparative"        # "Сравни ОПРР и ОПТТИ"
    ELIGIBILITY_CHECK = "eligibility"          # "Подходящ ли съм за програма X?"
    DEADLINE_MONITORING = "deadline"           # "Кои програми затварят до март?"
    BUDGET_ANALYSIS = "budget"                 # "Програми с бюджет 50-100К"
    PROCEDURAL_GUIDANCE = "procedural"         # "Как да кандидатствам?"
    MULTI_CRITERIA = "multi_criteria"          # Complex multi-part queries

@dataclass
class ConsultantQuery:
    """Structured representation of consultant query"""
    original_query: str
    query_type: ConsultantQueryType
    sub_queries: List[str]
    criteria: Dict[str, Any]
    priority_order: List[str]
    expected_result_type: str
    confidence: float

@dataclass
class ConsultantRecommendation:
    """Consultant-level recommendation"""
    program_id: str
    program_name: str
    relevance_score: float
    eligibility_match: float
    deadline_urgency: str
    budget_fit: str
    recommendation_reason: str
    next_steps: List[str]
    warnings: List[str]

class SmartConsultantAgent:
    """
    Advanced AI agent that mimics professional European funding consultants
    """
    
    def __init__(self):
        self.consultation_history = []
        self.client_profiles = {}
        
    async def analyze_consultant_query(
        self,
        query: str,
        async_openai_client: Optional[AsyncOpenAI] = None,
        client_context: Optional[Dict[str, Any]] = None
    ) -> ConsultantQuery:
        """
        Analyze query like a professional consultant would
        
        Args:
            query: Original user query
            async_openai_client: OpenAI client for advanced analysis
            client_context: Optional client profile information
            
        Returns:
            Structured consultant query analysis
        """
        logger.info(f"🧠 Consultant analyzing query: '{query[:50]}...'")
        
        if not async_openai_client:
            return self._fallback_query_analysis(query)
        
        try:
            # Advanced consultant-level query analysis
            analysis_prompt = f"""
Ти си експерт консултант по европейски финансиращи програми. Анализирай следната заявка като професионален консултант:

ЗАЯВКА: "{query}"

Върни JSON с точно тази структура:
{{
    "query_type": "program_search|comparative|eligibility|deadline|budget|procedural|multi_criteria",
    "sub_queries": ["подзаявка 1", "подзаявка 2", ...],
    "criteria": {{
        "program_types": ["ОПРР", "ОПТТИ", "ОПОС", "Interreg", "МСП"],
        "budget_range": {{"min": 0, "max": 1000000}},
        "deadlines": {{"urgent": true, "months_ahead": 6}},
        "sectors": ["транспорт", "околна среда", "иновации"],
        "target_group": "МСП|НПО|общини|физически лица",
        "geographic_scope": "национален|регионален|трансграничен"
    }},
    "priority_order": ["най-важен критерий", "втори по важност", ...],
    "expected_result_type": "program_list|comparison_table|eligibility_assessment|procedure_guide",
    "confidence": 0.95
}}

ВАЖНО: Отговори САМО с валиден JSON, без допълнителен текст!
"""

            response = await async_openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.1,
                max_tokens=1000
            )
            
            analysis_text = response.choices[0].message.content
            if analysis_text:
                analysis_text = analysis_text.strip()
            else:
                analysis_text = ""
            
            # Clean JSON response
            if analysis_text.startswith("```json"):
                analysis_text = analysis_text[7:-3]
            elif analysis_text.startswith("```"):
                analysis_text = analysis_text[3:-3]
            
            analysis = json.loads(analysis_text)
            
            return ConsultantQuery(
                original_query=query,
                query_type=ConsultantQueryType(analysis["query_type"]),
                sub_queries=analysis["sub_queries"],
                criteria=analysis["criteria"],
                priority_order=analysis["priority_order"],
                expected_result_type=analysis["expected_result_type"],
                confidence=analysis["confidence"]
            )
            
        except Exception as e:
            logger.warning(f"Consultant query analysis failed: {e}")
            return self._fallback_query_analysis(query)
    
    def _fallback_query_analysis(self, query: str) -> ConsultantQuery:
        """Fallback analysis when OpenAI is not available"""
        
        # Simple heuristic analysis
        query_lower = query.lower()
        
        # Determine query type
        if any(word in query_lower for word in ["сравни", "разлика", "по-добър"]):
            query_type = ConsultantQueryType.COMPARATIVE_ANALYSIS
        elif any(word in query_lower for word in ["дедлайн", "срок", "затваря"]):
            query_type = ConsultantQueryType.DEADLINE_MONITORING
        elif any(word in query_lower for word in ["бюджет", "лева", "евро", "финансиране"]):
            query_type = ConsultantQueryType.BUDGET_ANALYSIS
        elif any(word in query_lower for word in ["как", "процедура", "стъпки"]):
            query_type = ConsultantQueryType.PROCEDURAL_GUIDANCE
        elif any(word in query_lower for word in ["подходящ", "мога ли", "имам право"]):
            query_type = ConsultantQueryType.ELIGIBILITY_CHECK
        else:
            query_type = ConsultantQueryType.PROGRAM_SEARCH
        
        return ConsultantQuery(
            original_query=query,
            query_type=query_type,
            sub_queries=[query],
            criteria={
                "program_types": [],
                "budget_range": {"min": 0, "max": 1000000},
                "deadlines": {"urgent": False, "months_ahead": 12},
                "sectors": [],
                "target_group": "",
                "geographic_scope": ""
            },
            priority_order=["relevance"],
            expected_result_type="program_list",
            confidence=0.6
        )
    
    async def execute_consultant_search(
        self,
        consultant_query: ConsultantQuery,
        supabase_client: Client,
        async_openai_client: Optional[AsyncOpenAI] = None,
        final_top_k: int = 5
    ) -> Dict[str, Any]:
        """
        Execute multi-step consultant-level search
        
        Args:
            consultant_query: Analyzed consultant query
            supabase_client: Supabase client
            async_openai_client: OpenAI client
            final_top_k: Number of final results
            
        Returns:
            Comprehensive consultant results
        """
        logger.info(f"🔍 Executing consultant search: {consultant_query.query_type.value}")
        
        all_results = []
        search_metadata = {
            "query_type": consultant_query.query_type.value,
            "sub_queries_executed": 0,
            "total_results_found": 0,
            "search_strategy": "multi_step_consultant"
        }
        
        try:
            # Execute sub-queries with different strategies
            for i, sub_query in enumerate(consultant_query.sub_queries):
                logger.info(f"🔍 Executing sub-query {i+1}/{len(consultant_query.sub_queries)}: '{sub_query[:30]}...'")
                
                # Use ultra smart RAG for each sub-query
                sub_result = await ultra_smart_rag_query(
                    query=sub_query,
                    supabase_client=supabase_client,
                    async_openai_client=async_openai_client,
                    similarity_threshold=0.7,
                    final_top_k=final_top_k * 2,  # Get more candidates
                    enable_all_optimizations=True
                )
                
                if sub_result.get("success") and sub_result.get("results"):
                    all_results.extend(sub_result["results"])
                    search_metadata["sub_queries_executed"] += 1
                
                # Small delay to avoid rate limiting
                await asyncio.sleep(0.1)
            
            # Remove duplicates and apply consultant-level filtering
            unique_results = self._deduplicate_results(all_results)
            filtered_results = await self._apply_consultant_filters(
                unique_results, 
                consultant_query,
                async_openai_client
            )
            
            # Generate consultant recommendations
            recommendations = await self._generate_consultant_recommendations(
                filtered_results[:final_top_k],
                consultant_query,
                async_openai_client
            )
            
            search_metadata["total_results_found"] = len(filtered_results)
            
            return {
                "success": True,
                "query_analysis": {
                    "original_query": consultant_query.original_query,
                    "query_type": consultant_query.query_type.value,
                    "confidence": consultant_query.confidence,
                    "criteria": consultant_query.criteria
                },
                "results": filtered_results[:final_top_k],
                "recommendations": recommendations,
                "metadata": search_metadata,
                "consultant_insights": await self._generate_consultant_insights(
                    consultant_query, filtered_results, async_openai_client
                )
            }
            
        except Exception as e:
            logger.error(f"Consultant search execution failed: {e}")
            return {
                "success": False,
                "error": f"Consultant search failed: {str(e)}",
                "query_analysis": {
                    "original_query": consultant_query.original_query,
                    "query_type": consultant_query.query_type.value
                },
                "results": [],
                "recommendations": [],
                "metadata": search_metadata
            }

    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate results based on URL"""
        seen_urls = set()
        unique_results = []

        for result in results:
            url = result.get("url", "")
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)

        return unique_results

    async def _apply_consultant_filters(
        self,
        results: List[Dict[str, Any]],
        consultant_query: ConsultantQuery,
        async_openai_client: Optional[AsyncOpenAI] = None
    ) -> List[Dict[str, Any]]:
        """Apply consultant-level filtering and ranking"""

        if not results:
            return results

        # Apply criteria-based filtering
        filtered_results = []

        for result in results:
            metadata = result.get("metadata", {})

            # Filter by program types if specified
            if consultant_query.criteria.get("program_types"):
                program_type = metadata.get("program_type", "")
                if not any(ptype in program_type for ptype in consultant_query.criteria["program_types"]):
                    continue

            # Filter by sectors if specified
            if consultant_query.criteria.get("sectors"):
                content = result.get("content", "").lower()
                if not any(sector in content for sector in consultant_query.criteria["sectors"]):
                    continue

            # Add consultant scoring
            result["consultant_score"] = self._calculate_consultant_score(result, consultant_query)
            filtered_results.append(result)

        # Sort by consultant score
        filtered_results.sort(key=lambda x: x.get("consultant_score", 0), reverse=True)

        return filtered_results

    def _calculate_consultant_score(
        self,
        result: Dict[str, Any],
        consultant_query: ConsultantQuery
    ) -> float:
        """Calculate consultant-level relevance score"""

        base_score = result.get("similarity", 0.0)

        # Boost score based on query type
        if consultant_query.query_type == ConsultantQueryType.DEADLINE_MONITORING:
            # Boost recent documents
            if "2024" in result.get("content", "") or "2025" in result.get("content", ""):
                base_score += 0.1

        elif consultant_query.query_type == ConsultantQueryType.BUDGET_ANALYSIS:
            # Boost documents with budget information
            content = result.get("content", "").lower()
            if any(word in content for word in ["лева", "евро", "бюджет", "финансиране"]):
                base_score += 0.1

        elif consultant_query.query_type == ConsultantQueryType.PROCEDURAL_GUIDANCE:
            # Boost documents with procedural information
            content = result.get("content", "").lower()
            if any(word in content for word in ["стъпки", "процедура", "документи", "кандидатстване"]):
                base_score += 0.1

        return min(base_score, 1.0)  # Cap at 1.0

    async def _generate_consultant_recommendations(
        self,
        results: List[Dict[str, Any]],
        consultant_query: ConsultantQuery,
        async_openai_client: Optional[AsyncOpenAI] = None
    ) -> List[ConsultantRecommendation]:
        """Generate professional consultant recommendations"""

        if not results or not async_openai_client:
            return []

        recommendations = []

        try:
            for result in results[:3]:  # Top 3 recommendations

                recommendation_prompt = f"""
Ти си експерт консултант по европейски програми. Генерирай професионална препоръка за следната програма:

ЗАЯВКА НА КЛИЕНТ: "{consultant_query.original_query}"
ПРОГРАМА: {result.get("metadata", {}).get("title", "Неизвестна програма")}
СЪДЪРЖАНИЕ: {result.get("content", "")[:1000]}...

Върни JSON с точно тази структура:
{{
    "relevance_score": 0.95,
    "eligibility_match": 0.85,
    "deadline_urgency": "висока|средна|ниска",
    "budget_fit": "отличен|добър|частичен|неподходящ",
    "recommendation_reason": "Кратко обяснение защо препоръчваш тази програма",
    "next_steps": ["Стъпка 1", "Стъпка 2", "Стъпка 3"],
    "warnings": ["Внимание 1", "Внимание 2"]
}}

ВАЖНО: Отговори САМО с валиден JSON!
"""

                response = await async_openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": recommendation_prompt}],
                    temperature=0.2,
                    max_tokens=800
                )

                rec_text = response.choices[0].message.content
                if rec_text:
                    rec_text = rec_text.strip()
                else:
                    rec_text = ""

                # Clean JSON response
                if rec_text.startswith("```json"):
                    rec_text = rec_text[7:-3]
                elif rec_text.startswith("```"):
                    rec_text = rec_text[3:-3]

                rec_data = json.loads(rec_text)

                recommendation = ConsultantRecommendation(
                    program_id=str(result.get("id", "")),
                    program_name=result.get("metadata", {}).get("title", "Неизвестна програма"),
                    relevance_score=rec_data["relevance_score"],
                    eligibility_match=rec_data["eligibility_match"],
                    deadline_urgency=rec_data["deadline_urgency"],
                    budget_fit=rec_data["budget_fit"],
                    recommendation_reason=rec_data["recommendation_reason"],
                    next_steps=rec_data["next_steps"],
                    warnings=rec_data["warnings"]
                )

                recommendations.append(recommendation)

                # Small delay to avoid rate limiting
                await asyncio.sleep(0.2)

        except Exception as e:
            logger.warning(f"Failed to generate consultant recommendations: {e}")

        return recommendations

    async def _generate_consultant_insights(
        self,
        consultant_query: ConsultantQuery,
        results: List[Dict[str, Any]],
        async_openai_client: Optional[AsyncOpenAI] = None
    ) -> Dict[str, Any]:
        """Generate high-level consultant insights"""

        if not async_openai_client or not results:
            return {
                "summary": "Намерени са резултати за вашата заявка.",
                "key_findings": [],
                "market_overview": "",
                "strategic_advice": []
            }

        try:
            # Prepare context from results
            context = "\n".join([
                f"- {r.get('metadata', {}).get('title', 'Програма')}: {r.get('content', '')[:200]}..."
                for r in results[:5]
            ])

            insights_prompt = f"""
Ти си старши консултант по европейски програми. Анализирай резултатите и дай стратегически съвети:

ЗАЯВКА: "{consultant_query.original_query}"
НАМЕРЕНИ ПРОГРАМИ:
{context}

Върни JSON с точно тази структура:
{{
    "summary": "Кратко резюме на намерените възможности",
    "key_findings": ["Ключово откритие 1", "Ключово откритие 2", "Ключово откритие 3"],
    "market_overview": "Общ преглед на пазара и възможностите",
    "strategic_advice": ["Стратегически съвет 1", "Стратегически съвет 2", "Стратегически съвет 3"]
}}

ВАЖНО: Отговори САМО с валиден JSON!
"""

            response = await async_openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": insights_prompt}],
                temperature=0.3,
                max_tokens=1000
            )

            insights_text = response.choices[0].message.content
            if insights_text:
                insights_text = insights_text.strip()
            else:
                insights_text = ""

            # Clean JSON response
            if insights_text.startswith("```json"):
                insights_text = insights_text[7:-3]
            elif insights_text.startswith("```"):
                insights_text = insights_text[3:-3]

            return json.loads(insights_text)

        except Exception as e:
            logger.warning(f"Failed to generate consultant insights: {e}")
            return {
                "summary": "Анализът на резултатите не е достъпен в момента.",
                "key_findings": [],
                "market_overview": "",
                "strategic_advice": []
            }


# Global consultant agent instance
smart_consultant = SmartConsultantAgent()
