#!/usr/bin/env python3
"""
Тест на поправката за consistency проверка
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Добавяне на src директорията към path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from production_embedding_regeneration import ProductionEmbeddingRegenerator

async def test_consistency_fix():
    """Тестване на поправената consistency проверка"""
    
    print("=== CONSISTENCY FIX TEST ===")
    
    # Зареждане на environment variables
    load_dotenv()
    
    try:
        # Създаване на regenerator
        regenerator = ProductionEmbeddingRegenerator()
        
        # Тестване на verify_consistency
        print("Стартиране на consistency проверка...")
        result = regenerator.verify_consistency()
        
        print(f"\nРезултат:")
        print(f"  Общо записи: {result.get('total_records', 'N/A')}")
        print(f"  Разпределение на размерности: {result.get('dimension_distribution', {})}")
        print(f"  Консистентност: {result.get('is_consistent', False)}")
        
        # Проверка дали всички са 1024
        dimension_dist = result.get('dimension_distribution', {})
        if len(dimension_dist) == 1 and 1024 in dimension_dist:
            print("✅ Всички embeddings са с правилна размерност 1024!")
        else:
            print("⚠️ Има embeddings с неправилни размерности")
            for dim, count in dimension_dist.items():
                if dim != 1024:
                    print(f"  - {count} записа с размерност {dim}")
        
        return result
        
    except Exception as e:
        print(f"❌ Грешка: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result = asyncio.run(test_consistency_fix())
    if result and result.get('is_consistent'):
        print("\n🎉 Consistency проверката е успешна!")
    else:
        print("\n❌ Consistency проверката неуспешна")
