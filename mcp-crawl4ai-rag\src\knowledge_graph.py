#!/usr/bin/env python3
"""
Knowledge Graph Enhancement System
Създава структурирани връзки между EU програми, организации, критерии и срокове
Оптимизирано за големи LLM модели като инструмент за заместване на консултанти
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import networkx as nx
import numpy as np
from supabase import Client

# Setup logging
logger = logging.getLogger(__name__)

@dataclass
class ProgramEntity:
    """EU програма като entity в knowledge graph"""
    id: str
    name: str
    program_type: str  # "ОПИК", "ОПРР", "ОПОС", etc.
    status: str  # "active", "closed", "upcoming"
    budget_total: Optional[float] = None
    budget_remaining: Optional[float] = None
    deadline: Optional[str] = None
    description: str = ""
    url: str = ""

@dataclass
class OrganizationEntity:
    """Организация като entity (МСП, НПО, университет, etc.)"""
    id: str
    name: str
    org_type: str  # "МСП", "НПО", "университет", "община", etc.
    size: Optional[str] = None  # "микро", "малко", "средно", "голямо"
    sector: Optional[str] = None  # "технологии", "култура", "образование", etc.
    region: Optional[str] = None  # "София", "Пловдив", "Северозападен", etc.

@dataclass
class CriteriaEntity:
    """Критерий за кандидатстване"""
    id: str
    name: str
    criteria_type: str  # "eligibility", "financial", "technical", "administrative"
    description: str
    is_mandatory: bool = True
    applies_to_programs: List[str] = None
    applies_to_orgs: List[str] = None

@dataclass
class Relationship:
    """Връзка между entities в knowledge graph"""
    source_id: str
    target_id: str
    relation_type: str  # "eligible_for", "requires", "deadline_for", "located_in", etc.
    strength: float = 1.0  # 0.0 - 1.0
    metadata: Dict[str, Any] = None

class EUProgramsKnowledgeGraph:
    """
    Knowledge Graph система за EU програми
    Оптимизирана за LLM консултиране по европейски финансиращи програми
    """

    def __init__(self, supabase_client: Client):
        self.supabase = supabase_client
        self.graph = nx.MultiDiGraph()
        self.entities = {
            'programs': {},
            'organizations': {},
            'criteria': {},
            'deadlines': {},
            'regions': {}
        }
        self.relationships = []
        self.last_updated = None

        logger.info("🔗 Knowledge Graph система инициализирана")

    async def build_knowledge_graph(self) -> Dict[str, Any]:
        """Изгражда цялостния knowledge graph от Supabase данни"""

        logger.info("🏗️ ЗАПОЧВАМ ИЗГРАЖДАНЕ НА KNOWLEDGE GRAPH")
        logger.info("=" * 60)

        start_time = time.time()

        try:
            # Стъпка 1: Извличане на програми
            programs = await self._extract_programs()
            logger.info(f"✅ Извлечени {len(programs)} програми")

            # Стъпка 2: Извличане на организации
            organizations = await self._extract_organizations()
            logger.info(f"✅ Извлечени {len(organizations)} типа организации")

            # Стъпка 3: Извличане на критерии
            criteria = await self._extract_criteria()
            logger.info(f"✅ Извлечени {len(criteria)} критерия")

            # Стъпка 4: Създаване на връзки
            relationships = await self._create_relationships()
            logger.info(f"✅ Създадени {len(relationships)} връзки")

            # Стъпка 5: Изграждане на NetworkX граф
            await self._build_networkx_graph()
            logger.info(f"✅ NetworkX граф с {self.graph.number_of_nodes()} nodes и {self.graph.number_of_edges()} edges")

            # Стъпка 6: Анализ на графа
            analysis = await self._analyze_graph()

            build_time = time.time() - start_time
            self.last_updated = datetime.now()

            logger.info("=" * 60)
            logger.info(f"🎉 KNOWLEDGE GRAPH ЗАВЪРШИ: {build_time:.2f}s")

            return {
                'status': 'success',
                'build_time': build_time,
                'entities': {
                    'programs': len(programs),
                    'organizations': len(organizations),
                    'criteria': len(criteria)
                },
                'relationships': len(relationships),
                'graph_stats': {
                    'nodes': self.graph.number_of_nodes(),
                    'edges': self.graph.number_of_edges()
                },
                'analysis': analysis,
                'last_updated': self.last_updated.isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Грешка при изграждане на knowledge graph: {e}", exc_info=True)
            return {'status': 'error', 'error': str(e)}

    async def _extract_programs(self) -> List[ProgramEntity]:
        """Извлича програми от crawled данни"""

        # Query за програми от Supabase
        response = self.supabase.table('crawled_pages').select('*').ilike('content', '%програма%').limit(100).execute()

        programs = []
        program_names = set()

        # Известни програми
        known_programs = [
            "ОПИК", "ОПРР", "ОПОС", "ОПТТП", "ОПРЧР", "ОПМДР",
            "Хоризонт Европа", "Еразъм+", "LIFE", "Creative Europe",
            "Interreg", "COSME", "Digital Europe"
        ]

        for program_name in known_programs:
            if program_name not in program_names:
                program_id = f"prog_{program_name.lower().replace(' ', '_')}"

                # Търси данни за програмата
                prog_response = self.supabase.table('crawled_pages').select('*').ilike('content', f'%{program_name}%').limit(5).execute()

                description = ""
                url = ""
                if prog_response.data:
                    description = prog_response.data[0].get('content', '')[:500]
                    url = prog_response.data[0].get('url', '')

                program = ProgramEntity(
                    id=program_id,
                    name=program_name,
                    program_type=self._classify_program_type(program_name),
                    status="active",
                    description=description,
                    url=url
                )

                programs.append(program)
                program_names.add(program_name)
                self.entities['programs'][program_id] = program

        return programs

    async def _extract_organizations(self) -> List[OrganizationEntity]:
        """Извлича типове организации"""

        organizations = []

        # Известни типове организации
        org_types = [
            {"name": "Малки и средни предприятия", "type": "МСП", "size": "малко-средно"},
            {"name": "Неправителствени организации", "type": "НПО", "size": None},
            {"name": "Университети", "type": "университет", "size": "голямо"},
            {"name": "Общини", "type": "община", "size": "голямо"},
            {"name": "Микропредприятия", "type": "микропредприятие", "size": "микро"},
            {"name": "Стартъп компании", "type": "стартъп", "size": "микро"},
            {"name": "Изследователски институти", "type": "изследователски_институт", "size": "средно"},
            {"name": "Болници", "type": "болница", "size": "голямо"},
            {"name": "Училища", "type": "училище", "size": "средно"}
        ]

        for org_data in org_types:
            org_id = f"org_{org_data['type'].lower().replace(' ', '_')}"

            organization = OrganizationEntity(
                id=org_id,
                name=org_data['name'],
                org_type=org_data['type'],
                size=org_data['size']
            )

            organizations.append(organization)
            self.entities['organizations'][org_id] = organization

        return organizations

    async def _extract_criteria(self) -> List[CriteriaEntity]:
        """Извлича критерии за кандидатстване"""

        criteria = []

        # Основни критерии
        criteria_data = [
            {"name": "Регистрация в България", "type": "eligibility", "mandatory": True},
            {"name": "Минимален оборот", "type": "financial", "mandatory": True},
            {"name": "Съфинансиране", "type": "financial", "mandatory": True},
            {"name": "Проектен опит", "type": "technical", "mandatory": False},
            {"name": "Партньорство", "type": "administrative", "mandatory": False},
            {"name": "Иновативност", "type": "technical", "mandatory": False},
            {"name": "Устойчивост", "type": "technical", "mandatory": False},
            {"name": "Дигитализация", "type": "technical", "mandatory": False}
        ]

        for crit_data in criteria_data:
            crit_id = f"crit_{crit_data['name'].lower().replace(' ', '_')}"

            criterion = CriteriaEntity(
                id=crit_id,
                name=crit_data['name'],
                criteria_type=crit_data['type'],
                description=f"Критерий за {crit_data['name']}",
                is_mandatory=crit_data['mandatory']
            )

            criteria.append(criterion)
            self.entities['criteria'][crit_id] = criterion

        return criteria

    async def _create_relationships(self) -> List[Relationship]:
        """Създава връзки между entities"""

        relationships = []

        # Връзки програма -> организация (eligibility)
        for prog_id, program in self.entities['programs'].items():
            for org_id, organization in self.entities['organizations'].items():

                # Логика за eligibility базирана на програмата
                strength = self._calculate_eligibility_strength(program, organization)

                if strength > 0.3:  # Threshold за релевантност
                    rel = Relationship(
                        source_id=org_id,
                        target_id=prog_id,
                        relation_type="eligible_for",
                        strength=strength,
                        metadata={"reason": self._get_eligibility_reason(program, organization)}
                    )
                    relationships.append(rel)

        # Връзки програма -> критерии
        for prog_id, program in self.entities['programs'].items():
            for crit_id, criterion in self.entities['criteria'].items():

                # Всички програми изискват основните критерии
                if criterion.is_mandatory:
                    strength = 1.0
                else:
                    strength = self._calculate_criteria_relevance(program, criterion)

                if strength > 0.2:
                    rel = Relationship(
                        source_id=prog_id,
                        target_id=crit_id,
                        relation_type="requires",
                        strength=strength,
                        metadata={"mandatory": criterion.is_mandatory}
                    )
                    relationships.append(rel)

        # Връзки организация -> критерии (compatibility)
        for org_id, organization in self.entities['organizations'].items():
            for crit_id, criterion in self.entities['criteria'].items():

                compatibility = self._calculate_org_criteria_compatibility(organization, criterion)

                if compatibility > 0.4:
                    rel = Relationship(
                        source_id=org_id,
                        target_id=crit_id,
                        relation_type="meets",
                        strength=compatibility,
                        metadata={"compatibility_score": compatibility}
                    )
                    relationships.append(rel)

        self.relationships = relationships
        return relationships

    def _calculate_eligibility_strength(self, program: ProgramEntity, organization: OrganizationEntity) -> float:
        """Изчислява силата на eligibility връзката"""

        strength = 0.5  # Base strength

        # ОПИК - силно за МСП
        if "ОПИК" in program.name and organization.org_type == "МСП":
            strength = 0.95
        elif "ОПИК" in program.name and organization.org_type in ["микропредприятие", "стартъп"]:
            strength = 0.85

        # ОПОС - силно за НПО, училища, болници
        elif "ОПОС" in program.name and organization.org_type in ["НПО", "училище", "болница"]:
            strength = 0.90

        # ОПРР - силно за общини
        elif "ОПРР" in program.name and organization.org_type == "община":
            strength = 0.95

        # Хоризонт Европа - силно за университети, изследователски институти
        elif "Хоризонт" in program.name and organization.org_type in ["университет", "изследователски_институт"]:
            strength = 0.90

        # Еразъм+ - силно за образователни институции
        elif "Еразъм" in program.name and organization.org_type in ["университет", "училище"]:
            strength = 0.95

        # Creative Europe - силно за НПО в културата
        elif "Creative" in program.name and organization.org_type == "НПО":
            strength = 0.80

        return min(strength, 1.0)

    def _get_eligibility_reason(self, program: ProgramEntity, organization: OrganizationEntity) -> str:
        """Връща причината за eligibility"""

        if "ОПИК" in program.name and organization.org_type == "МСП":
            return "ОПИК е специално насочена към малки и средни предприятия"
        elif "ОПОС" in program.name and organization.org_type == "НПО":
            return "ОПОС финансира социални проекти, подходящи за НПО"
        elif "ОПРР" in program.name and organization.org_type == "община":
            return "ОПРР е за регионално развитие, управлявано от общини"
        elif "Хоризонт" in program.name and organization.org_type == "университет":
            return "Хоризонт Европа е за научни изследвания и иновации"
        else:
            return "Общи критерии за eligibility"

    def _calculate_criteria_relevance(self, program: ProgramEntity, criterion: CriteriaEntity) -> float:
        """Изчислява релевантността на критерий за програма"""

        relevance = 0.3  # Base relevance

        # Дигитализация - високо за ОПИК, ОПТТП
        if "дигитализация" in criterion.name.lower():
            if any(prog in program.name for prog in ["ОПИК", "ОПТТП", "Digital"]):
                relevance = 0.9

        # Иновативност - високо за Хоризонт Европа, ОПИК
        elif "иновативност" in criterion.name.lower():
            if any(prog in program.name for prog in ["Хоризонт", "ОПИК"]):
                relevance = 0.85

        # Устойчивост - високо за LIFE, ОПОС
        elif "устойчивост" in criterion.name.lower():
            if any(prog in program.name for prog in ["LIFE", "ОПОС"]):
                relevance = 0.90

        # Партньорство - високо за Interreg, Хоризонт
        elif "партньорство" in criterion.name.lower():
            if any(prog in program.name for prog in ["Interreg", "Хоризонт"]):
                relevance = 0.85

        return min(relevance, 1.0)

    def _calculate_org_criteria_compatibility(self, organization: OrganizationEntity, criterion: CriteriaEntity) -> float:
        """Изчислява съвместимостта между организация и критерий"""

        compatibility = 0.5  # Base compatibility

        # Финансови критерии - по-лесни за големи организации
        if criterion.criteria_type == "financial":
            if organization.size == "голямо":
                compatibility = 0.8
            elif organization.size == "средно":
                compatibility = 0.6
            elif organization.size == "малко":
                compatibility = 0.4
            elif organization.size == "микро":
                compatibility = 0.2

        # Технически критерии - по-лесни за университети, изследователски институти
        elif criterion.criteria_type == "technical":
            if organization.org_type in ["университет", "изследователски_институт"]:
                compatibility = 0.9
            elif organization.org_type in ["МСП", "стартъп"]:
                compatibility = 0.7

        # Административни критерии - по-лесни за големи организации
        elif criterion.criteria_type == "administrative":
            if organization.size == "голямо":
                compatibility = 0.8
            elif organization.org_type == "община":
                compatibility = 0.9

        return min(compatibility, 1.0)

    def _classify_program_type(self, program_name: str) -> str:
        """Класифицира типа на програмата"""

        if "ОПИК" in program_name:
            return "иновации_конкурентоспособност"
        elif "ОПРР" in program_name:
            return "регионално_развитие"
        elif "ОПОС" in program_name:
            return "социално_включване"
        elif "ОПТТП" in program_name:
            return "транспорт_технологии"
        elif "ОПРЧР" in program_name:
            return "човешки_ресурси"
        elif "Хоризонт" in program_name:
            return "научни_изследвания"
        elif "Еразъм" in program_name:
            return "образование"
        elif "LIFE" in program_name:
            return "околна_среда"
        elif "Creative" in program_name:
            return "култура_медии"
        else:
            return "общо"

    async def _build_networkx_graph(self):
        """Изгражда NetworkX граф от entities и relationships"""

        # Добавяне на nodes
        for entity_type, entities in self.entities.items():
            for entity_id, entity in entities.items():
                self.graph.add_node(
                    entity_id,
                    entity_type=entity_type,
                    name=getattr(entity, 'name', entity_id),
                    data=asdict(entity)
                )

        # Добавяне на edges
        for rel in self.relationships:
            self.graph.add_edge(
                rel.source_id,
                rel.target_id,
                relation_type=rel.relation_type,
                strength=rel.strength,
                metadata=rel.metadata or {}
            )

    async def _analyze_graph(self) -> Dict[str, Any]:
        """Анализира knowledge graph за insights"""

        analysis = {}

        try:
            # Основни статистики
            analysis['basic_stats'] = {
                'total_nodes': self.graph.number_of_nodes(),
                'total_edges': self.graph.number_of_edges(),
                'density': nx.density(self.graph),
                'is_connected': nx.is_weakly_connected(self.graph)
            }

            # Най-централни nodes (PageRank)
            pagerank = nx.pagerank(self.graph)
            top_nodes = sorted(pagerank.items(), key=lambda x: x[1], reverse=True)[:10]

            analysis['most_central_entities'] = [
                {
                    'id': node_id,
                    'name': self.graph.nodes[node_id].get('name', node_id),
                    'type': self.graph.nodes[node_id].get('entity_type', 'unknown'),
                    'centrality_score': score
                }
                for node_id, score in top_nodes
            ]

            # Най-популярни програми (по входящи връзки)
            program_popularity = {}
            for node_id in self.graph.nodes():
                if self.graph.nodes[node_id].get('entity_type') == 'programs':
                    in_degree = self.graph.in_degree(node_id)
                    program_popularity[node_id] = in_degree

            top_programs = sorted(program_popularity.items(), key=lambda x: x[1], reverse=True)[:5]
            analysis['most_popular_programs'] = [
                {
                    'id': prog_id,
                    'name': self.graph.nodes[prog_id].get('name', prog_id),
                    'eligibility_connections': connections
                }
                for prog_id, connections in top_programs
            ]

            # Най-универсални организации (по изходящи връзки)
            org_versatility = {}
            for node_id in self.graph.nodes():
                if self.graph.nodes[node_id].get('entity_type') == 'organizations':
                    out_degree = self.graph.out_degree(node_id)
                    org_versatility[node_id] = out_degree

            top_orgs = sorted(org_versatility.items(), key=lambda x: x[1], reverse=True)[:5]
            analysis['most_versatile_organizations'] = [
                {
                    'id': org_id,
                    'name': self.graph.nodes[org_id].get('name', org_id),
                    'program_eligibility_count': connections
                }
                for org_id, connections in top_orgs
            ]

            # Критични критерии (най-често изисквани)
            criteria_importance = {}
            for node_id in self.graph.nodes():
                if self.graph.nodes[node_id].get('entity_type') == 'criteria':
                    in_degree = self.graph.in_degree(node_id)
                    criteria_importance[node_id] = in_degree

            top_criteria = sorted(criteria_importance.items(), key=lambda x: x[1], reverse=True)[:5]
            analysis['most_critical_criteria'] = [
                {
                    'id': crit_id,
                    'name': self.graph.nodes[crit_id].get('name', crit_id),
                    'required_by_programs': connections
                }
                for crit_id, connections in top_criteria
            ]

        except Exception as e:
            logger.warning(f"Грешка при анализ на граф: {e}")
            analysis['error'] = str(e)

        return analysis

    async def query_knowledge_graph(
        self,
        query_type: str,
        entity_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Заявка към knowledge graph за LLM консултиране

        Args:
            query_type: "eligible_programs", "program_requirements", "similar_organizations", etc.
            entity_id: ID на entity за заявката
            filters: Допълнителни филтри
        """

        try:
            if query_type == "eligible_programs":
                return await self._get_eligible_programs(entity_id, filters)
            elif query_type == "program_requirements":
                return await self._get_program_requirements(entity_id, filters)
            elif query_type == "similar_organizations":
                return await self._get_similar_organizations(entity_id, filters)
            elif query_type == "program_compatibility":
                return await self._get_program_compatibility(entity_id, filters)
            else:
                return {"error": f"Неподдържан query_type: {query_type}"}

        except Exception as e:
            logger.error(f"Грешка при заявка към knowledge graph: {e}", exc_info=True)
            return {"error": str(e)}

    async def _get_eligible_programs(self, org_id: str, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Връща програми за които организацията е eligible"""

        if org_id not in self.graph.nodes():
            return {"error": f"Организация {org_id} не е намерена"}

        eligible_programs = []

        # Намери всички програми свързани с организацията
        for target_id in self.graph.successors(org_id):
            edge_data = self.graph.get_edge_data(org_id, target_id)

            if edge_data and any(edge.get('relation_type') == 'eligible_for' for edge in edge_data.values()):
                program_data = self.graph.nodes[target_id].get('data', {})

                # Вземи най-силната връзка
                max_strength = max(edge.get('strength', 0) for edge in edge_data.values())

                eligible_programs.append({
                    'program_id': target_id,
                    'program_name': program_data.get('name', target_id),
                    'program_type': program_data.get('program_type', 'unknown'),
                    'eligibility_strength': max_strength,
                    'status': program_data.get('status', 'unknown'),
                    'deadline': program_data.get('deadline'),
                    'url': program_data.get('url', '')
                })

        # Сортирай по eligibility strength
        eligible_programs.sort(key=lambda x: x['eligibility_strength'], reverse=True)

        # Прилагай филтри ако има
        if filters:
            if 'min_strength' in filters:
                eligible_programs = [p for p in eligible_programs if p['eligibility_strength'] >= filters['min_strength']]
            if 'program_type' in filters:
                eligible_programs = [p for p in eligible_programs if p['program_type'] == filters['program_type']]
            if 'status' in filters:
                eligible_programs = [p for p in eligible_programs if p['status'] == filters['status']]

        return {
            'organization_id': org_id,
            'organization_name': self.graph.nodes[org_id].get('name', org_id),
            'eligible_programs': eligible_programs,
            'total_programs': len(eligible_programs)
        }

    async def _get_program_requirements(self, program_id: str, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Връща изискванията за дадена програма"""

        if program_id not in self.graph.nodes():
            return {"error": f"Програма {program_id} не е намерена"}

        requirements = []

        # Намери всички критерии изисквани от програмата
        for target_id in self.graph.successors(program_id):
            edge_data = self.graph.get_edge_data(program_id, target_id)

            if edge_data and any(edge.get('relation_type') == 'requires' for edge in edge_data.values()):
                criteria_data = self.graph.nodes[target_id].get('data', {})

                # Вземи най-силната връзка
                max_strength = max(edge.get('strength', 0) for edge in edge_data.values())
                is_mandatory = any(edge.get('metadata', {}).get('mandatory', False) for edge in edge_data.values())

                requirements.append({
                    'criteria_id': target_id,
                    'criteria_name': criteria_data.get('name', target_id),
                    'criteria_type': criteria_data.get('criteria_type', 'unknown'),
                    'description': criteria_data.get('description', ''),
                    'is_mandatory': is_mandatory,
                    'importance': max_strength
                })

        # Сортирай по важност (mandatory първо, после по strength)
        requirements.sort(key=lambda x: (not x['is_mandatory'], -x['importance']))

        return {
            'program_id': program_id,
            'program_name': self.graph.nodes[program_id].get('name', program_id),
            'requirements': requirements,
            'total_requirements': len(requirements),
            'mandatory_count': sum(1 for r in requirements if r['is_mandatory']),
            'optional_count': sum(1 for r in requirements if not r['is_mandatory'])
        }

    async def _get_similar_organizations(self, org_id: str, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Намира подобни организации базирано на program eligibility"""

        if org_id not in self.graph.nodes():
            return {"error": f"Организация {org_id} не е намерена"}

        # Вземи програмите за които тази организация е eligible
        org_programs = set()
        for target_id in self.graph.successors(org_id):
            edge_data = self.graph.get_edge_data(org_id, target_id)
            if edge_data and any(edge.get('relation_type') == 'eligible_for' for edge in edge_data.values()):
                org_programs.add(target_id)

        # Намери други организации с припокриващи се програми
        similar_orgs = []

        for node_id in self.graph.nodes():
            if (self.graph.nodes[node_id].get('entity_type') == 'organizations' and
                node_id != org_id):

                # Вземи програмите за тази организация
                other_programs = set()
                for target_id in self.graph.successors(node_id):
                    edge_data = self.graph.get_edge_data(node_id, target_id)
                    if edge_data and any(edge.get('relation_type') == 'eligible_for' for edge in edge_data.values()):
                        other_programs.add(target_id)

                # Изчисли similarity (Jaccard coefficient)
                if other_programs:
                    intersection = len(org_programs.intersection(other_programs))
                    union = len(org_programs.union(other_programs))
                    similarity = intersection / union if union > 0 else 0

                    if similarity > 0.1:  # Threshold за similarity
                        org_data = self.graph.nodes[node_id].get('data', {})
                        similar_orgs.append({
                            'organization_id': node_id,
                            'organization_name': org_data.get('name', node_id),
                            'organization_type': org_data.get('org_type', 'unknown'),
                            'similarity_score': similarity,
                            'common_programs': intersection,
                            'total_programs': len(other_programs)
                        })

        # Сортирай по similarity
        similar_orgs.sort(key=lambda x: x['similarity_score'], reverse=True)

        return {
            'organization_id': org_id,
            'organization_name': self.graph.nodes[org_id].get('name', org_id),
            'similar_organizations': similar_orgs[:10],  # Top 10
            'total_similar': len(similar_orgs)
        }

    async def _get_program_compatibility(self, org_id: str, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Анализира съвместимостта между организация и програми"""

        if org_id not in self.graph.nodes():
            return {"error": f"Организация {org_id} не е намерена"}

        compatibility_analysis = []

        # За всяка програма, анализирай съвместимостта
        for node_id in self.graph.nodes():
            if self.graph.nodes[node_id].get('entity_type') == 'programs':

                # Провери дали има директна eligible_for връзка
                direct_eligibility = 0.0
                if self.graph.has_edge(org_id, node_id):
                    edge_data = self.graph.get_edge_data(org_id, node_id)
                    for edge in edge_data.values():
                        if edge.get('relation_type') == 'eligible_for':
                            direct_eligibility = max(direct_eligibility, edge.get('strength', 0))

                # Анализирай criteria compatibility
                program_requirements = []
                for target_id in self.graph.successors(node_id):
                    edge_data = self.graph.get_edge_data(node_id, target_id)
                    if edge_data and any(edge.get('relation_type') == 'requires' for edge in edge_data.values()):

                        # Провери дали организацията meets този критерий
                        org_meets_criteria = 0.0
                        if self.graph.has_edge(org_id, target_id):
                            meets_edge_data = self.graph.get_edge_data(org_id, target_id)
                            for edge in meets_edge_data.values():
                                if edge.get('relation_type') == 'meets':
                                    org_meets_criteria = max(org_meets_criteria, edge.get('strength', 0))

                        criteria_data = self.graph.nodes[target_id].get('data', {})
                        is_mandatory = any(edge.get('metadata', {}).get('mandatory', False) for edge in edge_data.values())

                        program_requirements.append({
                            'criteria_name': criteria_data.get('name', target_id),
                            'is_mandatory': is_mandatory,
                            'organization_compatibility': org_meets_criteria
                        })

                # Изчисли общ compatibility score
                if program_requirements:
                    mandatory_reqs = [r for r in program_requirements if r['is_mandatory']]
                    optional_reqs = [r for r in program_requirements if not r['is_mandatory']]

                    # Mandatory requirements трябва да са > 0.5
                    mandatory_score = np.mean([r['organization_compatibility'] for r in mandatory_reqs]) if mandatory_reqs else 1.0
                    optional_score = np.mean([r['organization_compatibility'] for r in optional_reqs]) if optional_reqs else 0.5

                    overall_compatibility = (mandatory_score * 0.7 + optional_score * 0.3 + direct_eligibility * 0.2) / 1.2
                else:
                    overall_compatibility = direct_eligibility

                program_data = self.graph.nodes[node_id].get('data', {})
                compatibility_analysis.append({
                    'program_id': node_id,
                    'program_name': program_data.get('name', node_id),
                    'program_type': program_data.get('program_type', 'unknown'),
                    'overall_compatibility': overall_compatibility,
                    'direct_eligibility': direct_eligibility,
                    'mandatory_requirements_met': len([r for r in program_requirements if r['is_mandatory'] and r['organization_compatibility'] > 0.5]),
                    'total_mandatory_requirements': len([r for r in program_requirements if r['is_mandatory']]),
                    'optional_requirements_met': len([r for r in program_requirements if not r['is_mandatory'] and r['organization_compatibility'] > 0.5]),
                    'total_optional_requirements': len([r for r in program_requirements if not r['is_mandatory']])
                })

        # Сортирай по compatibility
        compatibility_analysis.sort(key=lambda x: x['overall_compatibility'], reverse=True)

        return {
            'organization_id': org_id,
            'organization_name': self.graph.nodes[org_id].get('name', org_id),
            'program_compatibility': compatibility_analysis,
            'total_programs_analyzed': len(compatibility_analysis),
            'highly_compatible_programs': len([p for p in compatibility_analysis if p['overall_compatibility'] > 0.7]),
            'moderately_compatible_programs': len([p for p in compatibility_analysis if 0.4 <= p['overall_compatibility'] <= 0.7]),
            'low_compatible_programs': len([p for p in compatibility_analysis if p['overall_compatibility'] < 0.4])
        }

# Temporal Knowledge Graph Extension
class TemporalKnowledgeGraph(EUProgramsKnowledgeGraph):
    """
    Разширение с temporal awareness за deadlines и времеви връзки
    """

    def __init__(self, supabase_client: Client):
        super().__init__(supabase_client)
        self.temporal_entities = {}
        self.deadline_alerts = []

    async def add_temporal_entities(self):
        """Добавя temporal entities за deadlines"""

        # Примерни deadlines
        deadlines = [
            {"program": "ОПИК", "deadline": "2024-12-31", "type": "final_deadline"},
            {"program": "ОПРР", "deadline": "2024-11-15", "type": "application_deadline"},
            {"program": "Хоризонт Европа", "deadline": "2024-10-30", "type": "proposal_deadline"},
            {"program": "Еразъм+", "deadline": "2024-09-15", "type": "application_deadline"}
        ]

        for deadline_data in deadlines:
            deadline_id = f"deadline_{deadline_data['program'].lower().replace(' ', '_')}_{deadline_data['type']}"

            self.temporal_entities[deadline_id] = {
                'id': deadline_id,
                'program': deadline_data['program'],
                'deadline_date': deadline_data['deadline'],
                'deadline_type': deadline_data['type'],
                'days_remaining': self._calculate_days_remaining(deadline_data['deadline'])
            }

            # Добави като node в графа
            self.graph.add_node(
                deadline_id,
                entity_type='deadline',
                deadline_date=deadline_data['deadline'],
                deadline_type=deadline_data['type'],
                days_remaining=self._calculate_days_remaining(deadline_data['deadline'])
            )

            # Създай връзка програма -> deadline
            program_id = f"prog_{deadline_data['program'].lower().replace(' ', '_')}"
            if program_id in self.graph.nodes():
                self.graph.add_edge(
                    program_id,
                    deadline_id,
                    relation_type="has_deadline",
                    urgency=self._calculate_urgency(deadline_data['deadline'])
                )

    def _calculate_days_remaining(self, deadline_str: str) -> int:
        """Изчислява оставащи дни до deadline"""
        try:
            deadline = datetime.strptime(deadline_str, "%Y-%m-%d")
            today = datetime.now()
            return (deadline - today).days
        except:
            return 0

    def _calculate_urgency(self, deadline_str: str) -> float:
        """Изчислява urgency score базирано на deadline"""
        days_remaining = self._calculate_days_remaining(deadline_str)

        if days_remaining <= 7:
            return 1.0  # Критично
        elif days_remaining <= 30:
            return 0.8  # Високо
        elif days_remaining <= 90:
            return 0.6  # Средно
        else:
            return 0.3  # Ниско

    async def get_deadline_alerts(self, days_threshold: int = 30) -> List[Dict[str, Any]]:
        """Връща deadline alerts за програми"""

        alerts = []

        for deadline_id, deadline_data in self.temporal_entities.items():
            days_remaining = deadline_data['days_remaining']

            if 0 <= days_remaining <= days_threshold:
                alerts.append({
                    'program': deadline_data['program'],
                    'deadline_date': deadline_data['deadline_date'],
                    'deadline_type': deadline_data['deadline_type'],
                    'days_remaining': days_remaining,
                    'urgency': self._calculate_urgency(deadline_data['deadline_date']),
                    'alert_level': self._get_alert_level(days_remaining)
                })

        # Сортирай по urgency
        alerts.sort(key=lambda x: x['days_remaining'])

        return alerts

    def _get_alert_level(self, days_remaining: int) -> str:
        """Определя alert level"""
        if days_remaining <= 3:
            return "КРИТИЧНО"
        elif days_remaining <= 7:
            return "СПЕШНО"
        elif days_remaining <= 14:
            return "ВАЖНО"
        else:
            return "ИНФОРМАЦИЯ"