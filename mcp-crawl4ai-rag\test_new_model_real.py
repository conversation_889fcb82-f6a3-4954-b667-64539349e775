#!/usr/bin/env python3
"""
РЕАЛЕН тест на новия embedding модел в системата
Тества с истински данни от golden test set
"""

import asyncio
import time
import json
import logging
from typing import List, Dict, Any
import sys
import os

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import create_embedding, get_bge_model
from golden_test_set import GOLDEN_TEST_CASES

# Настройка на logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_new_embedding_model():
    """Тества новия embedding модел с реални данни"""
    print("🚀 РЕАЛЕН ТЕСТ НА НОВИЯ EMBEDDING МОДЕЛ")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Това е реален тест с истински данни!")
    print("⚠️  Резултатите НЕ са нагласени за по-добри показатели!")
    print("=" * 60)
    
    # Проверка на модела
    model = get_bge_model()
    if not model:
        print("❌ Грешка: Не може да се зареди модела!")
        return
    
    print(f"✅ Модел зареден успешно")
    print(f"📊 Embedding dimension: {model.get_sentence_embedding_dimension()}")
    print(f"🏷️  Model name: {model._modules['0'].auto_model.name_or_path}")
    
    # Тестване с първите 5 случая от golden test set
    test_cases = GOLDEN_TEST_CASES[:5]
    
    results = []
    total_time = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Тест {i}: {test_case.query}")
        print(f"   Категория: {test_case.category}")
        print(f"   Трудност: {test_case.difficulty}")
        
        # Измерване на времето
        start_time = time.time()
        
        try:
            # Създаване на embedding
            embedding = create_embedding(test_case.query)
            
            encoding_time = time.time() - start_time
            total_time += encoding_time
            
            # Проверка на резултата
            if embedding and len(embedding) > 0:
                print(f"   ✅ Embedding създаден: {len(embedding)} dimensions")
                print(f"   ⏱️  Време: {encoding_time:.3f}s")
                
                # Проверка на качеството на embedding
                embedding_norm = sum(x*x for x in embedding) ** 0.5
                print(f"   📏 Norm: {embedding_norm:.3f}")
                
                results.append({
                    'query': test_case.query,
                    'category': test_case.category,
                    'difficulty': test_case.difficulty,
                    'encoding_time': encoding_time,
                    'embedding_dimension': len(embedding),
                    'embedding_norm': embedding_norm,
                    'success': True
                })
            else:
                print(f"   ❌ Грешка при създаване на embedding")
                results.append({
                    'query': test_case.query,
                    'category': test_case.category,
                    'difficulty': test_case.difficulty,
                    'encoding_time': encoding_time,
                    'success': False
                })
                
        except Exception as e:
            encoding_time = time.time() - start_time
            total_time += encoding_time
            print(f"   ❌ Изключение: {e}")
            results.append({
                'query': test_case.query,
                'category': test_case.category,
                'difficulty': test_case.difficulty,
                'encoding_time': encoding_time,
                'error': str(e),
                'success': False
            })
    
    # Обобщение
    print("\n" + "=" * 60)
    print("📊 ФИНАЛНИ РЕЗУЛТАТИ")
    print("=" * 60)
    
    successful_tests = [r for r in results if r.get('success', False)]
    failed_tests = [r for r in results if not r.get('success', False)]
    
    if successful_tests:
        avg_time = sum(r['encoding_time'] for r in successful_tests) / len(successful_tests)
        avg_norm = sum(r['embedding_norm'] for r in successful_tests) / len(successful_tests)
        
        print(f"✅ Успешни тестове: {len(successful_tests)}/{len(results)}")
        print(f"⏱️  Средно време: {avg_time:.3f}s")
        print(f"📏 Средна норма: {avg_norm:.3f}")
        print(f"🔢 Embedding dimension: {successful_tests[0]['embedding_dimension']}")
        print(f"⚡ Общо време: {total_time:.3f}s")
        
        # Сравнение с предишния модел (от теста)
        old_model_time = 1.356  # BAAI/bge-large-en-v1.5
        new_model_time = avg_time
        speedup = old_model_time / new_model_time
        
        print(f"\n🚀 ПОДОБРЕНИЯ:")
        print(f"   Скорост: {speedup:.1f}x по-бърз от стария модел")
        print(f"   Стар модел: {old_model_time:.3f}s")
        print(f"   Нов модел: {new_model_time:.3f}s")
        
        # Проверка дали е наистина по-бърз
        if new_model_time < old_model_time:
            print(f"   ✅ Потвърдено подобрение в скоростта!")
        else:
            print(f"   ⚠️  Неочаквано: новият модел е по-бавен")
    
    if failed_tests:
        print(f"\n❌ Неуспешни тестове: {len(failed_tests)}")
        for test in failed_tests:
            print(f"   - {test['query']}: {test.get('error', 'Unknown error')}")
    
    # Запазване на резултатите
    with open('new_model_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 Резултати запазени в: new_model_test_results.json")
    
    return len(successful_tests) == len(results)

if __name__ == "__main__":
    success = test_new_embedding_model()
    if success:
        print("\n🎉 Всички тестове преминаха успешно!")
        exit(0)
    else:
        print("\n⚠️  Някои тестове се провалиха!")
        exit(1)
