#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 Phase 8.6.1: Keyword Validation Layer Testing
Test the new keyword validation system for improved precision.
"""

import asyncio
import logging
import time
from typing import List, Dict, Any
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import (
    search_crawled_pages_advanced_query,
    extract_bulgarian_keywords,
    calculate_keyword_overlap,
    validate_results_with_keywords,
    calculate_enhanced_score
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_phase_8_6_1.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Test queries with expected keywords
TEST_QUERIES = [
    {
        "query": "програми за малки и средни предприятия",
        "expected_keywords": ["малки", "средни", "предприятия", "мсп", "бизнес", "стартъп"],
        "description": "МСП програми"
    },
    {
        "query": "иновации и технологично развитие",
        "expected_keywords": ["иновации", "технологично", "развитие", "r&d", "изследвания"],
        "description": "Иновационни програми"
    },
    {
        "query": "околна среда и зелена енергия",
        "expected_keywords": ["околна", "среда", "зелена", "енергия", "възобновяеми", "екология"],
        "description": "Екологични програми"
    },
    {
        "query": "образование и обучение",
        "expected_keywords": ["образование", "обучение", "квалификация", "умения", "компетентности"],
        "description": "Образователни програми"
    }
]

async def test_keyword_extraction():
    """Test keyword extraction functionality"""
    logger.info("🔍 Testing keyword extraction...")
    
    for test_case in TEST_QUERIES:
        query = test_case["query"]
        expected = test_case["expected_keywords"]
        
        extracted = extract_bulgarian_keywords(query)
        
        logger.info(f"Query: '{query}'")
        logger.info(f"Extracted: {extracted}")
        logger.info(f"Expected: {expected}")
        
        # Check overlap
        overlap = len(set(extracted) & set(expected))
        logger.info(f"Overlap: {overlap}/{len(expected)} keywords")
        logger.info("---")

async def test_keyword_validation_search():
    """Test search with keyword validation"""
    logger.info("🚀 Testing Phase 8.6.1 keyword validation search...")
    
    results = []
    
    for test_case in TEST_QUERIES:
        query = test_case["query"]
        description = test_case["description"]
        expected_keywords = test_case["expected_keywords"]
        
        logger.info(f"\n🔍 Testing: {description}")
        logger.info(f"Query: '{query}'")
        
        start_time = time.time()
        
        try:
            # Search with keyword validation
            search_results = await search_crawled_pages_advanced_query(
                query,
                match_count=5,
                use_bulgarian_embeddings=True,
                rerank_top_k=15,
                enable_query_expansion=True,
                enable_hyde=True,
                enable_multi_query=True
            )
            
            search_time = time.time() - start_time
            
            # Analyze results
            if search_results:
                logger.info(f"✅ Found {len(search_results)} results in {search_time:.2f}s")
                
                # Check keyword scores
                keyword_scores = []
                for i, result in enumerate(search_results):
                    keyword_score = result.get('keyword_score', 0.0)
                    final_score = result.get('final_score', 0.0)
                    content_preview = result.get('content', '')[:100] + '...'
                    
                    keyword_scores.append(keyword_score)
                    
                    logger.info(f"  Result {i+1}:")
                    logger.info(f"    Keyword Score: {keyword_score:.3f}")
                    logger.info(f"    Final Score: {final_score:.3f}")
                    logger.info(f"    Content: {content_preview}")
                
                avg_keyword_score = sum(keyword_scores) / len(keyword_scores)
                min_keyword_score = min(keyword_scores)
                max_keyword_score = max(keyword_scores)
                
                # Calculate success metrics
                high_quality_results = sum(1 for score in keyword_scores if score >= 0.3)
                success_rate = (high_quality_results / len(search_results)) * 100
                
                results.append({
                    'query': query,
                    'description': description,
                    'results_count': len(search_results),
                    'avg_keyword_score': avg_keyword_score,
                    'min_keyword_score': min_keyword_score,
                    'max_keyword_score': max_keyword_score,
                    'high_quality_count': high_quality_results,
                    'success_rate': success_rate,
                    'search_time': search_time
                })
                
                logger.info(f"  📊 Keyword Score Stats:")
                logger.info(f"    Average: {avg_keyword_score:.3f}")
                logger.info(f"    Range: {min_keyword_score:.3f} - {max_keyword_score:.3f}")
                logger.info(f"    High Quality (≥0.3): {high_quality_results}/{len(search_results)} ({success_rate:.1f}%)")
                
            else:
                logger.warning(f"❌ No results found for '{query}'")
                results.append({
                    'query': query,
                    'description': description,
                    'results_count': 0,
                    'success_rate': 0,
                    'search_time': search_time
                })
                
        except Exception as e:
            logger.error(f"❌ Error testing '{query}': {e}")
            results.append({
                'query': query,
                'description': description,
                'error': str(e),
                'success_rate': 0
            })
    
    return results

async def test_enhanced_scoring():
    """Test enhanced scoring algorithm"""
    logger.info("🎯 Testing enhanced scoring algorithm...")
    
    # Mock test data
    test_query = "програми за малки и средни предприятия"
    test_results = [
        {
            'content': 'Програма за подкрепа на малки и средни предприятия в България. МСП могат да кандидатстват за финансиране.',
            'similarity': 0.8
        },
        {
            'content': 'Европейски фондове за развитие на икономиката. Различни програми за бизнес развитие.',
            'similarity': 0.7
        },
        {
            'content': 'Програма за иновации и технологично развитие. Подкрепа за стартъп компании и предприемачество.',
            'similarity': 0.6
        }
    ]
    
    logger.info(f"Test query: '{test_query}'")
    
    for i, result in enumerate(test_results):
        content = result['content']
        similarity = result['similarity']
        
        # Calculate keyword overlap
        keyword_score = calculate_keyword_overlap(
            extract_bulgarian_keywords(test_query),
            content
        )
        
        # Calculate enhanced score
        enhanced_score = calculate_enhanced_score(
            test_query,
            {'content': content, 'keyword_score': keyword_score},
            cross_encoder_score=similarity,
            bm25_score=0.5
        )
        
        logger.info(f"Result {i+1}:")
        logger.info(f"  Content: {content[:80]}...")
        logger.info(f"  Original similarity: {similarity:.3f}")
        logger.info(f"  Keyword score: {keyword_score:.3f}")
        logger.info(f"  Enhanced score: {enhanced_score:.3f}")
        logger.info("---")

async def generate_summary_report(results: List[Dict[str, Any]]):
    """Generate summary report for Phase 8.6.1"""
    logger.info("\n" + "="*80)
    logger.info("📊 PHASE 8.6.1 KEYWORD VALIDATION - SUMMARY REPORT")
    logger.info("="*80)
    
    if not results:
        logger.warning("❌ No results to analyze")
        return
    
    # Calculate overall metrics
    total_queries = len(results)
    successful_queries = sum(1 for r in results if r.get('results_count', 0) > 0)
    avg_success_rate = sum(r.get('success_rate', 0) for r in results) / total_queries
    avg_search_time = sum(r.get('search_time', 0) for r in results if 'search_time' in r) / total_queries
    
    # Keyword score statistics
    keyword_scores = []
    for r in results:
        if 'avg_keyword_score' in r:
            keyword_scores.append(r['avg_keyword_score'])
    
    avg_keyword_score = sum(keyword_scores) / len(keyword_scores) if keyword_scores else 0
    
    logger.info(f"🎯 OVERALL PERFORMANCE:")
    logger.info(f"   Successful Queries: {successful_queries}/{total_queries} ({(successful_queries/total_queries)*100:.1f}%)")
    logger.info(f"   Average Success Rate: {avg_success_rate:.1f}%")
    logger.info(f"   Average Keyword Score: {avg_keyword_score:.3f}")
    logger.info(f"   Average Search Time: {avg_search_time:.2f}s")
    
    logger.info(f"\n📋 DETAILED RESULTS:")
    for r in results:
        query = r['query']
        description = r['description']
        success_rate = r.get('success_rate', 0)
        keyword_score = r.get('avg_keyword_score', 0)
        
        logger.info(f"   {description}: {success_rate:.1f}% success, {keyword_score:.3f} keyword score")
    
    # Improvement assessment
    if avg_keyword_score >= 0.25:
        logger.info(f"\n✅ PHASE 8.6.1 SUCCESS: Keyword validation shows good precision!")
    elif avg_keyword_score >= 0.15:
        logger.info(f"\n⚠️ PHASE 8.6.1 PARTIAL: Keyword validation shows moderate improvement")
    else:
        logger.info(f"\n❌ PHASE 8.6.1 NEEDS WORK: Keyword validation needs further optimization")

async def main():
    """Main test function"""
    logger.info("🚀 Starting Phase 8.6.1 Keyword Validation Testing")
    
    # Test 1: Keyword extraction
    await test_keyword_extraction()
    
    # Test 2: Enhanced scoring
    await test_enhanced_scoring()
    
    # Test 3: Full search with keyword validation
    results = await test_keyword_validation_search()
    
    # Test 4: Generate summary
    await generate_summary_report(results)
    
    logger.info("✅ Phase 8.6.1 testing completed!")

if __name__ == "__main__":
    asyncio.run(main())
