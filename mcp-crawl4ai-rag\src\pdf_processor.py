import sys
import os

# Добавяме родителската директория (коренната на проекта) към пътя за импорти,
# за да може да намерим utils.py. Това е необходимо, когато стартираме
# src/pdf_processor.py директно.
# Ако utils.py е в същата директория 'src', това не е нужно.
# Тъй като utils.py е в коренната директория, трябва да направим това:
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from utils import extract_text_from_pdf_url
except ImportError as e:
    print(f"Грешка при импорт на extract_text_from_pdf_url от utils: {e}")
    print("Уверете се, че utils.py се намира в коренната директория на проекта.")
    sys.exit(1)

# --- КОНФИГУРАЦИЯ за самостоятелно тестване ---
DEFAULT_PDF_URL_FOR_TESTING = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"
# Можеш да добавиш и други URL-и за тестване тук:
# DEFAULT_PDF_URL_FOR_TESTING = "https://www.africau.edu/images/default/sample.pdf"
# DEFAULT_PDF_URL_FOR_TESTING = "https://адрес_на_невалиден_или_не_pdf_файл.com/file.zip"
# DEFAULT_PDF_URL_FOR_TESTING = "https://www.clickdimensions.com/links/TestPDFfile.pdf" # Този, който връщаше HTML

# --- КРАЙ НА КОНФИГУРАЦИЯ ---

def test_pdf_extraction_from_utils(url_to_test: str):
    """
    Тестова функция, която извиква extract_text_from_pdf_url от utils.py
    и отпечатва резултата.
    """
    print(f"\n--- Тестване на извличане на текст от PDF чрез utils.extract_text_from_pdf_url: {url_to_test} ---")
    
    # Извикваме функцията, която вече е в utils.py
    text, error = extract_text_from_pdf_url(url_to_test) 
    
    if error:
        print("\n--- Грешка при обработка ---")
        print(error)
    else:
        print("\n--- Извлечен текст (първите 1000 символа) ---")
        print(text[:1000] if text else "Няма извлечен текст или текстът е празен.")
        print("--- Край на извлечения текст ---")
        
if __name__ == "__main__":
    print("--- Стартиране на src/pdf_processor.py (тест за функцията от utils.py) ---")
    
    # Можеш да подадеш URL като аргумент от командния ред
    pdf_url_to_use = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_PDF_URL_FOR_TESTING
    
    test_pdf_extraction_from_utils(pdf_url_to_use)
    
    print("\n--- src/pdf_processor.py завърши изпълнението си ---")