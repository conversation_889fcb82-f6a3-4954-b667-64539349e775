#!/usr/bin/env python3
"""
Test Knowledge Graph integration specifically
"""
import sys
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('../.env')

from knowledge_graph_integration import enhance_rag_with_knowledge_graph
from utils import get_supabase_client

async def test_knowledge_graph():
    print("🧪 Testing Knowledge Graph integration...")
    
    supabase = get_supabase_client()
    
    # Test query about EU programs
    query = "Кои са програмите за иновации в предприятията?"
    
    print(f"📝 Query: {query}")
    
    # Test Knowledge Graph enhancement
    enhanced_results = await enhance_rag_with_knowledge_graph(
        query=query,
        original_results=[]  # Empty initial results to test KG standalone
    )
    
    print(f"✅ Knowledge Graph returned: {type(enhanced_results)}")

    # Handle KnowledgeGraphRAGResult object
    if hasattr(enhanced_results, 'enhanced_results'):
        results = enhanced_results.enhanced_results
        print(f"✅ Enhanced results count: {len(results)}")

        if results:
            first_result = results[0]
            print(f"✅ First KG result: {first_result.get('content', '')[:200]}...")
            print(f"✅ Source: {first_result.get('source', 'Unknown')}")
            print(f"✅ Score: {first_result.get('score', 'N/A')}")
    else:
        print(f"✅ Enhanced results object: {enhanced_results}")
        if hasattr(enhanced_results, '__dict__'):
            print(f"✅ Object attributes: {list(enhanced_results.__dict__.keys())}")
    
    print("\n🎉 Knowledge Graph working correctly!")

if __name__ == "__main__":
    asyncio.run(test_knowledge_graph())
