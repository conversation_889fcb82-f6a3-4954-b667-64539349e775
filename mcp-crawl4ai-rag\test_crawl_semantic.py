#!/usr/bin/env python3
"""
Тест за проверка на semantic chunking в crawl функцията
"""

import asyncio
import logging
from src.crawl4ai_mcp import get_supabase_client
from src.utils import create_semantic_chunks

# Настройка на logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_crawl_with_semantic_chunking():
    """Тест на crawl функцията с новия semantic chunking"""
    
    print("🧪 Тестване на Crawl с Semantic Chunking...")
    print("=" * 60)
    
    # Тестов HTML съдържание
    test_html = """
    <html>
    <head><title>Тестова страница за финансиране</title></head>
    <body>
        <h1>Програми за финансиране на МСП</h1>
        <p>Малките и средните предприятия (МСП) могат да кандидатстват за различни програми за финансиране.</p>
        
        <h2>Програма "Иновации и конкурентоспособност"</h2>
        <p>Тази програма предоставя безвъзмездна финансова помощ за иновативни проекти. 
        Максималният размер на помощта е 200,000 лева за проект.</p>
        
        <h2>Програма за развитие на селските райони</h2>
        <p>Програмата подкрепя развитието на селските райони чрез различни мерки. 
        Общият бюджет е 2.37 милиарда евро за периода 2014-2020 г.</p>
        
        <h3>Критерии за кандидатстване</h3>
        <ul>
            <li>Регистрирано предприятие в България</li>
            <li>Минимум 2 години дейност</li>
            <li>Годишен оборот под 50 милиона евро</li>
        </ul>
    </body>
    </html>
    """
    
    # Симулиране на markdown извличане
    from markdownify import markdownify as md
    markdown_content = md(test_html).strip()
    
    print(f"📝 Markdown съдържание: {len(markdown_content)} символа")
    print(f"📊 Брой думи: {len(markdown_content.split())}")
    
    # Тест на semantic chunking
    print(f"\n🔍 Тестване на semantic chunking...")
    print("-" * 40)
    
    try:
        chunks = create_semantic_chunks(
            text=markdown_content,
            max_tokens=256,
            min_tokens=64,
            overlap_ratio=0.1
        )
        
        print(f"📊 Брой chunks: {len(chunks)}")
        
        if chunks:
            for i, chunk in enumerate(chunks):
                print(f"\n📄 Chunk {i+1}:")
                print(f"   Tokens: {chunk.get('tokens', 0)}")
                print(f"   Sentences: {chunk.get('sentences', 0)}")
                print(f"   Content: {chunk.get('content', '')[:150]}...")
                
                # Проверка за metadata
                metadata = chunk.get('metadata', {})
                if metadata:
                    print(f"   Has numbers: {metadata.get('has_numbers', False)}")
                    print(f"   Has dates: {metadata.get('has_dates', False)}")
                    print(f"   Has currency: {metadata.get('has_currency', False)}")
                    print(f"   Section type: {metadata.get('section_type', 'unknown')}")
                    print(f"   Content density: {metadata.get('content_density', 0):.2f}")
                    print(f"   Program indicators: {metadata.get('program_indicators', [])}")
                    print(f"   Topic indicators: {metadata.get('topic_indicators', [])}")
            
            print("✅ УСПЕХ: Semantic chunking работи правилно!")
        else:
            print("❌ ГРЕШКА: Няма генерирани chunks")
            
    except Exception as e:
        print(f"❌ ГРЕШКА: {e}")
        logger.error(f"Грешка при semantic chunking: {e}", exc_info=True)
    
    print("\n" + "=" * 60)
    print("🏁 Тестването завърши!")

if __name__ == "__main__":
    asyncio.run(test_crawl_with_semantic_chunking())
