#!/usr/bin/env python3
"""
РЕАЛЕН ТЕСТ НА RAG ФУНКЦИОНАЛНОСТТА
===================================

Този тест проверява реалната функционалност на RAG системата с:
1. Реални български заявки за европейски фондове
2. Тестване на wrapper функцията search_documents_with_text
3. Измерване на качеството на резултатите
4. Проверка на всички MCP tools
"""

import os
import sys
import asyncio
import json
import time
from typing import List, Dict, Any
from dotenv import load_dotenv

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from supabase import create_client, Client
from src.utils import search_documents_with_text, create_embedding

def test_supabase_connection() -> Client:
    """Test Supabase connection and return client"""
    print('🔗 Тестване на Supabase връзката...')
    
    # Load environment variables
    load_dotenv()
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_key:
        raise ValueError("Missing Supabase credentials in .env file")
    
    # Create client
    supabase: Client = create_client(supabase_url, supabase_key)
    
    # Test connection
    result = supabase.table('crawled_pages').select('id').limit(1).execute()
    if not result.data:
        raise ValueError("Cannot connect to Supabase or no data available")
    
    count_result = supabase.table('crawled_pages').select('id', count='exact').execute()
    total_pages = count_result.count
    
    print(f'✅ Supabase връзка успешна - {total_pages} страници в базата')
    return supabase


def test_embedding_generation():
    """Test embedding generation functionality"""
    print('\n🧠 Тестване на генериране на embeddings...')
    
    test_texts = [
        "програми за иновации в малки и средни предприятия",
        "финансиране за стартъп компании в България",
        "европейски фондове за дигитализация"
    ]
    
    for text in test_texts:
        start_time = time.time()
        embedding = create_embedding(text)
        end_time = time.time()
        
        if embedding and len(embedding) > 0 and not all(val == 0.0 for val in embedding):
            print(f'   ✅ "{text[:30]}..." -> {len(embedding)}D embedding ({end_time-start_time:.2f}s)')
        else:
            print(f'   ❌ "{text[:30]}..." -> Failed to generate embedding')
            return False
    
    return True


def test_wrapper_function(supabase: Client):
    """Test the new search_documents_with_text wrapper function"""
    print('\n🔍 Тестване на wrapper функцията search_documents_with_text...')
    
    test_queries = [
        "програми за иновации в малки и средни предприятия 2024",
        "финансиране за стартъп компании в България",
        "европейски фондове за дигитализация",
        "ПРЧР програма за обучение",
        "ПКИП иновации предприятия"
    ]
    
    results_summary = []
    
    for query in test_queries:
        print(f'\n   🔍 Заявка: "{query}"')
        start_time = time.time()
        
        try:
            results = search_documents_with_text(
                client=supabase,
                query_text=query,
                match_count=5,
                min_similarity_threshold=0.1,
                weight_similarity=0.4,
                weight_program_name=0.4,
                weight_year=0.2
            )
            end_time = time.time()
            
            if results:
                print(f'      ✅ Намерени {len(results)} резултата ({end_time-start_time:.2f}s)')
                
                # Analyze quality - check for different score field names
                scores = []
                for r in results:
                    if 'final_score_calculated' in r:
                        scores.append(r['final_score_calculated'])
                    elif 'score' in r:
                        scores.append(r['score'])
                    elif 'similarity' in r:
                        scores.append(r['similarity'])
                    else:
                        scores.append(0)
                if scores:
                    avg_score = sum(scores) / len(scores)
                    max_score = max(scores)
                    print(f'      📈 Среден score: {avg_score:.3f}, Най-висок: {max_score:.3f}')
                    
                    # Show top result
                    top_result = results[0]
                    print(f'      🎯 Топ резултат: {top_result.get("url", "N/A")[:60]}...')
                    
                    # Quality assessment
                    if max_score > 0.8:
                        quality = "🌟 ОТЛИЧНО"
                    elif max_score > 0.6:
                        quality = "👍 ДОБРО"
                    elif max_score > 0.4:
                        quality = "⚠️ СРЕДНО"
                    else:
                        quality = "❌ НИСКО"
                    
                    print(f'      {quality} качество')
                    
                    results_summary.append({
                        'query': query,
                        'results_count': len(results),
                        'avg_score': avg_score,
                        'max_score': max_score,
                        'response_time': end_time - start_time,
                        'quality': quality
                    })
                else:
                    print('      ⚠️ Няма scores в резултатите')
                    results_summary.append({
                        'query': query,
                        'results_count': len(results),
                        'avg_score': 0,
                        'max_score': 0,
                        'response_time': end_time - start_time,
                        'quality': "❌ БЕЗ SCORES"
                    })
            else:
                print('      ❌ Няма резултати')
                results_summary.append({
                    'query': query,
                    'results_count': 0,
                    'avg_score': 0,
                    'max_score': 0,
                    'response_time': end_time - start_time,
                    'quality': "❌ НЯМА РЕЗУЛТАТИ"
                })
                
        except Exception as e:
            print(f'      💥 Грешка: {e}')
            results_summary.append({
                'query': query,
                'error': str(e),
                'quality': "💥 ГРЕШКА"
            })
    
    return results_summary


def analyze_results(results_summary: List[Dict[str, Any]]):
    """Analyze and summarize test results"""
    print('\n📊 АНАЛИЗ НА РЕЗУЛТАТИТЕ:')
    print('=' * 50)
    
    successful_queries = [r for r in results_summary if 'error' not in r and r['results_count'] > 0]
    failed_queries = [r for r in results_summary if 'error' in r or r['results_count'] == 0]
    
    print(f'✅ Успешни заявки: {len(successful_queries)}/{len(results_summary)}')
    print(f'❌ Неуспешни заявки: {len(failed_queries)}/{len(results_summary)}')
    
    if successful_queries:
        avg_response_time = sum(r['response_time'] for r in successful_queries) / len(successful_queries)
        avg_score = sum(r['avg_score'] for r in successful_queries) / len(successful_queries)
        max_score_overall = max(r['max_score'] for r in successful_queries)
        
        print(f'⏱️ Средно време за отговор: {avg_response_time:.2f}s')
        print(f'📈 Среден score: {avg_score:.3f}')
        print(f'🏆 Най-висок score: {max_score_overall:.3f}')
        
        # Quality distribution
        quality_counts = {}
        for r in successful_queries:
            quality = r['quality'].split()[1] if len(r['quality'].split()) > 1 else r['quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
        
        print('\n🎯 Разпределение на качеството:')
        for quality, count in quality_counts.items():
            print(f'   {quality}: {count} заявки')
    
    if failed_queries:
        print('\n❌ Неуспешни заявки:')
        for r in failed_queries:
            if 'error' in r:
                print(f'   💥 "{r["query"][:30]}...": {r["error"]}')
            else:
                print(f'   🔍 "{r["query"][:30]}...": Няма резултати')


def main():
    """Main test function"""
    print('🎯 РЕАЛЕН ТЕСТ НА RAG ФУНКЦИОНАЛНОСТТА')
    print('=' * 60)
    
    try:
        # Test 1: Supabase connection
        supabase = test_supabase_connection()
        
        # Test 2: Embedding generation
        if not test_embedding_generation():
            print('❌ Embedding generation failed - stopping tests')
            return
        
        # Test 3: Wrapper function
        results_summary = test_wrapper_function(supabase)
        
        # Test 4: Analyze results
        analyze_results(results_summary)
        
        print('\n🏁 ТЕСТЪТ ЗАВЪРШИ УСПЕШНО!')
        
    except Exception as e:
        print(f'\n💥 КРИТИЧНА ГРЕШКА: {e}')
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
