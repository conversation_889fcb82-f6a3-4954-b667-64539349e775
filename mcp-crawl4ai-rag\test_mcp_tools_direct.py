#!/usr/bin/env python3
"""
ДИРЕКТЕН ТЕСТ НА MCP TOOLS
==========================

Този тест проверява реалната функционалност на MCP tools чрез директно извикване:
1. Тестване на perform_rag_query
2. Тестване на enhanced_rag_query  
3. Тестване на consultant_agent_query
4. Тестване на multi_vector_search
5. Измерване на качеството и производителността

ВАЖНО: Този тест използва същите функции които MCP сървърът използва,
но ги извиква директно без HTTP протокола.
"""

import asyncio
import json
import time
import sys
import os
from typing import Dict, List, Any

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_direct_rag_tools():
    """Test RAG tools by calling them directly"""
    
    print('🎯 ДИРЕКТЕН ТЕСТ НА MCP RAG TOOLS')
    print('=' * 50)
    
    # Test queries in Bulgarian
    test_queries = [
        "програми за иновации в малки и средни предприятия 2024",
        "финансиране за стартъп компании в България", 
        "европейски фондове за дигитализация",
        "ПРЧР програма за обучение",
        "ПКИП иновации предприятия"
    ]
    
    results_summary = []
    
    try:
        # Import the MCP tools directly
        print('📦 Импортиране на MCP tools...')
        
        # Import the wrapper function we created
        from src.utils import search_documents_with_text, get_supabase_client
        
        # Get Supabase client
        print('🔗 Свързване към Supabase...')
        supabase = get_supabase_client()
        print('   ✅ Supabase клиент инициализиран')
        
        # Test the wrapper function directly
        print(f'\n🔧 Тестване на search_documents_with_text (wrapper function)...')
        
        tool_results = []
        
        for query in test_queries:
            print(f'   🔍 Заявка: "{query[:40]}..."')
            
            start_time = time.time()
            
            try:
                # Call the wrapper function directly
                results = search_documents_with_text(
                    client=supabase,
                    query_text=query,
                    match_count=5,
                    weight_similarity=0.4,
                    weight_program_name=0.4,
                    weight_year=0.2
                )
                
                end_time = time.time()
                
                if results:
                    # Calculate average score
                    scores = [r.get('final_score_calculated', 0) for r in results]
                    avg_score = sum(scores) / len(scores) if scores else 0
                    max_score = max(scores) if scores else 0
                    
                    print(f'      ✅ Успех: {len(results)} резултата ({end_time-start_time:.2f}s)')
                    print(f'         📊 Средно score: {avg_score:.3f}, Макс: {max_score:.3f}')
                    
                    tool_results.append({
                        'query': query,
                        'results_count': len(results),
                        'response_time': end_time - start_time,
                        'avg_score': avg_score,
                        'max_score': max_score,
                        'success': True
                    })
                else:
                    print(f'      ⚠️ Празен отговор ({end_time-start_time:.2f}s)')
                    tool_results.append({
                        'query': query,
                        'results_count': 0,
                        'response_time': end_time - start_time,
                        'avg_score': 0,
                        'max_score': 0,
                        'success': False
                    })
                    
            except Exception as e:
                end_time = time.time()
                print(f'      ❌ Грешка: {str(e)} ({end_time-start_time:.2f}s)')
                tool_results.append({
                    'query': query,
                    'error': str(e),
                    'response_time': end_time - start_time,
                    'success': False
                })
        
        # Summarize results
        successful = [r for r in tool_results if r.get('success', False)]
        failed = [r for r in tool_results if not r.get('success', False)]
        
        print(f'\n   📊 search_documents_with_text резултати:')
        print(f'      ✅ Успешни: {len(successful)}/{len(tool_results)}')
        print(f'      ❌ Неуспешни: {len(failed)}/{len(tool_results)}')
        
        if successful:
            avg_time = sum(r['response_time'] for r in successful) / len(successful)
            avg_results = sum(r['results_count'] for r in successful) / len(successful)
            avg_score_overall = sum(r['avg_score'] for r in successful) / len(successful)
            max_score_overall = max(r['max_score'] for r in successful)
            
            print(f'      ⏱️ Средно време: {avg_time:.2f}s')
            print(f'      📈 Средно резултати: {avg_results:.1f}')
            print(f'      🎯 Средно score: {avg_score_overall:.3f}')
            print(f'      🏆 Най-високо score: {max_score_overall:.3f}')
            
            # Quality assessment
            if avg_score_overall > 0.8:
                quality = "ОТЛИЧНО"
            elif avg_score_overall > 0.6:
                quality = "ДОБРО"
            elif avg_score_overall > 0.4:
                quality = "СРЕДНО"
            else:
                quality = "НИСКО"
            
            print(f'      🏅 Качество: {quality}')
        
        results_summary.append({
            'tool': 'search_documents_with_text',
            'successful': len(successful),
            'failed': len(failed),
            'total': len(tool_results),
            'avg_time': sum(r['response_time'] for r in successful) / len(successful) if successful else 0,
            'avg_results': sum(r['results_count'] for r in successful) / len(successful) if successful else 0,
            'avg_score': sum(r['avg_score'] for r in successful) / len(successful) if successful else 0,
            'max_score': max(r['max_score'] for r in successful) if successful else 0
        })
        
    except Exception as e:
        print(f'💥 КРИТИЧНА ГРЕШКА при импортиране: {e}')
        import traceback
        traceback.print_exc()
        return []
    
    return results_summary


async def test_system_health():
    """Test system health by checking database connection"""
    
    print('\n🏥 ТЕСТ НА СИСТЕМНО ЗДРАВЕ...')
    
    try:
        from src.utils import get_supabase_client
        
        print('   🔍 Тестване на Supabase връзка...')
        
        supabase = get_supabase_client()
        
        # Test database connection by counting documents
        result = supabase.table('crawled_pages').select('id', count='exact').limit(1).execute()
        
        if result.count is not None:
            print(f'      ✅ Supabase връзка работи: {result.count} документа в базата')
            return True
        else:
            print('      ❌ Supabase връзка не работи правилно')
            return False
            
    except Exception as e:
        print(f'      ❌ Грешка при тестване на системното здраве: {e}')
        return False


def analyze_overall_results(results_summary: List[Dict[str, Any]]):
    """Analyze and display overall test results"""
    
    print('\n🎯 ОБЩ АНАЛИЗ НА РЕЗУЛТАТИТЕ')
    print('=' * 50)
    
    if not results_summary:
        print('❌ Няма резултати за анализ!')
        return
    
    total_tests = sum(r['total'] for r in results_summary)
    total_successful = sum(r['successful'] for r in results_summary)
    total_failed = sum(r['failed'] for r in results_summary)
    
    print(f'📊 Общо тестове: {total_tests}')
    print(f'✅ Успешни: {total_successful} ({total_successful/total_tests*100:.1f}%)')
    print(f'❌ Неуспешни: {total_failed} ({total_failed/total_tests*100:.1f}%)')
    
    if total_successful > 0:
        avg_time_overall = sum(r['avg_time'] * r['successful'] for r in results_summary) / total_successful
        avg_results_overall = sum(r['avg_results'] * r['successful'] for r in results_summary) / total_successful
        avg_score_overall = sum(r['avg_score'] * r['successful'] for r in results_summary) / total_successful
        max_score_overall = max(r['max_score'] for r in results_summary)
        
        print(f'⏱️ Средно време за отговор: {avg_time_overall:.2f}s')
        print(f'📈 Средно резултати на заявка: {avg_results_overall:.1f}')
        print(f'🎯 Средно RAG score: {avg_score_overall:.3f}')
        print(f'🏆 Най-високо RAG score: {max_score_overall:.3f}')
        
        # Quality assessment
        if avg_score_overall > 0.8:
            quality = "ОТЛИЧНО"
            emoji = "🌟"
        elif avg_score_overall > 0.6:
            quality = "ДОБРО"
            emoji = "👍"
        elif avg_score_overall > 0.4:
            quality = "СРЕДНО"
            emoji = "⚠️"
        else:
            quality = "НИСКО"
            emoji = "❌"
        
        print(f'🏅 Общо качество на RAG: {quality}')
    
    print('\n🔧 Резултати по tools:')
    for r in results_summary:
        success_rate = r['successful'] / r['total'] * 100 if r['total'] > 0 else 0
        score_info = f" (score: {r['avg_score']:.3f})" if 'avg_score' in r else ""
        print(f'   {r["tool"]}: {success_rate:.1f}% успех ({r["successful"]}/{r["total"]}){score_info}')
    
    # Overall assessment
    if total_successful / total_tests >= 0.8:
        print('\n🌟 ОТЛИЧНА ПРОИЗВОДИТЕЛНОСТ! Системата работи много добре.')
    elif total_successful / total_tests >= 0.6:
        print('\n👍 ДОБРА ПРОИЗВОДИТЕЛНОСТ! Системата работи добре с малки проблеми.')
    elif total_successful / total_tests >= 0.4:
        print('\n⚠️ СРЕДНА ПРОИЗВОДИТЕЛНОСТ! Системата има проблеми които трябва да се решат.')
    else:
        print('\n❌ НИСКА ПРОИЗВОДИТЕЛНОСТ! Системата има сериозни проблеми.')


async def main():
    """Main test function"""
    
    print('🚀 ЗАПОЧВАНЕ НА ДИРЕКТЕН ТЕСТ НА MCP TOOLS')
    print('=' * 60)
    
    try:
        # Test RAG tools directly
        results_summary = await test_direct_rag_tools()
        
        # Test system health
        health_ok = await test_system_health()
        
        # Analyze results
        analyze_overall_results(results_summary)
        
        print('\n🏁 ДИРЕКТНИЯТ ТЕСТ НА MCP TOOLS ЗАВЪРШИ!')
        
    except Exception as e:
        print(f'\n💥 КРИТИЧНА ГРЕШКА: {e}')
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
