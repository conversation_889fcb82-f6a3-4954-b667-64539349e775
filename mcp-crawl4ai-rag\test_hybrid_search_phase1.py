#!/usr/bin/env python3
"""
🔥 PHASE 1: HY<PERSON><PERSON> SEARCH TESTING
=================================

Тестване на новата hybrid search система която комбинира:
- BM25 sparse search (keyword matching)
- Dense vector search (semantic similarity)
- Weighted fusion за оптимални резултати

Очакван резултат: 50% подобрение в точността (0.298 → ~0.45)
"""

import asyncio
import json
import time
import sys
import os
from typing import Dict, List, Any

# Add src to path for imports
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Import after adding to path
import utils

# Use the functions
search_documents_with_text_hybrid = utils.search_documents_with_text_hybrid
search_documents_with_text_legacy = utils.search_documents_with_text_legacy
get_supabase_client = utils.get_supabase_client


def test_hybrid_vs_legacy_comparison():
    """🔥 PHASE 1: Compare hybrid search vs legacy dense-only search."""
    print("\n" + "="*80)
    print("🔥 PHASE 1: HYBRID SEARCH vs LEGACY COMPARISON")
    print("="*80)
    
    # Initialize Supabase client
    client = get_supabase_client()
    if not client:
        print("❌ Failed to initialize Supabase client")
        return
    
    # Real Bulgarian test queries for European funding
    test_queries = [
        "Какви са условията за кандидатстване по програма Еразъм+ за 2024 година?",
        "Финансиране за малки и средни предприятия в България", 
        "Програми за дигитализация и иновации в образованието",
        "Подкрепа за зелени технологии и устойчиво развитие",
        "Възможности за финансиране на научни изследвания"
    ]
    
    total_hybrid_score = 0.0
    total_legacy_score = 0.0
    successful_queries = 0
    
    print(f"\n🧪 Testing {len(test_queries)} real Bulgarian queries...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*60}")
        print(f"📝 Query {i}: {query}")
        print('='*60)
        
        # Test HYBRID search
        print("\n🔥 HYBRID SEARCH (BM25 + Dense):")
        start_time = time.time()
        try:
            hybrid_results = search_documents_with_text_hybrid(
                client=client,
                query_text=query,
                match_count=5,
                # 🎯 Using optimized default weights (0.7 dense, 0.3 sparse)
                min_similarity_threshold=0.1
            )
            hybrid_time = time.time() - start_time
            
            if hybrid_results:
                # Calculate average hybrid score
                hybrid_scores = [r.get('hybrid_score', 0.0) for r in hybrid_results]
                avg_hybrid_score = sum(hybrid_scores) / len(hybrid_scores)
                total_hybrid_score += avg_hybrid_score
                
                print(f"✅ Found {len(hybrid_results)} results in {hybrid_time:.3f}s")
                print(f"📊 Average hybrid score: {avg_hybrid_score:.3f}")
                print(f"🔍 Search methods: {[r.get('search_method', 'unknown') for r in hybrid_results[:3]]}")
                
                # Show top result
                top_result = hybrid_results[0]
                print(f"🏆 Top result score: {top_result.get('hybrid_score', 0.0):.3f}")
                print(f"📄 Content preview: {top_result.get('content', '')[:100]}...")
            else:
                print("❌ No hybrid results found")
                
        except Exception as e:
            print(f"❌ Hybrid search error: {str(e)}")
            hybrid_time = 0
            avg_hybrid_score = 0.0
        
        # Test LEGACY search
        print("\n🔍 LEGACY SEARCH (Dense only):")
        start_time = time.time()
        try:
            legacy_results = search_documents_with_text_legacy(
                client=client,
                query_text=query,
                match_count=5,
                min_similarity_threshold=0.1
            )
            legacy_time = time.time() - start_time
            
            if legacy_results:
                # Calculate average legacy score
                legacy_scores = [r.get('final_score_calculated', r.get('similarity', 0.0)) for r in legacy_results]
                avg_legacy_score = sum(legacy_scores) / len(legacy_scores)
                total_legacy_score += avg_legacy_score
                
                print(f"✅ Found {len(legacy_results)} results in {legacy_time:.3f}s")
                print(f"📊 Average legacy score: {avg_legacy_score:.3f}")
                
                # Show top result
                top_result = legacy_results[0]
                top_score = top_result.get('final_score_calculated', top_result.get('similarity', 0.0))
                print(f"🏆 Top result score: {top_score:.3f}")
                print(f"📄 Content preview: {top_result.get('content', '')[:100]}...")
            else:
                print("❌ No legacy results found")
                
        except Exception as e:
            print(f"❌ Legacy search error: {str(e)}")
            legacy_time = 0
            avg_legacy_score = 0.0
        
        # Compare performance
        if hybrid_results and legacy_results:
            successful_queries += 1
            improvement = ((avg_hybrid_score - avg_legacy_score) / avg_legacy_score * 100) if avg_legacy_score > 0 else 0
            print(f"\n📈 PERFORMANCE COMPARISON:")
            print(f"   Hybrid: {avg_hybrid_score:.3f} | Legacy: {avg_legacy_score:.3f}")
            print(f"   Improvement: {improvement:+.1f}%")
            print(f"   Speed: Hybrid {hybrid_time:.3f}s | Legacy {legacy_time:.3f}s")
    
    # Final summary
    print(f"\n{'='*80}")
    print("🏁 FINAL PHASE 1 RESULTS")
    print('='*80)
    
    if successful_queries > 0:
        avg_hybrid_final = total_hybrid_score / successful_queries
        avg_legacy_final = total_legacy_score / successful_queries
        overall_improvement = ((avg_hybrid_final - avg_legacy_final) / avg_legacy_final * 100) if avg_legacy_final > 0 else 0
        
        print(f"📊 Successful queries: {successful_queries}/{len(test_queries)}")
        print(f"🔥 Average HYBRID score: {avg_hybrid_final:.3f}")
        print(f"🔍 Average LEGACY score: {avg_legacy_final:.3f}")
        print(f"📈 Overall improvement: {overall_improvement:+.1f}%")
        
        # Check if we achieved Phase 1 target
        target_improvement = 50.0  # 50% improvement target
        if overall_improvement >= target_improvement:
            print(f"🎯 ✅ PHASE 1 TARGET ACHIEVED! ({overall_improvement:.1f}% >= {target_improvement}%)")
            print("🚀 Ready to proceed to Phase 2: Query Expansion with HyDE")
        else:
            print(f"⚠️ PHASE 1 TARGET NOT MET ({overall_improvement:.1f}% < {target_improvement}%)")
            print("🔧 Need to optimize hybrid fusion weights or BM25 parameters")
    else:
        print("❌ No successful queries - need to debug hybrid search implementation")
    
    return successful_queries > 0


def test_hybrid_weight_optimization():
    """🔧 Test different weight combinations for optimal hybrid fusion."""
    print("\n" + "="*80)
    print("🔧 HYBRID WEIGHT OPTIMIZATION")
    print("="*80)
    
    client = get_supabase_client()
    if not client:
        print("❌ Failed to initialize Supabase client")
        return
    
    # Test query
    test_query = "Финансиране за малки и средни предприятия в България"
    
    # Different weight combinations to test
    weight_combinations = [
        (0.3, 0.7),  # More sparse
        (0.4, 0.6),  # Balanced toward sparse
        (0.5, 0.5),  # Equal
        (0.6, 0.4),  # Balanced toward dense
        (0.7, 0.3),  # More dense
    ]
    
    print(f"🧪 Testing query: {test_query}")
    print(f"🔧 Testing {len(weight_combinations)} weight combinations...")
    
    best_score = 0.0
    best_weights = None
    
    for dense_weight, sparse_weight in weight_combinations:
        print(f"\n⚖️ Testing weights: Dense={dense_weight}, Sparse={sparse_weight}")
        
        try:
            results = search_documents_with_text_hybrid(
                client=client,
                query_text=test_query,
                match_count=5,
                weight_dense=dense_weight,
                weight_sparse=sparse_weight,
                min_similarity_threshold=0.1
            )
            
            if results:
                avg_score = sum(r.get('hybrid_score', 0.0) for r in results) / len(results)
                print(f"📊 Average score: {avg_score:.3f}")
                
                if avg_score > best_score:
                    best_score = avg_score
                    best_weights = (dense_weight, sparse_weight)
                    print("🏆 New best combination!")
            else:
                print("❌ No results")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
    
    print(f"\n🏆 OPTIMAL WEIGHTS FOUND:")
    print(f"   Dense: {best_weights[0]}, Sparse: {best_weights[1]}")
    print(f"   Best score: {best_score:.3f}")
    
    return best_weights


if __name__ == "__main__":
    print("🔥 STARTING PHASE 1: HYBRID SEARCH TESTING")
    
    # Run hybrid vs legacy comparison
    success = test_hybrid_vs_legacy_comparison()
    
    if success:
        # Optimize weights for best performance
        optimal_weights = test_hybrid_weight_optimization()
        
        print(f"\n🎯 PHASE 1 COMPLETE!")
        print(f"✅ Hybrid search implemented and tested")
        print(f"⚖️ Optimal weights: Dense={optimal_weights[0]}, Sparse={optimal_weights[1]}")
        print(f"🚀 Ready for Phase 2: Query Expansion with HyDE")
    else:
        print(f"\n❌ PHASE 1 FAILED - Need to debug hybrid search")
