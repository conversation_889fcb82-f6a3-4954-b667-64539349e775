#!/usr/bin/env python3
"""
🎯 ФАЗА 8.6.5: ФИНАЛНА ИНТЕГРАЦИЯ И ТЕСТВАНЕ
============================================

Финален тест на цялата българска RAG оптимизация система
с всички внедрени подобрения от фази 8.1 до 8.6.4.

Тестове:
1. Пълна интеграция на всички компоненти
2. Производителност и скорост
3. Качество на резултатите
4. Българска езикова поддръжка
5. Стабилност на системата
"""

import asyncio
import time
import os
import sys
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

async def test_full_integration():
    """🎯 Тест на пълната интеграция на системата"""
    print("\n🎯 ТЕСТВАНЕ НА ПЪЛНАТА ИНТЕГРАЦИЯ")
    print("=" * 60)
    
    try:
        from src.utils import search_crawled_pages_advanced_query
        
        # Комплексна българска заявка
        query = "европейски фондове за иновации и дигитализация на МСП в България"
        
        print(f"🔍 Заявка: {query}")
        print(f"📊 Очаквани компоненти:")
        print(f"   ✅ Българска езикова оптимизация (8.6.4)")
        print(f"   ✅ Cross-encoder reranking (8.2)")
        print(f"   ✅ Hybrid search (8.1)")
        print(f"   ✅ Advanced query processing (8.4)")
        print(f"   ✅ Enhanced scoring (8.6.3)")
        print(f"   ✅ Content chunking (8.6.2)")
        print(f"   ✅ Keyword validation (8.6.1)")
        
        start_time = time.time()
        
        # Пълно търсене с всички оптимизации
        results = await search_crawled_pages_advanced_query(
            query_text=query,
            match_count=5,
            use_bulgarian_embeddings=True,
            rerank_top_k=20,
            enable_query_expansion=True,
            enable_hyde=True,
            enable_multi_query=True
        )
        
        search_time = time.time() - start_time
        
        print(f"\n📊 РЕЗУЛТАТИ ОТ ПЪЛНАТА ИНТЕГРАЦИЯ:")
        print(f"   ⏱️ Време за търсене: {search_time:.2f}s")
        print(f"   📄 Намерени резултати: {len(results)}")
        
        if results:
            print(f"\n🏆 ТОП 3 РЕЗУЛТАТА:")
            for i, result in enumerate(results[:3], 1):
                title = result.get('title', 'Без заглавие')[:50]
                enhanced_score = result.get('enhanced_score', 0)
                rerank_score = result.get('rerank_score', 0)
                keyword_score = result.get('keyword_score', 0)
                
                print(f"   {i}. {title}...")
                print(f"      Enhanced Score: {enhanced_score:.3f}")
                print(f"      Rerank Score: {rerank_score:.3f}")
                print(f"      Keyword Score: {keyword_score:.3f}")
            
            # Анализ на качеството
            avg_enhanced = sum(r.get('enhanced_score', 0) for r in results) / len(results)
            avg_rerank = sum(r.get('rerank_score', 0) for r in results) / len(results)
            
            print(f"\n📈 АНАЛИЗ НА КАЧЕСТВОТО:")
            print(f"   🏆 Средна Enhanced Score: {avg_enhanced:.3f}")
            print(f"   🧠 Средна Rerank Score: {avg_rerank:.3f}")
            
            # Оценка на успеха
            if avg_enhanced > 8.0 and len(results) >= 3:
                print(f"   ✅ ОТЛИЧЕН резултат! Системата работи перфектно!")
                return True
            elif avg_enhanced > 5.0 and len(results) >= 2:
                print(f"   ✅ ДОБЪР резултат! Системата работи добре!")
                return True
            else:
                print(f"   ⚠️ Умерен резултат. Има място за подобрение.")
                return False
        else:
            print(f"   ❌ Няма намерени резултати")
            return False
            
    except Exception as e:
        print(f"❌ Грешка при тестване на интеграцията: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_benchmark():
    """⚡ Тест на производителността"""
    print("\n⚡ ТЕСТВАНЕ НА ПРОИЗВОДИТЕЛНОСТТА")
    print("=" * 60)
    
    try:
        from src.utils import search_crawled_pages_advanced_query
        
        test_queries = [
            "МСП финансиране",
            "екологични проекти",
            "образование обучение",
            "инфраструктура транспорт",
            "иновации технологии"
        ]
        
        total_time = 0
        successful_searches = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"🔍 Тест {i}/5: {query}")
            
            start_time = time.time()
            
            try:
                results = await search_crawled_pages_advanced_query(
                    query_text=query,
                    match_count=3,
                    use_bulgarian_embeddings=True,
                    rerank_top_k=10
                )
                
                search_time = time.time() - start_time
                total_time += search_time
                
                if results:
                    successful_searches += 1
                    avg_score = sum(r.get('enhanced_score', 0) for r in results) / len(results)
                    print(f"   ✅ {len(results)} резултата за {search_time:.2f}s (avg score: {avg_score:.2f})")
                else:
                    print(f"   ⚠️ Няма резултати за {search_time:.2f}s")
                    
            except Exception as e:
                print(f"   ❌ Грешка: {e}")
        
        print(f"\n📊 ОБОБЩЕНИЕ НА ПРОИЗВОДИТЕЛНОСТТА:")
        print(f"   ⏱️ Общо време: {total_time:.2f}s")
        print(f"   📈 Средно време на заявка: {total_time/len(test_queries):.2f}s")
        print(f"   ✅ Успешни търсения: {successful_searches}/{len(test_queries)}")
        print(f"   📊 Процент успех: {(successful_searches/len(test_queries)*100):.1f}%")
        
        # Оценка на производителността
        avg_time = total_time / len(test_queries)
        success_rate = successful_searches / len(test_queries)
        
        if avg_time < 15.0 and success_rate >= 0.8:
            print(f"   🚀 ОТЛИЧНА производителност!")
            return True
        elif avg_time < 25.0 and success_rate >= 0.6:
            print(f"   ✅ ДОБРА производителност!")
            return True
        else:
            print(f"   ⚠️ Производителността може да се подобри.")
            return False
            
    except Exception as e:
        print(f"❌ Грешка при тестване на производителността: {e}")
        return False

async def test_bulgarian_language_support():
    """🇧🇬 Тест на българската езикова поддръжка"""
    print("\n🇧🇬 ТЕСТВАНЕ НА БЪЛГАРСКАТА ЕЗИКОВА ПОДДРЪЖКА")
    print("=" * 60)
    
    try:
        from src.utils import extract_bulgarian_keywords, expand_query_with_synonyms
        
        # Тест на извличане на ключови думи
        test_text = "Програми за финансиране на малки и средни предприятия в България чрез европейски фондове"
        keywords = extract_bulgarian_keywords(test_text)
        
        print(f"📝 Тест текст: {test_text}")
        print(f"🔑 Извлечени ключови думи ({len(keywords)}): {', '.join(keywords)}")
        
        # Тест на разширяване на заявки
        test_query = "МСП финансиране иновации"
        expanded = expand_query_with_synonyms(test_query)
        
        print(f"\n🔍 Тест заявка: {test_query}")
        print(f"📝 Разширени варианти ({len(expanded)}):")
        for i, variant in enumerate(expanded[:3], 1):
            print(f"   {i}. {variant}")
        
        # Оценка на българската поддръжка
        keyword_quality = len(keywords) >= 5  # Поне 5 ключови думи
        expansion_quality = len(expanded) >= 3  # Поне 3 варианта
        
        print(f"\n📊 ОЦЕНКА НА БЪЛГАРСКАТА ПОДДРЪЖКА:")
        print(f"   🔑 Качество на ключови думи: {'✅' if keyword_quality else '⚠️'}")
        print(f"   📝 Качество на разширяване: {'✅' if expansion_quality else '⚠️'}")
        
        if keyword_quality and expansion_quality:
            print(f"   🇧🇬 ОТЛИЧНА българска поддръжка!")
            return True
        else:
            print(f"   ⚠️ Българската поддръжка може да се подобри.")
            return False
            
    except Exception as e:
        print(f"❌ Грешка при тестване на българската поддръжка: {e}")
        return False

async def main():
    """🎯 Главна функция за финалното тестване"""
    print("🎉 ФАЗА 8.6.5: ФИНАЛНА ИНТЕГРАЦИЯ И ТЕСТВАНЕ")
    print("=" * 80)
    
    # Списък с тестове
    tests = [
        ("Пълна интеграция", test_full_integration),
        ("Производителност", test_performance_benchmark),
        ("Българска поддръжка", test_bulgarian_language_support)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 СТАРТИРАНЕ НА ТЕСТ: {test_name}")
        print("-" * 60)
        
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Критична грешка в тест '{test_name}': {e}")
            results.append((test_name, False))
    
    # Финално обобщение
    print(f"\n🎯 ФИНАЛНО ОБОБЩЕНИЕ НА ФАЗА 8.6.5")
    print("=" * 80)
    
    successful_tests = sum(1 for _, success in results if success)
    total_tests = len(results)
    
    for test_name, success in results:
        status = "✅ УСПЕШЕН" if success else "❌ НЕУСПЕШЕН"
        print(f"   {status}: {test_name}")
    
    success_rate = (successful_tests / total_tests) * 100
    
    print(f"\n📊 ОБЩ РЕЗУЛТАТ:")
    print(f"   ✅ Успешни тестове: {successful_tests}/{total_tests}")
    print(f"   📈 Процент успех: {success_rate:.1f}%")
    
    if success_rate >= 100:
        print(f"\n🎉 ПЕРФЕКТЕН РЕЗУЛТАТ! Всички системи работят отлично!")
        print(f"🚀 Фаза 8.6.5 завършена с пълен успех!")
    elif success_rate >= 66:
        print(f"\n✅ ДОБЪР РЕЗУЛТАТ! Системата работи добре!")
        print(f"🎯 Фаза 8.6.5 завършена успешно!")
    else:
        print(f"\n⚠️ ЧАСТИЧЕН УСПЕХ. Има области за подобрение.")
        print(f"🔧 Фаза 8.6.5 завършена с предупреждения.")

if __name__ == "__main__":
    asyncio.run(main())
