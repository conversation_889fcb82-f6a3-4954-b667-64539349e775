widths = {'A': 722,
 'AE': 1000,
 'Aacute': 722,
 'Acircumflex': 722,
 'Adieresis': 722,
 'Agrave': 722,
 'Aring': 722,
 'Atilde': 722,
 'B': 667,
 'C': 722,
 '<PERSON><PERSON><PERSON>': 722,
 'D': 722,
 'E': 667,
 'Eacute': 667,
 'Ecircumflex': 667,
 'Edieresis': 667,
 'Egrave': 667,
 'Eth': 722,
 'Euro': 500,
 'F': 611,
 'G': 778,
 'H': 778,
 'I': 389,
 'Iacute': 389,
 'Icircumflex': 389,
 'Idieresis': 389,
 'Igrave': 389,
 'J': 500,
 'K': 778,
 'L': 667,
 'Lslash': 667,
 'M': 944,
 'N': 722,
 'Ntilde': 722,
 'O': 778,
 'OE': 1000,
 'Oacute': 778,
 'Ocircumflex': 778,
 'Odieresis': 778,
 'Ograve': 778,
 'Oslash': 778,
 'Otilde': 778,
 'P': 611,
 'Q': 778,
 'R': 722,
 '<PERSON>': 556,
 '<PERSON>aron': 556,
 'T': 667,
 'Thorn': 611,
 'U': 722,
 'Uacute': 722,
 'Ucircumflex': 722,
 'Udieresis': 722,
 'Ugrave': 722,
 'V': 722,
 'W': 1000,
 'X': 722,
 'Y': 722,
 'Yacute': 722,
 'Ydieresis': 722,
 'Z': 667,
 'Zcaron': 667,
 'a': 500,
 'aacute': 500,
 'acircumflex': 500,
 'acute': 333,
 'adieresis': 500,
 'ae': 722,
 'agrave': 500,
 'ampersand': 833,
 'aring': 500,
 'asciicircum': 581,
 'asciitilde': 520,
 'asterisk': 500,
 'at': 930,
 'atilde': 500,
 'b': 556,
 'backslash': 278,
 'bar': 220,
 'braceleft': 394,
 'braceright': 394,
 'bracketleft': 333,
 'bracketright': 333,
 'breve': 333,
 'brokenbar': 220,
 'bullet': 350,
 'c': 444,
 'caron': 333,
 'ccedilla': 444,
 'cedilla': 333,
 'cent': 500,
 'circumflex': 333,
 'colon': 333,
 'comma': 250,
 'copyright': 747,
 'currency': 500,
 'd': 556,
 'dagger': 500,
 'daggerdbl': 500,
 'degree': 400,
 'dieresis': 333,
 'divide': 570,
 'dollar': 500,
 'dotaccent': 333,
 'dotlessi': 278,
 'e': 444,
 'eacute': 444,
 'ecircumflex': 444,
 'edieresis': 444,
 'egrave': 444,
 'eight': 500,
 'ellipsis': 1000,
 'emdash': 1000,
 'endash': 500,
 'equal': 570,
 'eth': 500,
 'exclam': 333,
 'exclamdown': 333,
 'f': 333,
 'fi': 556,
 'five': 500,
 'fl': 556,
 'florin': 500,
 'four': 500,
 'fraction': 167,
 'g': 500,
 'germandbls': 556,
 'grave': 333,
 'greater': 570,
 'guillemotleft': 500,
 'guillemotright': 500,
 'guilsinglleft': 333,
 'guilsinglright': 333,
 'h': 556,
 'hungarumlaut': 333,
 'hyphen': 333,
 'i': 278,
 'iacute': 278,
 'icircumflex': 278,
 'idieresis': 278,
 'igrave': 278,
 'j': 333,
 'k': 556,
 'l': 278,
 'less': 570,
 'logicalnot': 570,
 'lslash': 278,
 'm': 833,
 'macron': 333,
 'minus': 570,
 'mu': 556,
 'multiply': 570,
 'n': 556,
 'nine': 500,
 'ntilde': 556,
 'numbersign': 500,
 'o': 500,
 'oacute': 500,
 'ocircumflex': 500,
 'odieresis': 500,
 'oe': 722,
 'ogonek': 333,
 'ograve': 500,
 'one': 500,
 'onehalf': 750,
 'onequarter': 750,
 'onesuperior': 300,
 'ordfeminine': 300,
 'ordmasculine': 330,
 'oslash': 500,
 'otilde': 500,
 'p': 556,
 'paragraph': 540,
 'parenleft': 333,
 'parenright': 333,
 'percent': 1000,
 'period': 250,
 'periodcentered': 250,
 'perthousand': 1000,
 'plus': 570,
 'plusminus': 570,
 'q': 556,
 'question': 500,
 'questiondown': 500,
 'quotedbl': 555,
 'quotedblbase': 500,
 'quotedblleft': 500,
 'quotedblright': 500,
 'quoteleft': 333,
 'quoteright': 333,
 'quotesinglbase': 333,
 'quotesingle': 278,
 'r': 444,
 'registered': 747,
 'ring': 333,
 's': 389,
 'scaron': 389,
 'section': 500,
 'semicolon': 333,
 'seven': 500,
 'six': 500,
 'slash': 278,
 'space': 250,
 'sterling': 500,
 't': 333,
 'thorn': 556,
 'three': 500,
 'threequarters': 750,
 'threesuperior': 300,
 'tilde': 333,
 'trademark': 1000,
 'two': 500,
 'twosuperior': 300,
 'u': 556,
 'uacute': 556,
 'ucircumflex': 556,
 'udieresis': 556,
 'ugrave': 556,
 'underscore': 500,
 'v': 500,
 'w': 722,
 'x': 500,
 'y': 500,
 'yacute': 500,
 'ydieresis': 500,
 'yen': 500,
 'z': 444,
 'zcaron': 444,
 'zero': 500}
