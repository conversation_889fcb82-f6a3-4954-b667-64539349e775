#!/usr/bin/env python3
"""
🎯 PHASE 7.2: QUERY PROCESSING ENHANCEMENT TEST
Тестване на query expansion, normalization и keyword boosting

Очаквано подобрение: +8-12% от текущите 50%
Цел: 58-62% Overall Success Index
"""

import asyncio
import os
import sys
from typing import List, Dict, Any, Optional
import json
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import required modules
from supabase import create_client, Client
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv
from query_enhancement import BulgarianQueryEnhancer

# Load environment variables
load_dotenv()

def get_supabase_client() -> Client:
    """Get Supabase client"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    if not url or not key:
        raise ValueError(f"Missing Supabase credentials: URL={url is not None}, KEY={key is not None}")
    return create_client(url, key)

def get_embedding_model():
    """Get embedding model"""
    return SentenceTransformer('BAAI/bge-large-en-v1.5')

# Test queries with expected difficulty levels
TEST_QUERIES = [
    # EASY queries (previously 50% success)
    {
        "query": "процедури за финансиране на проекти",
        "difficulty": "EASY",
        "expected_keywords": ["процедури", "финансиране", "проекти", "програми"]
    },
    {
        "query": "образование и професионално обучение", 
        "difficulty": "EASY",
        "expected_keywords": ["образование", "обучение", "развитие", "квалификация"]
    },
    
    # MEDIUM queries (previously 50% success)
    {
        "query": "европейски фондове за околна среда",
        "difficulty": "MEDIUM", 
        "expected_keywords": ["околна среда", "екология", "зелена", "климат"]
    },
    {
        "query": "програма за развитие на човешките ресурси",
        "difficulty": "MEDIUM",
        "expected_keywords": ["човешки ресурси", "развитие", "заетост", "умения"]
    },
    {
        "query": "транспортна инфраструктура и мобилност",
        "difficulty": "MEDIUM",
        "expected_keywords": ["транспорт", "инфраструктура", "мобилност", "пътища"]
    },
    {
        "query": "подкрепа за малки и средни предприятия",
        "difficulty": "MEDIUM", 
        "expected_keywords": ["МСП", "предприятия", "бизнес", "подкрепа"]
    },
    
    # HARD queries (previously 50% success)
    {
        "query": "иновации и технологии за МСП",
        "difficulty": "HARD",
        "expected_keywords": ["иновации", "технологии", "МСП", "дигитални"]
    },
    {
        "query": "регионално развитие и местни общности", 
        "difficulty": "HARD",
        "expected_keywords": ["регионално", "развитие", "общности", "местни"]
    },
    {
        "query": "цифрова трансформация и дигитализация",
        "difficulty": "HARD",
        "expected_keywords": ["цифрова", "дигитализация", "технологии", "ИКТ"]
    },
    {
        "query": "енергийна ефективност и възобновяеми източници",
        "difficulty": "HARD", 
        "expected_keywords": ["енергийна", "ефективност", "възобновяеми", "енергия"]
    }
]

def calculate_content_relevance(query: str, content: str, threshold: float = 0.3) -> float:
    """Calculate content relevance based on keyword matching"""
    if not content:
        return 0.0
    
    query_words = set(query.lower().split())
    content_words = set(content.lower().split())
    
    # Remove common stopwords
    stopwords = {'и', 'на', 'за', 'в', 'с', 'от', 'до', 'по', 'при', 'към', 'или', 'че', 'да', 'се', 'не'}
    query_words = query_words - stopwords
    content_words = content_words - stopwords
    
    if not query_words:
        return 0.0
    
    matches = len(query_words.intersection(content_words))
    relevance = matches / len(query_words)
    
    return relevance

async def test_query_enhancement():
    """Test the new query enhancement features"""
    print("🎯 PHASE 7.2: QUERY PROCESSING ENHANCEMENT TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 Features: Query expansion, normalization, keyword boosting")
    print(f"📊 Previous Performance (Phase 7.1): 50.0% Overall Success Index")
    print(f"🎯 Target: 58-62% Overall Success Index (+8-12%)")
    print("=" * 60)
    
    # Initialize
    supabase = get_supabase_client()
    embedding_model = get_embedding_model()
    query_enhancer = BulgarianQueryEnhancer()
    
    results = []
    total_tests = len(TEST_QUERIES)
    
    for i, test_case in enumerate(TEST_QUERIES, 1):
        query = test_case["query"]
        difficulty = test_case["difficulty"]
        expected_keywords = test_case["expected_keywords"]
        
        print(f"\n🔍 Test {i}/{total_tests}: {query}")
        print(f"📊 Difficulty: {difficulty}")
        
        try:
            # Step 1: Enhance query
            enhancement_result = query_enhancer.enhance_query_for_search(query)
            enhanced_query = enhancement_result['expanded_query']
            keywords = enhancement_result['keywords']
            
            print(f"🚀 Enhanced Query: {enhanced_query}")
            print(f"🔑 Keywords: {keywords}")
            
            # Step 2: Generate embedding for enhanced query
            query_embedding = embedding_model.encode([enhanced_query])[0].tolist()
            
            # Step 3: Test NEW enhanced function
            response = supabase.rpc(
                'match_crawled_pages_v7_2_enhanced',
                {
                    'p_query_embedding': query_embedding,
                    'p_keywords': keywords,
                    'p_weight_similarity': 0.65,  # Maintained from Phase 7.1
                    'p_weight_program_name': 0.15,  # Maintained
                    'p_weight_year': 0.05,  # Maintained
                    'p_weight_metadata': 0.10,  # Reduced to make room for keywords
                    'p_weight_keywords': 0.05,  # NEW: Keyword boost weight
                    'p_match_count': 5
                }
            ).execute()
            
            if response.data:
                # Analyze top 3 results
                top_3_results = response.data[:3]
                relevance_scores = []
                
                print(f"📋 Top 3 Results:")
                for j, result in enumerate(top_3_results, 1):
                    url = result.get('url', 'No URL')
                    url_display = url.split('/')[-1][:50] if url else 'No URL'
                    final_score = result.get('final_score', 0)
                    similarity = result.get('similarity', 0)
                    metadata_score = result.get('metadata_score', 0)
                    keyword_boost = result.get('keyword_boost_score', 0)
                    
                    # Calculate content relevance
                    content_relevance = calculate_content_relevance(query, result.get('content', ''))
                    relevance_scores.append(content_relevance)
                    
                    print(f"   {j}. {url_display}...")
                    print(f"      📊 Final: {final_score:.3f} | Sim: {similarity:.3f} | Meta: {metadata_score:.3f} | KW: {keyword_boost:.3f} | Rel: {content_relevance:.3f}")
                
                # Calculate metrics
                success_at_3 = any(score >= 0.3 for score in relevance_scores)
                precision_at_3 = sum(1 for score in relevance_scores if score >= 0.3) / 3
                best_relevance = max(relevance_scores) if relevance_scores else 0
                
                # Calculate MRR
                mrr = 0
                for j, score in enumerate(relevance_scores, 1):
                    if score >= 0.3:
                        mrr = 1.0 / j
                        break
                
                results.append({
                    'query': query,
                    'enhanced_query': enhanced_query,
                    'keywords': keywords,
                    'difficulty': difficulty,
                    'success_at_3': success_at_3,
                    'precision_at_3': precision_at_3,
                    'best_relevance': best_relevance,
                    'mrr': mrr,
                    'enhancement_applied': enhancement_result['enhancement_applied'],
                    'top_scores': [r.get('final_score', 0) for r in top_3_results]
                })
                
                print(f"✅ Success@3: {success_at_3} | Precision@3: {precision_at_3:.1%} | Best Rel: {best_relevance:.3f} | MRR: {mrr:.3f}")
                
            else:
                print("❌ No results found")
                results.append({
                    'query': query,
                    'enhanced_query': enhanced_query,
                    'keywords': keywords,
                    'difficulty': difficulty,
                    'success_at_3': False,
                    'precision_at_3': 0,
                    'best_relevance': 0,
                    'mrr': 0,
                    'enhancement_applied': enhancement_result['enhancement_applied'],
                    'top_scores': []
                })
                
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                'query': query,
                'enhanced_query': query,
                'keywords': [],
                'difficulty': difficulty,
                'success_at_3': False,
                'precision_at_3': 0,
                'best_relevance': 0,
                'mrr': 0,
                'enhancement_applied': False,
                'top_scores': []
            })
    
    # Calculate overall metrics
    print("\n" + "=" * 60)
    print("📊 PHASE 7.2 QUERY ENHANCEMENT RESULTS")
    print("=" * 60)
    
    total_success_at_3 = sum(1 for r in results if r['success_at_3'])
    avg_precision_at_3 = sum(r['precision_at_3'] for r in results) / len(results)
    avg_mrr = sum(r['mrr'] for r in results) / len(results)
    overall_success_index = (total_success_at_3 / len(results)) * 100
    
    print(f"🎯 Overall Success Index: {overall_success_index:.1f}%")
    print(f"📈 Success@3 Rate: {total_success_at_3}/{len(results)} ({total_success_at_3/len(results)*100:.1f}%)")
    print(f"🎯 Average Precision@3: {avg_precision_at_3:.1%}")
    print(f"📊 Average MRR: {avg_mrr:.3f}")
    
    # Analyze by difficulty
    print(f"\n📊 Results by Difficulty:")
    for difficulty in ['EASY', 'MEDIUM', 'HARD']:
        difficulty_results = [r for r in results if r['difficulty'] == difficulty]
        if difficulty_results:
            success_rate = sum(1 for r in difficulty_results if r['success_at_3']) / len(difficulty_results) * 100
            print(f"   {difficulty}: {success_rate:.0f}% success rate")
    
    # Analyze enhancement effectiveness
    enhanced_queries = [r for r in results if r['enhancement_applied']]
    if enhanced_queries:
        enhanced_success = sum(1 for r in enhanced_queries if r['success_at_3']) / len(enhanced_queries) * 100
        print(f"\n🚀 Enhancement Effectiveness:")
        print(f"   Enhanced queries: {len(enhanced_queries)}/{len(results)}")
        print(f"   Enhanced success rate: {enhanced_success:.1f}%")
    
    # Compare with previous results
    print(f"\n📈 IMPROVEMENT ANALYSIS:")
    previous_success = 50.0  # Phase 7.1 result
    improvement = overall_success_index - previous_success
    improvement_pct = (improvement / previous_success) * 100 if previous_success > 0 else 0
    
    print(f"   Previous (Phase 7.1): {previous_success:.1f}%")
    print(f"   Current (Phase 7.2): {overall_success_index:.1f}%")
    print(f"   Improvement: {improvement:+.1f}% ({improvement_pct:+.1f}%)")
    
    if improvement >= 8:
        print(f"✅ TARGET ACHIEVED! Expected +8-12%, got {improvement:+.1f}%")
    elif improvement > 0:
        print(f"🔄 PARTIAL SUCCESS: Got {improvement:+.1f}%, target was +8-12%")
    else:
        print(f"❌ NO IMPROVEMENT: Need to investigate query enhancement")
    
    # Save detailed results
    results_data = {
        'test_date': datetime.now().isoformat(),
        'overall_success_index': overall_success_index,
        'improvement_from_phase_7_1': improvement,
        'success_at_3_rate': total_success_at_3/len(results),
        'avg_precision_at_3': avg_precision_at_3,
        'avg_mrr': avg_mrr,
        'detailed_results': results,
        'scoring_weights': {
            'similarity': 0.65,
            'program': 0.15,
            'year': 0.05,
            'metadata': 0.10,
            'keywords': 0.05
        }
    }
    
    with open('phase7_2_query_enhancement_results.json', 'w', encoding='utf-8') as f:
        json.dump(results_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 Detailed results saved to: phase7_2_query_enhancement_results.json")
    
    return overall_success_index, improvement

if __name__ == "__main__":
    asyncio.run(test_query_enhancement())
