"""
🎯 Simple Multi-modal Test for Integration Verification
=====================================================

Simple test to verify multi-modal processor integration works correctly.

Author: Augment Agent
Date: 2025-01-05
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any
from io import BytesIO

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from multimodal_processor import MultiModalProcessor, MultiModalConfig, ProcessingResult
    MULTIMODAL_AVAILABLE = True
    print("✅ Multi-modal processor imports successful")
except ImportError as e:
    MULTIMODAL_AVAILABLE = False
    print(f"❌ Multi-modal processor not available: {e}")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_basic_functionality():
    """Test basic multi-modal processor functionality"""
    print("\n🔍 Testing basic multi-modal functionality...")
    
    if not MULTIMODAL_AVAILABLE:
        return {"success": False, "error": "Multi-modal processor not available"}
    
    try:
        # Initialize processor
        config = MultiModalConfig(
            ocr_language="bul+eng",
            enhance_images=True,
            extract_tables=True,
            use_vision_model=False,  # Disable vision model for basic test
            use_bulgarian_nlp=True,
            max_file_size_mb=5
        )
        
        processor = MultiModalProcessor(config)
        print("✅ Multi-modal processor initialized successfully")
        
        # Test with simple text content
        test_text = """
        ОПЕРАТИВНА ПРОГРАМА "ИНОВАЦИИ И КОНКУРЕНТОСПОСОБНОСТ" 2021-2027
        
        Програмата подкрепя:
        - Иновации в малки и средни предприятия
        - Дигитализация на бизнеса
        - Енергийна ефективност
        
        Общ бюджет: 1.2 милиарда лева
        Период: 2021-2027 година
        """
        
        # Process as HTML content
        result = await processor.process_document(
            content=test_text.encode('utf-8'),
            content_type="text/html",
            source_url="test_document.html"
        )
        
        test_result = {
            "success": result.success,
            "processing_time": result.processing_time,
            "text_length": len(result.extracted_text),
            "has_bulgarian_text": "ОПЕРАТИВНА" in result.extracted_text,
            "confidence_score": result.confidence_score,
            "error": result.error_message
        }
        
        print(f"✅ Basic processing result:")
        print(f"   Success: {result.success}")
        print(f"   Text length: {len(result.extracted_text)} chars")
        print(f"   Processing time: {result.processing_time:.3f}s")
        print(f"   Has Bulgarian text: {'ОПЕРАТИВНА' in result.extracted_text}")
        
        if result.extracted_text:
            print(f"   Text preview: {result.extracted_text[:100]}...")
        
        return test_result
        
    except Exception as e:
        logger.error(f"❌ Basic functionality test failed: {e}")
        return {"success": False, "error": str(e)}

async def test_integration_with_crawl4ai():
    """Test integration with main crawl4ai system"""
    print("\n🔍 Testing integration with crawl4ai system...")
    
    try:
        # Import main crawl4ai context
        from crawl4ai_mcp import app_context, MULTIMODAL_AVAILABLE as CRAWL4AI_MULTIMODAL
        
        if not CRAWL4AI_MULTIMODAL:
            return {"success": False, "error": "Multi-modal not available in crawl4ai"}
        
        # Check if app_context has multimodal_processor
        if not app_context:
            return {"success": False, "error": "App context not initialized"}
        
        has_multimodal = hasattr(app_context, 'multimodal_processor') and app_context.multimodal_processor is not None
        
        result = {
            "success": has_multimodal,
            "app_context_available": app_context is not None,
            "multimodal_processor_available": has_multimodal,
            "multimodal_imports_available": CRAWL4AI_MULTIMODAL
        }
        
        print(f"✅ Integration test result:")
        print(f"   App context available: {result['app_context_available']}")
        print(f"   Multi-modal imports: {result['multimodal_imports_available']}")
        print(f"   Multi-modal processor: {result['multimodal_processor_available']}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return {"success": False, "error": str(e)}

async def test_configuration():
    """Test multi-modal configuration options"""
    print("\n🔍 Testing configuration options...")
    
    if not MULTIMODAL_AVAILABLE:
        return {"success": False, "error": "Multi-modal processor not available"}
    
    try:
        # Test different configurations
        configs = [
            {
                "name": "basic",
                "config": MultiModalConfig(
                    ocr_language="eng",
                    enhance_images=False,
                    extract_tables=False,
                    use_vision_model=False,
                    use_bulgarian_nlp=False
                )
            },
            {
                "name": "bulgarian_optimized", 
                "config": MultiModalConfig(
                    ocr_language="bul+eng",
                    enhance_images=True,
                    extract_tables=True,
                    use_vision_model=False,
                    use_bulgarian_nlp=True
                )
            }
        ]
        
        results = []
        
        for config_test in configs:
            try:
                processor = MultiModalProcessor(config_test["config"])
                results.append({
                    "name": config_test["name"],
                    "success": True,
                    "error": None
                })
                print(f"   ✅ {config_test['name']} configuration: OK")
            except Exception as e:
                results.append({
                    "name": config_test["name"],
                    "success": False,
                    "error": str(e)
                })
                print(f"   ❌ {config_test['name']} configuration: {e}")
        
        successful_configs = len([r for r in results if r["success"]])
        
        return {
            "success": successful_configs > 0,
            "successful_configs": successful_configs,
            "total_configs": len(configs),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        return {"success": False, "error": str(e)}

async def main():
    """Main test function"""
    print("🎯 Simple Multi-modal Integration Test")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Integration with Crawl4AI", test_integration_with_crawl4ai),
        ("Configuration Options", test_configuration)
    ]
    
    results = {}
    successful_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = await test_func()
            results[test_name] = result
            if result.get("success"):
                successful_tests += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
        except Exception as e:
            results[test_name] = {"success": False, "error": str(e)}
            print(f"❌ {test_name}: FAILED - {e}")
    
    # Summary
    print(f"\n📊 Test Summary:")
    print("=" * 50)
    print(f"Tests Passed: {successful_tests}/{len(tests)}")
    print(f"Success Rate: {(successful_tests/len(tests)*100):.1f}%")
    
    if successful_tests > 0:
        print("✅ Multi-modal processor is working!")
        if successful_tests == len(tests):
            print("🎉 All tests passed - multi-modal integration is complete!")
        else:
            print("⚠️ Some tests failed - check configuration and dependencies")
    else:
        print("❌ Multi-modal processor has issues - check installation and dependencies")
    
    return {
        "overall_success": successful_tests > 0,
        "tests_passed": successful_tests,
        "total_tests": len(tests),
        "success_rate": f"{(successful_tests/len(tests)*100):.1f}%",
        "results": results
    }

if __name__ == "__main__":
    asyncio.run(main())
