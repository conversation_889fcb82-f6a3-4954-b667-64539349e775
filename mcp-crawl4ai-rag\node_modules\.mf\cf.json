{"clientTcpRtt": 7, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "EU", "asn": 8866, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "BG", "isEUCountry": "1", "region": "<PERSON><PERSON><PERSON>", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "UYLT3LYyokl4tU1udgXV7ADHui8BVDkgq3PIlRXkMf8=", "tlsExportedAuthenticator": {"clientFinished": "12a426e6da559624d5cf1d3a840e6e46ec9688ceeb9df5e5aa4de47849844527fa56d6b8707bce11126eb4b73648fc52", "clientHandshake": "3a49e97e575b05dfcafc22f0c36e3bbc662c7ab49252ed33849972c43e7eb842df9e67061dd8414276947a9481cbeeb4", "serverHandshake": "056d152b2cc7dd07e96e237d06d1856f6eeed0d54a6f8188e100a08b2a3a05a8bc2f0ac9a30a819345f9aa9acce7bbc0", "serverFinished": "ea82a96912034fd9cdbf31c34ce2b97f627e0b4eebf9cce1aca3da766bc942618b3b4418935e6ff614111ccec8dd5be4"}, "tlsClientHelloLength": "386", "colo": "SOF", "timezone": "Europe/Sofia", "longitude": "23.55790", "latitude": "43.21590", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "3000", "city": "<PERSON><PERSON><PERSON>", "tlsVersion": "TLSv1.3", "regionCode": "06", "asOrganization": "Vivacom", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}