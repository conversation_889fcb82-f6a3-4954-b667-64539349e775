#!/usr/bin/env python3
"""
Test Knowledge Graph Integration
Тества интеграцията на knowledge graph с RAG системата
"""

import asyncio
import logging
import json
import time
import os
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, Any

# Load environment variables
env_path = Path(__file__).parent / '.env'
load_dotenv(env_path)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_knowledge_graph_integration():
    """Тества knowledge graph integration"""
    
    logger.info("🔗 ЗАПОЧВАМ ТЕСТ НА KNOWLEDGE GRAPH INTEGRATION")
    logger.info("=" * 60)
    
    try:
        # Import необходимите модули
        from src.utils import knowledge_graph_enhanced_rag_query, get_supabase_client
        
        # Инициализирай Supabase клиент
        supabase_client = get_supabase_client()
        if not supabase_client:
            logger.error("❌ Не мога да инициализирам Supabase клиент")
            return
        
        logger.info("✅ Supabase клиент инициализиран")
        
        # Тестови заявки
        test_queries = [
            {
                "query": "Какви програми са подходящи за МСП в областта на дигитализацията?",
                "organization_type": "МСП",
                "description": "МСП заявка за дигитализация"
            },
            {
                "query": "Програми за НПО в социалната сфера",
                "organization_type": "НПО", 
                "description": "НПО заявка за социални програми"
            },
            {
                "query": "Университетски програми за научни изследвания",
                "organization_type": "университет",
                "description": "Университетска заявка за изследвания"
            },
            {
                "query": "ОПИК програми за иновации",
                "organization_type": None,
                "description": "Обща заявка за ОПИК"
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_queries, 1):
            logger.info(f"\n📋 ТЕСТ {i}: {test_case['description']}")
            logger.info(f"Query: {test_case['query']}")
            logger.info(f"Organization type: {test_case['organization_type']}")
            
            start_time = time.time()
            
            try:
                # Изпълни knowledge graph enhanced RAG query
                result = await knowledge_graph_enhanced_rag_query(
                    query=test_case['query'],
                    supabase_client=supabase_client,
                    organization_type=test_case['organization_type'],
                    similarity_threshold=0.62,
                    final_top_k=3
                )
                
                execution_time = time.time() - start_time
                
                # Анализирай резултата
                if isinstance(result, dict):
                    num_results = len(result.get('results', []))
                    improvement_score = result.get('improvement_score', 0.0)
                    kg_insights = result.get('knowledge_graph_insights', {})
                    temporal_alerts = result.get('temporal_alerts', [])
                    eligibility_analysis = result.get('eligibility_analysis', {})
                    method = result.get('method', 'unknown')
                    
                    logger.info(f"✅ Резултат получен: {execution_time:.2f}s")
                    logger.info(f"📊 Брой резултати: {num_results}")
                    logger.info(f"🎯 Improvement score: {improvement_score:.2f}")
                    logger.info(f"🔗 Method: {method}")
                    logger.info(f"🧠 KG insights: {len(kg_insights)} keys")
                    logger.info(f"⏰ Temporal alerts: {len(temporal_alerts)}")
                    logger.info(f"✅ Eligibility analysis: {len(eligibility_analysis)} keys")
                    
                    # Запази резултата
                    results.append({
                        "test_case": test_case,
                        "execution_time": execution_time,
                        "num_results": num_results,
                        "improvement_score": improvement_score,
                        "method": method,
                        "kg_insights_keys": len(kg_insights),
                        "temporal_alerts_count": len(temporal_alerts),
                        "eligibility_analysis_keys": len(eligibility_analysis),
                        "success": True
                    })
                    
                    # Покажи първия резултат ако има
                    if result.get('results'):
                        first_result = result['results'][0]
                        logger.info(f"📄 Първи резултат: {first_result.get('title', 'No title')[:100]}...")
                        if 'kg_relevance_score' in first_result:
                            logger.info(f"🔗 KG relevance score: {first_result['kg_relevance_score']:.2f}")
                    
                else:
                    logger.warning(f"⚠️ Неочакван формат на резултата: {type(result)}")
                    results.append({
                        "test_case": test_case,
                        "execution_time": execution_time,
                        "error": f"Unexpected result type: {type(result)}",
                        "success": False
                    })
                
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"❌ Грешка в тест {i}: {e}", exc_info=True)
                results.append({
                    "test_case": test_case,
                    "execution_time": execution_time,
                    "error": str(e),
                    "success": False
                })
        
        # Обобщение на резултатите
        logger.info("\n" + "=" * 60)
        logger.info("📊 ОБОБЩЕНИЕ НА ТЕСТОВЕТЕ")
        logger.info("=" * 60)
        
        successful_tests = [r for r in results if r.get('success', False)]
        failed_tests = [r for r in results if not r.get('success', False)]
        
        logger.info(f"✅ Успешни тестове: {len(successful_tests)}/{len(results)}")
        logger.info(f"❌ Неуспешни тестове: {len(failed_tests)}/{len(results)}")
        
        if successful_tests:
            avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
            avg_results = sum(r['num_results'] for r in successful_tests) / len(successful_tests)
            avg_improvement = sum(r['improvement_score'] for r in successful_tests) / len(successful_tests)
            
            logger.info(f"⏱️ Средно време за изпълнение: {avg_time:.2f}s")
            logger.info(f"📋 Среден брой резултати: {avg_results:.1f}")
            logger.info(f"🎯 Среден improvement score: {avg_improvement:.2f}")
            
            # Анализ на методите
            methods = [r['method'] for r in successful_tests]
            method_counts = {}
            for method in methods:
                method_counts[method] = method_counts.get(method, 0) + 1
            
            logger.info("🔧 Използвани методи:")
            for method, count in method_counts.items():
                logger.info(f"  - {method}: {count} пъти")
        
        if failed_tests:
            logger.info("❌ Грешки в тестовете:")
            for i, failed_test in enumerate(failed_tests, 1):
                logger.info(f"  {i}. {failed_test['test_case']['description']}: {failed_test.get('error', 'Unknown error')}")
        
        # Запази резултатите в JSON файл
        output_file = "knowledge_graph_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "timestamp": time.time(),
                "total_tests": len(results),
                "successful_tests": len(successful_tests),
                "failed_tests": len(failed_tests),
                "results": results
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Резултатите са запазени в {output_file}")
        
        # Финален статус
        if len(successful_tests) == len(results):
            logger.info("🎉 ВСИЧКИ ТЕСТОВЕ ПРЕМИНАХА УСПЕШНО!")
            return True
        elif len(successful_tests) > 0:
            logger.info("⚠️ ЧАСТИЧНО УСПЕШНИ ТЕСТОВЕ")
            return False
        else:
            logger.info("❌ ВСИЧКИ ТЕСТОВЕ СЕ ПРОВАЛИХА")
            return False
            
    except Exception as e:
        logger.error(f"❌ Критична грешка в теста: {e}", exc_info=True)
        return False

async def main():
    """Main function"""
    success = await test_knowledge_graph_integration()
    if success:
        print("\n✅ Knowledge Graph Integration тестът премина успешно!")
    else:
        print("\n❌ Knowledge Graph Integration тестът се провали!")

if __name__ == "__main__":
    asyncio.run(main())
