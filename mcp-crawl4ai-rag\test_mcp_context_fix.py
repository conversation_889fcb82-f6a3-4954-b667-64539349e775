#!/usr/bin/env python3
"""
Тест за поправката на MCP Context Initialization
Проверява дали unified_rag_query работи след поправката
"""

import asyncio
import sys
import os
import time

# Добави src директорията към path
sys.path.append(os.path.join(os.getcwd(), 'src'))

async def test_mcp_context_fix():
    """Тества дали MCP context initialization работи"""
    
    print("🧪 ТЕСТ НА MCP CONTEXT INITIALIZATION ПОПРАВКАТА")
    print("="*60)
    
    try:
        # Импортираме MCP функцията
        from crawl4ai_mcp import unified_rag_query
        
        print("✅ Успешен импорт на unified_rag_query")
        
        # Симулираме MCP Context (празен обект)
        class MockContext:
            pass
        
        # Тестваме unified_rag_query с реална заявка
        test_query = "европейски фондове за малки предприятия"
        print(f"📝 Тестова заявка: '{test_query}'")
        
        # ТЕСТ 1: Standard режим
        print("\n🔍 ТЕСТ 1: Standard режим")
        start_time = time.time()
        
        result_standard = await unified_rag_query(
            ctx=MockContext(),
            query=test_query,
            mode="standard",
            match_count=3
        )
        
        standard_time = time.time() - start_time
        print(f"⏱️ Standard режим време: {standard_time:.2f}s")
        
        # Парсираме резултата
        import json
        try:
            result_data = json.loads(result_standard)
            if result_data.get("success"):
                print(f"✅ Standard режим: {result_data.get('results_count', 0)} резултата")
                print(f"📊 Метод: {result_data.get('method', 'unknown')}")
            else:
                print(f"❌ Standard режим грешка: {result_data.get('error', 'unknown')}")
        except json.JSONDecodeError:
            print(f"❌ Standard режим: Невалиден JSON отговор")
            print(f"Raw response: {result_standard[:200]}...")
        
        # ТЕСТ 2: Advanced режим
        print("\n🚀 ТЕСТ 2: Advanced режим")
        start_time = time.time()
        
        result_advanced = await unified_rag_query(
            ctx=MockContext(),
            query=test_query,
            mode="advanced",
            match_count=3
        )
        
        advanced_time = time.time() - start_time
        print(f"⏱️ Advanced режим време: {advanced_time:.2f}s")
        
        # Парсираме резултата
        try:
            result_data = json.loads(result_advanced)
            if result_data.get("success"):
                print(f"✅ Advanced режим: {result_data.get('results_count', 0)} резултата")
                print(f"📊 Метод: {result_data.get('method', 'unknown')}")
            else:
                print(f"❌ Advanced режим грешка: {result_data.get('error', 'unknown')}")
        except json.JSONDecodeError:
            print(f"❌ Advanced режим: Невалиден JSON отговор")
            print(f"Raw response: {result_advanced[:200]}...")
        
        # РЕЗУЛТАТИ
        print("\n" + "="*60)
        print("📊 РЕЗУЛТАТИ ОТ ТЕСТА:")
        print(f"⏱️ Standard режим: {standard_time:.2f}s")
        print(f"⏱️ Advanced режим: {advanced_time:.2f}s")
        
        if standard_time > 0 and advanced_time > 0:
            if standard_time > advanced_time:
                ratio = standard_time / advanced_time
                print(f"⚠️ Performance парадокс: Standard е {ratio:.1f}x по-бавен от Advanced")
            else:
                ratio = advanced_time / standard_time
                print(f"✅ Нормална производителност: Advanced е {ratio:.1f}x по-бавен от Standard")
        
        print("\n🎯 ЗАКЛЮЧЕНИЕ:")
        print("✅ MCP Context Initialization поправката работи!")
        print("✅ unified_rag_query вече може да се извиква директно")
        print("✅ Автоматична инициализация на app_context")
        
    except Exception as e:
        print(f"❌ Грешка в теста: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mcp_context_fix())
