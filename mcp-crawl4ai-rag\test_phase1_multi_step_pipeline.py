#!/usr/bin/env python3
"""
Phase 1 Critical Improvements - Multi-Step Pipeline Test
Тестване на новия multi-step retrieval pipeline
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import time
import logging
from typing import Dict, Any, List

# Настройка на logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_multi_step_pipeline():
    """Тестване на multi-step pipeline"""
    
    print("🧪 PHASE 1 CRITICAL IMPROVEMENTS - MULTI-STEP PIPELINE TEST")
    print("=" * 70)
    
    try:
        # Импортирай функциите
        from src.utils import multi_step_rag_query, MULTI_STEP_PIPELINE_AVAILABLE
        
        print(f"📊 Multi-Step Pipeline наличен: {MULTI_STEP_PIPELINE_AVAILABLE}")
        
        if not MULTI_STEP_PIPELINE_AVAILABLE:
            print("⚠️ Multi-Step Pipeline не е наличен - тестът ще използва fallback")
        
        # Тестови заявки
        test_queries = [
            "Кои са програмите за развитие на регионите в България?",
            "Interreg програми за трансгранично сътрудничество",
            "ОПРР финансиране на транспортни проекти",
            "Horizon Europe иновационни проекти",
            "Програми за малки и средни предприятия"
        ]
        
        print(f"\n🔍 Тестване с {len(test_queries)} заявки...")
        
        all_results = []
        total_time = 0
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{'='*50}")
            print(f"🔍 ТЕСТ {i}: {query}")
            print(f"{'='*50}")
            
            start_time = time.time()
            
            try:
                # Тествай multi-step pipeline
                result = multi_step_rag_query(
                    query=query,
                    max_results=5,
                    similarity_threshold=0.3,
                    enable_groq_reranking=True,
                    enable_program_extraction=True,
                    enable_bulgarian_nlp=True,
                    enable_query_expansion=True,
                    enable_content_enhancement=True,
                    debug=True
                )
                
                processing_time = time.time() - start_time
                total_time += processing_time
                
                # Анализирай резултата
                documents = result.get('documents', [])
                method = result.get('method', 'unknown')
                success_metrics = result.get('success_metrics', {})
                query_analysis = result.get('query_analysis', {})
                program_matches = result.get('program_matches', {})
                
                print(f"\n✅ РЕЗУЛТАТ:")
                print(f"  📄 Документи: {len(documents)}")
                print(f"  ⏱️ Време: {processing_time:.2f}s")
                print(f"  🔧 Метод: {method}")
                
                if success_metrics:
                    print(f"  📊 Метрики:")
                    for key, value in success_metrics.items():
                        if isinstance(value, float):
                            print(f"    {key}: {value:.3f}")
                        else:
                            print(f"    {key}: {value}")
                
                if query_analysis:
                    keywords = query_analysis.get('keywords', [])
                    expanded_terms = query_analysis.get('expanded_terms', [])
                    if keywords:
                        print(f"  🔑 Ключови думи: {', '.join(keywords[:5])}")
                    if expanded_terms and len(expanded_terms) > 1:
                        print(f"  📈 Разширени термини: {len(expanded_terms)}")
                
                if program_matches:
                    total_programs = program_matches.get('total_programs', 0)
                    unique_programs = program_matches.get('unique_programs', [])
                    if total_programs > 0:
                        print(f"  🏷️ Програми: {total_programs} уникални")
                        if unique_programs:
                            print(f"    Примери: {', '.join(unique_programs[:3])}")
                
                # Покажи топ 3 документа
                print(f"\n📄 ТОП 3 ДОКУМЕНТА:")
                for j, doc in enumerate(documents[:3], 1):
                    title = doc.get('title', 'Без заглавие')[:60]
                    similarity = doc.get('similarity_score', 0)
                    final_score = doc.get('final_pipeline_score', doc.get('enhanced_score', 0))
                    programs = doc.get('extracted_programs', [])
                    
                    print(f"  {j}. {title}...")
                    print(f"     Similarity: {similarity:.3f} | Final Score: {final_score:.3f}")
                    
                    if programs:
                        program_names = [p.get('name', p) for p in programs[:2]]
                        print(f"     Програми: {', '.join(program_names)}")
                
                # Запази резултата
                all_results.append({
                    'query': query,
                    'documents_count': len(documents),
                    'processing_time': processing_time,
                    'method': method,
                    'success_metrics': success_metrics,
                    'has_programs': len(program_matches.get('unique_programs', [])) > 0
                })
                
            except Exception as e:
                print(f"❌ Грешка в тест {i}: {e}")
                logger.error(f"Тест {i} грешка: {e}", exc_info=True)
        
        # Обобщени резултати
        print(f"\n{'='*70}")
        print("📊 ОБОБЩЕНИ РЕЗУЛТАТИ")
        print(f"{'='*70}")
        
        if all_results:
            total_docs = sum(r['documents_count'] for r in all_results)
            avg_time = total_time / len(all_results)
            tests_with_programs = sum(1 for r in all_results if r['has_programs'])
            
            print(f"✅ Успешни тестове: {len(all_results)}/{len(test_queries)}")
            print(f"📄 Общо документи: {total_docs}")
            print(f"⏱️ Средно време: {avg_time:.2f}s")
            print(f"🏷️ Тестове с програми: {tests_with_programs}/{len(all_results)}")
            
            # Метрики по метод
            methods = {}
            for result in all_results:
                method = result['method']
                if method not in methods:
                    methods[method] = 0
                methods[method] += 1
            
            print(f"\n🔧 Използвани методи:")
            for method, count in methods.items():
                print(f"  {method}: {count} тестове")
            
            # Средни метрики
            if all_results[0]['success_metrics']:
                print(f"\n📊 Средни метрики:")
                metric_sums = {}
                metric_counts = {}
                
                for result in all_results:
                    for key, value in result['success_metrics'].items():
                        if isinstance(value, (int, float)):
                            if key not in metric_sums:
                                metric_sums[key] = 0
                                metric_counts[key] = 0
                            metric_sums[key] += value
                            metric_counts[key] += 1
                
                for key in metric_sums:
                    avg_value = metric_sums[key] / metric_counts[key]
                    print(f"  {key}: {avg_value:.3f}")
        
        print(f"\n✅ Multi-Step Pipeline тест завършен успешно!")
        
    except Exception as e:
        print(f"❌ Критична грешка в теста: {e}")
        logger.error(f"Критична грешка: {e}", exc_info=True)
        return False
    
    return True


def test_individual_components():
    """Тестване на отделните компоненти"""
    
    print(f"\n{'='*70}")
    print("🧪 ТЕСТВАНЕ НА ОТДЕЛНИ КОМПОНЕНТИ")
    print(f"{'='*70}")
    
    try:
        # Тест на Bulgarian NLP
        print("\n🔤 Тестване на Bulgarian NLP...")
        try:
            from src.bulgarian_nlp import BulgarianNLP
            nlp = BulgarianNLP()
            
            test_text = "Програма Interreg VI-A IPA България Северна Македония 2021-2027"
            result = nlp.process_text(test_text)
            
            print(f"  ✅ NLP метод: {result['method']}")
            print(f"  🔤 Токени: {len(result['tokens'])}")
            print(f"  🏷️ Именувани единици: {len(result['entities'])}")
            
            keywords = nlp.extract_keywords(test_text)
            print(f"  🔑 Ключови думи: {', '.join(keywords[:3])}")
            
        except Exception as e:
            print(f"  ❌ Bulgarian NLP грешка: {e}")
        
        # Тест на Program Name Extractor
        print("\n🏷️ Тестване на Program Name Extractor...")
        try:
            from src.program_name_extractor import BulgarianProgramExtractor
            extractor = BulgarianProgramExtractor()
            
            test_text = "ОПРР 2021-2027 финансира транспортни проекти. Interreg програмата подкрепя трансгранично сътрудничество."
            matches = extractor.extract_program_names(test_text)
            
            print(f"  ✅ Извлечени програми: {len(matches)}")
            for match in matches[:3]:
                print(f"    {match.exact_name} (confidence: {match.confidence:.2f})")
            
        except Exception as e:
            print(f"  ❌ Program Extractor грешка: {e}")
        
        # Тест на Groq Reranker
        print("\n🤖 Тестване на Groq Reranker...")
        try:
            from src.groq_reranker import create_groq_reranker
            reranker = create_groq_reranker()
            
            test_query = "програми за развитие"
            complexity = reranker.analyze_query_complexity(test_query)
            
            print(f"  ✅ Query complexity: {complexity.get('complexity_score', 'N/A')}")
            print(f"  🔧 Fallback наличен: {hasattr(reranker, '_fallback_score_document')}")
            
        except Exception as e:
            print(f"  ❌ Groq Reranker грешка: {e}")
        
        print(f"\n✅ Тестване на компоненти завършено!")
        
    except Exception as e:
        print(f"❌ Грешка в тестването на компоненти: {e}")


if __name__ == "__main__":
    print("🚀 Започвам Phase 1 Critical Improvements тестове...")
    
    # Тествай отделните компоненти
    test_individual_components()
    
    # Тествай цялостния pipeline
    success = test_multi_step_pipeline()
    
    if success:
        print(f"\n🎉 Всички тестове завършени успешно!")
    else:
        print(f"\n⚠️ Някои тестове имат проблеми - вижте логовете за детайли")
