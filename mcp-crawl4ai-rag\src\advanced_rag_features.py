#!/usr/bin/env python3
"""
🚀 ADVANCED RAG FEATURES MODULE
Имплементира multi-vector search, query classification и context-aware scoring
"""

import re
from typing import List, Dict, Set, Tuple, Optional, Any
import logging
from enum import Enum
import json

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Query classification types"""
    SIMPLE_KEYWORD = "simple_keyword"      # Basic keyword search
    SEMANTIC_COMPLEX = "semantic_complex"  # Complex semantic queries
    PROCEDURAL = "procedural"              # Process/procedure questions
    TEMPORAL = "temporal"                  # Time-based queries
    FINANCIAL = "financial"                # Budget/funding queries
    COMPARATIVE = "comparative"            # Comparison queries

class QueryComplexity(Enum):
    """Query complexity levels"""
    SIMPLE = "simple"      # 1-2 concepts
    MEDIUM = "medium"      # 3-4 concepts
    COMPLEX = "complex"    # 5+ concepts

class AdvancedRAGProcessor:
    """Advanced RAG processing with multi-vector search and intelligent scoring"""
    
    def __init__(self):
        """Initialize advanced RAG processor"""
        
        # Query classification patterns
        self.QUERY_PATTERNS = {
            QueryType.PROCEDURAL: [
                r'как\s+да\s+',
                r'процедур[аи]',
                r'стъпк[аи]',
                r'начин\s+за',
                r'как\s+се\s+',
                r'процес\s+за'
            ],
            QueryType.TEMPORAL: [
                r'\d{4}',  # Years
                r'срок',
                r'време',
                r'период',
                r'дата',
                r'кога',
                r'до\s+кога',
                r'актуален'
            ],
            QueryType.FINANCIAL: [
                r'бюджет',
                r'сума',
                r'лева',
                r'евро',
                r'финансиране',
                r'средства',
                r'размер\s+на',
                r'колко'
            ],
            QueryType.COMPARATIVE: [
                r'разлика',
                r'сравнение',
                r'различие',
                r'или',
                r'vs',
                r'спрямо',
                r'в\s+сравнение'
            ]
        }
        
        # Domain-specific boost factors
        self.DOMAIN_BOOST = {
            'образование': 1.2,
            'околна среда': 1.15,
            'транспорт': 1.1,
            'иновации': 1.25,
            'МСП': 1.3,
            'регионално развитие': 1.1,
            'цифрова трансформация': 1.2,
            'енергийна ефективност': 1.15
        }
        
        # Program priority weights
        self.PROGRAM_PRIORITIES = {
            'развитие на човешките ресурси': 1.3,
            'околна среда': 1.2,
            'транспорт': 1.1,
            'иновации': 1.25,
            'регионално развитие': 1.15
        }
        
    def classify_query(self, query: str) -> Dict[str, Any]:
        """Classify query by type and complexity"""
        if not query:
            return {
                'type': QueryType.SIMPLE_KEYWORD,
                'complexity': QueryComplexity.SIMPLE,
                'confidence': 0.0,
                'features': []
            }
        
        query_lower = query.lower()
        detected_types = []
        features = []
        
        # Check for each query type
        for query_type, patterns in self.QUERY_PATTERNS.items():
            matches = 0
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    matches += 1
                    features.append(f"{query_type.value}:{pattern}")
            
            if matches > 0:
                confidence = min(matches / len(patterns), 1.0)
                detected_types.append((query_type, confidence))
        
        # Determine primary type
        if detected_types:
            primary_type = max(detected_types, key=lambda x: x[1])[0]
            max_confidence = max(detected_types, key=lambda x: x[1])[1]
        else:
            primary_type = QueryType.SEMANTIC_COMPLEX if len(query.split()) > 3 else QueryType.SIMPLE_KEYWORD
            max_confidence = 0.5
        
        # Determine complexity
        word_count = len(query.split())
        concept_count = len([w for w in query.split() if len(w) > 3])
        
        if concept_count <= 2:
            complexity = QueryComplexity.SIMPLE
        elif concept_count <= 4:
            complexity = QueryComplexity.MEDIUM
        else:
            complexity = QueryComplexity.COMPLEX
        
        return {
            'type': primary_type,
            'complexity': complexity,
            'confidence': max_confidence,
            'features': features,
            'word_count': word_count,
            'concept_count': concept_count
        }
    
    def calculate_domain_boost(self, query: str, content: str) -> float:
        """Calculate domain-specific boost score"""
        if not query or not content:
            return 1.0
        
        query_lower = query.lower()
        content_lower = content.lower()
        
        total_boost = 1.0
        matched_domains = 0
        
        for domain, boost_factor in self.DOMAIN_BOOST.items():
            if domain in query_lower and domain in content_lower:
                total_boost *= boost_factor
                matched_domains += 1
        
        # Diminishing returns for multiple domain matches
        if matched_domains > 1:
            total_boost = 1.0 + (total_boost - 1.0) * 0.8
        
        return min(total_boost, 1.5)  # Cap at 50% boost
    
    def calculate_program_relevance(self, query: str, metadata: Dict) -> float:
        """Calculate program-specific relevance score"""
        if not metadata or not query:
            return 0.5
        
        program_name = metadata.get('program_name', '').lower()
        query_lower = query.lower()
        
        # Direct program name matching
        for program, priority in self.PROGRAM_PRIORITIES.items():
            if program in query_lower and program in program_name:
                return priority * 0.8  # High relevance
        
        # Partial matching
        query_words = set(query_lower.split())
        program_words = set(program_name.split())
        
        if query_words and program_words:
            overlap = len(query_words.intersection(program_words))
            if overlap > 0:
                return 0.5 + (overlap / len(query_words)) * 0.3
        
        return 0.5  # Default neutral score
    
    def calculate_temporal_relevance(self, query: str, metadata: Dict) -> float:
        """Calculate temporal relevance based on query and document dates"""
        current_year = 2025
        
        # Extract year from query
        query_years = re.findall(r'\b(20\d{2})\b', query)
        doc_year = metadata.get('year')
        
        if not doc_year:
            return 0.7  # Neutral for documents without year
        
        try:
            doc_year = int(doc_year)
        except (ValueError, TypeError):
            return 0.7
        
        # If query mentions specific year
        if query_years:
            query_year = int(query_years[0])
            year_diff = abs(doc_year - query_year)
            if year_diff == 0:
                return 1.0
            elif year_diff <= 1:
                return 0.9
            elif year_diff <= 2:
                return 0.7
            else:
                return 0.5
        
        # General recency preference
        year_diff = current_year - doc_year
        if year_diff <= 1:
            return 1.0
        elif year_diff <= 2:
            return 0.9
        elif year_diff <= 3:
            return 0.8
        else:
            return 0.6
    
    def calculate_context_aware_score(self, query: str, content: str, metadata: Dict, 
                                    base_similarity: float) -> Dict[str, float]:
        """Calculate comprehensive context-aware scoring"""
        
        # Classify query
        classification = self.classify_query(query)
        
        # Calculate various boost factors
        domain_boost = self.calculate_domain_boost(query, content)
        program_relevance = self.calculate_program_relevance(query, metadata)
        temporal_relevance = self.calculate_temporal_relevance(query, metadata)
        
        # Complexity-based adjustments
        complexity_factor = {
            QueryComplexity.SIMPLE: 1.0,
            QueryComplexity.MEDIUM: 1.1,
            QueryComplexity.COMPLEX: 1.2
        }.get(classification['complexity'], 1.0)
        
        # Type-based adjustments
        type_factor = {
            QueryType.SIMPLE_KEYWORD: 1.0,
            QueryType.SEMANTIC_COMPLEX: 1.1,
            QueryType.PROCEDURAL: 1.15,
            QueryType.TEMPORAL: 1.05,
            QueryType.FINANCIAL: 1.1,
            QueryType.COMPARATIVE: 1.2
        }.get(classification['type'], 1.0)
        
        # Calculate final context score
        context_score = (
            domain_boost * 0.3 +
            program_relevance * 0.25 +
            temporal_relevance * 0.2 +
            complexity_factor * 0.15 +
            type_factor * 0.1
        )
        
        # Combine with base similarity
        final_score = base_similarity * 0.7 + context_score * 0.3
        
        return {
            'final_score': final_score,
            'base_similarity': base_similarity,
            'context_score': context_score,
            'domain_boost': domain_boost,
            'program_relevance': program_relevance,
            'temporal_relevance': temporal_relevance,
            'complexity_factor': complexity_factor,
            'type_factor': type_factor,
            'query_type': classification['type'].value,
            'query_complexity': classification['complexity'].value
        }
    
    def get_optimal_search_strategy(self, query: str) -> Dict[str, Any]:
        """Determine optimal search strategy based on query characteristics"""
        classification = self.classify_query(query)
        
        # Strategy recommendations based on query type and complexity
        strategy_weights = {
            'semantic_weight': 0.65,
            'keyword_weight': 0.05,
            'metadata_weight': 0.15,
            'program_weight': 0.15
        }
        
        # Adjust weights based on query type
        if classification['type'] == QueryType.SIMPLE_KEYWORD:
            strategy_weights['keyword_weight'] = 0.15
            strategy_weights['semantic_weight'] = 0.55
        elif classification['type'] == QueryType.PROCEDURAL:
            strategy_weights['metadata_weight'] = 0.25
            strategy_weights['semantic_weight'] = 0.55
        elif classification['type'] == QueryType.TEMPORAL:
            strategy_weights['metadata_weight'] = 0.20
            strategy_weights['program_weight'] = 0.20
        elif classification['type'] == QueryType.FINANCIAL:
            strategy_weights['program_weight'] = 0.25
            strategy_weights['metadata_weight'] = 0.20
        
        # Adjust based on complexity
        if classification['complexity'] == QueryComplexity.COMPLEX:
            strategy_weights['semantic_weight'] += 0.1
            strategy_weights['keyword_weight'] -= 0.05
            strategy_weights['metadata_weight'] -= 0.05
        
        return {
            'strategy_weights': strategy_weights,
            'query_classification': classification,
            'recommended_match_count': 10 if classification['complexity'] == QueryComplexity.COMPLEX else 5,
            'use_reranking': classification['complexity'] != QueryComplexity.SIMPLE
        }

# Global instance
advanced_rag = AdvancedRAGProcessor()

def classify_query(query: str) -> Dict[str, Any]:
    """Convenience function for query classification"""
    return advanced_rag.classify_query(query)

def calculate_context_aware_score(query: str, content: str, metadata: Dict, base_similarity: float) -> Dict[str, float]:
    """Convenience function for context-aware scoring"""
    return advanced_rag.calculate_context_aware_score(query, content, metadata, base_similarity)

def get_optimal_search_strategy(query: str) -> Dict[str, Any]:
    """Convenience function for search strategy optimization"""
    return advanced_rag.get_optimal_search_strategy(query)

if __name__ == "__main__":
    # Test the advanced features
    test_queries = [
        "процедури за финансиране на проекти",
        "как да кандидатствам за образователни програми 2024",
        "колко е бюджетът за МСП иновации",
        "разлика между околна среда и транспорт програми"
    ]
    
    processor = AdvancedRAGProcessor()
    
    for query in test_queries:
        print(f"\n🔍 Query: {query}")
        
        classification = processor.classify_query(query)
        print(f"📊 Type: {classification['type'].value}")
        print(f"📈 Complexity: {classification['complexity'].value}")
        print(f"🎯 Confidence: {classification['confidence']:.2f}")
        
        strategy = processor.get_optimal_search_strategy(query)
        print(f"⚙️ Weights: {strategy['strategy_weights']}")
        print(f"🔢 Match Count: {strategy['recommended_match_count']}")
        print(f"🔄 Reranking: {strategy['use_reranking']}")
