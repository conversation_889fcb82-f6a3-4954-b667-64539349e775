name = "mcp-crawl4ai-rag"
main = "src/crawl4ai_mcp.py"
compatibility_date = "2024-06-10"
compatibility_flags = ["python_workers"]
account_id = "0fad35d6da80020472becc9e971a5716"

# Секцията за разрешаване на изходящи мрежови заявки.
[[unsafe.bindings]]
name = "allow_outgoing_network"
type = "network"
# За по-голяма сигурност, особено при deployment, ограничете достъпа
# само до необходимите хостове.
rules = [
  { access = "allow", hosts = [
    # Основни домейни за вашия проект:
    "eufunds.bg",            # Главен домейн
    "*.eufunds.bg",          # Всички поддомейни на eufunds.bg (напр. http2020.eufunds.bg)
    
    # Задължителни за Supabase:
    # Заменете 'jbdpiowmhaxghnzhvxse' с реалния ID на вашия Supabase проект,
    # ако е различен или ако използвате специфични URL-и.
    # Обикновено wildcard-овете покриват нуждите.
    "*.supabase.co",         
    "*.supabase.io",         
    
    # Задължително за OpenAI:
    "api.openai.com",

    # Можете да добавите други специфични домейни тук, ако е необходимо, например:
    # "government-portal.bg",
    # "another-api.com",
  ]}
]

# --- Алтернатива за максимална гъвкавост при ЛОКАЛНО ТЕСТВАНЕ ---
# Ако по време на локално тестване с `wrangler dev` искате да разрешите
# ВСИЧКИ изходящи връзки, без да изброявате всеки хост,
# можете да КОМЕНТИРАТЕ горната `rules = [ ... ]` секция
# и да РАЗКОМЕНТИРАТЕ следния ред (само за `wrangler dev`!):
# # rules = [ { access = "allow", hosts = ["*"] } ] # РАЗРЕШАВА ВСИЧКО - САМО ЗА DEV!
# Или просто оставете само `type = "network"` без `rules` за локално тестване,
# което също би трябвало да разреши повечето връзки в `wrangler dev`.
# ПРЕДИ ДЕПЛОЙМЪНТ В CLOUDFLARE, ВИНАГИ ВРЪЩАЙТЕ И КОНФИГУРИРАЙТЕ
# СПЕЦИФИЧНИТЕ `rules` за сигурност!
# --- Край на алтернативата ---


# Секцията [vars] остава празна в този файл.
# Променливите на средата за локално развитие се управляват чрез .dev.vars.
# За deployment в Cloudflare ще се използват Secrets.