import re
import unicodedata
import logging
from typing import List, Set, Dict, Optional, Union, Any
import stanza

# --- START LOGGER INITIALIZATION ---
# This must be before any code that uses the logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
# --- END LOGGER INITIALIZATION ---

# Глобална инициализация на Stanza pipeline
NLP_PIPELINE_BG: Optional[stanza.Pipeline] = None
try:
    logger.info("Зареждане на Stanza pipeline за entity_utils (може да отнеме време)...")
    NLP_PIPELINE_BG = stanza.Pipeline('bg', processors='tokenize,lemma', verbose=False, logging_level='ERROR')
    logger.info("Stanza pipeline за entity_utils зареден успешно.")
except Exception as e:
    logger.warning(f"Неуспешно зареждане на Stanza pipeline в entity_utils: {e}. Лематизацията няма да работи.")

class ProgramNameNormalizer:
    def __init__(self):
        self.nlp = NLP_PIPELINE_BG
        self.general_stopwords: Set[str] = {
            'и', 'за', 'на', 'от', 'до', 'в', 'във', 'със', 'с', 'по', 'при',
            'към', 'чрез', 'между', 'въз', 'под', 'над', 'след', 'преди'
        }
        self.domain_stopwords_l1: Set[str] = {
            # 'програма', # Решихме "програма" да остане значим термин
            'оперативна', 'оперативен', 'мярка', 'схема', 'фонд',
            'инициатива', 'механизъм', 'стратегия'
        }
        self.domain_stopwords_l2: Set[str] = {
            'развитие', 'подкрепа', 'насърчаване', 'подобряване', 'създаване',
            'укрепване', 'повишаване', 'осигуряване', 'изграждане', 'прилагане'
        }
        self.abbreviation_dict: Dict[str, str] = {
            'оп ноир': 'наука образование интелигентен растеж',
            'прчр': 'развитие човешки ресурси',
            'опик': 'иновации конкурентоспособност',
            'опос': 'околна среда',
            'опрр': 'регионално развитие',
            'оптранс': 'транспорт свързаност',
            'флаг': 'местни лидерски групи',
            'лидер': 'местно развитие лидерски групи'
        }
        self.punctuation_pattern = re.compile(r'[\u201E\"\u00AB\u00BB\'()\u005B\u005D\u002D\u2013\u2014\/\\,.;:!?]+')
        self.year_pattern = re.compile(r'\b\d{4}[-–—]\d{4}г?\b|\b\d{4}г?\b')

    def _lemmatize_text_to_tokens(self, text: str) -> List[str]:
        if NLP_PIPELINE_BG is None or not text.strip():
            logger.debug(f"Лематизация: Stanza pipeline не е зареден или текстът е празен ('{text}'). Пропускам.")
            return [t.strip() for t in text.split() if t.strip()]

        logger.debug(f"Лематизация: Подавам към Stanza: '{text}'")
        doc = NLP_PIPELINE_BG(text)
        lemmatized_tokens = []
        for sent in getattr(doc, 'sentences', []):
            for word in sent.words:
                if word.lemma and word.lemma.strip():
                    lemmatized_tokens.append(word.lemma.lower())
        logger.debug(f"Лематизация: Stanza върна леми: {lemmatized_tokens}")
        return lemmatized_tokens

    def normalize(self, text: str, has_year_entity: bool = False) -> Dict[str, Union[str, List[str]]]:
        logger.debug(f"ProgramNameNormalizer: Започвам нормализация за '{text}', has_year_entity={has_year_entity}")

        default_result_structure = {
            "final_normalized_string": "",
            "intermediate_tokens": [],
            "lemmatized_tokens_sorted_unique": []
        }

        if text is None:
            logger.debug(f"ProgramNameNormalizer: Входната стойност е None.")
            return default_result_structure

        text_stripped_at_start = text.strip()
        if not text_stripped_at_start:
            logger.debug(f"ProgramNameNormalizer: Входната стойност е празен стринг или само интервали ('{text}').")
            return default_result_structure

        current_text = text_stripped_at_start
        logger.debug(f"ProgramNameNormalizer: Начален текст за обработка: '{current_text}'")

        if has_year_entity:
            logger.debug(f"ProgramNameNormalizer (Етап 1 - years): Опит за премахване на години от: '{current_text}'")
            text_without_years = self._remove_years(current_text)
            if text_without_years.strip() != current_text.strip():
                logger.debug(f"ProgramNameNormalizer (Етап 1 - years): Години премахнати, резултат: '{text_without_years}'")
                current_text = text_without_years
            else:
                logger.debug(f"ProgramNameNormalizer (Етап 1 - years): Няма години за премахване от '{current_text}' или резултатът е същият.")
        else:
            logger.debug(f"ProgramNameNormalizer (Етап 1 - years): Пропуска се премахване на години (has_year_entity=False). ")

        current_text = self._basic_normalization(current_text)
        logger.debug(f"ProgramNameNormalizer (Етап 2 - basic): '{current_text}'")

        current_text = self._expand_abbreviations(current_text)
        logger.debug(f"ProgramNameNormalizer (Етап 3 - abbr): '{current_text}'")

        fallback_string_if_all_else_fails = current_text.strip()
        text_for_further_processing = current_text

        if not text_for_further_processing.strip():
            logger.debug(f"ProgramNameNormalizer: Текстът е празен след основните обработки. Връщам fallback: '{fallback_string_if_all_else_fails}'")
            default_result_structure["final_normalized_string"] = fallback_string_if_all_else_fails
            fb_tokens = self._tokenize(fallback_string_if_all_else_fails) if fallback_string_if_all_else_fails else []
            default_result_structure["intermediate_tokens"] = fb_tokens
            default_result_structure["lemmatized_tokens_sorted_unique"] = fb_tokens
            return default_result_structure

        tokens_for_intermediate_step = self._tokenize(text_for_further_processing)
        tokens_after_l1_intermediate = self._remove_stopwords_l1(tokens_for_intermediate_step)

        intermediate_tokens_result = tokens_after_l1_intermediate
        if len(tokens_after_l1_intermediate) >= 3:
            tokens_after_l2_intermediate = self._remove_stopwords_l2(tokens_after_l1_intermediate)
            if not tokens_after_l2_intermediate and tokens_after_l1_intermediate:
                intermediate_tokens_result = tokens_after_l1_intermediate
            else:
                intermediate_tokens_result = tokens_after_l2_intermediate
        logger.debug(f"ProgramNameNormalizer (Междинни токени след L1/L2): {intermediate_tokens_result}")

        text_for_lemmatization_final_clean = re.sub(r'[^\w\s&]', '', text_for_further_processing, flags=re.UNICODE)
        text_for_lemmatization_final_clean = re.sub(r'\s+', ' ', text_for_lemmatization_final_clean).strip()
        logger.debug(f"ProgramNameNormalizer (Текст за Stanza СЛЕД агресивно почистване): '{text_for_lemmatization_final_clean}'")

        lemmatized_tokens_from_stanza = self._lemmatize_text_to_tokens(text_for_lemmatization_final_clean)
        logger.debug(f"ProgramNameNormalizer (Етап Лематизация от Stanza): {lemmatized_tokens_from_stanza}")

        if not lemmatized_tokens_from_stanza:
            logger.debug(f"ProgramNameNormalizer: Няма токени след лематизация от Stanza. Връщам структура с intermediate_tokens или fallback_string. ")
            final_str = " ".join(sorted(list(set(intermediate_tokens_result)))) if intermediate_tokens_result else fallback_string_if_all_else_fails
            return {
                "final_normalized_string": final_str.strip(),
                "intermediate_tokens": intermediate_tokens_result,
                "lemmatized_tokens_sorted_unique": []
            }

        lemmas_after_l1_stopwords = self._remove_stopwords_l1(lemmatized_tokens_from_stanza)
        logger.debug(f"ProgramNameNormalizer (Стоп-думи L1 върху леми): {lemmas_after_l1_stopwords}")

        lemmas_after_l2_stopwords = lemmas_after_l1_stopwords
        if len(lemmas_after_l1_stopwords) >= 3:
            temp_lemmas_after_l2 = self._remove_stopwords_l2(lemmas_after_l1_stopwords)
            logger.debug(f"ProgramNameNormalizer (Стоп-думи L2 върху леми): {temp_lemmas_after_l2}")
            if not temp_lemmas_after_l2 and lemmas_after_l1_stopwords:
                lemmas_after_l2_stopwords = lemmas_after_l1_stopwords
            else:
                lemmas_after_l2_stopwords = temp_lemmas_after_l2

        final_lemmas_to_sort = lemmas_after_l2_stopwords
        if not final_lemmas_to_sort:
            logger.warning(f"ProgramNameNormalizer: Няма финални леми за '{text}' след всички филтри.")
            final_str = " ".join(sorted(list(set(intermediate_tokens_result)))) if intermediate_tokens_result else fallback_string_if_all_else_fails
            return {
                "final_normalized_string": final_str.strip(),
                "intermediate_tokens": intermediate_tokens_result,
                "lemmatized_tokens_sorted_unique": []
            }

        sorted_unique_final_lemmas = sorted(list(set(final_lemmas_to_sort)))
        logger.debug(f"ProgramNameNormalizer (Финални сортирани уникални леми): {sorted_unique_final_lemmas}")

        final_normalized_string_output = ' '.join(sorted_unique_final_lemmas)

        if not final_normalized_string_output.strip():
            logger.debug(f"ProgramNameNormalizer: Финалният низ от леми е празен. Опитвам fallback с intermediate или basic.")
            if intermediate_tokens_result:
                final_normalized_string_output = " ".join(sorted(list(set(intermediate_tokens_result))))
            else:
                final_normalized_string_output = fallback_string_if_all_else_fails

            if not final_normalized_string_output.strip():
                sorted_unique_final_lemmas = []
                final_normalized_string_output = ""

        logger.info(f"ProgramNameNormalizer: Оригинал: '{text}' -> Резултат: final_string='{final_normalized_string_output.strip()}', intermediate_tokens={intermediate_tokens_result}, lemmatized_tokens={sorted_unique_final_lemmas}")

        return {
            "final_normalized_string": final_normalized_string_output.strip(),
            "intermediate_tokens": intermediate_tokens_result,
            "lemmatized_tokens_sorted_unique": sorted_unique_final_lemmas
        }

    def _basic_normalization(self, text: str) -> str:
        text_normalized_unicode = unicodedata.normalize('NFKC', text)
        text_lower = text_normalized_unicode.lower()
        text_no_punct = self.punctuation_pattern.sub('', text_lower)
        text_cleaned_spaces = re.sub(r'\s+', ' ', text_no_punct).strip()
        return text_cleaned_spaces

    def _expand_abbreviations(self, text: str) -> str:
        text_to_process_lower_for_exact_match = text.lower().strip()

        if text_to_process_lower_for_exact_match in self.abbreviation_dict:
            expanded_value = self.abbreviation_dict[text_to_process_lower_for_exact_match]
            logger.debug(f"Abbr: Точно съвпадение на целия текст '{text_to_process_lower_for_exact_match}' (оригинал: '{text}') -> '{expanded_value}'")
            return expanded_value

        original_case_tokens = [token for token in text.split() if token]
        lower_case_tokens_for_lookup = [token.lower() for token in original_case_tokens]
        expanded_parts_final = []
        i = 0
        made_expansion_in_loop = False

        while i < len(lower_case_tokens_for_lookup):
            matched_in_current_iteration = False
            for length in range(min(5, len(lower_case_tokens_for_lookup) - i), 0, -1):
                phrase_to_check_lower = " ".join(lower_case_tokens_for_lookup[i: i + length])

                if phrase_to_check_lower in self.abbreviation_dict:
                    expansion = self.abbreviation_dict[phrase_to_check_lower]
                    original_phrase_replaced = " ".join(original_case_tokens[i: i + length])
                    logger.debug(f"Abbr: Съвпадение на фраза '{phrase_to_check_lower}' (оригинал: '{original_phrase_replaced}') -> '{expansion}'")

                    expanded_parts_final.append(expansion)
                    i += length
                    made_expansion_in_loop = True
                    matched_in_current_iteration = True
                    break

            if not matched_in_current_iteration:
                if i < len(original_case_tokens):
                    expanded_parts_final.append(original_case_tokens[i])
                i += 1

        if made_expansion_in_loop:
            result_text = ' '.join(expanded_parts_final)
            result_text = re.sub(r'\s+', ' ', result_text).strip()
            logger.debug(f"Abbr: Резултат след разширяване на абревиатури (от цикъл): '{result_text}' за вход: '{text}'")
            return result_text
        else:
            logger.debug(f"Abbr: Няма намерени абревиатури за разширяване в '{text}'. Връщам оригинал.")
            return text

    def _remove_years(self, text: str) -> str:
        return self.year_pattern.sub('', text).strip()

    def _tokenize(self, text: str) -> List[str]:
        tokens = [t.strip() for t in text.split() if t.strip()]
        return [t for t in tokens if len(t) > 1 or t.isdigit() or t == '&']

    def _remove_stopwords_l1(self, tokens: List[str]) -> List[str]:
        all_l1_stopwords = self.general_stopwords | self.domain_stopwords_l1
        return [t for t in tokens if t not in all_l1_stopwords]

    def _remove_stopwords_l2(self, tokens: List[str]) -> List[str]:
        return [t for t in tokens if t not in self.domain_stopwords_l2]

    def _sort_preserving_numbers(self, tokens: List[str]) -> List[str]:
        numbers_and_symbols = []
        words = []
        for token in tokens:
            if re.fullmatch(r'\d+(\.\d+)?', token):
                numbers_and_symbols.append(token)
            elif not token.isalnum() and token != '&':
                numbers_and_symbols.append(token)
            elif any(char.isdigit() for char in token) and token != '&':
                numbers_and_symbols.append(token)
            else:
                words.append(token)
        words.sort()
        return words + numbers_and_symbols

    def add_abbreviation(self, abbr: str, expansion: str) -> None:
        self.abbreviation_dict[abbr.lower().strip()] = expansion.lower().strip()

program_name_normalizer_instance = ProgramNameNormalizer()

def normalize_extracted_entity(entity: dict,
                             has_year_entity_for_program_name: bool = False
                             ) -> Optional[Dict[str, Any]]:
    if not isinstance(entity, dict) or "type" not in entity or "name" not in entity:
        logger.warning(f"normalize_extracted_entity: Невалидна структура на входната същност: {entity}. Пропускам.")
        return None

    entity_type = entity["type"]
    original_value_from_llm = entity.get("name")

    if original_value_from_llm is None:
        original_value_from_llm = ""
        logger.debug(f"normalize_extracted_entity: Стойността 'name' за същност тип '{entity_type}' е None, третирам като празен стринг.")

    entity_name_processed = str(original_value_from_llm).strip()

    normalized_entity_output: Dict[str, Any] = {
        "type": entity_type,
        "original_value": original_value_from_llm,
        "normalized_value": entity_name_processed,
        "intermediate_tokens": [],
        "lemmatized_tokens_sorted_unique": []
    }

    if not entity_name_processed:
        if not original_value_from_llm or original_value_from_llm.isspace():
            logger.info(f"normalize_extracted_entity: Име на същност (тип '{entity_type}') е празен стринг или само интервали ('{original_value_from_llm}'). Нормализирано до празен стринг.")
            normalized_entity_output["normalized_value"] = ""
        return normalized_entity_output

    if entity_type == "YEAR":
        logger.debug(f"Нормализация на YEAR: Получено име за обработка: '{entity_name_processed}' (оригинал: '{original_value_from_llm}')")
        temp_name_for_year_extraction = re.sub(
            r'(\d{4})\s*(?:г\.?|год\.?|година|годината)\b', r'\1', entity_name_processed, flags=re.IGNORECASE
        )
        years_found = re.findall(r'\b(\d{4})\b', temp_name_for_year_extraction)
        normalized_value_specific = entity_name_processed

        if len(years_found) == 1:
            normalized_value_specific = years_found[0]
        elif len(years_found) == 2:
            try:
                year1 = int(years_found[0]); year2 = int(years_found[1])
                if year1 > year2: year1, year2 = year2, year1
                normalized_value_specific = f"{year1}-{year2}"
            except ValueError:
                logger.warning(f"Невалидни години ({years_found[0]}, {years_found[1]}) в '{temp_name_for_year_extraction}'. Връщам: '{entity_name_processed}'.")
        elif len(years_found) > 2:
            logger.warning(f"Намерени повече от две години ({len(years_found)}) в '{temp_name_for_year_extraction}'. Връщам: '{entity_name_processed}'.")

        normalized_entity_output["normalized_value"] = normalized_value_specific
        if normalized_value_specific != original_value_from_llm:
            logger.info(f"Нормализирана ГОДИНА: '{original_value_from_llm}' -> '{normalized_value_specific}'")

    elif entity_type == "LEGAL_ACT":
        logger.debug(f"Нормализация на LEGAL_ACT: Получено име за обработка: '{entity_name_processed}' (оригинал: '{original_value_from_llm}')")
        prefixes_to_remove = [
            "Регламент", "Закон", "Постановление", "Директива", "Наредба", "Решение", "Указ",
            "Regulation", "Law", "Decree", "Directive", "Ordinance", "Decision", "Order", "Act"
        ]
        temp_name = entity_name_processed
        normalized_by_first_loop = False
        for prefix in prefixes_to_remove:
            pattern = rf"(?i)^\s*\b{re.escape(prefix)}\b\s+(№|No\.?|#)?\s*"
            temp_name, num_subs = re.subn(pattern, "", temp_name)
            temp_name = temp_name.strip()
            if num_subs > 0:
                normalized_by_first_loop = True
                break
        if not normalized_by_first_loop:
            for prefix in prefixes_to_remove:
                pattern_liberal = rf"(?i)^\s*\b{re.escape(prefix)}\s*"
                temp_name, num_subs = re.subn(pattern_liberal, "", temp_name)
                temp_name = temp_name.strip()
                if num_subs > 0:
                    break

        normalized_value_specific = temp_name
        if not temp_name and entity_name_processed:
            normalized_value_specific = entity_name_processed
            logger.warning(f"Нормализацията на LEGAL_ACT '{entity_name_processed}' доведе до празен низ. Връщам обработения (почистен) оригинал.")

        normalized_entity_output["normalized_value"] = normalized_value_specific
        if normalized_value_specific != original_value_from_llm:
            logger.info(f"Нормализиран ПРАВЕН АКТ: '{original_value_from_llm}' -> '{normalized_value_specific}'")

    elif entity_type == "PROGRAM_NAME":
        logger.debug(f"Нормализация на PROGRAM_NAME: Получено име за обработка: '{entity_name_processed}' (оригинал: '{original_value_from_llm}')")
        try:
            normalization_result_dict = program_name_normalizer_instance.normalize(
                entity_name_processed,
                has_year_entity=has_year_entity_for_program_name
            )

            normalized_entity_output["normalized_value"] = normalization_result_dict.get("final_normalized_string", "")
            normalized_entity_output["intermediate_tokens"] = normalization_result_dict.get("intermediate_tokens", [])
            normalized_entity_output["lemmatized_tokens_sorted_unique"] = normalization_result_dict.get("lemmatized_tokens_sorted_unique", [])

            if normalized_entity_output["normalized_value"] != original_value_from_llm:
                if not (not normalized_entity_output["normalized_value"] and not original_value_from_llm.strip()):
                    logger.info(f"Нормализирано ИМЕ НА ПРОГРАМА: '{original_value_from_llm}' -> final_string='{normalized_entity_output['normalized_value']}'")
        except NameError:
            logger.error("Грешка: program_name_normalizer_instance не е дефиниран в обхвата на normalize_extracted_entity!")

    elif normalized_entity_output["normalized_value"] != original_value_from_llm:
        logger.info(f"Нормализирано име (само премахнати интервали): '{original_value_from_llm}' -> '{normalized_entity_output['normalized_value']}' за тип '{entity_type}'")

    return normalized_entity_output

def test_year_normalization():
    test_cases_year = [
        {"name": "2021 - 2027", "type": "YEAR", "expected_normalized_value": "2021-2027"},
        {"name": "2021-2027", "type": "YEAR", "expected_normalized_value": "2021-2027"},
        {"name": "Период 2021/2027", "type": "YEAR", "expected_normalized_value": "2021-2027"},
        {"name": "от 2021 до 2027 година", "type": "YEAR", "expected_normalized_value": "2021-2027"},
        {"name": "от 2021 г. до 2027 г.", "type": "YEAR", "expected_normalized_value": "2021-2027"},
        {"name": "2021г. - 2027г.", "type": "YEAR", "expected_normalized_value": "2021-2027"},
        {"name": "  2023  ", "type": "YEAR", "expected_normalized_value": "2023"},
        {"name": "Финансова година 2024", "type": "YEAR", "expected_normalized_value": "2024"},
        {"name": "2024 г.", "type": "YEAR", "expected_normalized_value": "2024"},
        {"name": "Обхваща годините 2025 и 2026", "type": "YEAR", "expected_normalized_value": "2025-2026"},
        {"name": "Планирано за 2027 или 2028", "type": "YEAR", "expected_normalized_value": "2027-2028"},
        {"name": "Между 2030 и 2029", "type": "YEAR", "expected_normalized_value": "2029-2030"},
        {"name": "само 2022", "type": "YEAR", "expected_normalized_value": "2022"},
        {"name": "Програмен период", "type": "YEAR", "expected_normalized_value": "Програмен период"},
        {"name": "Годината е 123", "type": "YEAR", "expected_normalized_value": "Годината е 123"},
        {"name": "2021, 2022, 2023", "type": "YEAR", "expected_normalized_value": "2021, 2022, 2023"},
        {"name": "   ", "type": "YEAR", "expected_normalized_value": ""},
        {"name": "", "type": "YEAR", "expected_normalized_value": ""},
        {"name": "Някоя си година (не е YEAR)", "type": "OTHER_TYPE", "expected_normalized_value": "Някоя си година (не е YEAR)"}
    ]
    logger.info("\nТестване на нормализацията на YEAR (и други типове, които не са YEAR):")
    for test_case in test_cases_year:
        original_entity_name = test_case['name']
        entity_type = test_case['type']
        expected_norm_val = test_case['expected_normalized_value']
        entity_to_normalize = {"name": original_entity_name, "type": entity_type}
        normalized_entity_structure = normalize_extracted_entity(entity_to_normalize.copy())

        logger.info(f"Вход: {{'name': '{original_entity_name}', 'type': '{entity_type}'}}")
        if normalized_entity_structure is None:
            logger.info(f"Изход: None (Грешка при нормализация!)")
            status = "НЕУСПЕХ (normalized_entity_structure is None)!"
        else:
            actual_normalized_value = normalized_entity_structure.get("normalized_value")
            logger.info(f"Изход (структура): {normalized_entity_structure}")
            logger.info(f"Очаквана normalized_value: '{expected_norm_val}'")

            if actual_normalized_value == expected_norm_val:
                status = "УСПЕХ"
            else:
                status = f"НЕУСПЕХ! (Реално: '{actual_normalized_value}')"
        logger.info(f"Статус: {status}")
        logger.info("---")

def test_legal_act_normalization():
    test_cases = [
        {"name": "Регламент 2021/1057", "type": "LEGAL_ACT", "expected_normalized_value": "2021/1057"},
        {"name": "Закон за изменение и допълнение на ЗОП", "type": "LEGAL_ACT", "expected_normalized_value": "за изменение и допълнение на ЗОП"},
        {"name": "Постановление № 123 от 2024 г.", "type": "LEGAL_ACT", "expected_normalized_value": "123 от 2024 г."},
        {"name": "Directive 2021/1057", "type": "LEGAL_ACT", "expected_normalized_value": "2021/1057"},
        {"name": "2021/1057", "type": "LEGAL_ACT", "expected_normalized_value": "2021/1057"},
        {"name": "  Регламент   2021/1057  ", "type": "LEGAL_ACT", "expected_normalized_value": "2021/1057"},
        {"name": "Регламент№123", "type": "LEGAL_ACT", "expected_normalized_value": "№123"},
        {"name": "Постановление123", "type": "LEGAL_ACT", "expected_normalized_value": "123"},
        {"name": "Само текст", "type": "LEGAL_ACT", "expected_normalized_value": "Само текст"},
        {"name": "Регламент", "type": "LEGAL_ACT", "expected_normalized_value": "Регламент"},
    ]
    logger.info("\nТестване на нормализацията на LEGAL_ACT:")
    for test_case in test_cases:
        original_entity_name = test_case['name']
        entity_type = test_case['type']
        expected_norm_val = test_case['expected_normalized_value']
        entity_to_normalize = {"name": original_entity_name, "type": entity_type}
        normalized_entity_structure = normalize_extracted_entity(entity_to_normalize.copy())

        logger.info(f"Вход: {{'name': '{original_entity_name}', 'type': '{entity_type}'}}")
        if normalized_entity_structure is None:
            logger.info(f"Изход: None (Грешка при нормализация!)")
            status = "НЕУСПЕХ (normalized_entity_structure is None)!"
        else:
            actual_normalized_value = normalized_entity_structure.get("normalized_value")
            logger.info(f"Изход (структура): {normalized_entity_structure}")
            logger.info(f"Очаквана normalized_value: '{expected_norm_val}'")

            if actual_normalized_value == expected_norm_val:
                status = "УСПЕХ"
            else:
                status = f"НЕУСПЕХ! (Реално: '{actual_normalized_value}')"
        logger.info(f"Статус: {status}")
        logger.info("---")

def test_program_name_normalization():
    test_cases_program = [
        {"name": "Оперативна програма „Наука и образование за интелигентен растеж“ 2021-2027", "has_year": True, "expected_final_string": "интелигентен наук образование програма растеж"},
        {"name": "ОП НОИР", "has_year": False, "expected_final_string": "интелигентен наук образование растеж"},
        {"name": "Програма за развитие на човешките ресурси", "has_year": False, "expected_final_string": "програма ресурс човешки"},
        {"name": "ПРЧР", "has_year": False, "expected_final_string": "ресурс човешки"},
        {"name": "Иновации и конкурентоспособност", "has_year": False, "expected_final_string": "иновация конкурентоспособност"},
        {"name": "Оперативна програма Иновации и конкурентоспособност (ОПИК)", "has_year": False, "expected_final_string": "иновация конкурентоспособност програма"},
        {"name": "Конкурентоспособност и иновации", "has_year": False, "expected_final_string": "иновация конкурентоспособност"},
        {"name": "Програма Морско дело и рибарство 2014-2020", "has_year": True, "expected_final_string": "дело морско програма рибарство"},
        {"name": "Морско дело & Рибарство", "has_year": False, "expected_final_string": "дело морско рибарство"},
        {"name": "ПМДР", "has_year": False, "expected_final_string": "пмдр"},
        {"name": "Много Кратко Име", "has_year": False, "expected_final_string": "име кратък много"},
        {"name": "Само Развитие", "has_year": False, "expected_final_string": "развитие само"},
        {"name": "Подкрепа", "has_year": False, "expected_final_string": "подкрепа"},
        {"name": "", "has_year": False, "expected_final_string": ""},
        {"name": "   ", "has_year": False, "expected_final_string": ""},
    ]

    logger.info("\nТестване на нормализацията на PROGRAM_NAME (ProgramNameNormalizer.normalize):")
    for test_case in test_cases_program:
        original_name = test_case['name']
        has_year = test_case.get("has_year", False)
        expected_str = test_case['expected_final_string']

        normalization_result_dict = program_name_normalizer_instance.normalize(
            original_name,
            has_year_entity=has_year
        )

        logger.info(f"Вход: '{original_name}', has_year: {has_year}")

        actual_final_string = normalization_result_dict.get("final_normalized_string")
        intermediate_tokens = normalization_result_dict.get("intermediate_tokens")
        lemmatized_tokens = normalization_result_dict.get("lemmatized_tokens_sorted_unique")

        logger.info(f"Изход (речник): ")
        logger.info(f"  final_normalized_string: '{actual_final_string}'")
        logger.info(f"  intermediate_tokens: {intermediate_tokens}")
        logger.info(f"  lemmatized_tokens_sorted_unique: {lemmatized_tokens}")
        logger.info(f"Очаквана 'final_normalized_string': '{expected_str}'")

        if actual_final_string == expected_str:
            status = "УСПЕХ"
        else:
            status = f"НЕУСПЕХ! (Реална 'final_normalized_string': '{actual_final_string}')"
        logger.info(f"Статус: {status}")
        logger.info("---")

if __name__ == "__main__":
    logger.info("!!! ТЕСТ НА PRINT В main БЛОКА !!!")
    if NLP_PIPELINE_BG is None:
        logger.warning("!!! ПРЕДУПРЕЖДЕНИЕ: Stanza pipeline не е зареден. Тестовете за PROGRAM_NAME може да не са напълно точни или да пропуснат лематизация. !!!")
        logger.warning("!!! Моля, уверете се, че моделът за Stanza е изтеглен и pipeline-ът се инициализира успешно. !!!")

    test_year_normalization()
    test_legal_act_normalization()
    test_program_name_normalization()

    print("\nТест на ProgramNameNormalizer с кавички (след промяна в _basic_normalization): ")
    test_text_with_quotes = "Програма „Развитие на човешките ресурси“"
    result_dict_quotes_test = program_name_normalizer_instance.normalize(
        test_text_with_quotes,
        has_year_entity=False
    )
    print(f"Оригинал: '{test_text_with_quotes}'")
    print(f"Резултат от нормализацията: {result_dict_quotes_test}")

    if '"' not in result_dict_quotes_test.get("lemmatized_tokens_sorted_unique", []):
        print(">>> УСПЕХ: Кавичката НЕ присъства в lemmatized_tokens_sorted_unique, както се очаква! <<< ")
    else:
        print(">>> НЕУСПЕХ: Кавичката ВСЕ ОЩЕ присъства в lemmatized_tokens_sorted_unique! <<< ")
    print("---")

