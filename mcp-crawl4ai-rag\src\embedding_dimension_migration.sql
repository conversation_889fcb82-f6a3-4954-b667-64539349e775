
-- Database Migration: Fix Embedding Dimension Mismatch
-- Поправка на несъответствието между модел и database schema

-- ============================================================
-- СТЪПКА 1: BACKUP НА ТЕКУЩИ ДАННИ (ПРЕПОРЪЧИТЕЛНО)
-- ============================================================

-- Създаване на backup таблица
CREATE TABLE crawled_pages_backup AS 
SELECT * FROM crawled_pages;

-- ============================================================
-- СТЪПКА 2: АНАЛИЗ НА ТЕКУЩИ РАЗМЕРНОСТИ
-- ============================================================

-- Проверка на текущи embedding размерности
SELECT 
    id,
    array_length(embedding, 1) as embedding_dimension,
    url
FROM crawled_pages 
LIMIT 10;

-- Статистика за размерности
SELECT 
    array_length(embedding, 1) as dimension,
    COUNT(*) as count
FROM crawled_pages 
WHERE embedding IS NOT NULL
GROUP BY array_length(embedding, 1)
ORDER BY count DESC;

-- ============================================================
-- СТЪПКА 3: ОПЦИЯ А - ПРОМЯНА НА SCHEMA ЗА СЪВМЕСТИМОСТ
-- ============================================================

-- Ако искаме да запазим данните и променим schema-та:
-- ALTER TABLE crawled_pages ALTER COLUMN embedding TYPE vector(1024);

-- Обновяване на RPC функциите за новата размерност
DROP FUNCTION IF EXISTS match_crawled_pages_v4_debug;

CREATE OR REPLACE FUNCTION match_crawled_pages_v4_debug(
    p_query_embedding vector(1024),
    p_match_count integer DEFAULT 10,
    p_min_similarity_threshold double precision DEFAULT 0.1,
    p_min_program_containment_threshold double precision DEFAULT 0.0,
    p_query_entities_filter text[] DEFAULT ARRAY[]::text[],
    p_query_program_canonical_names_jsonb jsonb DEFAULT '{}'::jsonb,
    p_source_filters_jsonb jsonb DEFAULT '{}'::jsonb,
    p_weight_similarity double precision DEFAULT 0.4,
    p_weight_program_name double precision DEFAULT 0.3,
    p_weight_year double precision DEFAULT 0.3
)
RETURNS TABLE(
    id bigint,
    url text,
    content text,
    metadata jsonb,
    similarity_score double precision,
    program_name_score double precision,
    year_score double precision,
    final_score double precision
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cp.id,
        cp.url,
        cp.content,
        cp.metadata,
        (1 - (cp.embedding <=> p_query_embedding)) as similarity_score,
        0.0 as program_name_score,  -- Simplified for now
        0.0 as year_score,          -- Simplified for now
        (1 - (cp.embedding <=> p_query_embedding)) as final_score
    FROM crawled_pages cp
    WHERE cp.embedding IS NOT NULL
    AND (1 - (cp.embedding <=> p_query_embedding)) >= p_min_similarity_threshold
    ORDER BY cp.embedding <=> p_query_embedding
    LIMIT p_match_count;
END;
$$;

-- ============================================================
-- СТЪПКА 4: ОПЦИЯ Б - ИЗЧИСТВАНЕ И REGENERATION НА EMBEDDINGS
-- ============================================================

-- Ако искаме да изчистим всички embeddings и да ги regenerate:
-- UPDATE crawled_pages SET embedding = NULL;

-- След това трябва да се стартира процес за regeneration на всички embeddings
-- с правилната размерност (1024)

-- ============================================================
-- СТЪПКА 5: ОБНОВЯВАНЕ НА ИНДЕКСИ
-- ============================================================

-- Премахване на стари индекси
DROP INDEX IF EXISTS crawled_pages_embedding_idx;
DROP INDEX IF EXISTS crawled_pages_embedding_hnsw_cosine_idx;
DROP INDEX IF EXISTS crawled_pages_embedding_hnsw_ip_idx;

-- Създаване на нови HNSW индекси с правилната размерност
CREATE INDEX crawled_pages_embedding_hnsw_cosine_idx 
ON crawled_pages 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

CREATE INDEX crawled_pages_embedding_hnsw_ip_idx 
ON crawled_pages 
USING hnsw (embedding vector_ip_ops) 
WITH (m = 16, ef_construction = 64);

-- ============================================================
-- СТЪПКА 6: ТЕСТВАНЕ НА НОВАТА SCHEMA
-- ============================================================

-- Тест на RPC функцията
-- SELECT * FROM match_crawled_pages_v4_debug(
--     ARRAY[0.1, 0.2, 0.3, ...]::vector(1024),
--     10,
--     0.1,
--     0.0,
--     ARRAY[]::text[],
--     '{}'::jsonb,
--     '{}'::jsonb,
--     1.0,
--     0.0,
--     0.0
-- );

-- ============================================================
-- ЗАБЕЛЕЖКИ
-- ============================================================

/*
КРИТИЧНО: Преди изпълнение на тази migration:

1. Направете пълен backup на базата данни
2. Тествайте на development environment първо
3. Планирайте downtime за production
4. Подгответе процес за regeneration на embeddings
5. Уведомете потребителите за временното прекъсване

ПРЕПОРЪЧАНА СТРАТЕГИЯ:
- Използвайте ОПЦИЯ Б (изчистване и regeneration)
- Това ще гарантира консистентност на данните
- Ще подобри качеството на търсенето значително
*/
