# --- START OF FILE: src/utils.py (MODIFIED FOR PHASE 1) ---

import os
import concurrent.futures
from typing import List, Dict, Any, Optional, Tuple, Union, TYPE_CHECKING
import json
from supabase import create_client, Client
from urllib.parse import urlparse
import openai # Standard OpenAI client for sync operations
from openai import AsyncOpenAI # For async operations
import logging
import asyncio
import re
import pybreaker
import numpy as np
import hashlib
from collections import OrderedDict
import time

# Phase 1 Critical Improvements - Multi-Step Pipeline Integration
try:
    from multi_step_pipeline import MultiStepRetrievalPipeline, PipelineConfig, create_multi_step_pipeline
    MULTI_STEP_PIPELINE_AVAILABLE = True
except ImportError:
    MULTI_STEP_PIPELINE_AVAILABLE = False
    print("⚠️ Multi-Step Pipeline не е наличен - използвам стандартен RAG")
import traceback 
import requests 
import fitz     # PyMuPDF
import cohere
from enum import Enum
from sentence_transformers import SentenceTransformer, CrossEncoder # MODIFIED: Added SentenceTransformer
import time
import statistics
from functools import lru_cache
from datetime import datetime, timedelta
from collections import Counter

# Enum for reranker selection
class RerankerType(Enum):
    COHERE = "cohere"
    CROSS_ENCODER = "cross_encoder"
    NONE = "none"

# --- ИНИЦИАЛИЗАЦИЯ НА ЛОГЕРА ЗА ТОЗИ МОДУЛ ---
logger = logging.getLogger(__name__)

# Phase 4.3: Advanced Ranking System
try:
    from src.advanced_ranking import advanced_rank_results
    logger.info("✅ Advanced ranking system imported successfully")
except ImportError as e:
    logger.warning(f"⚠️ Advanced ranking not available: {e}")
    advanced_rank_results = None

# Phase 4.4: Query Understanding & Intent Detection
try:
    from src.query_understanding import analyze_user_query
    logger.info("✅ Query understanding system imported successfully")
except ImportError as e:
    logger.warning(f"⚠️ Query understanding not available: {e}")
    analyze_user_query = None

# Phase 8.2: Cross-encoder Reranking
try:
    from src.cross_encoder_reranker import rerank_search_results, CrossEncoderConfig
    logger.info("✅ Cross-encoder reranking system imported successfully")
except ImportError as e:
    logger.warning(f"⚠️ Cross-encoder reranking not available: {e}")
    rerank_search_results = None

# Phase 8.3: Bulgarian-specific Embeddings
try:
    from src.bulgarian_embeddings import get_bulgarian_embedding_engine, create_bulgarian_embeddings
    logger.info("✅ Bulgarian-specific embeddings system imported successfully")
except ImportError as e:
    logger.warning(f"⚠️ Bulgarian embeddings not available: {e}")
    get_bulgarian_embedding_engine = None
    create_bulgarian_embeddings = None

# Phase 4.5: Intelligent Result Reranking
try:
    from src.intelligent_reranking import intelligent_rerank_results
    logger.info("✅ Intelligent reranking system imported successfully")
except ImportError as e:
    logger.warning(f"⚠️ Intelligent reranking not available: {e}")
    intelligent_rerank_results = None

# Phase 8.4: Advanced Query Processing
try:
    from src.advanced_query_processing import get_advanced_query_processor, process_query_with_advanced_techniques
    logger.info("✅ Advanced query processing system imported successfully")
except ImportError as e:
    logger.warning(f"⚠️ Advanced query processing system not available: {e}")
    get_advanced_query_processor = None
    process_query_with_advanced_techniques = None

# --- НОВ БЛОК ЗА TYPE HINTING ---
if TYPE_CHECKING:
    from .crawl4ai_mcp import Crawl4AIContext # За type hinting само
    import cohere.client # За type hinting на клиента
    # from sentence_transformers import CrossEncoder # This is already imported above

# PHASE 3: Intelligent Caching System
class LRUCache:
    """Intelligent LRU Cache for embeddings with automatic memory management"""

    def __init__(self, max_size: int = 1000, max_memory_mb: int = 100):
        self.max_size = max_size
        self.max_memory_mb = max_memory_mb
        self.cache = OrderedDict()
        self.memory_usage = 0
        self.hits = 0
        self.misses = 0

    def _estimate_memory(self, embedding: List[float]) -> float:
        """Estimate memory usage in MB for an embedding"""
        return len(embedding) * 4 / (1024 * 1024)  # 4 bytes per float32

    def get(self, key: str) -> Optional[List[float]]:
        """Get embedding from cache"""
        if key in self.cache:
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            self.hits += 1
            return self.cache[key]
        self.misses += 1
        return None

    def put(self, key: str, embedding: List[float]):
        """Put embedding in cache with automatic cleanup"""
        if key in self.cache:
            # Update existing
            self.cache.move_to_end(key)
            return

        # Check memory limit
        embedding_memory = self._estimate_memory(embedding)

        # Remove old entries if needed
        while (len(self.cache) >= self.max_size or
               self.memory_usage + embedding_memory > self.max_memory_mb):
            if not self.cache:
                break
            oldest_key, oldest_embedding = self.cache.popitem(last=False)
            self.memory_usage -= self._estimate_memory(oldest_embedding)

        # Add new embedding
        self.cache[key] = embedding
        self.memory_usage += embedding_memory

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        total_requests = self.hits + self.misses
        hit_rate = self.hits / total_requests if total_requests > 0 else 0
        return {
            'size': len(self.cache),
            'max_size': self.max_size,
            'memory_usage_mb': round(self.memory_usage, 2),
            'max_memory_mb': self.max_memory_mb,
            'hits': self.hits,
            'misses': self.misses,
            'hit_rate': round(hit_rate * 100, 1)
        }

# Global cache instance
_embedding_cache = LRUCache(max_size=1000, max_memory_mb=100)

# --- ГЛОБАЛНИ ПРОМЕНЛИВИ ЗА МОДЕЛИ (ЗА ДА СЕ ЗАРЕЖДАТ САМО ВЕДНЪЖ) ---
_supabase_client_instance: Optional[Client] = None
_openai_client_sync_instance: Optional[openai.OpenAI] = None
_cohere_client_instance: Optional[cohere.Client] = None  # NEW: Instance for Cohere client
_bge_model_instance: Optional[SentenceTransformer] = None # NEW: Instance for BGE model

# --- ОПИТ ЗА ИМПОРТ НА normalize_extracted_entity ---
try:
    # Използваме абсолютен импорт вместо относителен
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from entity_utils import normalize_extracted_entity
    logger.info("Successfully imported REAL normalize_extracted_entity from entity_utils in utils.py.")
    setattr(normalize_extracted_entity, '__is_real__', True)
except ImportError as e_import_entity_utils:
    logger.error(f"!!! CRITICAL ERROR IN utils.py: FAILED TO IMPORT normalize_extracted_entity from .entity_utils !!!")
    logger.error(f"!!! IMPORT ERROR DETAILS: {e_import_entity_utils}")
    logger.error(f"!!! TRACEBACK:\n{traceback.format_exc()}")
    
    def normalize_extracted_entity(entity: dict, has_year_entity_for_program_name: bool = False) -> Optional[Dict[str, Any]]: # type: ignore
        logger.warning("###### Using DUMMY normalize_extracted_entity in utils.py due to IMPORT ERROR. ######")
        if not isinstance(entity, dict) or "type" not in entity or "name" not in entity: return None
        setattr(normalize_extracted_entity, '__is_dummy__', True)
        return {"type": entity.get("type", "UNKNOWN_DUMMY"), "original_value": entity.get("name", ""), "normalized_value": entity.get("name", ""), "intermediate_tokens": [], "lemmatized_tokens_sorted_unique": []}
    logger.warning("###### DUMMY normalize_extracted_entity function has been defined. ######")


try:
    openai_breaker = pybreaker.CircuitBreaker(fail_max=5, reset_timeout=60)
    logger.info(f"OpenAI Circuit Breaker (utils.py) initialized: fail_max={openai_breaker.fail_max}, reset_timeout={openai_breaker.reset_timeout}s")
except NameError: 
    logger.error("pybreaker is not defined. Circuit breaker for OpenAI calls will not work.")
    def openai_breaker(func): # type: ignore
        return func

if not os.getenv("OPENAI_API_KEY"):
    logger.warning("OPENAI_API_KEY environment variable not found (utils.py). Some OpenAI functionalities might fail.")

from pathlib import Path

# --- PROMPT MANAGEMENT (NO CHANGES)---
def load_prompt(file_name: str) -> str:
    """Loads a prompt from the prompts/ directory."""
    prompt_path = Path(__file__).parent.parent / "prompts" / file_name
    try:
        with open(prompt_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.error(f"Prompt file not found: {prompt_path}")
        return "" # Return empty string if prompt is not found

# Load prompts at startup
SYSTEM_PROMPT_EXTRACTION_DOCUMENT_CONTENT_V2 = load_prompt('entity_extraction_document.txt')
SYSTEM_PROMPT_EXTRACTION_QUERY_V2 = load_prompt('entity_extraction_query.txt')
FEW_SHOT_EXAMPLES_QUERY_V2 = load_prompt('few_shot_examples_query.txt')
# --- END PROMPT MANAGEMENT ---

# --- HELPER FUNCTIONS (NO CHANGES) ---
def calculate_sha256(content: Union[str, bytes]) -> str:
    if isinstance(content, str):
        return hashlib.sha256(content.encode('utf-8', errors='surrogateescape')).hexdigest()
    elif isinstance(content, bytes):
        return hashlib.sha256(content).hexdigest()
    else:
        logger.warning(f"Calculating SHA256 for unexpected type: {type(content)}. Using str representation.")
        return hashlib.sha256(str(content).encode('utf-8', errors='surrogateescape')).hexdigest()

def get_document_hash_info(client: Client, url: str) -> Optional[Dict[str, Any]]:
    try:
        response = client.table("document_content_hashes").select("*").eq("url", url).limit(1).execute()
        return response.data[0] if response.data else None
    except Exception as e:
        logger.error(f"Error retrieving document hash for URL {url}: {e}")
        return None

def upsert_document_hash(client: Client, url: str, new_hash: str) -> None:
    try:
        data_to_upsert = {"url": url, "content_hash": new_hash, "last_crawled_at": "now()"}
        response = client.table("document_content_hashes").upsert(data_to_upsert, on_conflict="url").execute()
        if hasattr(response, 'error') and getattr(response, 'error', None):
            logger.error(f"Error upserting document hash for URL {url}: {getattr(response, 'error', 'Unknown error')}")
        else:
            logger.info(f"Successfully upserted hash for URL {url}. New hash: {new_hash[:10]}...")
    except Exception as e:
        logger.error(f"Exception during upsert_document_hash for URL {url}: {e}")

# --- ENTITY EXTRACTION (NO CHANGES) ---
@openai_breaker
async def _extract_entities_openai_call(llm_model: str, messages: List[Dict[str, str]], async_openai_client: AsyncOpenAI, url_for_logging: str) -> str:
    logger.debug(f"Calling OpenAI API for entity extraction (URL: {url_for_logging}, Model: {llm_model}).")
    response = await async_openai_client.chat.completions.create(model=llm_model, messages=messages, temperature=0.0, response_format={"type": "json_object"}, max_tokens=1000) # type: ignore
    return response.choices[0].message.content or ""

async def extract_entities_from_text_llm(text_content: str, entity_types: List[str], llm_model: str, async_openai_client: Optional[AsyncOpenAI], url_for_logging: str = "N/A", extraction_context: str = "document_content") -> List[Dict[str, Any]]:
    # ... (цялата функция остава същата, не я копирам тук за краткост)
    if not async_openai_client:
        logger.error(f"extract_entities_from_text_llm (URL: {url_for_logging}, Context: {extraction_context}): AsyncOpenAI client not provided. Skipping.")
        return []
    if not all([llm_model, text_content, text_content.strip(), entity_types]):
        logger.warning(f"extract_entities_from_text_llm (URL: {url_for_logging}, Context: {extraction_context}): Missing model, text, or types. Skipping.")
        return []

    system_message_for_llm: str; user_message_for_llm: str
    text_content_limited = text_content[:8000]
    
    if extraction_context == "document_content":
        current_entity_types_for_processing = ["PROGRAM_NAME", "YEAR"] 
        if not all(et_type in entity_types for et_type in current_entity_types_for_processing):
            logger.warning(f"extract_entities_from_text_llm (URL: {url_for_logging}, Context: document_content): Input entity_types {entity_types} does not contain required {current_entity_types_for_processing}. Will use {current_entity_types_for_processing}.")
        system_message_for_llm = SYSTEM_PROMPT_EXTRACTION_DOCUMENT_CONTENT_V2 
        user_message_for_llm = f"""Текст за анализ:\n---\n{text_content_limited}\n---"""
    elif extraction_context == "user_query":
        few_shot_examples_to_use = FEW_SHOT_EXAMPLES_QUERY_V2
        system_message_for_llm = "You are an expert in information extraction. Respond ONLY with the valid JSON object as described. Do not add any explanatory text before or after the JSON object."
        user_message_for_llm = SYSTEM_PROMPT_EXTRACTION_QUERY_V2.format(
            entity_types_str=', '.join(entity_types), 
            few_shot_examples_str=few_shot_examples_to_use, 
            text_content_limited_str=text_content_limited
        )
        current_entity_types_for_processing = entity_types
    else:
        logger.error(f"extract_entities_from_text_llm (URL: {url_for_logging}): Invalid extraction_context: {extraction_context}. Skipping.")
        return []

    extracted_data_str: Optional[str] = None
    logger.debug(f"Entity extraction call (URL: {url_for_logging}, Context: {extraction_context}, Model: {llm_model})")
    try:
        messages_for_api_call = [{"role": "system", "content": system_message_for_llm}, {"role": "user", "content": user_message_for_llm}]
        logger.debug(f"LLM messages_for_api_call for {extraction_context} (URL: {url_for_logging}):\nSystem: {system_message_for_llm}\nUser: {user_message_for_llm}")
        extracted_data_str = await _extract_entities_openai_call(llm_model, messages_for_api_call, async_openai_client, url_for_logging)
        if not extracted_data_str:
            logger.warning(f"LLM entity extraction (URL: {url_for_logging}, Model: {llm_model}, Context: {extraction_context}) response was empty.")
            return []
        
        logger.info(f"RAW LLM RESPONSE (URL: {url_for_logging}, Context: {extraction_context}, Model: {llm_model}): >>>{extracted_data_str}<<<")
        
        if extracted_data_str.strip().startswith("```json"):
            extracted_data_str = extracted_data_str.strip()[7:]
            if extracted_data_str.strip().endswith("```"):
                extracted_data_str = extracted_data_str.rsplit("```", 1)[0].strip()
        
        parsed_json = json.loads(extracted_data_str.strip())
        entities_from_llm_intermediate: List[Dict[str, Any]] = []

        if isinstance(parsed_json, dict) and "entities" in parsed_json and isinstance(parsed_json["entities"], list):
            entities_from_llm_intermediate = parsed_json["entities"]
        elif extraction_context == "user_query" and isinstance(parsed_json, dict):
             temp_entities = []
             for entity_type_key in entity_types:
                 if entity_type_key in parsed_json and isinstance(parsed_json[entity_type_key], list):
                     for name_val in parsed_json[entity_type_key]:
                         if isinstance(name_val, str) and name_val.strip():
                             temp_entities.append({"name": name_val.strip(), "type": entity_type_key})
                         elif isinstance(name_val, dict) and "name" in name_val and isinstance(name_val["name"], str) and name_val["name"].strip():
                             temp_entities.append({"name": name_val["name"].strip(), "type": entity_type_key})
             if temp_entities:
                 logger.warning(f"LLM for user_query returned flat JSON, adapting. Raw: {extracted_data_str[:300]}")
                 entities_from_llm_intermediate = temp_entities
             elif not ("entities" in parsed_json):
                logger.error(f"LLM entity extraction (URL: {url_for_logging}, Context: {extraction_context}) returned unexpected JSON structure (flat or other): {type(parsed_json)}. Raw: {extracted_data_str[:300]}")
                return []
        if not isinstance(entities_from_llm_intermediate, list):
            logger.warning(f"LLM entity extraction 'entities' processing step did not result in a list. Intermediate: {entities_from_llm_intermediate}")
            return []

        valid_and_normalized_entities = []
        if entities_from_llm_intermediate:
            found_year_entity_in_llm_extraction = any(isinstance(e, dict) and e.get("type") == "YEAR" and isinstance(e.get("name"), str) and e.get("name","").strip() for e in entities_from_llm_intermediate)
            if found_year_entity_in_llm_extraction and extraction_context == "document_content":
                logger.debug(f"extract_entities_from_text_llm (URL: {url_for_logging}, Context: {extraction_context}): Found YEAR entity in LLM extraction for document content.")
            
            for entity_dict_from_llm in entities_from_llm_intermediate:
                if (isinstance(entity_dict_from_llm, dict) and "name" in entity_dict_from_llm and isinstance(entity_dict_from_llm["name"], str) and entity_dict_from_llm["name"].strip() and "type" in entity_dict_from_llm and entity_dict_from_llm["type"] in current_entity_types_for_processing): 
                    normalized_rich_entity = normalize_extracted_entity(entity_dict_from_llm.copy(), has_year_entity_for_program_name=(found_year_entity_in_llm_extraction and entity_dict_from_llm["type"] == "PROGRAM_NAME"))
                    if normalized_rich_entity: 
                        valid_and_normalized_entities.append(normalized_rich_entity)
                else: 
                    logger.warning(f"Invalid entity structure, empty name, or type mismatch from LLM (URL: {url_for_logging}, Context: {extraction_context}): {entity_dict_from_llm}. Expected one of {current_entity_types_for_processing}")
            
            if valid_and_normalized_entities: 
                logger.info(f"Successfully extracted and normalized {len(valid_and_normalized_entities)} entities (URL: {url_for_logging}, Context: {extraction_context}).")
        else: 
            logger.info(f"No entities found or processed by LLM for URL: {url_for_logging}, Context: {extraction_context}")
        
        return valid_and_normalized_entities
        
    except pybreaker.CircuitBreakerError as e_breaker: 
        logger.error(f"Circuit Breaker OPEN for entity extraction (URL: {url_for_logging}, Context: {extraction_context}): {e_breaker}")
    except openai.APIError as e_openai_api: 
        logger.error(f"OpenAI API Error during entity extraction (URL: {url_for_logging}, Model: {llm_model}, Context: {extraction_context}): {type(e_openai_api).__name__} - {e_openai_api}", exc_info=True)
    except json.JSONDecodeError as e_json: 
        logger.error(f"JSON parsing error from LLM (URL: {url_for_logging}, Context: {extraction_context}): {e_json}. Raw: '{(extracted_data_str if extracted_data_str is not None else 'N/A')[:500]}'")
    except Exception as e: 
        logger.error(f"Unexpected error in entity extraction (URL: {url_for_logging}, Model: {llm_model}, Context: {extraction_context}): {type(e).__name__} - {e}", exc_info=True)
    
    return []

# --- CLIENT INITIALIZATION (NO CHANGES) ---
def get_supabase_client() -> Client:
    global _supabase_client_instance
    if _supabase_client_instance is None:
        url, key = os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_SERVICE_KEY")
        if not url or not key: logger.critical("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set."); raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_KEY must be set.")
        _supabase_client_instance = create_client(url, key)
        logger.info("Supabase client (utils.py) initialized.")
    return _supabase_client_instance

def get_openai_client_sync() -> Optional[openai.OpenAI]:
    global _openai_client_sync_instance
    if _openai_client_sync_instance is None:
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key: _openai_client_sync_instance = openai.OpenAI(api_key=api_key); logger.info("Sync OpenAI client (utils.py) initialized.")
        else: logger.warning("OPENAI_API_KEY not set (utils.py). Sync OpenAI client not initialized.")
    return _openai_client_sync_instance

def get_cohere_client() -> Optional[cohere.Client]:
    """Get or create Cohere client instance for reranking."""
    global _cohere_client_instance
    if _cohere_client_instance is None:
        api_key = os.getenv("COHERE_API_KEY")
        if api_key:
            _cohere_client_instance = cohere.Client(api_key=api_key)
            logger.info("Cohere client (utils.py) initialized.")
        else:
            logger.warning("COHERE_API_KEY not set (utils.py). Cohere client not initialized.")
    return _cohere_client_instance

# --- EMBEDDING LOGIC (HEAVILY MODIFIED) ---

def get_bge_model() -> Optional[SentenceTransformer]:
    """
    MODIFIED: Initializes and returns the BGE-M3 model instance, loading it only once.
    BGE-M3 supports hybrid retrieval (sparse + dense) and multilingual capabilities.
    """
    global _bge_model_instance
    if _bge_model_instance is None:
        try:
            # Преминаваме към BGE-M3 за хибридно търсене (sparse + dense)
            # Този модел е специално проектиран за RAG и многоезични задачи
            model_name = os.getenv("EMBEDDING_MODEL_NAME", 'BAAI/bge-large-en-v1.5')  # FIXED: Връщаме към 1024-dim модел за съвместимост с базата
            logger.info(f"Attempting to load BGE-M3 SentenceTransformer model: '{model_name}'... This may take a moment.")
            _bge_model_instance = SentenceTransformer(model_name)
            logger.info(f"BGE-M3 SentenceTransformer model '{model_name}' loaded successfully.")
            logger.info(f"Model embedding dimension: {_bge_model_instance.get_sentence_embedding_dimension()}")
        except Exception as e:
            logger.critical(f"CRITICAL: Failed to load BGE-M3 SentenceTransformer model. Error: {e}", exc_info=True)
            # При неуспех, _bge_model_instance ще остане None и другите функции ще се провалят елегантно.
    return _bge_model_instance


def create_embeddings_batch(texts: List[str], batch_urls_for_logging: Optional[List[str]] = None) -> List[List[float]]:
    """
    MODIFIED: Creates a batch of embeddings using BGE-M3 model for hybrid retrieval (sparse + dense)
    instead of the OpenAI API. BGE-M3 provides better multilingual support and retrieval performance.
    """
    if not texts:
        return []

    model = get_bge_model()
    if not model:
        logger.error("Cannot create embeddings: Multilingual model is not available. Returning zero vectors.")
        # Връщаме вектор с правилната размерност за BAAI/bge-large-en-v1.5 (1024)
        return [[0.0] * 1024 for _ in range(len(texts))]

    # Почистваме празни текстове, за да не подаваме None на модела. Моделът може да се справи, но е добра практика.
    # Заменяме празен/None текст с един интервал.
    valid_texts_for_model = [text if text and text.strip() else " " for text in texts]
    
    logger.info(f"Creating embeddings for a batch of {len(valid_texts_for_model)} texts using optimized multilingual model...")
    try:
        # BGE-M3 моделът връща numpy array, трябва да го конвертираме в list of lists.
        embeddings = model.encode(valid_texts_for_model, show_progress_bar=False).tolist()
        logger.info(f"Successfully created {len(embeddings)} multilingual embeddings with dimension {len(embeddings[0]) if embeddings else 'N/A'}.")
        return embeddings
    except Exception as e:
        logger.error(f"Error during multilingual model.encode(): {e}", exc_info=True)
        # При грешка, връщаме нулеви вектори с правилната размерност за BAAI/bge-large-en-v1.5.
        embedding_dim = model.get_sentence_embedding_dimension()
        return [[0.0] * (embedding_dim if embedding_dim else 384) for _ in range(len(texts))]


def create_embedding(text: str) -> List[float]:
    """MODIFIED: Uses optimized multilingual model with intelligent caching."""
    if not text or not text.strip():
        model = get_bge_model()
        embedding_dim = model.get_sentence_embedding_dimension() if model else 1024
        return [0.0] * (embedding_dim or 1024)

    # Check cache first
    cache_key = hashlib.md5(text.encode('utf-8')).hexdigest()
    cached_embedding = _embedding_cache.get(cache_key)
    if cached_embedding is not None:
        return cached_embedding

    # Create embedding
    embeddings = create_embeddings_batch([text])
    embedding = embeddings[0] if embeddings else []

    # Cache the result
    if embedding:
        _embedding_cache.put(cache_key, embedding)

    return embedding

def get_cache_stats() -> Dict[str, Any]:
    """Get embedding cache statistics"""
    return _embedding_cache.get_stats()

# OpenAI embedding functions removed - using BGE-M3 exclusively

# def _generate_contextual_embedding_openai_call(...) -> ...:
#     # This function is no longer needed.

# def generate_contextual_embedding(...) -> ...:
#     # This function is no longer needed.

# def process_chunk_with_context_and_sync_client(...) -> ...:
#     # This function is no longer needed.

# --- DATA INGESTION (MODIFIED) ---

async def add_documents_to_supabase(
    app_ctx: 'Crawl4AIContext',
    urls: List[str], 
    chunk_numbers: List[int], 
    contents: List[str], 
    metadatas: List[Dict[str, Any]], 
    # url_to_full_document: Dict[str, str], # REMOVED: No longer needed for contextual embeddings
    batch_size: int = 20
) -> None:
    """
    MODIFIED: Simplified by removing the complex and slow "contextual embedding" logic.
    Now it directly uses the BGE-M3 model for creating embeddings with hybrid retrieval capabilities.
    """
    if not app_ctx:
        logger.error("add_documents_to_supabase: app_ctx is None. Skipping operation.")
        return
    
    supabase_client = app_ctx.supabase_client
    program_normalizer = app_ctx.program_normalizer 
    gazetteer_data = app_ctx.gazetteer_data
    async_openai_client = app_ctx.async_openai_client

    if not supabase_client:
        logger.error("add_documents_to_supabase: Supabase client not available in app_ctx. Skipping.")
        return

    if not async_openai_client:
        logger.warning("AsyncOpenAI client not available in app_ctx for add_documents_to_supabase. Entity extraction will be skipped.")

    model_for_entity_extraction = os.getenv("MODEL_CHOICE_ENTITY_EXTRACTION") or os.getenv("MODEL_CHOICE", "gpt-4o-mini")
    use_entity_extraction = bool(model_for_entity_extraction and async_openai_client)
    ENTITY_TYPES_FOR_DOCUMENT_CONTENT = ["PROGRAM_NAME", "YEAR"]
    MIN_CHUNK_LENGTH_FOR_NO_ENTITIES_WARNING = 100

    for i in range(0, len(contents), batch_size):
        batch_start_idx, batch_end_idx = i, min(i + batch_size, len(contents))
        batch_urls, batch_chunk_numbers, batch_contents_original, batch_metadatas_original = \
            urls[batch_start_idx:batch_end_idx], chunk_numbers[batch_start_idx:batch_end_idx], \
            contents[batch_start_idx:batch_end_idx], metadatas[batch_start_idx:batch_end_idx]
        
        # The content for embedding is now the original content.
        processed_contents_for_embedding = list(batch_contents_original)

        # The complex logic for contextual embeddings has been removed here for simplicity and speed.

        batch_extracted_entities_list = [[] for _ in range(len(batch_contents_original))]
        if use_entity_extraction and async_openai_client:
            entity_tasks = [
                extract_entities_from_text_llm(
                    text_content=oc, 
                    entity_types=ENTITY_TYPES_FOR_DOCUMENT_CONTENT, 
                    llm_model=model_for_entity_extraction, 
                    async_openai_client=async_openai_client, 
                    url_for_logging=batch_urls[idx_local] if idx_local < len(batch_urls) else "N/A_URL", 
                    extraction_context="document_content"
                ) for idx_local, oc in enumerate(batch_contents_original)
            ]
            try:
                entity_results = await asyncio.gather(*entity_tasks, return_exceptions=True)
                for idx_local, result_item in enumerate(entity_results):
                    if isinstance(result_item, list):
                        batch_extracted_entities_list[idx_local] = result_item
                    elif isinstance(result_item, Exception):
                        logger.error(f"Entity extraction task failed for item {idx_local} (URL: {batch_urls[idx_local] if idx_local < len(batch_urls) else 'N/A_URL'}): {result_item}")
            except Exception as e_gather_entities_main:
                logger.error(f"Critical error during asyncio.gather for entity extraction (utils.py/add_docs): {e_gather_entities_main}")
        
        # --- Gazetteer Canonization (NO CHANGES) ---
        if gazetteer_data and program_normalizer: 
            # ... (цялата логика остава същата)
            logger.info(f"Attempting Gazetteer Canonization for batch (Gazetteer entries: {len(gazetteer_data)})")
            for idx_chunk_entities, chunk_entities in enumerate(batch_extracted_entities_list):
                if not chunk_entities: continue
                enriched_chunk_entities = []
                for entity_dict in chunk_entities:
                    current_entity_enriched = entity_dict.copy() 
                    if isinstance(entity_dict, dict) and entity_dict.get("type") == "PROGRAM_NAME":
                        entity_lemmas_set = set(entity_dict.get("lemmatized_tokens_sorted_unique", []))
                        if not entity_lemmas_set:
                            enriched_chunk_entities.append(current_entity_enriched)
                            continue
                        best_match_canonical_name = None
                        highest_containment_score = 0.0
                        for gaz_entry in gazetteer_data:
                            if not gaz_entry.get("normalized_lemmatized_forms"): continue
                            for gaz_form_lemmas_list in gaz_entry.get("normalized_lemmatized_forms", []):
                                gaz_form_lemmas_set = set(gaz_form_lemmas_list)
                                if not gaz_form_lemmas_set: continue
                                intersection_size = len(entity_lemmas_set.intersection(gaz_form_lemmas_set))
                                current_score = intersection_size / len(gaz_form_lemmas_set) if gaz_form_lemmas_set else 0.0
                                if current_score > highest_containment_score:
                                    highest_containment_score = current_score
                                    best_match_canonical_name = gaz_entry.get("canonical_name")
                        if best_match_canonical_name and highest_containment_score >= 0.7: 
                            current_entity_enriched["gazetteer_canonical_name"] = best_match_canonical_name
                            current_entity_enriched["gazetteer_match_score"] = round(highest_containment_score, 4)
                            logger.info(f"Gazetteer match for '{entity_dict.get('original_value')}' -> '{best_match_canonical_name}' (Score: {highest_containment_score:.4f}) in URL: {batch_urls[idx_chunk_entities] if idx_chunk_entities < len(batch_urls) else 'N/A'}")
                    enriched_chunk_entities.append(current_entity_enriched)
                batch_extracted_entities_list[idx_chunk_entities] = enriched_chunk_entities

        # --- Embedding Creation (MODIFIED TO USE BGE) ---
        batch_embeddings = create_embeddings_batch(processed_contents_for_embedding, batch_urls_for_logging=batch_urls)
        
        if len(batch_embeddings) != len(processed_contents_for_embedding):
            logger.error(f"Embeddings count mismatch for batch starting with URL {batch_urls[0] if batch_urls else 'N/A_URL'}. Skipping batch.")
            continue
            
        batch_data_to_insert = []
        for j_local in range(len(batch_contents_original)):
            current_metadata_item = dict(batch_metadatas_original[j_local])
            current_metadata_item['extracted_entities'] = batch_extracted_entities_list[j_local]
            # MODIFIED: Променяме името на полето, за да отразим, че вече не е "контекстуално"
            current_metadata_item['chunk_size_chars'] = len(processed_contents_for_embedding[j_local])
            
            batch_data_to_insert.append({
                "url": batch_urls[j_local], 
                "chunk_number": batch_chunk_numbers[j_local], 
                "content": batch_contents_original[j_local], 
                "metadata": current_metadata_item, 
                "embedding": batch_embeddings[j_local]
            })
            
        try:
            if batch_data_to_insert:
                current_batch_num_for_log_try = i // batch_size + 1
                logger.info(f"Attempting to insert batch {current_batch_num_for_log_try} of {len(batch_data_to_insert)} documents in Supabase (crawled_pages)...")
                insert_response = await asyncio.to_thread(supabase_client.table("crawled_pages").insert(batch_data_to_insert).execute)
                if hasattr(insert_response, 'error') and getattr(insert_response, 'error', None):
                    logger.error(f"Supabase insert error for batch {current_batch_num_for_log_try} (crawled_pages): {getattr(insert_response, 'error', 'Unknown error')}")
                else:
                    logger.info(f"Successfully attempted insert to crawled_pages for batch {current_batch_num_for_log_try}.")
            else:
                logger.warning(f"Batch data for batch {i // batch_size + 1} (crawled_pages) is empty. Skipping insert.")
        except Exception as e_insert:
            logger.error(f"Exception inserting batch into Supabase (crawled_pages): {type(e_insert).__name__} - {e_insert}", exc_info=True)
        
        await asyncio.sleep(0.01)

# --- SEARCH AND RERANK (NO MAJOR CHANGES) ---
# Логиката за търсене и пренареждане остава същата, защото тя просто използва
# вече създадените embeddings и не се интересува как са направени.
# create_embedding() за заявката вече е променена и ще използва BGE.

def search_documents_with_text_hybrid_bulgarian(
    client: Client,
    query_text: str,
    query_embeddings: np.ndarray,
    match_count: int = 10,
    weight_dense: float = 0.6,
    weight_sparse: float = 0.4,
    min_similarity_threshold: float = 0.1
) -> List[Dict[str, Any]]:
    """
    Phase 8.3: Hybrid search with pre-computed Bulgarian embeddings (LaBSE)

    Args:
        client: Supabase client
        query_text: Search query text
        query_embeddings: Pre-computed LaBSE embeddings for the query
        match_count: Number of results to return
        weight_dense: Weight for semantic similarity
        weight_sparse: Weight for keyword matching
        min_similarity_threshold: Minimum similarity threshold

    Returns:
        List of search results optimized for Bulgarian content
    """
    logger.info(f"🇧🇬 Bulgarian hybrid search with LaBSE embeddings: '{query_text[:50]}...'")

    try:
        # Convert numpy array to list for JSON serialization
        if isinstance(query_embeddings, np.ndarray):
            # Ensure we have a 1D array (flatten if needed)
            if query_embeddings.ndim > 1:
                query_embedding_list = query_embeddings.flatten().tolist()
            else:
                query_embedding_list = query_embeddings.tolist()
        else:
            query_embedding_list = query_embeddings

        # Call the LaBSE-specific hybrid RPC function (768 dimensions)
        response = client.rpc(
            'match_crawled_pages_labse_hybrid',
            {
                'p_query_embedding': query_embedding_list,
                'p_query_text': query_text,
                'p_match_count': match_count,
                'p_weight_dense': weight_dense,
                'p_weight_sparse': weight_sparse,
                'p_min_similarity_threshold': min_similarity_threshold
            }
        ).execute()

        if response.data:
            # Map the response fields to include all necessary information
            results = []
            for item in response.data:
                result = {
                    'id': item.get('id'),
                    'url': item.get('url'),
                    'title': item.get('title', ''),
                    'content': item.get('content', ''),
                    'metadata': item.get('metadata', {}),
                    'chunk_number': item.get('chunk_number', 0),
                    'hybrid_score': item.get('hybrid_score', 0.0),
                    'similarity': item.get('similarity', 0.0),  # LaBSE similarity
                    'keyword_score': item.get('keyword_score', 0.0),
                    'created_at': item.get('created_at'),
                    'embedding_model': 'LaBSE'  # Mark as LaBSE-powered
                }
                results.append(result)

            logger.info(f"🇧🇬 Bulgarian hybrid search SUCCESS: Found {len(results)} results")
            return results
        else:
            logger.warning("🔍 No results found with Bulgarian embeddings")
            return []

    except Exception as e:
        logger.error(f"❌ Bulgarian hybrid search failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return []

def search_documents_with_text_hybrid(
    client: Client,
    query_text: str,
    match_count: int = 10,
    weight_dense: float = 0.6,  # 🎯 PHASE 8.1.2: 60% dense weight (external LLM recommendation)
    weight_sparse: float = 0.4,  # 🎯 PHASE 8.1.2: 40% sparse weight (external LLM recommendation)
    min_similarity_threshold: float = 0.1
) -> List[Dict[str, Any]]:
    """
    🔥 PHASE 1: HYBRID SEARCH IMPLEMENTATION

    NEW HYBRID WRAPPER: Combines BM25 sparse search + Dense vector search with weighted fusion.
    This implements the first phase of RAG optimization for 50% performance improvement.

    Args:
        client: Supabase client
        query_text: Text query to search for
        match_count: Number of results to return
        weight_dense: Weight for dense vector similarity (0.0-1.0)
        weight_sparse: Weight for BM25 sparse ranking (0.0-1.0)
        min_similarity_threshold: Minimum hybrid score threshold

    Returns:
        List of hybrid search results with enhanced scoring
    """
    if not query_text or not query_text.strip():
        logger.warning("search_documents_with_text_hybrid: Empty query text provided")
        return []

    # Generate embedding from text
    query_embedding = create_embedding(query_text.strip())
    if not query_embedding or all(val == 0.0 for val in query_embedding):
        logger.error("search_documents_with_text_hybrid: Failed to create embedding for query")
        return []

    try:
        # Call new hybrid search RPC function
        result = client.rpc(
            'match_crawled_pages_hybrid',
            {
                'p_query_text': query_text.strip(),
                'p_query_embedding': query_embedding,
                'p_match_count': match_count,
                'p_weight_dense': weight_dense,
                'p_weight_sparse': weight_sparse,
                'p_min_similarity_threshold': min_similarity_threshold
            }
        ).execute()  # 🔥 ВАЖНО: Трябва да извикаме .execute()!

        if result.data:
            logger.info(f"🔥 HYBRID SEARCH SUCCESS: Found {len(result.data)} results for query: {query_text[:50]}...")

            # 🎯 PHASE 8.1.3: Transform RPC output to standard format
            formatted_results = []
            for row in result.data:
                formatted_result = {
                    'id': row.get('id'),
                    'url': row.get('url'),
                    'chunk_number': row.get('chunk_number'),
                    'content': row.get('content'),
                    'metadata': row.get('metadata', {}),
                    'similarity': row.get('dense_similarity', 0.0),  # Map dense_similarity to similarity
                    'hybrid_score': row.get('hybrid_score', 0.0),
                    'keyword_score': row.get('sparse_rank', 0.0),
                    'search_method': row.get('search_method', 'hybrid')
                }
                formatted_results.append(formatted_result)

            return formatted_results
        else:
            logger.warning(f"🔍 HYBRID SEARCH: No results found for query: {query_text[:50]}...")
            return []

    except Exception as e:
        logger.error(f"❌ HYBRID SEARCH ERROR: {str(e)}")
        # Fallback to original dense-only search
        logger.info("🔄 FALLING BACK to dense-only search...")
        return search_documents_with_text_legacy(
            client=client,
            query_text=query_text,
            match_count=match_count,
            min_similarity_threshold=min_similarity_threshold
        )


def search_documents_with_text_legacy(
    client: Client,
    query_text: str,
    match_count: int = 10,
    source_filters: Optional[List[str]] = None,
    query_entities_filter: Optional[List[Dict[str, Any]]] = None,
    query_program_canonical_names: Optional[List[str]] = None,
    min_program_containment_threshold: Optional[float] = 0.6,
    min_similarity_threshold: Optional[float] = 0.1,
    weight_similarity: Optional[float] = 0.4,
    weight_program_name: Optional[float] = 0.4,
    weight_year: Optional[float] = 0.2
) -> List[Dict[str, Any]]:
    """
    LEGACY WRAPPER FUNCTION: Original dense-only search for fallback.
    Accepts text query and automatically generates embeddings for RPC calls.

    Args:
        client: Supabase client
        query_text: Text query to search for
        match_count: Number of results to return
        source_filters: Optional list of source domains to filter by
        query_entities_filter: Optional extracted entities from query
        query_program_canonical_names: Optional canonical program names
        min_program_containment_threshold: Minimum program name containment score
        min_similarity_threshold: Minimum similarity threshold
        weight_similarity: Weight for similarity scoring (0.0-1.0)
        weight_program_name: Weight for program name scoring (0.0-1.0)
        weight_year: Weight for year scoring (0.0-1.0)

    Returns:
        List of search results with scores and metadata
    """
    if not query_text or not query_text.strip():
        logger.warning("search_documents_with_text_legacy: Empty query text provided")
        return []

    # Generate embedding from text
    query_embedding = create_embedding(query_text.strip())
    if not query_embedding or all(val == 0.0 for val in query_embedding):
        logger.error("search_documents_with_text_legacy: Failed to create embedding for query")
        return []

    # Call the existing function with the generated embedding
    return search_documents(
        client=client,
        query=query_text,  # Keep original for logging
        match_count=match_count,
        source_filters=source_filters,
        query_entities_filter=query_entities_filter,
        query_program_canonical_names=query_program_canonical_names,
        min_program_containment_threshold=min_program_containment_threshold,
        min_similarity_threshold=min_similarity_threshold,
        weight_similarity=weight_similarity,
        weight_program_name=weight_program_name,
        weight_year=weight_year
    )


# Alias for backward compatibility
search_documents_with_text = search_documents_with_text_hybrid


def search_documents(
    client: Client,
    query: str,
    match_count: int = 10,
    source_filters: Optional[List[str]] = None,
    query_entities_filter: Optional[List[Dict[str, Any]]] = None,
    query_program_canonical_names: Optional[List[str]] = None,
    min_program_containment_threshold: Optional[float] = None,
    min_similarity_threshold: Optional[float] = None,
    weight_similarity: Optional[float] = None,
    weight_program_name: Optional[float] = None,
    weight_year: Optional[float] = None
) -> List[Dict[str, Any]]:
    # ... (цялата функция остава същата, не я копирам тук за краткост)
    query_embedding: Optional[List[float]] = None
    try:
        query_embedding = create_embedding(query)
        if not query_embedding or all(val == 0.0 for val in query_embedding):
            logger.error(f"Query embedding for '{query[:50]}...' resulted in a None or zero vector (utils.py).")
            return []
    except Exception as e:
        logger.error(f"Failed to create embedding for query '{query[:50]}...' (utils.py). Error: {e}.", exc_info=True)
        return []

    try:
        query_entities_filter_for_rpc = None
        if query_entities_filter and len(query_entities_filter) > 0:
            query_entities_filter_for_rpc = json.dumps(query_entities_filter)

        rpc_params: Dict[str, Any] = {
            'p_query_embedding': query_embedding,
            'p_match_count': match_count,
            'p_min_program_containment_threshold': min_program_containment_threshold,
            'p_min_similarity_threshold': min_similarity_threshold,
            'p_weight_similarity': weight_similarity,
            'p_weight_program_name': weight_program_name,
            'p_weight_year': weight_year
        }

        if source_filters:
            rpc_params['p_source_filters_jsonb'] = source_filters 
        
        if query_entities_filter_for_rpc:
            rpc_params['p_query_entities_filter'] = query_entities_filter_for_rpc
        
        if query_program_canonical_names:
            rpc_params['p_query_program_canonical_names_jsonb'] = query_program_canonical_names
        
        log_params_for_rpc_display = {k: v for k,v in rpc_params.items() if k != 'p_query_embedding'}
        if query_embedding:
             log_params_for_rpc_display['p_query_embedding_preview'] = str(query_embedding[:3]) + ('...' if len(query_embedding) > 3 else '')

        if 'p_query_entities_filter' in log_params_for_rpc_display and log_params_for_rpc_display['p_query_entities_filter']:
            try:
                parsed_log_entities = json.loads(log_params_for_rpc_display['p_query_entities_filter'])
                log_params_for_rpc_display['p_query_entities_filter_log_count'] = len(parsed_log_entities)
                log_params_for_rpc_display['p_query_entities_filter_log_preview'] = json.dumps(parsed_log_entities[:2], ensure_ascii=False) + ('...' if len(parsed_log_entities) > 2 else '')
                del log_params_for_rpc_display['p_query_entities_filter']
            except:
                pass

        rpc_function_to_call = 'match_crawled_pages_v4_debug'
        logger.info(f"search_documents (utils.py): Calling RPC '{rpc_function_to_call}' with params: {json.dumps(log_params_for_rpc_display, default=str, ensure_ascii=False)}")
        
        result = client.rpc(rpc_function_to_call, rpc_params).execute()
        
        if hasattr(result, 'data') and result.data is not None:
            logger.info(f"search_documents (utils.py): RPC '{rpc_function_to_call}' returned {len(result.data)} documents.")
            if result.data: 
                 first_res = result.data[0]
                 logger.debug(
                    f"search_documents (utils.py): First result from {rpc_function_to_call} (ID: {first_res.get('id')}, "
                    f"Final Score: {first_res.get('final_score_calculated')}, "
                    f"Match Type: {first_res.get('match_type')}, "
                    f"Temporal Score: {first_res.get('temporal_overlap_score_debug')})"
                )
            return result.data
        elif hasattr(result, 'error') and getattr(result, 'error', None) is not None:
            error_obj = getattr(result, 'error', None)
            error_details = getattr(error_obj, 'message', str(error_obj)) if error_obj else 'Unknown error'
            logger.error(f"search_documents (utils.py): Error from RPC '{rpc_function_to_call}': {error_details}")
            logger.error(f"search_documents (utils.py): RPC params (excluding embedding) that may have caused error: {json.dumps(log_params_for_rpc_display, default=str, ensure_ascii=False)}")
            return []
        else:
            logger.warning(f"search_documents (utils.py): Unexpected response structure from RPC '{rpc_function_to_call}'. Response: {result}")
            return []
    except Exception as e:
        logger.error(f"search_documents (utils.py): Unexpected error calling RPC for query '{query[:50]}...': {type(e).__name__} - {e}", exc_info=True)
        return []


# --- MULTI-QUERY TRANSFORMATION FUNCTIONS (PHASE 2) ---

async def generate_query_variations(
    original_query: str,
    num_variations: int = 3,
    async_openai_client: Optional[AsyncOpenAI] = None
) -> List[str]:
    """
    DEACTIVATED: Query variations generation disabled for performance optimization.
    Returns only the original query to eliminate API calls and improve response time.
    """
    logger.info("generate_query_variations: DEACTIVATED - returning original query only for performance")
    return [original_query]


def reciprocal_rank_fusion(
    ranked_lists: List[List[Dict[str, Any]]],
    k: int = 60
) -> List[Dict[str, Any]]:
    """
    PHASE 2: Имплементира Reciprocal Rank Fusion (RRF) за обединяване на резултати
    от множество заявки. RRF е доказан метод за комбиниране на различни rankings.

    Args:
        ranked_lists: Списък от ранкирани списъци с документи
        k: RRF параметър (обикновено 60)

    Returns:
        Обединен и ранкиран списък с документи
    """
    if not ranked_lists:
        return []

    # Събираме всички уникални документи по URL
    all_docs = {}

    for ranked_list in ranked_lists:
        for rank, doc in enumerate(ranked_list, 1):
            doc_url = doc.get('url', '')
            if not doc_url:
                continue

            if doc_url not in all_docs:
                all_docs[doc_url] = {
                    'doc': doc,
                    'rrf_score': 0.0,
                    'appearances': 0
                }

            # RRF формула: 1 / (k + rank)
            rrf_contribution = 1.0 / (k + rank)
            all_docs[doc_url]['rrf_score'] += rrf_contribution
            all_docs[doc_url]['appearances'] += 1

    # Сортираме по RRF score (по-висок е по-добър)
    sorted_docs = sorted(
        all_docs.values(),
        key=lambda x: x['rrf_score'],
        reverse=True
    )

    # Добавяме RRF метаданни към документите
    result = []
    for item in sorted_docs:
        doc = item['doc'].copy()
        doc['rrf_score'] = item['rrf_score']
        doc['rrf_appearances'] = item['appearances']
        result.append(doc)

    logger.info(f"reciprocal_rank_fusion: Merged {len(ranked_lists)} ranked lists into {len(result)} unique documents")

    return result


async def extract_metadata_filters(
    query: str,
    async_openai_client: Optional[AsyncOpenAI] = None
) -> Dict[str, Any]:
    """
    PHASE 2: Извлича метаданни от заявката за автоматично филтриране.
    Анализира заявката и идентифицира програми, години, типове документи и др.
    """
    if not async_openai_client:
        logger.warning("extract_metadata_filters: OpenAI client not available. Returning empty filters.")
        return {}

    try:
        prompt = f"""Анализирай следната заявка за търсене в база данни с документи за европейски програми и фондове.

Заявка: "{query}"

Извлечи следните метаданни, ако са споменати в заявката:

1. ПРОГРАМИ: Имена на конкретни програми (напр. "Програма за развитие на селските райони", "ОПИК", "Хоризонт 2020")
2. ГОДИНИ: Конкретни години или периоди (напр. "2021", "2020-2027", "2014-2020")
3. ТИПОВЕ_ДОКУМЕНТИ: Типове документи (напр. "наредба", "правилник", "ръководство", "покана")
4. КЛЮЧОВИ_ДУМИ: Важни термини за филтриране (напр. "МСП", "иновации", "селско стопанство")

Отговори САМО в JSON формат без допълнителен текст:
{{
  "programs": ["програма1", "програма2"],
  "years": ["2021", "2022"],
  "document_types": ["тип1", "тип2"],
  "keywords": ["ключова_дума1", "ключова_дума2"]
}}

Ако няма информация за някоя категория, остави празен списък []."""

        response = await async_openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=300
        )

        raw_content = response.choices[0].message.content
        content = raw_content.strip() if raw_content else ""

        # Опитваме се да парсираме JSON отговора
        try:
            metadata = json.loads(content)
            logger.info(f"extract_metadata_filters: Extracted metadata: {metadata}")
            return metadata
        except json.JSONDecodeError:
            logger.warning(f"extract_metadata_filters: Failed to parse JSON response: {content}")
            return {}

    except Exception as e:
        logger.error(f"extract_metadata_filters: Error extracting metadata: {e}", exc_info=True)
        return {}


def apply_metadata_filters(
    documents: List[Dict[str, Any]],
    metadata_filters: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    PHASE 2: Прилага метаданни филтри към списък с документи.
    Филтрира документи базирано на извлечените метаданни.
    """
    if not metadata_filters:
        return documents

    filtered_docs = []

    for doc in documents:
        should_include = True

        # Филтриране по програми - интелигентно съвпадение
        if metadata_filters.get('programs'):
            doc_content = (doc.get('content', '') + ' ' + doc.get('title', '')).lower()
            program_match = False

            for program in metadata_filters['programs']:
                program_lower = program.lower()

                # Специални правила за известни програми
                if 'хоризонт' in program_lower:
                    # За Хоризонт програми търсим "хоризонт", "horizon", "h2020", "he"
                    if any(term in doc_content for term in ['хоризонт', 'horizon', 'h2020', 'he']):
                        program_match = True
                        break
                elif 'еразъм' in program_lower or 'erasmus' in program_lower:
                    # За Еразъм програми
                    if any(term in doc_content for term in ['еразъм', 'erasmus']):
                        program_match = True
                        break
                else:
                    # Проверка за точно съвпадение
                    if program_lower in doc_content:
                        program_match = True
                        break
                    # Проверка за частично съвпадение (ключови думи)
                    program_words = program_lower.split()
                    if len(program_words) > 1:
                        # За многословни програми проверяваме дали поне 50% от думите са налице
                        word_matches = sum(1 for word in program_words if word in doc_content)
                        if word_matches >= len(program_words) * 0.5:
                            program_match = True
                            break

            # Ако няма съвпадение, не филтрираме твърде строго - намаляваме score вместо да елиминираме
            if not program_match:
                # Вместо да елиминираме документа, просто намаляваме неговия score
                if 'similarity' in doc:
                    doc['similarity'] = doc['similarity'] * 0.7  # Намаляваме с 30%
                # should_include остава True

        # Филтриране по години
        if metadata_filters.get('years') and should_include:
            doc_content = (doc.get('content', '') + ' ' + doc.get('title', '')).lower()
            year_match = any(
                str(year) in doc_content
                for year in metadata_filters['years']
            )
            if not year_match:
                should_include = False

        # Филтриране по типове документи
        if metadata_filters.get('document_types') and should_include:
            doc_content = (doc.get('content', '') + ' ' + doc.get('title', '')).lower()
            type_match = any(
                doc_type.lower() in doc_content
                for doc_type in metadata_filters['document_types']
            )
            if not type_match:
                should_include = False

        # Филтриране по ключови думи
        if metadata_filters.get('keywords') and should_include:
            doc_content = (doc.get('content', '') + ' ' + doc.get('title', '')).lower()
            keyword_match = any(
                keyword.lower() in doc_content
                for keyword in metadata_filters['keywords']
            )
            if not keyword_match:
                should_include = False

        if should_include:
            filtered_docs.append(doc)

    logger.info(f"apply_metadata_filters: Filtered {len(documents)} -> {len(filtered_docs)} documents")
    if metadata_filters.get('programs') and len(filtered_docs) == 0 and len(documents) > 0:
        logger.warning(f"apply_metadata_filters: Program filter '{metadata_filters['programs']}' eliminated all {len(documents)} documents")
        # Debug: show first document content sample
        if documents:
            sample_content = documents[0].get('content', '')[:200]
            logger.debug(f"apply_metadata_filters: Sample document content: {sample_content}...")

    return filtered_docs


async def small_to_big_retrieval(
    query: str,
    supabase_client: Client,
    async_openai_client: Optional[AsyncOpenAI] = None,
    similarity_threshold: float = 0.62,
    initial_chunk_count: int = 20,
    final_top_k: int = 5,
    context_expansion_strategy: str = "parent_document"  # "parent_document", "surrounding_chunks", "hybrid"
) -> List[Dict[str, Any]]:
    """
    Phase 3.1: Small-to-Big Retrieval Implementation

    Strategy:
    1. Search on small chunks for precision (current chunk-based approach)
    2. Expand to larger contexts (parent documents or surrounding chunks)
    3. Use Cohere reranker on expanded contexts for final ranking
    4. Return top-k results with rich context

    This approach combines the precision of small-chunk retrieval with the
    rich context needed for high-quality RAG responses.
    """
    logger.info(f"small_to_big_retrieval: Starting with query '{query[:50]}...', strategy='{context_expansion_strategy}'")

    try:
        # Step 1: Initial small-chunk retrieval using enhanced semantic search
        logger.info("Step 1: Performing initial small-chunk retrieval...")
        initial_results = await enhanced_semantic_search(
            query=query,
            supabase_client=supabase_client,
            async_openai_client=async_openai_client,
            num_query_variations=2,  # Fewer variations for speed
            similarity_threshold=similarity_threshold,
            max_results_per_query=initial_chunk_count // 2,
            final_top_k=initial_chunk_count,
            use_metadata_routing=True
        )

        if not initial_results:
            logger.warning("small_to_big_retrieval: No initial results found")
            return []

        logger.info(f"Step 1 complete: Found {len(initial_results)} initial chunks")

        # Step 2: Expand chunks to larger contexts
        logger.info(f"Step 2: Expanding chunks using strategy '{context_expansion_strategy}'...")
        expanded_contexts = await _expand_chunks_to_contexts(
            chunks=initial_results,
            supabase_client=supabase_client,
            expansion_strategy=context_expansion_strategy
        )

        logger.info(f"Step 2 complete: Expanded to {len(expanded_contexts)} contexts")

        # Step 3: Rerank expanded contexts using Cohere
        logger.info("Step 3: Reranking expanded contexts with Cohere...")
        final_results = await _rerank_expanded_contexts(
            query=query,
            expanded_contexts=expanded_contexts,
            top_k=final_top_k
        )

        logger.info(f"small_to_big_retrieval: Completed successfully, returning {len(final_results)} results")
        return final_results

    except Exception as e:
        logger.error(f"small_to_big_retrieval: Error: {e}", exc_info=True)
        return []


async def _expand_chunks_to_contexts(
    chunks: List[Dict[str, Any]],
    supabase_client: Client,
    expansion_strategy: str = "parent_document"
) -> List[Dict[str, Any]]:
    """
    Helper function: Expand small chunks to larger contexts for better RAG performance.

    Strategies:
    - parent_document: Get the full document that contains each chunk
    - surrounding_chunks: Get chunks before and after each found chunk
    - hybrid: Combine both approaches
    """
    logger.info(f"_expand_chunks_to_contexts: Expanding {len(chunks)} chunks using '{expansion_strategy}' strategy")

    if not chunks:
        return []

    expanded_contexts = []

    try:
        if expansion_strategy == "parent_document":
            # Group chunks by URL to get full documents
            url_to_chunks = {}
            for chunk in chunks:
                url = chunk.get('url', '')
                if url not in url_to_chunks:
                    url_to_chunks[url] = []
                url_to_chunks[url].append(chunk)

            # For each URL, get the full document content
            for url, url_chunks in url_to_chunks.items():
                try:
                    # Get all chunks for this URL, ordered by chunk_number
                    response = supabase_client.table('crawled_pages').select('*').eq('url', url).order('chunk_number').execute()

                    if response.data:
                        # Combine all chunks into one large context
                        combined_content = ' '.join([chunk_data['content'] for chunk_data in response.data])

                        # Create expanded context with metadata from the best matching chunk
                        best_chunk = max(url_chunks, key=lambda x: x.get('similarity', 0))
                        expanded_context = {
                            'id': f"expanded_{url}",
                            'url': url,
                            'content': combined_content,
                            'metadata': best_chunk.get('metadata', {}),
                            'similarity': best_chunk.get('similarity', 0),
                            'expansion_strategy': 'parent_document',
                            'original_chunk_count': len(response.data),
                            'source_chunks': url_chunks
                        }
                        expanded_contexts.append(expanded_context)

                except Exception as e:
                    logger.error(f"Error expanding chunks for URL {url}: {e}")
                    # Fallback: use original chunk
                    expanded_contexts.extend(url_chunks)

        elif expansion_strategy == "surrounding_chunks":
            # For each chunk, get surrounding chunks (before and after)
            for chunk in chunks:
                try:
                    url = chunk.get('url', '')
                    chunk_number = chunk.get('chunk_number', 0)

                    # Get chunks around this one (e.g., ±2 chunks)
                    context_window = 2
                    start_chunk = max(0, chunk_number - context_window)
                    end_chunk = chunk_number + context_window

                    response = supabase_client.table('crawled_pages').select('*').eq('url', url).gte('chunk_number', start_chunk).lte('chunk_number', end_chunk).order('chunk_number').execute()

                    if response.data:
                        combined_content = ' '.join([chunk_data['content'] for chunk_data in response.data])

                        expanded_context = {
                            'id': f"expanded_{chunk.get('id', '')}_surrounding",
                            'url': url,
                            'content': combined_content,
                            'metadata': chunk.get('metadata', {}),
                            'similarity': chunk.get('similarity', 0),
                            'expansion_strategy': 'surrounding_chunks',
                            'original_chunk_count': len(response.data),
                            'source_chunks': [chunk]
                        }
                        expanded_contexts.append(expanded_context)
                    else:
                        # Fallback: use original chunk
                        expanded_contexts.append(chunk)

                except Exception as e:
                    logger.error(f"Error expanding surrounding chunks for chunk {chunk.get('id', '')}: {e}")
                    expanded_contexts.append(chunk)

        else:  # hybrid or fallback
            logger.warning(f"Unsupported expansion strategy '{expansion_strategy}', using parent_document")
            return await _expand_chunks_to_contexts(chunks, supabase_client, "parent_document")

        logger.info(f"_expand_chunks_to_contexts: Successfully expanded to {len(expanded_contexts)} contexts")
        return expanded_contexts

    except Exception as e:
        logger.error(f"_expand_chunks_to_contexts: Critical error: {e}", exc_info=True)
        return chunks  # Fallback to original chunks


async def _rerank_expanded_contexts(
    query: str,
    expanded_contexts: List[Dict[str, Any]],
    top_k: int = 5
) -> List[Dict[str, Any]]:
    """
    Helper function: Rerank expanded contexts using Cohere reranker for final selection.
    """
    logger.info(f"_rerank_expanded_contexts: Reranking {len(expanded_contexts)} contexts for top {top_k}")

    if not expanded_contexts:
        return []

    if len(expanded_contexts) <= top_k:
        logger.info("_rerank_expanded_contexts: Fewer contexts than top_k, returning all")
        return expanded_contexts

    # DEACTIVATED: Cohere reranking disabled for performance optimization
    logger.info("_rerank_expanded_contexts: Cohere reranking DEACTIVATED - using similarity scores")
    # Fallback: sort by similarity score
    return sorted(expanded_contexts, key=lambda x: x.get('similarity', 0), reverse=True)[:top_k]


async def agent_based_retrieval(
    query: str,
    supabase_client: Client,
    async_openai_client: Optional[AsyncOpenAI] = None,
    max_iterations: int = 3,
    similarity_threshold: float = 0.62,
    final_top_k: int = 5,
    enable_multi_step: bool = True
) -> List[Dict[str, Any]]:
    """
    Phase 3.2: Agent-based Retrieval System

    Intelligent agent that can:
    1. Analyze query complexity and decompose complex queries into sub-queries
    2. Perform multi-step retrieval operations with reasoning chains
    3. Synthesize results from multiple documents and searches
    4. Use different retrieval strategies based on query type

    Args:
        query: The user's query
        supabase_client: Supabase client for database operations
        async_openai_client: OpenAI client for LLM operations
        max_iterations: Maximum number of retrieval iterations
        similarity_threshold: Minimum similarity threshold for results
        final_top_k: Number of final results to return
        enable_multi_step: Whether to enable multi-step reasoning

    Returns:
        List of retrieved documents with agent reasoning metadata
    """
    logger.info(f"agent_based_retrieval: Starting with query: '{query}'")

    try:
        # Step 1: Query Analysis and Decomposition
        logger.info("Step 1: Analyzing query complexity and decomposing...")
        query_analysis = await _analyze_query_complexity(query, async_openai_client)

        if not query_analysis:
            logger.warning("agent_based_retrieval: Query analysis failed, falling back to enhanced search")
            return await enhanced_semantic_search(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=async_openai_client,
                final_top_k=final_top_k
            )

        # Fixed: Type checking for query_analysis
        if isinstance(query_analysis, dict):
            logger.info(f"Query complexity: {query_analysis.get('complexity', 'unknown')}")
            logger.info(f"Sub-queries: {len(query_analysis.get('sub_queries', []))}")
        else:
            logger.warning(f"Invalid query_analysis type: {type(query_analysis)}")
            query_analysis = {"complexity": "simple", "sub_queries": [query]}

        # Step 2: Multi-step Retrieval Execution
        logger.info("Step 2: Executing multi-step retrieval...")
        all_results = []
        reasoning_chain = []

        # Handle simple queries with direct retrieval
        if isinstance(query_analysis, dict) and query_analysis.get('complexity') == 'simple':
            logger.info("Simple query detected, using enhanced semantic search")
            results = await enhanced_semantic_search(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=async_openai_client,
                final_top_k=final_top_k * 2  # Get more candidates for synthesis
            )
            all_results.extend(results)
            reasoning_chain.append({
                "step": 1,
                "action": "direct_search",
                "query": query,
                "results_count": len(results)
            })

        # Handle complex queries with decomposition
        else:
            sub_queries = query_analysis.get('sub_queries', [query])
            for i, sub_query in enumerate(sub_queries[:max_iterations]):
                logger.info(f"Processing sub-query {i+1}/{len(sub_queries)}: '{sub_query}'")

                # Choose retrieval strategy based on sub-query type
                strategy = _determine_retrieval_strategy(sub_query, query_analysis)
                logger.info(f"Using strategy: {strategy}")

                if strategy == "small_to_big":
                    sub_results = await small_to_big_retrieval(
                        query=sub_query,
                        supabase_client=supabase_client,
                        async_openai_client=async_openai_client,
                        similarity_threshold=similarity_threshold,
                        final_top_k=final_top_k
                    )
                else:  # enhanced_semantic
                    sub_results = await enhanced_semantic_search(
                        query=sub_query,
                        supabase_client=supabase_client,
                        async_openai_client=async_openai_client,
                        similarity_threshold=similarity_threshold,
                        final_top_k=final_top_k
                    )

                all_results.extend(sub_results)
                reasoning_chain.append({
                    "step": i + 1,
                    "action": strategy,
                    "query": sub_query,
                    "results_count": len(sub_results)
                })

                logger.info(f"Sub-query {i+1} completed: {len(sub_results)} results")

        # Step 3: Result Synthesis and Deduplication
        logger.info("Step 3: Synthesizing and deduplicating results...")
        synthesized_results = await _synthesize_agent_results(
            original_query=query,
            all_results=all_results,
            reasoning_chain=reasoning_chain,
            async_openai_client=async_openai_client,
            final_top_k=final_top_k
        )

        logger.info(f"agent_based_retrieval: Completed successfully, returning {len(synthesized_results)} results")
        return synthesized_results

    except Exception as e:
        logger.error(f"agent_based_retrieval: Error: {e}", exc_info=True)
        # Fallback to enhanced semantic search
        logger.info("agent_based_retrieval: Falling back to enhanced semantic search")
        return await enhanced_semantic_search(
            query=query,
            supabase_client=supabase_client,
            async_openai_client=async_openai_client,
            final_top_k=final_top_k
        )


async def _analyze_query_complexity(
    query: str,
    async_openai_client: Optional[AsyncOpenAI] = None
) -> Optional[Dict[str, Any]]:
    """
    Helper function: Analyze query complexity and decompose into sub-queries if needed.

    Returns:
        Dict with 'complexity', 'sub_queries', 'query_type', and 'reasoning'
    """
    if not async_openai_client:
        logger.warning("_analyze_query_complexity: No OpenAI client provided, using simple analysis")
        return {
            "complexity": "simple",
            "sub_queries": [query],
            "query_type": "factual",
            "reasoning": "No LLM available for analysis"
        }

    try:
        analysis_prompt = f"""
Analyze this query and determine if it needs to be decomposed into sub-queries for better retrieval.

Query: "{query}"

Respond with a JSON object containing:
1. "complexity": "simple" or "complex"
2. "query_type": "factual", "comparative", "analytical", "procedural"
3. "sub_queries": array of sub-queries (if complex) or [original query] (if simple)
4. "reasoning": brief explanation of the analysis

Guidelines:
- Simple queries: single concept, direct factual questions
- Complex queries: multiple concepts, comparisons, multi-step reasoning, "how to" questions

Examples:
Simple: "Програма за развитие на селските райони 2021"
Complex: "Сравни програмите за селски райони между 2014-2020 и 2021-2027 периодите"

Respond only with valid JSON.
"""

        response = await async_openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a query analysis expert. Respond only with valid JSON."},
                {"role": "user", "content": analysis_prompt}
            ],
            temperature=0.1,
            max_tokens=500
        )

        # Fixed: Safe access to content with None check
        content = response.choices[0].message.content
        analysis_text = content.strip() if content else ""
        logger.info(f"_analyze_query_complexity: Raw LLM response: {analysis_text}")

        # Parse JSON response
        analysis = json.loads(analysis_text)

        # Validate required fields
        required_fields = ["complexity", "query_type", "sub_queries", "reasoning"]
        if not all(field in analysis for field in required_fields):
            logger.warning(f"_analyze_query_complexity: Missing required fields in analysis: {analysis}")
            return None

        logger.info(f"_analyze_query_complexity: Analysis completed - {analysis['complexity']} query with {len(analysis['sub_queries'])} sub-queries")
        return analysis

    except json.JSONDecodeError as e:
        logger.error(f"_analyze_query_complexity: JSON parsing error: {e}")
        return None
    except Exception as e:
        logger.error(f"_analyze_query_complexity: Error: {e}", exc_info=True)
        return None


def _determine_retrieval_strategy(sub_query: str, query_analysis: Dict[str, Any]) -> str:
    """
    Helper function: Determine the best retrieval strategy for a sub-query.

    Returns:
        "small_to_big" or "enhanced_semantic"
    """
    query_type = query_analysis.get("query_type", "factual")

    # Use small-to-big for factual queries that need rich context
    if query_type in ["factual", "procedural"]:
        return "small_to_big"

    # Use enhanced semantic for comparative and analytical queries
    elif query_type in ["comparative", "analytical"]:
        return "enhanced_semantic"

    # Default to enhanced semantic
    return "enhanced_semantic"


class QueryClassifier:
    """
    Phase 3.3: Intelligent Query Classifier

    Classifies queries by:
    1. Query Type: factual, comparative, analytical, procedural, exploratory
    2. Complexity Level: simple, medium, complex
    3. Optimal Strategy: enhanced_semantic, small_to_big, agent_based
    4. Domain: programs, funding, procedures, general
    """

    def __init__(self):
        self.classification_cache = {}
        logger.info("QueryClassifier initialized")

    async def classify_query(
        self,
        query: str,
        async_openai_client: Optional[AsyncOpenAI] = None
    ) -> Dict[str, Any]:
        """
        Classify a query and determine optimal retrieval strategy.

        Returns:
            Dict with classification results and recommended strategy
        """
        # Check cache first
        cache_key = hashlib.md5(query.encode()).hexdigest()
        if cache_key in self.classification_cache:
            logger.info(f"QueryClassifier: Using cached classification for query")
            return self.classification_cache[cache_key]

        try:
            # Rule-based classification (fast fallback)
            rule_based_result = self._rule_based_classification(query)

            # LLM-enhanced classification (if available)
            if async_openai_client:
                llm_result = await self._llm_enhanced_classification(query, async_openai_client)
                if llm_result:
                    # Combine rule-based and LLM results
                    final_result = self._combine_classifications(rule_based_result, llm_result)
                else:
                    final_result = rule_based_result
            else:
                final_result = rule_based_result

            # Add recommended strategy
            final_result["recommended_strategy"] = self._determine_optimal_strategy(final_result)

            # Cache result
            self.classification_cache[cache_key] = final_result

            logger.info(f"QueryClassifier: Classified query as {final_result['query_type']} "
                       f"({final_result['complexity']}) -> {final_result['recommended_strategy']}")

            return final_result

        except Exception as e:
            logger.error(f"QueryClassifier: Error classifying query: {e}", exc_info=True)
            # Return safe fallback
            return {
                "query_type": "factual",
                "complexity": "medium",
                "domain": "general",
                "confidence": 0.5,
                "recommended_strategy": "enhanced_semantic",
                "reasoning": f"Fallback classification due to error: {str(e)}"
            }

    def _rule_based_classification(self, query: str) -> Dict[str, Any]:
        """Fast rule-based classification using keywords and patterns."""
        query_lower = query.lower()

        # Query type classification - improved patterns
        query_type = "factual"  # default
        if any(word in query_lower for word in ["сравни", "разлика", "различие", "между", "vs", "versus", "сравнение"]):
            query_type = "comparative"
        elif any(word in query_lower for word in ["как да", "как мога", "стъпки", "процедура", "кандидатствам", "документи", "необходими"]):
            query_type = "procedural"
        elif any(word in query_lower for word in ["анализирай", "оцени", "защо", "причина", "влияние", "тенденции"]):
            query_type = "analytical"
        elif any(word in query_lower for word in ["покажи", "намери", "всички", "списък", "обзор", "налични"]):
            query_type = "exploratory"
        elif any(word in query_lower for word in ["какво е", "кога е", "къде е", "кой е", "колко е"]):
            query_type = "factual"

        # Complexity classification
        complexity = "simple"
        complexity_indicators = 0

        # Check for complexity indicators
        if len(query.split()) > 10:
            complexity_indicators += 1
        if any(word in query_lower for word in ["и", "или", "също", "както", "освен това"]):
            complexity_indicators += 1
        if query_lower.count("?") > 1 or query_lower.count(",") > 2:
            complexity_indicators += 1
        if any(word in query_lower for word in ["2014-2020", "2021-2027", "период", "години"]):
            complexity_indicators += 1

        if complexity_indicators >= 3:
            complexity = "complex"
        elif complexity_indicators >= 1:
            complexity = "medium"

        # Domain classification
        domain = "general"
        if any(word in query_lower for word in ["програма", "програми", "проект", "проекти"]):
            domain = "programs"
        elif any(word in query_lower for word in ["финансиране", "средства", "бюджет", "евро"]):
            domain = "funding"
        elif any(word in query_lower for word in ["кандидатствам", "документи", "процедура", "срок"]):
            domain = "procedures"

        return {
            "query_type": query_type,
            "complexity": complexity,
            "domain": domain,
            "confidence": 0.7,
            "method": "rule_based",
            "reasoning": f"Rule-based classification: {query_type} query with {complexity} complexity in {domain} domain"
        }

    async def _llm_enhanced_classification(
        self,
        query: str,
        async_openai_client: AsyncOpenAI
    ) -> Optional[Dict[str, Any]]:
        """Enhanced classification using LLM."""
        try:
            classification_prompt = f"""
Analyze this Bulgarian query and classify it across multiple dimensions:

Query: "{query}"

Respond with a JSON object containing:
1. "query_type": "factual", "comparative", "analytical", "procedural", or "exploratory"
2. "complexity": "simple", "medium", or "complex"
3. "domain": "programs", "funding", "procedures", or "general"
4. "confidence": float between 0.0 and 1.0
5. "reasoning": brief explanation

Classification guidelines:
- factual: asking for specific information
- comparative: comparing multiple things
- analytical: requiring analysis or evaluation
- procedural: asking how to do something
- exploratory: broad discovery queries

- simple: single concept, direct question
- medium: multiple concepts or moderate complexity
- complex: multi-part questions, comparisons across time periods

- programs: about EU programs, projects
- funding: about money, budgets, financing
- procedures: about application processes, requirements
- general: other topics

Respond only with valid JSON.
"""

            response = await async_openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a query classification expert. Respond only with valid JSON."},
                    {"role": "user", "content": classification_prompt}
                ],
                temperature=0.1,
                max_tokens=300
            )

            # Fixed: Safe access to content with None check
            content = response.choices[0].message.content
            classification_text = content.strip() if content else "{}"
            classification = json.loads(classification_text)

            # Validate required fields
            required_fields = ["query_type", "complexity", "domain", "confidence", "reasoning"]
            if all(field in classification for field in required_fields):
                classification["method"] = "llm_enhanced"
                return classification
            else:
                logger.warning(f"QueryClassifier: LLM response missing required fields: {classification}")
                return None

        except json.JSONDecodeError as e:
            logger.error(f"QueryClassifier: JSON parsing error in LLM response: {e}")
            return None
        except Exception as e:
            logger.error(f"QueryClassifier: Error in LLM classification: {e}")
            return None

    def _combine_classifications(
        self,
        rule_based: Dict[str, Any],
        llm_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Combine rule-based and LLM classifications."""
        # Use LLM result as base, but validate with rule-based
        combined = llm_result.copy()

        # Adjust confidence based on agreement
        agreement_score = 0
        if rule_based["query_type"] == llm_result["query_type"]:
            agreement_score += 0.3
        if rule_based["complexity"] == llm_result["complexity"]:
            agreement_score += 0.3
        if rule_based["domain"] == llm_result["domain"]:
            agreement_score += 0.4

        # Boost confidence if methods agree
        combined["confidence"] = min(1.0, llm_result["confidence"] + agreement_score)
        combined["method"] = "combined"
        combined["agreement_score"] = agreement_score

        return combined

    def _determine_optimal_strategy(self, classification: Dict[str, Any]) -> str:
        """Determine optimal retrieval strategy based on classification."""
        query_type = classification.get("query_type", "factual")
        complexity = classification.get("complexity", "medium")
        domain = classification.get("domain", "general")

        # Strategy selection logic - improved
        if complexity == "complex":
            return "agent_based"
        elif query_type == "factual" and complexity == "simple":
            return "enhanced_semantic"  # Simple factual queries work best with enhanced semantic
        elif query_type == "procedural" and complexity in ["medium", "complex"]:
            return "small_to_big"  # Procedural queries need rich context
        elif query_type in ["comparative", "analytical"]:
            if complexity == "complex":
                return "agent_based"
            else:
                return "enhanced_semantic"
        elif query_type == "exploratory":
            return "enhanced_semantic"  # Exploratory queries work well with semantic search
        else:
            return "enhanced_semantic"  # Default to enhanced semantic for most cases


# Global query classifier instance
query_classifier = QueryClassifier()


class StrategyRouter:
    """
    Phase 3.3: Intelligent Strategy Router

    Routes queries to optimal retrieval strategies based on:
    1. Query classification results
    2. Performance monitoring data
    3. Adaptive learning from success metrics
    """

    def __init__(self):
        self.performance_history = {}
        self.strategy_stats = {
            "enhanced_semantic": {"total_queries": 0, "avg_similarity": 0.0, "success_rate": 0.0},
            "small_to_big": {"total_queries": 0, "avg_similarity": 0.0, "success_rate": 0.0},
            "agent_based": {"total_queries": 0, "avg_similarity": 0.0, "success_rate": 0.0}
        }
        logger.info("StrategyRouter initialized")

    async def route_query(
        self,
        query: str,
        supabase_client: Client,
        async_openai_client: Optional[AsyncOpenAI] = None,
        similarity_threshold: float = 0.62,
        final_top_k: int = 5,
        force_strategy: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Route query to optimal strategy and execute retrieval.

        Args:
            query: The search query
            supabase_client: Supabase client
            async_openai_client: OpenAI client
            similarity_threshold: Minimum similarity threshold
            final_top_k: Number of results to return
            force_strategy: Force specific strategy (for testing)

        Returns:
            Dict with results and routing metadata
        """
        logger.info(f"StrategyRouter: Routing query: '{query[:50]}...'")

        try:
            # Step 1: Classify query
            classification = await query_classifier.classify_query(query, async_openai_client)

            # Step 2: Determine strategy
            if force_strategy:
                strategy = force_strategy
                logger.info(f"StrategyRouter: Using forced strategy: {strategy}")
            else:
                strategy = self._select_optimal_strategy(classification)
                logger.info(f"StrategyRouter: Selected strategy: {strategy}")

            # Step 3: Execute retrieval
            start_time = asyncio.get_event_loop().time()

            if strategy == "enhanced_semantic":
                results = await enhanced_semantic_search(
                    query=query,
                    supabase_client=supabase_client,
                    async_openai_client=async_openai_client,
                    similarity_threshold=similarity_threshold,
                    final_top_k=final_top_k
                )
            elif strategy == "small_to_big":
                results = await small_to_big_retrieval(
                    query=query,
                    supabase_client=supabase_client,
                    async_openai_client=async_openai_client,
                    similarity_threshold=similarity_threshold,
                    final_top_k=final_top_k
                )
            elif strategy == "agent_based":
                results = await agent_based_retrieval(
                    query=query,
                    supabase_client=supabase_client,
                    async_openai_client=async_openai_client,
                    similarity_threshold=similarity_threshold,
                    final_top_k=final_top_k
                )
            else:
                logger.error(f"StrategyRouter: Unknown strategy: {strategy}")
                # Fallback to enhanced semantic
                results = await enhanced_semantic_search(
                    query=query,
                    supabase_client=supabase_client,
                    async_openai_client=async_openai_client,
                    similarity_threshold=similarity_threshold,
                    final_top_k=final_top_k
                )
                strategy = "enhanced_semantic"

            execution_time = asyncio.get_event_loop().time() - start_time

            # Step 4: Calculate performance metrics
            avg_similarity = sum(r.get('similarity', 0) for r in results) / len(results) if results else 0.0
            success_rate = 1.0 if avg_similarity >= similarity_threshold else 0.0

            # Step 5: Update performance tracking
            self._update_performance_stats(strategy, avg_similarity, success_rate, execution_time)

            # Step 6: Prepare response
            routing_metadata = {
                "strategy_used": strategy,
                "classification": classification,
                "execution_time": round(execution_time, 3),
                "avg_similarity": round(avg_similarity, 3),
                "success_rate": success_rate,
                "results_count": len(results),
                "performance_stats": self.strategy_stats[strategy].copy()
            }

            logger.info(f"StrategyRouter: Completed in {execution_time:.3f}s, "
                       f"avg similarity: {avg_similarity:.3f}, {len(results)} results")

            return {
                "results": results,
                "routing_metadata": routing_metadata
            }

        except Exception as e:
            logger.error(f"StrategyRouter: Error during routing: {e}", exc_info=True)
            # Fallback to enhanced semantic search
            try:
                results = await enhanced_semantic_search(
                    query=query,
                    supabase_client=supabase_client,
                    async_openai_client=async_openai_client,
                    similarity_threshold=similarity_threshold,
                    final_top_k=final_top_k
                )
                return {
                    "results": results,
                    "routing_metadata": {
                        "strategy_used": "enhanced_semantic",
                        "classification": {"error": str(e)},
                        "execution_time": 0.0,
                        "avg_similarity": 0.0,
                        "success_rate": 0.0,
                        "results_count": len(results),
                        "fallback": True
                    }
                }
            except Exception as fallback_error:
                logger.error(f"StrategyRouter: Fallback also failed: {fallback_error}")
                return {
                    "results": [],
                    "routing_metadata": {
                        "strategy_used": "none",
                        "classification": {"error": str(e)},
                        "execution_time": 0.0,
                        "avg_similarity": 0.0,
                        "success_rate": 0.0,
                        "results_count": 0,
                        "fallback_error": str(fallback_error)
                    }
                }

    def _select_optimal_strategy(self, classification: Dict[str, Any]) -> str:
        """Select optimal strategy based on classification and performance history."""
        # Get recommended strategy from classification
        recommended = classification.get("recommended_strategy", "enhanced_semantic")

        # Check if we have enough performance data to make adaptive decisions
        total_queries = sum(stats["total_queries"] for stats in self.strategy_stats.values())

        if total_queries < 10:
            # Not enough data, use classification recommendation
            return recommended

        # Adaptive selection based on performance
        confidence = classification.get("confidence", 0.5)

        if confidence < 0.7:
            # Low confidence, choose strategy with best overall performance
            best_strategy = max(
                self.strategy_stats.keys(),
                key=lambda s: self.strategy_stats[s]["avg_similarity"] * self.strategy_stats[s]["success_rate"]
            )
            logger.info(f"StrategyRouter: Low confidence ({confidence:.2f}), using best performing strategy: {best_strategy}")
            return best_strategy

        # High confidence, use recommendation but consider performance
        recommended_stats = self.strategy_stats[recommended]
        if recommended_stats["success_rate"] < 0.5 and recommended_stats["total_queries"] > 5:
            # Recommended strategy is performing poorly, try alternative
            alternatives = [s for s in self.strategy_stats.keys() if s != recommended]
            best_alternative = max(
                alternatives,
                key=lambda s: self.strategy_stats[s]["avg_similarity"] * self.strategy_stats[s]["success_rate"]
            )
            logger.info(f"StrategyRouter: Recommended strategy {recommended} performing poorly, using {best_alternative}")
            return best_alternative

        return recommended

    def _update_performance_stats(
        self,
        strategy: str,
        avg_similarity: float,
        success_rate: float,
        execution_time: float
    ):
        """Update performance statistics for a strategy."""
        stats = self.strategy_stats[strategy]

        # Update running averages
        total = stats["total_queries"]
        stats["avg_similarity"] = (stats["avg_similarity"] * total + avg_similarity) / (total + 1)
        stats["success_rate"] = (stats["success_rate"] * total + success_rate) / (total + 1)
        stats["total_queries"] += 1

        # Store in performance history (keep last 100 entries per strategy)
        if strategy not in self.performance_history:
            self.performance_history[strategy] = []

        self.performance_history[strategy].append({
            "timestamp": asyncio.get_event_loop().time(),
            "avg_similarity": avg_similarity,
            "success_rate": success_rate,
            "execution_time": execution_time
        })

        # Keep only last 100 entries
        if len(self.performance_history[strategy]) > 100:
            self.performance_history[strategy] = self.performance_history[strategy][-100:]

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all strategies."""
        return {
            "strategy_stats": self.strategy_stats.copy(),
            "total_queries": sum(stats["total_queries"] for stats in self.strategy_stats.values()),
            "best_strategy": max(
                self.strategy_stats.keys(),
                key=lambda s: self.strategy_stats[s]["avg_similarity"] * self.strategy_stats[s]["success_rate"]
            ) if any(stats["total_queries"] > 0 for stats in self.strategy_stats.values()) else "none"
        }


# Global strategy router instance
strategy_router = StrategyRouter()


async def smart_rag_query(
    query: str,
    supabase_client: Client,
    async_openai_client: Optional[AsyncOpenAI] = None,
    similarity_threshold: float = 0.62,
    final_top_k: int = 5,
    force_strategy: Optional[str] = None,
    include_metadata: bool = True
) -> Dict[str, Any]:
    """
    Phase 3.3: Smart RAG Query - Unified intelligent retrieval

    Automatically classifies queries and routes them to the optimal retrieval strategy.
    This is the main entry point for all RAG queries in Phase 3.3+.

    Args:
        query: The search query
        supabase_client: Supabase client
        async_openai_client: OpenAI client for LLM operations
        similarity_threshold: Minimum similarity threshold
        final_top_k: Number of results to return
        force_strategy: Force specific strategy (for testing/debugging)
        include_metadata: Whether to include routing metadata in response

    Returns:
        Dict with results and optional routing metadata
    """
    logger.info(f"SmartRAG: Processing query: '{query[:50]}...'")

    try:
        # Route query through strategy router
        routing_result = await strategy_router.route_query(
            query=query,
            supabase_client=supabase_client,
            async_openai_client=async_openai_client,
            similarity_threshold=similarity_threshold,
            final_top_k=final_top_k,
            force_strategy=force_strategy
        )

        results = routing_result["results"]
        routing_metadata = routing_result["routing_metadata"]

        # Format response
        response = {
            "results": results,
            "total_results": len(results),
            "query": query,
            "strategy_used": routing_metadata["strategy_used"],
            "avg_similarity": routing_metadata["avg_similarity"],
            "execution_time": routing_metadata["execution_time"]
        }

        if include_metadata:
            response["routing_metadata"] = routing_metadata

        logger.info(f"SmartRAG: Completed successfully with {len(results)} results "
                   f"using {routing_metadata['strategy_used']} strategy")

        return response

    except Exception as e:
        logger.error(f"SmartRAG: Error processing query: {e}", exc_info=True)
        return {
            "results": [],
            "total_results": 0,
            "query": query,
            "strategy_used": "error",
            "avg_similarity": 0.0,
            "execution_time": 0.0,
            "error": str(e)
        }


async def get_rag_performance_summary() -> Dict[str, Any]:
    """
    Get comprehensive performance summary for all RAG strategies.

    Returns:
        Dict with performance statistics and recommendations
    """
    try:
        performance_summary = strategy_router.get_performance_summary()

        # Add recommendations
        total_queries = performance_summary["total_queries"]
        best_strategy = performance_summary["best_strategy"]

        recommendations = []

        if total_queries < 10:
            recommendations.append("More queries needed for reliable performance analysis")

        if best_strategy != "none":
            best_stats = strategy_router.strategy_stats[best_strategy]
            recommendations.append(f"Best performing strategy: {best_strategy} "
                                 f"(avg similarity: {best_stats['avg_similarity']:.3f}, "
                                 f"success rate: {best_stats['success_rate']:.3f})")

        # Check for underperforming strategies
        for strategy, stats in strategy_router.strategy_stats.items():
            if stats["total_queries"] > 5 and stats["success_rate"] < 0.5:
                recommendations.append(f"Strategy {strategy} may need optimization "
                                     f"(success rate: {stats['success_rate']:.3f})")

        performance_summary["recommendations"] = recommendations
        performance_summary["analysis_timestamp"] = asyncio.get_event_loop().time()

        return performance_summary

    except Exception as e:
        logger.error(f"Error getting performance summary: {e}", exc_info=True)
        return {
            "error": str(e),
            "strategy_stats": {},
            "total_queries": 0,
            "best_strategy": "none",
            "recommendations": ["Error retrieving performance data"]
        }


async def _synthesize_agent_results(
    original_query: str,
    all_results: List[Dict[str, Any]],
    reasoning_chain: List[Dict[str, Any]],
    async_openai_client: Optional[AsyncOpenAI] = None,
    final_top_k: int = 5
) -> List[Dict[str, Any]]:
    """
    Helper function: Synthesize and deduplicate results from multiple retrieval steps.

    Args:
        original_query: The original user query
        all_results: All results from different retrieval steps
        reasoning_chain: Chain of reasoning steps taken
        async_openai_client: OpenAI client for intelligent synthesis
        final_top_k: Number of final results to return

    Returns:
        Synthesized and ranked results with agent metadata
    """
    if not all_results:
        logger.warning("_synthesize_agent_results: No results to synthesize")
        return []

    try:
        # Step 1: Deduplication by URL and chunk_number
        seen_chunks = set()
        deduplicated_results = []

        for result in all_results:
            chunk_key = (result.get('url', ''), result.get('chunk_number', 0))
            if chunk_key not in seen_chunks:
                seen_chunks.add(chunk_key)
                deduplicated_results.append(result)

        logger.info(f"_synthesize_agent_results: Deduplicated {len(all_results)} -> {len(deduplicated_results)} results")

        # Step 2: Score results based on relevance and agent reasoning
        scored_results = []
        for result in deduplicated_results:
            base_score = result.get('similarity', 0.0)

            # Boost score if result appears in multiple reasoning steps
            reasoning_boost = 0.0
            for step in reasoning_chain:
                if any(r.get('id') == result.get('id') for r in all_results):
                    reasoning_boost += 0.1

            # Boost score for results with rich metadata
            metadata_boost = 0.0
            if result.get('metadata', {}).get('extracted_entities'):
                metadata_boost = 0.05

            final_score = base_score + reasoning_boost + metadata_boost

            scored_results.append({
                **result,
                'agent_score': final_score,
                'reasoning_boost': reasoning_boost,
                'metadata_boost': metadata_boost
            })

        # Step 3: Sort by agent score and return top results
        scored_results.sort(key=lambda x: x.get('agent_score', 0), reverse=True)
        final_results = scored_results[:final_top_k]

        # Step 4: Add agent metadata to results
        for i, result in enumerate(final_results):
            result['agent_metadata'] = {
                'rank': i + 1,
                'reasoning_chain': reasoning_chain,
                'synthesis_method': 'agent_based',
                'total_steps': len(reasoning_chain),
                'deduplication_ratio': len(deduplicated_results) / len(all_results) if all_results else 1.0
            }

        logger.info(f"_synthesize_agent_results: Synthesis completed, returning {len(final_results)} results")
        return final_results

    except Exception as e:
        logger.error(f"_synthesize_agent_results: Error during synthesis: {e}", exc_info=True)
        # Fallback: return top results by similarity
        logger.info("_synthesize_agent_results: Falling back to similarity-based ranking")
        return sorted(all_results, key=lambda x: x.get('similarity', 0), reverse=True)[:final_top_k]


# ============================================================================
# PHASE 4: ADVANCED RAG OPTIMIZATION FOR 99% ACCURACY
# ============================================================================

async def enhanced_rag_search_v4(
    query: str,
    supabase_client: Client,
    async_openai_client: Optional[AsyncOpenAI] = None,
    use_content_enhancement: bool = True,
    use_hybrid_search: bool = True,
    similarity_threshold: float = 0.62,
    final_top_k: int = 5
) -> List[Dict[str, Any]]:
    """
    Phase 4: Enhanced RAG search with content enhancement and hybrid search

    This is the most advanced RAG implementation targeting 99% accuracy.
    """
    logger.info(f"🚀 Phase 4 Enhanced RAG Search: '{query[:50]}...'")

    try:
        # Import Phase 4 modules - FIXED: absolute imports
        from content_enhancement import enhance_document_batch
        from hybrid_search import hybrid_rag_search

        # Step 0: Query Understanding & Intent Detection (Phase 4.4)
        query_metadata = None
        if analyze_user_query:
            logger.info("🧠 Analyzing query for intent and entities")
            query_metadata = await analyze_user_query(query, async_openai_client)
            logger.info(f"📊 Query analysis: {query_metadata['query_type']}, "
                       f"complexity: {query_metadata['complexity']}, "
                       f"confidence: {query_metadata['confidence']:.2f}")
        else:
            # Fallback query metadata
            query_metadata = {
                'query_type': 'factual',
                'complexity': 'simple',
                'programs': [],
                'keywords': query.lower().split(),
                'confidence': 0.5
            }

        # Step 1: Hybrid Search (semantic + keyword)
        if use_hybrid_search:
            logger.info("🔍 Using hybrid search (semantic + keyword)")
            results = await hybrid_rag_search(
                query=query,
                supabase_client=supabase_client,
                limit=final_top_k * 2,  # Get more candidates
                semantic_weight=0.7,
                keyword_weight=0.3
            )
        else:
            # Fallback to enhanced semantic search
            logger.info("🔍 Using enhanced semantic search")
            results = await enhanced_semantic_search(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=async_openai_client,
                final_top_k=final_top_k * 2
            )

        # Step 2: Content Enhancement
        if use_content_enhancement and results:
            logger.info("✨ Applying content enhancement")
            results = await enhance_document_batch(results, async_openai_client)

        # Step 3: Advanced Ranking (Phase 4.3)
        if results and advanced_rank_results:
            logger.info("🎯 Applying advanced ranking")

            # Apply advanced ranking with query metadata from Phase 4.4
            results = await advanced_rank_results(
                results=results,
                query=query,
                query_metadata=query_metadata,
                ranking_strategy="context_aware"
            )
        else:
            # Fallback: Re-rank based on quality scores
            for result in results:
                quality_score = result.get('quality_score', 0.5)
                original_score = result.get('similarity', 0.0)
                # Boost score based on content quality
                result['enhanced_similarity'] = original_score * (1 + quality_score * 0.3)

            # Sort by enhanced similarity
            results.sort(key=lambda x: x.get('enhanced_similarity', 0), reverse=True)

        # Step 3.5: Intelligent Result Reranking (Phase 4.5)
        if results and intelligent_rerank_results:
            logger.info("🧠 Applying intelligent multi-stage reranking")

            # Apply intelligent reranking with all available metadata
            results = await intelligent_rerank_results(
                results=results,
                query=query,
                query_metadata=query_metadata,
                async_openai_client=async_openai_client,
                top_k=min(final_top_k * 2, len(results))  # Get more candidates for final selection
            )

            logger.info(f"✅ Intelligent reranking completed: {len(results)} results")
        else:
            logger.info("⚠️ Intelligent reranking not available, using existing ranking")

        # Step 4: Final filtering and ranking
        final_results = results[:final_top_k]

        logger.info(f"✅ Phase 4 Enhanced RAG completed: {len(final_results)} results")
        return final_results

    except Exception as e:
        logger.error(f"❌ Phase 4 Enhanced RAG failed: {e}")
        # Fallback to Phase 3 smart RAG
        fallback_result = await smart_rag_query(
            query=query,
            supabase_client=supabase_client,
            async_openai_client=async_openai_client,
            final_top_k=final_top_k
        )
        return fallback_result.get('results', [])


async def ultra_smart_rag_query(
    query: str,
    supabase_client: Client,
    async_openai_client: Optional[AsyncOpenAI] = None,
    similarity_threshold: float = 0.62,
    final_top_k: int = 5,
    enable_all_optimizations: bool = True,
    adaptive_threshold: bool = True,
    multi_pass_search: bool = True,
    query_expansion: bool = True,
    # Phase 4.7 NEW PARAMETERS
    intelligent_clustering: bool = True,
    dynamic_learning: bool = True,
    performance_optimization: bool = True
) -> Dict[str, Any]:
    """
    Phase 4.7: Ultra Smart RAG Query - Maximum optimization for 99%+ accuracy

    PHASE 4.6 OPTIMIZATIONS:
    - Adaptive similarity threshold based on query complexity
    - Multi-pass search with query expansion
    - Dynamic result fusion from multiple strategies
    - Enhanced quality scoring with confidence intervals

    PHASE 4.7 NEW OPTIMIZATIONS:
    - Intelligent semantic clustering for better result organization
    - Dynamic learning from query patterns and result quality
    - Performance optimization with caching and parallel processing
    - Advanced query understanding with intent classification
    """
    start_time = time.time()
    logger.info(f"🚀 ultra_smart_rag_query ЗАПОЧВА: query='{query[:50]}...', threshold={similarity_threshold}")

    try:
        # Phase 4.7: Enhanced adaptive threshold with intelligent query understanding
        if adaptive_threshold:
            # Advanced query analysis for better threshold adjustment
            query_words = len(query.split())
            query_complexity = await _analyze_query_complexity(query, async_openai_client) if performance_optimization else "simple"

            # Dynamic threshold based on complexity and historical performance
            if query_complexity == "complex" or query_words >= 10:
                similarity_threshold = min(0.8, similarity_threshold + 0.15)  # Higher for complex queries
            elif query_complexity == "simple" or query_words <= 3:
                similarity_threshold = max(0.45, similarity_threshold - 0.15)  # Lower for simple queries
            else:  # medium complexity
                similarity_threshold = similarity_threshold  # Keep original

            logger.info(f"📊 Adaptive threshold adjusted to: {similarity_threshold} (complexity: {query_complexity})")

        # Phase 4.6: Multi-pass search strategy
        all_results = []

        if multi_pass_search and enable_all_optimizations:
            # Pass 1: Standard enhanced search
            results_pass1 = await enhanced_rag_search_v4(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=async_openai_client,
                use_content_enhancement=True,
                use_hybrid_search=True,
                similarity_threshold=similarity_threshold,
                final_top_k=final_top_k * 2  # Get more for fusion
            )
            all_results.extend(results_pass1)
            logger.info(f"🔍 Pass 1: Retrieved {len(results_pass1)} results")

            # Pass 2: Query expansion search (if enabled)
            if query_expansion and len(results_pass1) < final_top_k:
                expanded_queries = await _generate_query_variations(query, async_openai_client)
                for expanded_query in expanded_queries[:2]:  # Limit to 2 variations
                    results_expansion = await enhanced_rag_search_v4(
                        query=expanded_query,
                        supabase_client=supabase_client,
                        async_openai_client=async_openai_client,
                        use_content_enhancement=True,
                        use_hybrid_search=True,
                        similarity_threshold=similarity_threshold - 0.1,  # Lower threshold for expansion
                        final_top_k=final_top_k
                    )
                    all_results.extend(results_expansion)
                logger.info(f"🔍 Pass 2: Query expansion added {len(all_results) - len(results_pass1)} results")

            # Phase 4.7: Enhanced result fusion with intelligent clustering
            if intelligent_clustering:
                results = await _fuse_and_cluster_results_v47(all_results, final_top_k, query)
            else:
                results = await _fuse_and_deduplicate_results(all_results, final_top_k)

        elif enable_all_optimizations:
            # Standard Phase 4 enhanced search
            results = await enhanced_rag_search_v4(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=async_openai_client,
                use_content_enhancement=True,
                use_hybrid_search=True,
                similarity_threshold=similarity_threshold,
                final_top_k=final_top_k
            )
        else:
            # Fallback to Phase 3 smart RAG
            smart_result = await smart_rag_query(
                query=query,
                supabase_client=supabase_client,
                async_openai_client=async_openai_client,
                similarity_threshold=similarity_threshold,
                final_top_k=final_top_k
            )
            results = smart_result.get('results', [])

        execution_time = time.time() - start_time

        # Phase 4.7: Enhanced metrics with clustering and learning insights
        avg_similarity = statistics.mean([r.get('similarity', 0) for r in results]) if results else 0
        avg_quality = statistics.mean([r.get('quality_score', 0.5) for r in results]) if results else 0.5

        # Phase 4.7: Additional metrics
        cluster_diversity = len(set(r.get('cluster_id', 0) for r in results)) if intelligent_clustering else 1
        confidence_score = min(avg_similarity * avg_quality * 1.2, 1.0)  # Combined confidence

        response = {
            "results": results,
            "total_results": len(results),
            "query": query,
            "strategy_used": "ultra_smart_v4.7",
            "avg_similarity": avg_similarity,
            "avg_quality_score": avg_quality,
            "execution_time": execution_time,
            "optimizations_enabled": enable_all_optimizations,
            "phase": "4.7",
            # Phase 4.7 new metrics
            "cluster_diversity": cluster_diversity,
            "confidence_score": confidence_score,
            "adaptive_threshold_used": similarity_threshold,
            "optimizations": {
                "intelligent_clustering": intelligent_clustering,
                "dynamic_learning": dynamic_learning,
                "performance_optimization": performance_optimization
            }
        }

        logger.info(f"🚀 ultra_smart_rag_query ЗАВЪРШИ: {execution_time:.2f}s, {len(results)} results, "
                   f"avg similarity: {avg_similarity:.3f}, avg quality: {avg_quality:.3f}")

        return response

    except Exception as e:
        logger.error(f"❌ Ultra Smart RAG failed: {e}")
        return {
            "results": [],
            "total_results": 0,
            "query": query,
            "strategy_used": "error",
            "avg_similarity": 0.0,
            "avg_quality_score": 0.0,
            "execution_time": time.time() - start_time,
            "error": str(e)
        }


async def _generate_query_variations(query: str, async_openai_client: Optional[AsyncOpenAI] = None) -> List[str]:
    """
    DEACTIVATED: Query variations generation disabled for performance optimization.
    Returns empty list to eliminate API calls and improve response time.
    """
    logger.info("_generate_query_variations: DEACTIVATED - returning empty list for performance")
    return []


async def _fuse_and_deduplicate_results(all_results: List[Dict[str, Any]], final_top_k: int) -> List[Dict[str, Any]]:
    """
    Phase 4.6: Advanced result fusion with deduplication and quality scoring
    """
    if not all_results:
        return []

    # Step 1: Deduplicate by URL
    seen_urls = set()
    unique_results = []

    for result in all_results:
        url = result.get('url', '')
        if url and url not in seen_urls:
            seen_urls.add(url)
            unique_results.append(result)

    # Step 2: Enhanced scoring with multiple factors
    for result in unique_results:
        base_score = result.get('similarity', 0.0)
        quality_score = result.get('quality_score', 0.5)

        # Boost score based on content quality
        enhanced_score = (base_score * 0.7) + (quality_score * 0.3)

        # Boost for program name matches
        content = result.get('content', '').lower()
        if any(keyword in content for keyword in ['програма', 'процедура', 'мярка']):
            enhanced_score += 0.1

        # Boost for recent content
        if any(year in content for year in ['2024', '2025', '2026', '2027']):
            enhanced_score += 0.05

        result['enhanced_score'] = enhanced_score

    # Step 3: Sort by enhanced score and return top results
    unique_results.sort(key=lambda x: x.get('enhanced_score', 0), reverse=True)

    logger.info(f"🔀 Fused {len(all_results)} → {len(unique_results)} unique → {min(final_top_k, len(unique_results))} final")

    return unique_results[:final_top_k]


# --- PHASE 4.7 NEW FUNCTIONS ---

async def analyze_query_complexity(query: str, async_openai_client: Optional[AsyncOpenAI] = None) -> str:
    """
    Phase 4.7: Analyze query complexity for better threshold adjustment
    """
    if not async_openai_client:
        # Fallback: simple heuristic analysis
        words = len(query.split())
        if words <= 3:
            return "simple"
        elif words >= 8:
            return "complex"
        else:
            return "medium"

    try:
        prompt = f"""
Анализирай сложността на следната заявка за търсене в база данни с европейски програми:

"{query}"

Върни само една дума:
- "simple" - за прости, директни въпроси (1-3 ключови думи)
- "medium" - за средно сложни въпроси (4-7 думи, ясна тема)
- "complex" - за сложни въпроси (8+ думи, множество теми, условия)

Отговор:"""

        response = await async_openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,
            max_tokens=10
        )

        # Fixed: Safe access to content with None check
        content = response.choices[0].message.content
        if content:
            complexity = content.strip().lower()
            if complexity in ["simple", "medium", "complex"]:
                return complexity
            else:
                return "medium"  # Default fallback
        else:
            logger.warning("analyze_query_complexity: No content in response")
            return "medium"

    except Exception as e:
        logger.warning(f"_analyze_query_complexity: Error analyzing complexity: {e}")
        return "medium"


async def _fuse_and_cluster_results_v47(all_results: List[Dict[str, Any]], final_top_k: int, query: str) -> List[Dict[str, Any]]:
    """
    Phase 4.7: Advanced result fusion with intelligent semantic clustering
    """
    if not all_results:
        return []

    # Step 1: Deduplicate by URL (same as Phase 4.6)
    seen_urls = set()
    unique_results = []

    for result in all_results:
        url = result.get('url', '')
        if url and url not in seen_urls:
            seen_urls.add(url)
            unique_results.append(result)

    logger.info(f"🔀 Deduplicated {len(all_results)} → {len(unique_results)} unique results")

    # Step 2: Phase 4.7 Intelligent Clustering
    clustered_results = await _apply_semantic_clustering(unique_results, query)

    # Step 3: Enhanced scoring with cluster diversity
    for result in clustered_results:
        base_score = result.get('similarity', 0.0)
        quality_score = result.get('quality_score', 0.5)

        # Phase 4.7: Cluster diversity bonus
        cluster_id = result.get('cluster_id', 0)
        cluster_size = sum(1 for r in clustered_results if r.get('cluster_id') == cluster_id)
        diversity_bonus = 0.05 if cluster_size <= 2 else 0.0  # Bonus for diverse clusters

        # Enhanced scoring with multiple factors
        enhanced_score = (base_score * 0.6) + (quality_score * 0.3) + diversity_bonus

        # Boost for program name matches
        content = result.get('content', '').lower()
        if any(keyword in content for keyword in ['програма', 'процедура', 'мярка', 'фонд']):
            enhanced_score += 0.1

        # Temporal relevance boost (prefer recent content)
        if any(year in content for year in ['2024', '2023', '2022']):
            enhanced_score += 0.05

        result['enhanced_score'] = enhanced_score

    # Step 4: Sort by enhanced score and return top results
    final_results = sorted(clustered_results, key=lambda x: x.get('enhanced_score', 0), reverse=True)[:final_top_k]

    logger.info(f"🎯 Phase 4.7 clustering: {len(unique_results)} → {len(final_results)} final with {len(set(r.get('cluster_id', 0) for r in final_results))} clusters")
    return final_results


async def _apply_semantic_clustering(results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
    """
    Phase 4.7: Apply intelligent semantic clustering to results
    """
    if len(results) <= 3:
        # Too few results for meaningful clustering
        for i, result in enumerate(results):
            result['cluster_id'] = i
        return results

    try:
        # Simple clustering based on content similarity and program types
        clusters = {}
        cluster_id = 0

        for result in results:
            content = result.get('content', '').lower()
            url = result.get('url', '')

            # Determine cluster based on program type
            if 'опрр' in content or 'регион' in content or 'oprd' in url:
                cluster_key = 'regional'
            elif 'оптти' in content or 'транспорт' in content or 'optti' in url:
                cluster_key = 'transport'
            elif 'опос' in content or 'околна среда' in content or 'opos' in url:
                cluster_key = 'environment'
            elif 'interreg' in content or 'interreg' in url:
                cluster_key = 'interreg'
            elif 'граници' in content or 'визова' in content:
                cluster_key = 'borders'
            elif 'справедлив преход' in content:
                cluster_key = 'just_transition'
            else:
                cluster_key = 'general'

            if cluster_key not in clusters:
                clusters[cluster_key] = cluster_id
                cluster_id += 1

            result['cluster_id'] = clusters[cluster_key]
            result['cluster_type'] = cluster_key

        logger.info(f"🎯 Semantic clustering: {len(results)} results → {len(clusters)} clusters")
        return results

    except Exception as e:
        logger.warning(f"_apply_semantic_clustering: Error in clustering: {e}")
        # Fallback: assign sequential cluster IDs
        for i, result in enumerate(results):
            result['cluster_id'] = i % 3  # Simple fallback clustering
        return results


async def enhanced_semantic_search(
    query: str,
    supabase_client: Client,
    async_openai_client: Optional[AsyncOpenAI] = None,
    num_query_variations: int = 3,
    similarity_threshold: float = 0.62,
    max_results_per_query: int = 20,
    final_top_k: int = 10,
    use_metadata_routing: bool = True
) -> List[Dict[str, Any]]:
    """
    PHASE 2: Подобрено семантично търсене с Multi-Query Transformation и RRF.

    Процес:
    1. Генерира алтернативни версии на заявката
    2. Извлича метаданни филтри от оригиналната заявка
    3. Изпълнява търсене за всяка версия на заявката
    4. Комбинира резултатите с Reciprocal Rank Fusion
    5. Прилага метаданни филтри
    """
    import time
    start_time = time.time()
    logger.info(f"🔍 enhanced_semantic_search ЗАПОЧВА: query='{query[:50]}...', threshold={similarity_threshold}")

    try:
        # Стъпка 1: Генериране на алтернативни заявки
        all_queries = await generate_query_variations(
            query,
            num_query_variations,
            async_openai_client
        )

        # Стъпка 2: Извличане на метаданни филтри
        metadata_filters = {}
        if use_metadata_routing and async_openai_client:
            metadata_filters = await extract_metadata_filters(query, async_openai_client)

        # Стъпка 3: Създаване на embeddings за всички заявки
        all_embeddings = create_embeddings_batch(all_queries)
        if not all_embeddings:
            logger.error("enhanced_semantic_search: Failed to create embeddings")
            return []

        # Стъпка 4: Изпълнение на търсене за всяка заявка
        all_ranked_lists = []

        for i, (query_text, embedding) in enumerate(zip(all_queries, all_embeddings)):
            try:
                # Търсене в Supabase с правилните параметри
                response = supabase_client.rpc(
                    'match_crawled_pages_v4_debug',
                    {
                        'p_query_embedding': embedding,
                        'p_match_count': max_results_per_query,
                        'p_min_similarity_threshold': similarity_threshold,
                        'p_weight_similarity': 0.7,  # Задължителен параметър
                        'p_weight_program_name': 0.2,  # Задължителен параметър
                        'p_weight_year': 0.1  # Задължителен параметър
                    }
                ).execute()

                if response.data:
                    # Добавяме информация за коя заявка е този резултат
                    for doc in response.data:
                        doc['source_query'] = query_text
                        doc['source_query_index'] = i

                    all_ranked_lists.append(response.data)
                    logger.debug(f"enhanced_semantic_search: Query {i+1} returned {len(response.data)} results")
                else:
                    logger.warning(f"enhanced_semantic_search: Query {i+1} returned no results")

            except Exception as e:
                logger.error(f"enhanced_semantic_search: Error searching for query {i+1}: {e}")
                continue

        if not all_ranked_lists:
            logger.warning("enhanced_semantic_search: No results from any query")
            return []

        # Стъпка 5: Reciprocal Rank Fusion
        fused_results = reciprocal_rank_fusion(all_ranked_lists)

        # Стъпка 6: Прилагане на метаданни филтри
        if metadata_filters:
            fused_results = apply_metadata_filters(fused_results, metadata_filters)

        # Стъпка 7: Връщане на топ резултати
        final_results = fused_results[:final_top_k]

        # TIMING LOG
        end_time = time.time()
        duration = end_time - start_time
        logger.info(f"🔍 enhanced_semantic_search ЗАВЪРШИ: {duration:.2f}s, processed {len(all_queries)} queries, "
                   f"got {len(fused_results)} fused results, returning top {len(final_results)}")

        return final_results

    except Exception as e:
        logger.error(f"enhanced_semantic_search: Critical error: {e}", exc_info=True)
        return []


def rerank_documents(
    query: str,
    documents: List[Dict[str, Any]],
    reranker_type: RerankerType,
    top_n: int = 5,
    cohere_client: Optional['cohere.client.Client'] = None,
    cross_encoder_model: Optional['CrossEncoder'] = None
) -> List[Dict[str, Any]]:
    # ... (цялата функция остава същата, не я копирам тук за краткост)
    if not documents:
        logger.info("rerank_documents: Няма документи за пренареждане.")
        return []

    if reranker_type == RerankerType.NONE:
        logger.info("rerank_documents: RerankerType е NONE. Връща оригиналните топ N документи.")
        return documents[:top_n]

    reranked_docs = []
    try:
        if reranker_type == RerankerType.COHERE and cohere_client:
            logger.info(f"rerank_documents: Cohere reranking DEACTIVATED - returning top {top_n} documents by similarity")
            # Return top documents without Cohere API call
            return documents[:top_n]

        elif reranker_type == RerankerType.CROSS_ENCODER and cross_encoder_model:
            logger.info(f"rerank_documents: Извикване на CrossEncoder модел за заявка '{query[:50]}...' с {len(documents)} документа. Изискани топ {top_n}.")
            sentence_pairs = [[query, doc.get('content', '')] for doc in documents]
            scores = cross_encoder_model.predict(sentence_pairs, show_progress_bar=False)
            for doc, score in zip(documents, scores):
                doc['rerank_score'] = float(score)
            reranked_docs = sorted(documents, key=lambda x: x['rerank_score'], reverse=True)
            logger.info(f"rerank_documents (CrossEncoder): Успешно пренареждане. Върнати {len(reranked_docs[:top_n])} документа.")
            return reranked_docs[:top_n]

        else:
            logger.warning(f"rerank_documents: Re-ranker '{reranker_type.value}' е избран, но нужният клиент/модел не е предоставен. Връща оригиналните топ N документи.")
            return documents[:top_n]

    except Exception as e_cohere:
        # Fixed: Generic exception handling for Cohere API errors
        if "cohere" in str(type(e_cohere)).lower():
            logger.error(f"rerank_documents: Cohere API грешка: {e_cohere}", exc_info=True)
        else:
            logger.error(f"rerank_documents: Грешка при reranking: {e_cohere}", exc_info=True)
        logger.warning(f"rerank_documents: Връщане към оригиналните топ {top_n} документи поради API грешка.")
        return documents[:top_n]

def extract_text_from_pdf_bytes(pdf_bytes: bytes, source_url_for_log: str = "N/A") -> tuple[Optional[str], Optional[str]]:
    # ... (цялата функция остава същата, не я копирам тук за краткост)
    logger.info(f"PDF Text Extractor (utils.py): Processing PDF from memory for {source_url_for_log}...")
    try:
        with fitz.open(stream=pdf_bytes, filetype="pdf") as pdf_doc_mem: 
            if pdf_doc_mem.page_count == 0: 
                logger.warning(f"PDF (from memory for {source_url_for_log}) has 0 pages. (utils.py)")
                return "", "PDF (from memory) has 0 pages." 
            # Fixed: Safe access to page.get_text method with try-except
            text_mem = ""
            for page in pdf_doc_mem:
                try:
                    text_mem += page.get_text("text")  # type: ignore
                except (AttributeError, TypeError):
                    text_mem += str(page)
            text_mem = text_mem.strip()
            if not text_mem and pdf_doc_mem.page_count > 0: 
                logger.info(f"PDF (from memory for {source_url_for_log}) has pages but no text (possibly image-based). (utils.py)")
                return "", "PDF (from memory) has pages but no text extracted." 
            logger.info(f"PDF Text Extractor (utils.py): Text (from memory) extracted from {source_url_for_log}. Length: {len(text_mem)} chars.")
            return text_mem, None 
    except Exception as e_mem: 
        final_err_msg = f"Error processing PDF from memory for {source_url_for_log} with fitz: {type(e_mem).__name__} - {e_mem} (utils.py)"
        logger.error(f"PDF Text Extractor (utils.py): {final_err_msg}", exc_info=True)
        return None, final_err_msg 

def get_all_chunks_for_document_url(client: Client, document_url: str) -> List[Dict[str, Any]]:
    # ... (цялата функция остава същата, не я копирам тук за краткост)
    if not client or not document_url: logger.error("get_all_chunks_for_document_url (utils.py): Client or document_url is missing."); return []
    try:
        logger.info(f"Fetching all chunks for document URL: {document_url} (utils.py)")
        response = client.table("crawled_pages").select("id, url, chunk_number, content, metadata").eq("url", document_url).order("chunk_number", desc=False).execute()
        # Fixed: Safe access to response attributes
        if response.data:
            logger.info(f"Found {len(response.data)} chunks for URL: {document_url} (utils.py)")
            return response.data
        elif hasattr(response, 'error') and getattr(response, 'error', None):
            logger.error(f"Error fetching chunks for URL {document_url} (utils.py): {getattr(response, 'error', 'Unknown error')}")
            return []
        else:
            logger.info(f"No chunks found for URL: {document_url} (utils.py)")
            return []
    except Exception as e: logger.error(f"Unexpected error in get_all_chunks_for_document_url for URL {document_url} (utils.py): {type(e).__name__} - {e}", exc_info=True); return []

async def search_documents_with_text_bulgarian_optimized(
    client: Client,
    query_text: str,
    match_count: int = 10,
    weight_dense: float = 0.6,
    weight_sparse: float = 0.4,
    min_similarity_threshold: float = 0.1,
    enable_reranking: bool = True,
    rerank_top_k: int = 20,
    use_bulgarian_embeddings: bool = True
) -> List[Dict[str, Any]]:
    """
    Phase 8.3: Bulgarian-optimized Search with LaBSE Embeddings

    Combines Bulgarian-specific embeddings (LaBSE) with hybrid search and cross-encoder reranking
    for maximum accuracy on Bulgarian content.

    Args:
        client: Supabase client
        query_text: Search query
        match_count: Final number of results to return
        weight_dense: Weight for semantic similarity (0.0-1.0)
        weight_sparse: Weight for keyword matching (0.0-1.0)
        min_similarity_threshold: Minimum similarity threshold
        enable_reranking: Whether to apply cross-encoder reranking
        rerank_top_k: Number of initial results to rerank
        use_bulgarian_embeddings: Whether to use LaBSE for Bulgarian content

    Returns:
        List of optimized search results for Bulgarian content
    """
    logger.info(f"🇧🇬 Phase 8.3 Bulgarian-optimized Search: '{query_text[:50]}...'")

    try:
        # Step 1: Detect if query is Bulgarian and use appropriate embeddings
        if use_bulgarian_embeddings and get_bulgarian_embedding_engine:
            try:
                engine = get_bulgarian_embedding_engine()
                is_bulgarian = engine.is_bulgarian_text(query_text)

                if is_bulgarian and create_bulgarian_embeddings is not None:
                    logger.info("🇧🇬 Bulgarian query detected - using LaBSE embeddings")

                    # Create Bulgarian-optimized embeddings
                    query_embeddings = await create_bulgarian_embeddings(query_text)

                    # Extract single embedding from batch result
                    if query_embeddings.ndim > 1 and query_embeddings.shape[0] == 1:
                        query_embeddings = query_embeddings[0]  # Extract first row

                    # Use Bulgarian-optimized search
                    initial_results = search_documents_with_text_hybrid_bulgarian(
                        client=client,
                        query_text=query_text,
                        query_embeddings=query_embeddings,
                        match_count=rerank_top_k,
                        weight_dense=weight_dense,
                        weight_sparse=weight_sparse,
                        min_similarity_threshold=min_similarity_threshold
                    )
                else:
                    logger.info("🌍 Non-Bulgarian query - using standard hybrid search")
                    initial_results = search_documents_with_text_hybrid(
                        client=client,
                        query_text=query_text,
                        match_count=rerank_top_k,
                        weight_dense=weight_dense,
                        weight_sparse=weight_sparse,
                        min_similarity_threshold=min_similarity_threshold
                    )
            except Exception as e:
                logger.warning(f"⚠️ Bulgarian embeddings failed, falling back to standard: {e}")
                initial_results = search_documents_with_text_hybrid(
                    client=client,
                    query_text=query_text,
                    match_count=rerank_top_k,
                    weight_dense=weight_dense,
                    weight_sparse=weight_sparse,
                    min_similarity_threshold=min_similarity_threshold
                )
        else:
            # Fallback to standard hybrid search
            logger.info("🔄 Using standard hybrid search")
            initial_results = search_documents_with_text_hybrid(
                client=client,
                query_text=query_text,
                match_count=rerank_top_k,
                weight_dense=weight_dense,
                weight_sparse=weight_sparse,
                min_similarity_threshold=min_similarity_threshold
            )

        if not initial_results:
            logger.warning("🔍 No initial search results found")
            return []

        logger.info(f"📊 Initial search: {len(initial_results)} results")

        # Step 2: Apply cross-encoder reranking if enabled
        if enable_reranking and rerank_search_results:
            logger.info("🧠 Applying cross-encoder reranking...")

            reranked_results = await rerank_search_results(
                query=query_text,
                results=initial_results,
                top_k=match_count
            )

            logger.info(f"✅ Bulgarian-optimized search completed: {len(reranked_results)} final results")
            return reranked_results

        else:
            # Return top results without reranking
            logger.info("⚠️ Cross-encoder reranking disabled")
            return initial_results[:match_count]

    except Exception as e:
        logger.error(f"❌ Bulgarian-optimized search failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Final fallback to standard hybrid search
        try:
            logger.info("🔄 Final fallback to standard hybrid search...")
            return search_documents_with_text_hybrid(
                client=client,
                query_text=query_text,
                match_count=match_count,
                weight_dense=weight_dense,
                weight_sparse=weight_sparse,
                min_similarity_threshold=min_similarity_threshold
            )
        except Exception as fallback_error:
            logger.error(f"❌ All search methods failed: {fallback_error}")
            return []

async def search_documents_with_text_hybrid_reranked(
    client: Client,
    query_text: str,
    match_count: int = 10,
    weight_dense: float = 0.6,
    weight_sparse: float = 0.4,
    min_similarity_threshold: float = 0.1,
    enable_reranking: bool = True,
    rerank_top_k: int = 20
) -> List[Dict[str, Any]]:
    """
    Phase 8.2: Hybrid Search with Cross-encoder Reranking

    Combines hybrid search (BM25 + Dense vectors) with cross-encoder reranking
    for maximum relevance and accuracy.

    Args:
        client: Supabase client
        query_text: Search query
        match_count: Final number of results to return
        weight_dense: Weight for semantic similarity (0.0-1.0)
        weight_sparse: Weight for keyword matching (0.0-1.0)
        min_similarity_threshold: Minimum similarity threshold
        enable_reranking: Whether to apply cross-encoder reranking
        rerank_top_k: Number of initial results to rerank

    Returns:
        List of reranked search results
    """
    logger.info(f"🔥 Phase 8.2 Hybrid Search with Reranking: '{query_text[:50]}...'")

    try:
        # Step 1: Get initial hybrid search results
        initial_results = search_documents_with_text_hybrid(
            client=client,
            query_text=query_text,
            match_count=rerank_top_k,  # Get more results for reranking
            weight_dense=weight_dense,
            weight_sparse=weight_sparse,
            min_similarity_threshold=min_similarity_threshold
        )

        if not initial_results:
            logger.warning("🔍 No initial hybrid search results found")
            return []

        logger.info(f"📊 Initial hybrid search: {len(initial_results)} results")

        # Step 2: Apply cross-encoder reranking if enabled
        if enable_reranking and rerank_search_results:
            logger.info("🧠 Applying cross-encoder reranking...")

            reranked_results = await rerank_search_results(
                query=query_text,
                results=initial_results,
                top_k=match_count
            )

            logger.info(f"✅ Reranking completed: {len(reranked_results)} final results")
            return reranked_results

        else:
            # Fallback: Return top results without reranking
            logger.info("⚠️ Cross-encoder reranking disabled or unavailable")
            return initial_results[:match_count]

    except Exception as e:
        logger.error(f"❌ Hybrid search with reranking failed: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")

        # Fallback to regular hybrid search
        try:
            logger.info("🔄 Falling back to regular hybrid search...")
            return search_documents_with_text_hybrid(
                client=client,
                query_text=query_text,
                match_count=match_count,
                weight_dense=weight_dense,
                weight_sparse=weight_sparse,
                min_similarity_threshold=min_similarity_threshold
            )
        except Exception as fallback_error:
            logger.error(f"❌ Fallback hybrid search also failed: {fallback_error}")
            return []

# =============================================================================
# PHASE 8.4: ADVANCED QUERY PROCESSING SEARCH
# =============================================================================

async def search_crawled_pages_advanced_query(
    query_text: str,
    match_count: int = 10,
    use_bulgarian_embeddings: bool = True,
    rerank_top_k: int = 20,
    enable_query_expansion: bool = True,
    enable_hyde: bool = True,
    enable_multi_query: bool = True
) -> List[Dict[str, Any]]:
    """
    🚀 Phase 8.4: Advanced Query Processing Search

    Combines Bulgarian embeddings with advanced query processing:
    - Query expansion with synonyms
    - HyDE (Hypothetical Document Embeddings) pattern
    - Multi-query transformation
    - Cross-encoder reranking

    Args:
        query_text: The search query
        match_count: Number of final results to return
        use_bulgarian_embeddings: Use LaBSE for Bulgarian queries
        rerank_top_k: Number of results to rerank
        enable_query_expansion: Enable synonym expansion
        enable_hyde: Enable HyDE pattern
        enable_multi_query: Enable query variants

    Returns:
        List of search results with advanced processing
    """
    start_time = time.time()

    logger.info(f"🚀 Phase 8.6.4 Bulgarian-Optimized Advanced Query Search: '{query_text[:50]}...'")

    # Get Supabase client
    client = get_supabase_client()

    try:
        # Step 0: Bulgarian Language Optimization (Phase 8.6.4)
        if enable_query_expansion:
            logger.info("🇧🇬 Applying Phase 8.6.4 Bulgarian Language Optimization...")
            expanded_queries = expand_query_with_synonyms(query_text)
            bulgarian_keywords = extract_bulgarian_keywords(query_text)

            logger.info(f"   🔍 Bulgarian keywords extracted: {len(bulgarian_keywords)}")
            logger.info(f"   📝 Query expanded to {len(expanded_queries)} variants")

            # Use expanded queries for better coverage
            if len(expanded_queries) > 1:
                query_text = expanded_queries[0]  # Use best expanded query
                logger.info(f"   ✅ Using optimized query: '{query_text[:50]}...'")

        # Step 1: Advanced Query Processing
        if process_query_with_advanced_techniques:
            query_processing = await process_query_with_advanced_techniques(query_text)
            all_queries = query_processing.get('all_queries', [query_text])
            hyde_doc = query_processing.get('hyde_document')

            logger.info(f"🔧 Advanced processing: {len(all_queries)} queries generated")
            if hyde_doc:
                logger.info(f"🎯 HyDE document: {len(hyde_doc)} characters")
        else:
            all_queries = [query_text]
            hyde_doc = None
            logger.warning("⚠️ Advanced query processing not available - using original query")

        # Step 2: Search with all query variants
        all_results = []
        query_weights = []

        for i, query in enumerate(all_queries):
            # Weight: original query gets highest weight, variants get lower weights
            weight = 1.0 if i == 0 else 0.7
            query_weights.append(weight)

            # Search with current query variant
            if use_bulgarian_embeddings and get_bulgarian_embedding_engine:
                engine = get_bulgarian_embedding_engine()
                is_bulgarian = engine.is_bulgarian_text(query)

                if is_bulgarian:
                    results = await search_crawled_pages_bulgarian_optimized(
                        query,
                        match_count=rerank_top_k,
                        use_bulgarian_embeddings=True,
                        rerank_top_k=rerank_top_k
                    )
                else:
                    results = await search_documents_with_text_hybrid_reranked(
                        client,
                        query,
                        match_count=rerank_top_k,
                        rerank_top_k=rerank_top_k
                    )
            else:
                results = await search_documents_with_text_hybrid_reranked(
                    client,
                    query,
                    match_count=rerank_top_k,
                    rerank_top_k=rerank_top_k
                )

            # Add weight to results
            for result in results:
                result['query_weight'] = weight
                result['source_query'] = query

            all_results.extend(results)

        # Step 3: HyDE search DEACTIVATED for performance optimization
        # if hyde_doc and enable_hyde:
        #     logger.info("🎯 Searching with HyDE document")
        #     hyde_results = await search_documents_with_text_hybrid_reranked(...)
        #     all_results.extend(hyde_results)
        logger.info("🎯 HyDE search DEACTIVATED - skipping for performance optimization")

        # Step 4: Combine and deduplicate results
        seen_urls = set()
        combined_results = []

        for result in all_results:
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                combined_results.append(result)

        # Step 5: Apply keyword validation (Phase 8.6.1)
        logger.info("🔍 Applying keyword validation...")
        validated_results = validate_results_with_keywords(
            query_text,
            combined_results,
            min_keyword_threshold=0.15  # 15% keyword overlap minimum
        )

        # If too few results pass validation, lower threshold
        if len(validated_results) < max(3, match_count // 2):
            logger.warning(f"⚠️ Only {len(validated_results)} results passed keyword validation, lowering threshold")
            validated_results = validate_results_with_keywords(
                query_text,
                combined_results,
                min_keyword_threshold=0.1  # 10% keyword overlap minimum
            )

        # Step 6: Enhanced scoring with keyword validation
        for result in validated_results:
            original_score = result.get('hybrid_score', 0) or result.get('similarity', 0) or 0.5
            query_weight = result.get('query_weight', 1.0)

            # Calculate enhanced score with keyword validation
            enhanced_score = calculate_enhanced_score(
                query_text,
                result,
                cross_encoder_score=original_score,
                bm25_score=0.0  # BM25 not available in this context
            )

            result['final_score'] = enhanced_score * query_weight

        # Step 7: Apply content chunking revolution (Phase 8.6.2)
        logger.info("🧩 Applying Phase 8.6.2 Content Chunking Revolution...")
        chunked_results = apply_chunking_to_search_results(validated_results, query_text)

        # Step 8: Apply enhanced scoring algorithm (Phase 8.6.3)
        logger.info("🎯 Applying Phase 8.6.3 Enhanced Scoring Algorithm...")
        enhanced_results = calculate_enhanced_scoring(
            chunked_results,
            query_text,
            weight_rerank=0.6,  # Cross-encoder reranking weight
            weight_bm25=0.1,    # BM25 weight (limited in current context)
            weight_keyword=0.3  # Keyword validation weight
        )

        # Step 9: Sort by enhanced score and limit results
        enhanced_results.sort(key=lambda x: x.get('enhanced_score', 0), reverse=True)
        final_results = enhanced_results[:match_count]

        search_time = time.time() - start_time

        logger.info(f"✅ Phase 8.6.3 Advanced query search with enhanced scoring completed: {len(final_results)} results in {search_time:.3f}s")
        logger.info(f"   📊 Total candidates: {len(combined_results)}")
        logger.info(f"   🔍 Keyword validated: {len(validated_results)}")
        logger.info(f"   🧩 Chunking applied: {len(chunked_results)}")
        logger.info(f"   🎯 Enhanced scoring applied: {len(enhanced_results)}")
        logger.info(f"   🔧 Query variants: {len(all_queries)}")
        logger.info(f"   🎯 HyDE enabled: {'✅' if hyde_doc else '❌'}")

        # Log enhanced scoring details for top results
        for i, result in enumerate(final_results[:3]):
            keyword_score = result.get('keyword_score', 0.0)
            chunk_score = result.get('chunk_score', 0.0)
            enhanced_score = result.get('enhanced_score', 0.0)
            doc_boost = result.get('doc_type_boost', 1.0)
            chunk_tokens = result.get('chunk_tokens', 0)
            chunks_available = result.get('chunks_available', 0)
            logger.info(f"   🏆 Result {i+1}: keyword={keyword_score:.2f}, chunk={chunk_score:.2f}, enhanced={enhanced_score:.2f}")
            logger.info(f"      📝 Chunk: {chunk_tokens} tokens, {chunks_available} chunks available")
            logger.info(f"      🚀 Doc boost: {doc_boost:.2f}x")

        return final_results

    except Exception as e:
        logger.error(f"❌ Advanced query search failed: {e}")
        # Fallback to hybrid search with reranking
        return await search_documents_with_text_hybrid_reranked(
            client,
            query_text,
            match_count=match_count,
            rerank_top_k=rerank_top_k
        )

async def search_crawled_pages_bulgarian_optimized(
    query_text: str,
    match_count: int = 10,
    use_bulgarian_embeddings: bool = True,
    rerank_top_k: int = 20
) -> List[Dict[str, Any]]:
    """
    🇧🇬 Phase 8.3: Bulgarian-optimized Search with LaBSE embeddings

    Uses LaBSE embeddings for Bulgarian queries and cross-encoder reranking.

    Args:
        query_text: The search query
        match_count: Number of final results to return
        use_bulgarian_embeddings: Use LaBSE for Bulgarian queries
        rerank_top_k: Number of results to rerank

    Returns:
        List of search results optimized for Bulgarian
    """
    start_time = time.time()

    logger.info(f"🇧🇬 Phase 8.3 Bulgarian-optimized Search: '{query_text[:50]}...'")

    # Get Supabase client
    client = get_supabase_client()

    try:
        # Check if Bulgarian embeddings are available
        if use_bulgarian_embeddings and get_bulgarian_embedding_engine:
            engine = get_bulgarian_embedding_engine()
            is_bulgarian = engine.is_bulgarian_text(query_text)

            if is_bulgarian and create_bulgarian_embeddings is not None:
                logger.info("🇧🇬 Bulgarian query detected - using LaBSE embeddings")

                # Create Bulgarian embeddings
                query_embeddings = await create_bulgarian_embeddings(query_text)

                # Extract single embedding from batch result
                if query_embeddings.ndim > 1 and query_embeddings.shape[0] == 1:
                    query_embeddings = query_embeddings[0]  # Extract first row

                query_embedding_list = query_embeddings.tolist()

                logger.info(f"🇧🇬 Bulgarian hybrid search with LaBSE embeddings: '{query_text[:50]}...'")

                # Call Bulgarian-specific RPC function
                response = client.rpc('match_crawled_pages_labse_hybrid', {
                    'p_query_embedding': query_embedding_list,
                    'p_query_text': query_text,
                    'p_match_count': rerank_top_k,
                    'p_weight_dense': 0.6,
                    'p_weight_sparse': 0.4,
                    'p_min_similarity_threshold': 0.1
                }).execute()

                if response.data:
                    initial_results = response.data
                    logger.info(f"🇧🇬 Bulgarian hybrid search SUCCESS: Found {len(initial_results)} results")
                else:
                    logger.warning("🇧🇬 Bulgarian hybrid search returned no results")
                    initial_results = []
            else:
                logger.info("🌍 Non-Bulgarian query - using standard hybrid search")
                initial_results = await search_documents_with_text_hybrid_reranked(
                    client,
                    query_text,
                    match_count=rerank_top_k,
                    rerank_top_k=rerank_top_k
                )
        else:
            logger.warning("⚠️ Bulgarian embeddings not available - using standard search")
            initial_results = await search_documents_with_text_hybrid_reranked(
                client,
                query_text,
                match_count=rerank_top_k,
                rerank_top_k=rerank_top_k
            )

        # Apply cross-encoder reranking if available
        if rerank_search_results and initial_results:
            logger.info("🧠 Applying cross-encoder reranking...")
            final_results = await rerank_search_results(
                query_text,
                initial_results,
                top_k=match_count
            )
        else:
            logger.info("⚠️ Cross-encoder reranking disabled or unavailable")
            final_results = initial_results[:match_count]

        search_time = time.time() - start_time
        logger.info(f"✅ Bulgarian-optimized search completed: {len(final_results)} final results")

        return final_results

    except Exception as e:
        logger.error(f"❌ Bulgarian-optimized search failed: {e}")
        # Fallback to standard hybrid search
        return await search_documents_with_text_hybrid_reranked(
            client,
            query_text,
            match_count=match_count,
            rerank_top_k=rerank_top_k
        )

# =============================================================================
# PHASE 8.5: PERFORMANCE OPTIMIZATION FUNCTIONS
# =============================================================================

# Global cache for query expansion results
_query_expansion_cache = {}
_hyde_document_cache = {}
_cache_ttl = timedelta(hours=1)  # Cache TTL: 1 hour

def _get_cache_key(text: str) -> str:
    """Generate cache key from text"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def _is_cache_valid(timestamp: datetime) -> bool:
    """Check if cache entry is still valid"""
    return datetime.now() - timestamp < _cache_ttl

async def search_crawled_pages_cascaded(
    query_text: str,
    match_count: int = 10,
    stage1_count: int = 5,
    confidence_threshold: float = 0.7
) -> List[Dict[str, Any]]:
    """
    Cascaded multi-stage execution for optimized performance
    Stage 1: Fast initial search with fewer results
    Stage 2: Advanced processing if stage 1 has good results
    """
    logger.info(f"🚀 Starting cascaded search for: '{query_text}'")
    client = get_supabase_client()

    try:
        # Stage 1: Fast initial search
        stage1_start = time.time()
        stage1_results = await search_documents_with_text_hybrid_reranked(
            client, query_text, match_count=stage1_count, rerank_top_k=stage1_count * 2
        )
        stage1_time = time.time() - stage1_start

        logger.info(f"⚡ Stage 1: {len(stage1_results)} results in {stage1_time:.3f}s")

        # Check if stage 1 results are good enough
        if not stage1_results or len(stage1_results) < 3:
            logger.info("⚠️ Stage 1 results insufficient, skipping stage 2")
            return stage1_results

        # Calculate average score for confidence assessment
        avg_score = sum(result.get('score', 0) for result in stage1_results) / len(stage1_results)

        if avg_score < confidence_threshold:
            logger.info(f"⚠️ Stage 1 confidence too low ({avg_score:.3f} < {confidence_threshold}), proceeding to stage 2")

            # Stage 2: Advanced processing
            stage2_start = time.time()

            # Import advanced query processing
            try:
                from advanced_query_processing import AdvancedQueryProcessor
                processor = AdvancedQueryProcessor()

                stage2_results = await search_crawled_pages_advanced_query(
                    query_text,
                    match_count=match_count,
                    use_bulgarian_embeddings=True,
                    enable_query_expansion=True,
                    enable_hyde=True,
                    enable_multi_query=True
                )
                stage2_time = time.time() - stage2_start

                logger.info(f"🧠 Stage 2: {len(stage2_results)} results in {stage2_time:.3f}s")

                # Combine results intelligently
                final_results = _combine_cascaded_results(stage1_results, stage2_results, match_count)

            except ImportError:
                logger.warning("⚠️ Advanced query processing not available, using stage 1 results")
                final_results = stage1_results
        else:
            logger.info(f"✅ Stage 1 confidence sufficient ({avg_score:.3f} >= {confidence_threshold})")
            final_results = stage1_results

        return final_results[:match_count]

    except Exception as e:
        logger.error(f"❌ Cascaded search failed: {e}")
        # Fallback to standard search
        return await search_documents_with_text_hybrid_reranked(
            client, query_text, match_count=match_count
        )

def _combine_cascaded_results(stage1: List[Dict], stage2: List[Dict], max_results: int) -> List[Dict]:
    """Intelligently combine results from cascaded stages"""
    if not stage2:
        return stage1

    # Use stage2 results as primary, supplement with unique stage1 results
    seen_urls = {result.get('url', '') for result in stage2}
    combined = list(stage2)

    for result in stage1:
        if result.get('url', '') not in seen_urls:
            combined.append(result)
            if len(combined) >= max_results:
                break

    return combined

@lru_cache(maxsize=100)
def _cached_query_expansion(query: str) -> str:
    """Cached query expansion to avoid repeated processing"""
    cache_key = _get_cache_key(query)

    if cache_key in _query_expansion_cache:
        cached_entry = _query_expansion_cache[cache_key]
        if _is_cache_valid(cached_entry['timestamp']):
            logger.info(f"🔥 Using cached query expansion for: '{query[:50]}...'")
            return cached_entry['expanded_query']
        else:
            # Remove expired entry
            del _query_expansion_cache[cache_key]

    # This would be implemented with actual query expansion logic
    # For now, return the original query
    expanded_query = query

    # Cache the result
    _query_expansion_cache[cache_key] = {
        'expanded_query': expanded_query,
        'timestamp': datetime.now()
    }

    logger.info(f"❄️ Cached new query expansion for: '{query[:50]}...'")
    return expanded_query

@lru_cache(maxsize=50)
def _cached_hyde_document(query: str) -> str:
    """
    DEACTIVATED: HyDE document generation disabled for performance optimization.
    Returns empty string to eliminate API calls and improve response time.
    """
    logger.info("_cached_hyde_document: DEACTIVATED - returning empty string for performance")
    return ""

def clear_performance_caches():
    """Clear all performance optimization caches"""
    global _query_expansion_cache, _hyde_document_cache
    _query_expansion_cache.clear()
    _hyde_document_cache.clear()
    logger.info("🧹 Performance caches cleared")

def get_cache_stats() -> Dict[str, Any]:
    """Get cache statistics"""
    return {
        'query_expansion_cache_size': len(_query_expansion_cache),
        'hyde_document_cache_size': len(_hyde_document_cache),
        'cache_ttl_hours': _cache_ttl.total_seconds() / 3600
    }

# ============================================================================
# PHASE 8.6: LLM-GUIDED PRECISION OPTIMIZATION
# ============================================================================



def calculate_keyword_overlap(query_keywords: List[str], document_text: str) -> float:
    """
    Calculate keyword overlap score between query keywords and document.
    Returns score between 0.0 and 1.0.
    """
    if not query_keywords or not document_text:
        return 0.0

    # Extract keywords from document
    doc_keywords = extract_bulgarian_keywords(document_text)
    doc_keywords_set = set(doc_keywords)

    # Count matches
    matches = 0
    for keyword in query_keywords:
        if keyword in doc_keywords_set:
            matches += 1

    # Calculate overlap ratio
    overlap_score = matches / len(query_keywords) if query_keywords else 0.0
    return min(overlap_score, 1.0)

def validate_results_with_keywords(query: str, results: List[Dict[str, Any]],
                                 min_keyword_threshold: float = 0.2) -> List[Dict[str, Any]]:
    """
    Validate search results using keyword overlap.
    Filters out results with insufficient keyword matches.

    Args:
        query: Original search query
        results: List of search results with 'content' field
        min_keyword_threshold: Minimum keyword overlap required (0.0-1.0)

    Returns:
        Filtered and enhanced results with keyword_score
    """
    if not results:
        return []

    # Extract keywords from query
    query_keywords = extract_bulgarian_keywords(query)
    if not query_keywords:
        logger.warning("🔍 No keywords extracted from query, returning original results")
        return results

    logger.info(f"🔍 Extracted {len(query_keywords)} keywords from query: {query_keywords}")

    validated_results = []
    for result in results:
        content = result.get('content', '')
        if not content:
            continue

        # Calculate keyword overlap
        keyword_score = calculate_keyword_overlap(query_keywords, content)

        # Add keyword score to result
        result['keyword_score'] = keyword_score

        # Filter by threshold
        if keyword_score >= min_keyword_threshold:
            validated_results.append(result)
            logger.debug(f"✅ Result passed keyword validation: {keyword_score:.2f} >= {min_keyword_threshold}")
        else:
            logger.debug(f"❌ Result failed keyword validation: {keyword_score:.2f} < {min_keyword_threshold}")

    logger.info(f"🔍 Keyword validation: {len(validated_results)}/{len(results)} results passed threshold {min_keyword_threshold}")

    return validated_results

def calculate_enhanced_score(query: str, result: Dict[str, Any],
                           cross_encoder_score: float, bm25_score: float = 0.0) -> float:
    """
    Calculate enhanced scoring with keyword validation.
    Implements LLM-recommended hybrid scoring approach.

    Args:
        query: Original search query
        result: Search result with content
        cross_encoder_score: Score from cross-encoder reranking
        bm25_score: BM25 score (if available)

    Returns:
        Enhanced final score
    """
    # Get keyword score
    keyword_score = result.get('keyword_score', 0.0)
    if keyword_score == 0.0:
        # Calculate if not already present
        keyword_score = calculate_keyword_overlap(
            extract_bulgarian_keywords(query),
            result.get('content', '')
        )

    # LLM-recommended weights
    w_cross_encoder = 0.4  # Cross-encoder reranking
    w_bm25 = 0.3          # BM25 traditional search
    w_keyword = 0.3       # Keyword overlap

    # Calculate final score
    final_score = (
        w_cross_encoder * cross_encoder_score +
        w_bm25 * bm25_score +
        w_keyword * keyword_score
    )

    return min(final_score, 1.0)

def expand_query_with_eu_funds_terms(query: str) -> List[str]:
    """
    Expand query with EU funds specific terminology.
    Based on LLM recommendations for Bulgarian context.
    """
    query_lower = query.lower()
    expanded_terms = [query]  # Always include original

    # EU Funds terminology mapping
    expansions = {
        'мсп': ['малки и средни предприятия', 'микропредприятия', 'стартъп', 'предприемачество'],
        'малки и средни предприятия': ['мсп', 'микропредприятия', 'стартъп', 'предприемачество'],
        'иновации': ['технологично развитие', 'дигитализация', 'модернизация', 'r&d'],
        'конкурентоспособност': ['растеж', 'развитие', 'подобряване', 'ефективност'],
        'околна среда': ['зелена енергия', 'възобновяеми източници', 'екология', 'устойчивост'],
        'образование': ['обучение', 'квалификация', 'умения', 'компетентности'],
        'туризм': ['културен туризм', 'екотуризм', 'хотелиерство', 'ресторантьорство'],
        'селско стопанство': ['земеделие', 'животновъдство', 'биопроизводство', 'органично'],
        'инфраструктура': ['транспорт', 'пътища', 'железопътен', 'водоснабдяване'],
        'здравеопазване': ['медицина', 'болници', 'здравни услуги', 'профилактика']
    }

    # Add relevant expansions
    for term, synonyms in expansions.items():
        if term in query_lower:
            expanded_terms.extend(synonyms)

    return list(set(expanded_terms))  # Remove duplicates

# ============================================================================
# PHASE 8.6.2: CONTENT CHUNKING REVOLUTION
# LLM-guided optimization for better precision through improved chunking
# ============================================================================

def create_semantic_chunks(text: str, max_tokens: int = 256, min_tokens: int = 64, overlap_ratio: float = 0.1) -> List[Dict[str, Any]]:
    """
    Create semantic chunks with Bulgarian-specific sentence boundaries and metadata enrichment.

    Based on LLM recommendations:
    - 256-512 tokens per chunk
    - Overlap strategy for context preservation
    - Bulgarian sentence boundary detection
    - Metadata enrichment for better retrieval
    """
    if not text or not isinstance(text, str):
        return []

    # Bulgarian sentence endings
    bulgarian_sentence_endings = ['.', '!', '?', ':', ';']

    # Split into sentences respecting Bulgarian punctuation
    sentences = []
    current_sentence = ""

    for char in text:
        current_sentence += char
        if char in bulgarian_sentence_endings:
            if current_sentence.strip():
                sentences.append(current_sentence.strip())
            current_sentence = ""

    # Add remaining text as sentence
    if current_sentence.strip():
        sentences.append(current_sentence.strip())

    if not sentences:
        return []

    chunks = []
    current_chunk = ""
    current_tokens = 0
    overlap_tokens = int(max_tokens * overlap_ratio)

    for i, sentence in enumerate(sentences):
        sentence_tokens = len(sentence.split())

        # If adding this sentence would exceed max_tokens, finalize current chunk
        if current_tokens + sentence_tokens > max_tokens and current_tokens >= min_tokens:
            if current_chunk.strip():
                # Create chunk with enhanced metadata
                chunk_metadata = extract_chunk_metadata(
                    chunk_text=current_chunk,
                    sentence_index=i,
                    document_title=None,  # Will be provided by caller if available
                    full_document=text
                )
                chunks.append({
                    'content': current_chunk.strip(),
                    'tokens': current_tokens,
                    'sentences': current_chunk.count('.') + current_chunk.count('!') + current_chunk.count('?'),
                    'metadata': chunk_metadata,
                    'chunk_id': len(chunks),
                    'overlap_start': max(0, len(chunks) - 1) if chunks else 0
                })

            # Start new chunk with overlap
            if chunks and overlap_tokens > 0:
                # Get last sentences for overlap
                overlap_text = get_overlap_text(current_chunk, overlap_tokens)
                current_chunk = overlap_text + " " + sentence
                current_tokens = len(current_chunk.split())
            else:
                current_chunk = sentence
                current_tokens = sentence_tokens
        else:
            # Add sentence to current chunk
            if current_chunk:
                current_chunk += " " + sentence
            else:
                current_chunk = sentence
            current_tokens += sentence_tokens

    # Add final chunk with enhanced metadata
    if current_chunk.strip() and current_tokens >= min_tokens:
        chunk_metadata = extract_chunk_metadata(
            chunk_text=current_chunk,
            sentence_index=len(sentences),
            document_title=None,  # Will be provided by caller if available
            full_document=text
        )
        chunks.append({
            'content': current_chunk.strip(),
            'tokens': current_tokens,
            'sentences': current_chunk.count('.') + current_chunk.count('!') + current_chunk.count('?'),
            'metadata': chunk_metadata,
            'chunk_id': len(chunks),
            'overlap_start': max(0, len(chunks) - 1) if chunks else 0
        })

    return chunks

def extract_chunk_metadata(chunk_text: str, sentence_index: int, document_title: str = None, full_document: str = None) -> Dict[str, Any]:
    """
    PHASE 2 ENHANCEMENT: Extract enhanced metadata from chunk for better retrieval

    Args:
        chunk_text: The text content of the chunk
        sentence_index: Index of the sentence in the document
        document_title: Title of the source document (optional)
        full_document: Full document text for context analysis (optional)
    """
    metadata = {
        'length': len(chunk_text),
        'sentence_index': sentence_index,
        'has_numbers': any(char.isdigit() for char in chunk_text),
        'has_dates': bool(re.search(r'\d{4}|\d{1,2}\.\d{1,2}\.\d{4}', chunk_text)),
        'has_percentages': '%' in chunk_text,
        'has_currency': any(currency in chunk_text.lower() for currency in ['лв', 'евро', 'euro', 'bgn', 'eur']),
        'program_indicators': [],
        'topic_indicators': [],
        'document_title': document_title or 'unknown',
        'section_type': _detect_section_type(chunk_text),
        'content_density': _calculate_content_density(chunk_text)
    }

    # EU funds program indicators
    program_patterns = {
        'мсп': ['мсп', 'малки и средни предприятия', 'микропредприятия'],
        'иновации': ['иновации', 'иновационни', 'технологии', 'изследвания'],
        'околна_среда': ['околна среда', 'екология', 'зелена енергия', 'устойчиво'],
        'образование': ['образование', 'обучение', 'квалификация', 'умения'],
        'инфраструктура': ['инфраструктура', 'транспорт', 'пътища', 'железопътен'],
        'регионално': ['регионално', 'местно', 'общини', 'територии'],
        'дигитализация': ['дигитализация', 'цифрови', 'информационни', 'ИКТ']
    }

    chunk_lower = chunk_text.lower()
    for program, keywords in program_patterns.items():
        if any(keyword in chunk_lower for keyword in keywords):
            metadata['program_indicators'].append(program)

    # Topic indicators
    if any(word in chunk_lower for word in ['финансиране', 'средства', 'фонд', 'програма']):
        metadata['topic_indicators'].append('финансиране')

    if any(word in chunk_lower for word in ['критерии', 'условия', 'изисквания']):
        metadata['topic_indicators'].append('критерии')

    if any(word in chunk_lower for word in ['срок', 'дата', 'период', 'време']):
        metadata['topic_indicators'].append('срокове')

    return metadata

def _detect_section_type(chunk_text: str) -> str:
    """Detect the type of section based on content patterns"""
    chunk_lower = chunk_text.lower()

    # Header patterns
    if any(pattern in chunk_lower for pattern in ['програма', 'мярка', 'схема']):
        return 'program_description'
    elif any(pattern in chunk_lower for pattern in ['критерии', 'условия', 'изисквания']):
        return 'criteria'
    elif any(pattern in chunk_lower for pattern in ['срок', 'дата', 'период']):
        return 'timeline'
    elif any(pattern in chunk_lower for pattern in ['бюджет', 'финансиране', 'средства']):
        return 'budget'
    elif any(pattern in chunk_lower for pattern in ['документи', 'заявление', 'кандидатстване']):
        return 'application'
    else:
        return 'general'

def _calculate_content_density(chunk_text: str) -> float:
    """Calculate content density score (0-1) based on information richness"""
    if not chunk_text:
        return 0.0

    # Count information indicators
    indicators = 0

    # Numbers and dates
    if re.search(r'\d+', chunk_text):
        indicators += 1
    if re.search(r'\d{4}', chunk_text):  # Years
        indicators += 1

    # Currency and percentages
    if any(curr in chunk_text.lower() for curr in ['лв', 'евро', 'euro', '%']):
        indicators += 1

    # Key terms
    key_terms = ['програма', 'мярка', 'критерии', 'срок', 'бюджет', 'финансиране']
    if any(term in chunk_text.lower() for term in key_terms):
        indicators += 1

    # Normalize to 0-1 scale
    return min(indicators / 4.0, 1.0)

def get_overlap_text(text: str, overlap_tokens: int) -> str:
    """Get last N tokens from text for overlap"""
    words = text.split()
    if len(words) <= overlap_tokens:
        return text

    return " ".join(words[-overlap_tokens:])

def enhance_chunking_for_query(chunks: List[Dict], query: str) -> List[Dict]:
    """Enhance chunk scoring based on query-specific factors"""
    if not chunks or not query:
        return chunks

    query_lower = query.lower()
    query_keywords = extract_bulgarian_keywords(query)

    enhanced_chunks = []
    for chunk in chunks:
        enhanced_chunk = chunk.copy()
        content = chunk.get('content', '')
        metadata = chunk.get('metadata', {})

        # Calculate query-specific enhancements
        keyword_density = calculate_keyword_density(content, query_keywords)
        metadata_relevance = calculate_metadata_relevance(metadata, query_lower)
        structural_bonus = calculate_structural_bonus(chunk, query_keywords)

        # Add enhancement scores
        enhanced_chunk['keyword_density'] = keyword_density
        enhanced_chunk['metadata_relevance'] = metadata_relevance
        enhanced_chunk['structural_bonus'] = structural_bonus
        enhanced_chunk['enhancement_score'] = (keyword_density * 0.5 +
                                             metadata_relevance * 0.3 +
                                             structural_bonus * 0.2)

        enhanced_chunks.append(enhanced_chunk)

    # Sort by enhancement score
    enhanced_chunks.sort(key=lambda x: x.get('enhancement_score', 0), reverse=True)

    return enhanced_chunks

def calculate_keyword_density(content: str, keywords: List[str]) -> float:
    """Calculate keyword density in content"""
    if not content or not keywords:
        return 0.0

    content_lower = content.lower()
    total_words = len(content.split())
    keyword_count = 0

    for keyword in keywords:
        keyword_count += content_lower.count(keyword.lower())

    return min(keyword_count / total_words, 1.0) if total_words > 0 else 0.0

def calculate_metadata_relevance(metadata: Dict[str, Any], query: str) -> float:
    """Calculate metadata relevance to query"""
    if not metadata or not query:
        return 0.0

    relevance_score = 0.0

    # Program indicators relevance
    program_indicators = metadata.get('program_indicators', [])
    for indicator in program_indicators:
        if indicator in query:
            relevance_score += 0.3

    # Topic indicators relevance
    topic_indicators = metadata.get('topic_indicators', [])
    for indicator in topic_indicators:
        if indicator in query:
            relevance_score += 0.2

    # Structural relevance
    if metadata.get('has_numbers') and any(char.isdigit() for char in query):
        relevance_score += 0.1

    if metadata.get('has_dates') and any(word in query for word in ['година', 'дата', 'период']):
        relevance_score += 0.1

    if metadata.get('has_currency') and any(word in query for word in ['финансиране', 'средства', 'лв', 'евро']):
        relevance_score += 0.1

    return min(relevance_score, 1.0)

def calculate_structural_bonus(chunk: Dict, keywords: List[str]) -> float:
    """Calculate structural bonus based on chunk properties"""
    if not chunk or not keywords:
        return 0.0

    bonus = 0.0

    # Token count bonus (prefer medium-sized chunks)
    tokens = chunk.get('tokens', 0)
    if 300 <= tokens <= 450:  # Optimal range
        bonus += 0.3
    elif 256 <= tokens <= 512:  # Acceptable range
        bonus += 0.2

    # Sentence count bonus
    sentences = chunk.get('sentences', 0)
    if 3 <= sentences <= 8:  # Good sentence structure
        bonus += 0.2

    # Metadata richness bonus
    metadata = chunk.get('metadata', {})
    if metadata.get('program_indicators'):
        bonus += 0.1
    if metadata.get('topic_indicators'):
        bonus += 0.1

    return min(bonus, 1.0)

def apply_chunking_to_search_results(results: List[Dict], query: str) -> List[Dict]:
    """Apply enhanced chunking to search results"""
    if not results or not query:
        return results

    enhanced_results = []

    for result in results:
        content = result.get('content', '')
        if not content:
            enhanced_results.append(result)
            continue

        # Create semantic chunks for this result
        chunks = create_semantic_chunks(content)

        if not chunks:
            enhanced_results.append(result)
            continue

        # Enhance chunks for query
        enhanced_chunks = enhance_chunking_for_query(chunks, query)

        # Take best chunk or combine top chunks
        if enhanced_chunks:
            best_chunk = enhanced_chunks[0]

            # Create enhanced result
            enhanced_result = result.copy()
            enhanced_result['content'] = best_chunk['content']
            enhanced_result['chunk_metadata'] = best_chunk.get('metadata', {})
            enhanced_result['chunk_score'] = best_chunk.get('enhancement_score', 0)
            enhanced_result['chunk_tokens'] = best_chunk.get('tokens', 0)
            enhanced_result['original_content_length'] = len(content)
            enhanced_result['chunks_available'] = len(enhanced_chunks)

            enhanced_results.append(enhanced_result)
        else:
            enhanced_results.append(result)

    return enhanced_results

# ============================================================================
# PHASE 8.6.4: BULGARIAN LANGUAGE OPTIMIZATION
# ============================================================================

def extract_bulgarian_keywords(text: str) -> List[str]:
    """Extract Bulgarian keywords with morphological analysis"""
    if not text:
        return []

    # Basic Bulgarian keyword extraction
    # Remove common Bulgarian stop words
    bulgarian_stop_words = {
        'и', 'в', 'на', 'за', 'с', 'от', 'до', 'по', 'при', 'към', 'без', 'над', 'под',
        'че', 'да', 'се', 'не', 'ще', 'би', 'са', 'си', 'е', 'съм', 'беше', 'бъде',
        'този', 'тази', 'това', 'тези', 'един', 'една', 'едно', 'някой', 'някоя', 'някое',
        'всеки', 'всяка', 'всяко', 'който', 'която', 'което', 'където', 'когато', 'как'
    }

    # Clean and tokenize text
    import re
    text = re.sub(r'[^\w\s]', ' ', text.lower())
    words = text.split()

    # Filter keywords
    keywords = []
    for word in words:
        if (len(word) >= 3 and
            word not in bulgarian_stop_words and
            not word.isdigit()):
            keywords.append(word)

    # Remove duplicates while preserving order
    seen = set()
    unique_keywords = []
    for keyword in keywords:
        if keyword not in seen:
            seen.add(keyword)
            unique_keywords.append(keyword)

    return unique_keywords[:10]  # Return top 10 keywords

def expand_query_with_synonyms(query: str) -> List[str]:
    """Expand Bulgarian query with synonyms and morphological variants"""
    if not query:
        return [query]

    # Bulgarian synonym dictionary for EU funding domain
    bulgarian_synonyms = {
        'мсп': ['малки и средни предприятия', 'микро предприятия', 'дребни фирми', 'малък бизнес'],
        'малки': ['дребни', 'микро', 'мини'],
        'предприятия': ['фирми', 'компании', 'бизнес', 'организации'],
        'финансиране': ['финансова подкрепа', 'безвъзмездна помощ', 'грантове', 'субсидии', 'средства'],
        'програми': ['схеми', 'инициативи', 'мерки', 'проекти'],
        'екологични': ['зелени', 'околна среда', 'природозащитни', 'устойчиви'],
        'енергия': ['електроенергия', 'ток', 'захранване', 'мощност'],
        'възобновяеми': ['алтернативни', 'чисти', 'зелени'],
        'източници': ['ресурси', 'генератори', 'производители'],
        'образование': ['обучение', 'учене', 'преподаване', 'образователен'],
        'обучение': ['курсове', 'тренинг', 'подготовка', 'квалификация'],
        'квалификация': ['сертификация', 'умения', 'компетентности', 'професионално развитие'],
        'инфраструктура': ['съоръжения', 'мрежи', 'системи', 'обекти'],
        'пътища': ['магистрали', 'шосета', 'улици', 'артерии'],
        'транспорт': ['превоз', 'движение', 'мобилност', 'логистика'],
        'проекти': ['инициативи', 'предложения', 'дейности', 'мероприятия'],
        'развитие': ['растеж', 'напредък', 'подобрение', 'усъвършенстване'],
        'подкрепа': ['помощ', 'съдействие', 'асистенция', 'подпомагане'],
        'иновации': ['нововъведения', 'технологии', 'модернизация', 'осъвременяване'],
        'конкурентоспособност': ['конкурентност', 'състезателност', 'ефективност']
    }

    # Extract keywords from original query
    keywords = extract_bulgarian_keywords(query)
    expanded_queries = [query]  # Start with original query

    # Generate expanded queries with synonyms
    for keyword in keywords:
        if keyword in bulgarian_synonyms:
            synonyms = bulgarian_synonyms[keyword]
            for synonym in synonyms[:2]:  # Use top 2 synonyms per keyword
                # Replace keyword with synonym in query
                expanded_query = query.replace(keyword, synonym)
                if expanded_query != query and expanded_query not in expanded_queries:
                    expanded_queries.append(expanded_query)

    # Generate combined synonym queries
    if len(keywords) >= 2:
        for i, keyword1 in enumerate(keywords[:2]):
            for keyword2 in keywords[i+1:3]:
                if keyword1 in bulgarian_synonyms and keyword2 in bulgarian_synonyms:
                    syn1 = bulgarian_synonyms[keyword1][0]
                    syn2 = bulgarian_synonyms[keyword2][0]
                    combined_query = query.replace(keyword1, syn1).replace(keyword2, syn2)
                    if combined_query not in expanded_queries:
                        expanded_queries.append(combined_query)

    return expanded_queries[:5]  # Return top 5 expanded queries

# ============================================================================
# PHASE 8.6.3: ENHANCED SCORING ALGORITHM
# ============================================================================

def calculate_document_type_boost(result: Dict[str, Any]) -> float:
    """Calculate document type boost based on content characteristics"""
    content = result.get('content', '').lower()
    url = result.get('url', '').lower()
    title = result.get('title', '').lower()

    boost_score = 1.0

    # EU funds program indicators (highest boost)
    eu_program_indicators = [
        'оперативна програма', 'operational programme', 'op ', 'програма за развитие',
        'европейски фондове', 'european funds', 'есиф', 'esif', 'кохезионен фонд',
        'cohesion fund', 'ефрр', 'erdf', 'есф', 'esf', 'езфрср', 'eafrd'
    ]

    # Official document indicators (high boost)
    official_indicators = [
        'министерство', 'ministry', 'агенция', 'agency', 'управляващ орган',
        'managing authority', 'официален сайт', 'official site', '.bg/', 'gov.bg'
    ]

    # Funding opportunity indicators (medium boost)
    funding_indicators = [
        'кандидатстване', 'application', 'финансиране', 'funding', 'грант', 'grant',
        'субсидия', 'subsidy', 'безвъзмездна помощ', 'non-refundable aid',
        'покана за предложения', 'call for proposals', 'схема за подпомагане'
    ]

    # Check for EU program indicators
    for indicator in eu_program_indicators:
        if indicator in content or indicator in url or indicator in title:
            boost_score *= 1.5
            break

    # Check for official document indicators
    for indicator in official_indicators:
        if indicator in content or indicator in url or indicator in title:
            boost_score *= 1.3
            break

    # Check for funding opportunity indicators
    for indicator in funding_indicators:
        if indicator in content or indicator in url or indicator in title:
            boost_score *= 1.2
            break

    # URL-based boosts
    if any(domain in url for domain in ['eufunds.bg', 'esif.bg', 'government.bg', 'strategy.bg']):
        boost_score *= 1.4
    elif '.bg' in url and any(keyword in url for keyword in ['eu', 'fund', 'program', 'grant']):
        boost_score *= 1.2

    return min(boost_score, 2.0)  # Cap at 2.0x boost


def calculate_enhanced_scoring(
    results: List[Dict[str, Any]],
    query: str,
    weight_rerank: float = 0.6,
    weight_bm25: float = 0.1,
    weight_keyword: float = 0.3
) -> List[Dict[str, Any]]:
    """
    Phase 8.6.3: Enhanced scoring algorithm with multi-component weights

    Args:
        results: Search results to score
        query: Original search query
        weight_rerank: Weight for reranking score (default: 0.6)
        weight_bm25: Weight for BM25 score (default: 0.1)
        weight_keyword: Weight for keyword score (default: 0.3)
    """
    if not results:
        return results

    logger.info(f"🎯 Phase 8.6.3 Enhanced Scoring: {len(results)} results")
    logger.info(f"   Weights: rerank={weight_rerank}, bm25={weight_bm25}, keyword={weight_keyword}")

    enhanced_results = []

    for result in results:
        # Get existing scores
        rerank_score = result.get('rerank_score', result.get('similarity_score', 0.0))
        bm25_score = result.get('bm25_score', 0.0)
        keyword_score = result.get('keyword_score', 0.0)
        chunk_score = result.get('chunk_score', 0.0)

        # Calculate document type boost
        doc_type_boost = calculate_document_type_boost(result)

        # Multi-component scoring with weights
        base_score = (
            weight_rerank * rerank_score +
            weight_bm25 * bm25_score +
            weight_keyword * keyword_score
        )

        # Apply document type boost
        enhanced_score = base_score * doc_type_boost

        # Add chunk score bonus (up to 10% boost)
        if chunk_score > 0:
            enhanced_score += 0.1 * chunk_score

        # Store enhanced scoring details
        result['enhanced_score'] = enhanced_score
        result['base_score'] = base_score
        result['doc_type_boost'] = doc_type_boost
        result['scoring_components'] = {
            'rerank': weight_rerank * rerank_score,
            'bm25': weight_bm25 * bm25_score,
            'keyword': weight_keyword * keyword_score,
            'chunk_bonus': 0.1 * chunk_score if chunk_score > 0 else 0.0
        }

        enhanced_results.append(result)

    # Sort by enhanced score
    enhanced_results.sort(key=lambda x: x.get('enhanced_score', 0.0), reverse=True)

    # Log top results
    if enhanced_results:
        top_result = enhanced_results[0]
        logger.info(f"   🏆 Top result enhanced_score: {top_result.get('enhanced_score', 0.0):.3f}")
        logger.info(f"      Components: {top_result.get('scoring_components', {})}")
        logger.info(f"      Doc boost: {top_result.get('doc_type_boost', 1.0):.2f}x")

    return enhanced_results


def calculate_success_rate_at_k(results: List[Dict[str, Any]], expected_content: List[str], k: int = 3) -> float:
    """Calculate SuccessRate@K metric - percentage of expected content found in top K results"""
    if not results or not expected_content or k <= 0:
        return 0.0

    top_k_results = results[:k]
    found_content = 0

    for expected in expected_content:
        expected_lower = expected.lower()
        for result in top_k_results:
            content = result.get('content', '').lower()
            title = result.get('title', '').lower()
            if expected_lower in content or expected_lower in title:
                found_content += 1
                break

    return found_content / len(expected_content)


def calculate_mrr(results: List[Dict[str, Any]], expected_content: List[str]) -> float:
    """Calculate Mean Reciprocal Rank (MRR) metric"""
    if not results or not expected_content:
        return 0.0

    reciprocal_ranks = []

    for expected in expected_content:
        expected_lower = expected.lower()
        rank = None

        for i, result in enumerate(results, 1):
            content = result.get('content', '').lower()
            title = result.get('title', '').lower()
            if expected_lower in content or expected_lower in title:
                rank = i
                break

        if rank:
            reciprocal_ranks.append(1.0 / rank)
        else:
            reciprocal_ranks.append(0.0)

    return sum(reciprocal_ranks) / len(reciprocal_ranks) if reciprocal_ranks else 0.0


def evaluate_with_flexible_metrics(
    results: List[Dict[str, Any]],
    expected_content: List[str],
    query: str
) -> Dict[str, float]:
    """
    Phase 8.6.3: Evaluate results with flexible metrics

    Returns:
        Dictionary with multiple evaluation metrics
    """
    metrics = {}

    # Traditional precision@1
    if results and expected_content:
        top_result = results[0]
        content = top_result.get('content', '').lower()
        title = top_result.get('title', '').lower()

        precision_at_1 = 0.0
        for expected in expected_content:
            if expected.lower() in content or expected.lower() in title:
                precision_at_1 = 1.0
                break
        metrics['precision_at_1'] = precision_at_1
    else:
        metrics['precision_at_1'] = 0.0

    # SuccessRate@3 and SuccessRate@5
    metrics['success_rate_at_3'] = calculate_success_rate_at_k(results, expected_content, 3)
    metrics['success_rate_at_5'] = calculate_success_rate_at_k(results, expected_content, 5)

    # Mean Reciprocal Rank
    metrics['mrr'] = calculate_mrr(results, expected_content)

    # Enhanced score quality (average of top 3)
    if results:
        top_3_scores = [r.get('enhanced_score', 0.0) for r in results[:3]]
        metrics['avg_enhanced_score_top3'] = sum(top_3_scores) / len(top_3_scores)
    else:
        metrics['avg_enhanced_score_top3'] = 0.0

    return metrics


# =============================================================================
# PHASE 1 CRITICAL IMPROVEMENTS - MULTI-STEP PIPELINE INTEGRATION
# =============================================================================

def multi_step_rag_query(
    query: str,
    max_results: int = 5,
    similarity_threshold: float = 0.3,
    enable_groq_reranking: bool = True,
    enable_program_extraction: bool = True,
    enable_bulgarian_nlp: bool = True,
    enable_query_expansion: bool = True,
    enable_content_enhancement: bool = True,
    debug: bool = False
) -> Dict[str, Any]:
    """
    Phase 1 Critical Improvements: Multi-Step RAG Query

    Използва новия Multi-Step Retrieval Pipeline за постигане на 100% точност
    в извличането на информация за европейски програми.

    Стъпки:
    1. Query Analysis & Expansion (Bulgarian NLP)
    2. Initial Retrieval (Existing RAG)
    3. Program Name Extraction (Gazetteer-based)
    4. Content Enhancement
    5. Groq Reranking (with fallback)
    6. Final Validation & Scoring

    Args:
        query: Заявката за търсене
        max_results: Максимален брой резултати
        similarity_threshold: Праг за similarity score
        enable_groq_reranking: Включи Groq reranking
        enable_program_extraction: Включи извличане на програмни имена
        enable_bulgarian_nlp: Включи български NLP
        enable_query_expansion: Включи разширяване на заявката
        enable_content_enhancement: Включи обогатяване на съдържанието
        debug: Debug режим

    Returns:
        Dict с резултати и метрики
    """

    if not MULTI_STEP_PIPELINE_AVAILABLE:
        logger.warning("⚠️ Multi-Step Pipeline не е наличен - използвам стандартен RAG")
        # Fallback към стандартния RAG
        return asyncio.run(ultra_smart_rag_query(
            query=query,
            supabase_client=get_supabase_client(),
            similarity_threshold=similarity_threshold,
            final_top_k=max_results
        ))

    try:
        # Създай pipeline конфигурация
        config = PipelineConfig(
            use_groq_reranking=enable_groq_reranking,
            use_program_extraction=enable_program_extraction,
            use_bulgarian_nlp=enable_bulgarian_nlp,
            enable_query_expansion=enable_query_expansion,
            enable_content_enhancement=enable_content_enhancement,
            max_documents=max_results * 2,  # Вземи повече за по-добра селекция
            similarity_threshold=similarity_threshold
        )

        # Създай pipeline
        pipeline = create_multi_step_pipeline(config)

        if debug:
            logger.info(f"🚀 Multi-Step Pipeline започва за: '{query}'")
            logger.info(f"📊 Конфигурация: {pipeline.get_pipeline_info()}")

        # Обработи заявката
        result = pipeline.process_query(query)

        # Конвертирай резултата в стандартен формат
        formatted_result = {
            'query': query,
            'documents': result.documents[:max_results],  # Ограничи до max_results
            'total_found': len(result.documents),
            'processing_time': result.processing_time,
            'pipeline_stages': result.pipeline_stages,
            'success_metrics': result.success_metrics,
            'query_analysis': result.query_analysis,
            'program_matches': result.program_matches,
            'method': 'multi_step_pipeline'
        }

        if debug:
            logger.info(f"✅ Multi-Step Pipeline завършен:")
            logger.info(f"  📄 Документи: {len(result.documents)}")
            logger.info(f"  ⏱️ Време: {result.processing_time:.2f}s")
            logger.info(f"  📊 Метрики: {result.success_metrics}")

        return formatted_result

    except Exception as e:
        logger.error(f"❌ Multi-Step Pipeline грешка: {e}")
        if debug:
            logger.error(f"Traceback: {traceback.format_exc()}")

        # Fallback към стандартния RAG
        logger.info("🔄 Fallback към стандартен RAG...")
        return asyncio.run(ultra_smart_rag_query(
            query=query,
            supabase_client=get_supabase_client(),
            similarity_threshold=similarity_threshold,
            final_top_k=max_results
        ))


def enhanced_multi_step_rag_query(
    query: str,
    **kwargs
) -> Dict[str, Any]:
    """
    Wrapper функция за multi_step_rag_query с enhanced параметри
    Запазва съвместимост с предишни версии
    """
    return multi_step_rag_query(query, **kwargs)


# --- Край на файла src/utils.py ---