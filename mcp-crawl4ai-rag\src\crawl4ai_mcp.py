# --- START OF FILE: src/crawl4ai_mcp.py (MODIFIED FOR PHASE 1 - Part 2 - FULL & CORRECT VERSION) ---

# --- НАЧАЛО: Конфигуриране на logging (ТРЯБВА ДА Е НАЙ-ОТГОРЕ) ---
import logging
import sys

logging.basicConfig(
    level=logging.DEBUG, # Промени на INFO за по-малко логове в продукция/тестове
    format='%(asctime)s - %(levelname)s - [%(name)s:%(module)s:%(funcName)s:%(lineno)d] - %(message)s',
    stream=sys.stdout # Изпраща логовете към стандартния изход
)
# Създаваме логер за текущия модул. Използвай module_logger навсякъде в този файл.
module_logger = logging.getLogger(__name__) # __name__ ще бъде "src.crawl4ai_mcp" ако се стартира с "python -m src.crawl4ai_mcp"
                                         # или "__main__" ако се стартира с "python src/crawl4ai_mcp.py"
module_logger.info("Logging configured at the very beginning of crawl4ai_mcp.py.")
# --- КРАЙ: Конфигуриране на logging ---

# --- ПЪРВО: Стандартни импорти, НУЖНИ ЗА ЗАРЕЖДАНЕ НА .ENV и основни операции ---
import os
from pathlib import Path
from dotenv import load_dotenv # За зареждане на .env файла

# --- ВТОРО: ЛОГИКА ЗА ЗАРЕЖДАНЕ НА .ENV ФАЙЛА ---
# Тази логика трябва да се изпълни ПРЕДИ импортирането на модули, които зависят от .env променливите
module_logger.debug("--- Attempting to load .env file (crawl4ai_mcp.py global scope) ---")
try:
    # Определяне на пътя до .env файла спрямо текущия скрипт
    # Предпочитаме .env в коренната директория на проекта (едно ниво над src/)
    current_file_path_for_dotenv = Path(__file__).resolve() # Път до src/crawl4ai_mcp.py
    project_root_for_dotenv = current_file_path_for_dotenv.parent.parent # Път до коренната директория на проекта
    
    dotenv_path_in_project_root = project_root_for_dotenv / '.env'
    # Като резервен вариант, проверяваме и в същата директория като скрипта (src/.env),
    # въпреки че обикновено .env е в корена на проекта.
    dotenv_path_in_script_dir = current_file_path_for_dotenv.parent / '.env'

    loaded_from_path = None
    if dotenv_path_in_project_root.exists():
        load_dotenv(dotenv_path=dotenv_path_in_project_root, override=True)
        loaded_from_path = dotenv_path_in_project_root
    elif dotenv_path_in_script_dir.exists(): # По-рядко срещан случай
        load_dotenv(dotenv_path=dotenv_path_in_script_dir, override=True)
        loaded_from_path = dotenv_path_in_script_dir
    
    if loaded_from_path:
        module_logger.info(f".env file loaded successfully from: {loaded_from_path}")
    else:
        module_logger.warning(
            f".env file NOT FOUND at expected locations: {dotenv_path_in_project_root} or "
            f"{dotenv_path_in_script_dir}. The application will rely on system environment variables if set."
        )
except Exception as e_dotenv_load:
    # Използваме module_logger, който вече е конфигуриран
    module_logger.error(f"An error occurred during .env file loading: {e_dotenv_load}", exc_info=True)
# --- КРАЙ НА ЛОГИКАТА ЗА ЗАРЕЖANE НА .ENV ---

# --- ТРЕТО: Останалите стандартни импорти ---
import asyncio
import json
import re
import traceback # За по-детайлни грешки
from collections.abc import AsyncIterator # Може да е нужно за типове
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Union, Tuple
from urllib.parse import urlparse, urldefrag, urljoin
from xml.etree import ElementTree # За sitemap
from io import BytesIO # За sitemap
from playwright_stealth import stealth_async
from markdownify import markdownify as md
from playwright.async_api import async_playwright
try:
    import trafilatura  # type: ignore
except ImportError:
    print("Warning: trafilatura not found. Install with: pip install trafilatura")
    trafilatura = None
import re
# from datetime import datetime  # Unused import
from typing import Dict, Any

# 🚀 ФАЗА 2 ОПТИМИЗАЦИЯ: Функция за извличане на метаданни
def extract_page_metadata(url: str, html_content: str, text_content: str) -> Dict[str, Any]:
    """Извлича метаданни от страницата за подобрено ранжиране"""
    metadata = {
        'document_type': 'unknown',
        'publication_date': None,
        'quality_score': 0.5,  # Базов резултат
        'program_indicators': [],
        'content_length': len(text_content),
        'url_indicators': []
    }

    # 1. Определяне на типа документ по URL
    if '/node/' in url:
        if '/opos/' in url:
            metadata['document_type'] = 'procedure'
            metadata['quality_score'] = 0.9  # Процедури са високо качество
        elif '/optti/' in url:
            metadata['document_type'] = 'transport_procedure'
            metadata['quality_score'] = 0.85
        elif '/opic/' in url:
            metadata['document_type'] = 'innovation_procedure'
            metadata['quality_score'] = 0.85
        else:
            metadata['document_type'] = 'document'
            metadata['quality_score'] = 0.7
    elif '/term/' in url or '/category/' in url:
        metadata['document_type'] = 'category'
        metadata['quality_score'] = 0.2  # Категории са шум
    elif '/news' in url or '/article' in url:
        metadata['document_type'] = 'news'
        metadata['quality_score'] = 0.4  # Новини са средно качество
    elif '/procedure/' in url:
        metadata['document_type'] = 'procedure'
        metadata['quality_score'] = 0.9
    elif url.endswith(('.pdf', '.doc', '.docx')):
        metadata['document_type'] = 'document'
        metadata['quality_score'] = 0.8

    # 2. Извличане на дата от HTML
    try:
        # Търсене на дати в HTML meta tags
        date_patterns = [
            r'<meta[^>]*property="article:published_time"[^>]*content="([^"]*)"',
            r'<meta[^>]*name="date"[^>]*content="([^"]*)"',
            r'<meta[^>]*name="DC\.Date"[^>]*content="([^"]*)"',
            r'<time[^>]*datetime="([^"]*)"',
            r'(\d{1,2}[\./-]\d{1,2}[\./-]\d{4})',  # DD.MM.YYYY формат
            r'(\d{4}[\./-]\d{1,2}[\./-]\d{1,2})'   # YYYY.MM.DD формат
        ]

        for pattern in date_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            if matches:
                metadata['publication_date'] = matches[0]
                break
    except Exception:
        pass

    # 3. Анализ на програмни индикатори в текста
    program_keywords = {
        'околна_среда': ['околна среда', 'екология', 'климат', 'отпадъци', 'води'],
        'иновации': ['иновации', 'технологии', 'изследвания', 'развитие', 'МСП'],
        'транспорт': ['транспорт', 'инфраструктура', 'пътища', 'железопътен'],
        'образование': ['образование', 'обучение', 'квалификация', 'умения'],
        'здравеопазване': ['здравеопазване', 'здраве', 'медицина', 'болници']
    }

    text_lower = text_content.lower()
    for program, keywords in program_keywords.items():
        score = sum(1 for keyword in keywords if keyword in text_lower)
        if score > 0:
            metadata['program_indicators'].append({'program': program, 'score': score})

    # 4. URL индикатори за качество
    url_lower = url.lower()
    if '/opos/' in url_lower:
        metadata['url_indicators'].append('environmental_program')
    if '/optti/' in url_lower:
        metadata['url_indicators'].append('transport_program')
    if '/opic/' in url_lower:
        metadata['url_indicators'].append('innovation_program')
    if '/node/' in url_lower:
        metadata['url_indicators'].append('specific_document')

    return metadata

# 🚀 ФАЗА 1 ОПТИМИЗАЦИЯ: Интелигентно URL филтриране
POSITIVE_URL_PATTERNS = [
    r'/node/\d+',           # Конкретни документи
    r'/procedure/',         # Процедури
    r'/opos/node/',         # ОПОС документи
    r'/optti/node/',        # ОПТИ документи
    r'/opic/node/',         # ОПИК документи
    r'/bg/news-eip',        # Новини за ЕИП
    r'/indicative-annual-work-programmes'  # Работни програми
]

NEGATIVE_URL_PATTERNS = [
    r'/term/',              # Категории/тагове
    r'/category/',          # Категории
    r'/all-events',         # Списъци със събития
    r'/sitemap',            # Sitemap страници
    r'/careers',            # Кариери
    r'/page/\d+$',          # Пагинация страници
    r'/taxonomy/term/',     # Таксономии
    r'/search',             # Търсене
    r'/user/',              # Потребителски профили
    r'/admin/',             # Административни страници
    r'/contact',            # Контакти
    r'/about',              # За нас
]

def should_crawl_url(url: str) -> bool:
    """
    🎯 ФАЗА 1: Определя дали URL трябва да бъде crawl-нат въз основа на позитивни/негативни шаблони
    """
    import re

    # Първо проверяваме негативните шаблони - ако има съвпадение, не crawl-ваме
    for negative_pattern in NEGATIVE_URL_PATTERNS:
        if re.search(negative_pattern, url):
            return False

    # След това проверяваме позитивните шаблони - ако има съвпадение, crawl-ваме
    for positive_pattern in POSITIVE_URL_PATTERNS:
        if re.search(positive_pattern, url):
            return True

    # Ако няма съвпадение с позитивни шаблони, не crawl-ваме
    return False

# --- ЧЕТВЪРТО: Импорти от трети страни (които МОЖЕ да зависят от .env променливите) ---
import requests # За синхронни HTTP заявки (напр. sitemap)
from openai import AsyncOpenAI # За асинхронни OpenAI заявки
from mcp.server.fastmcp import FastMCP, Context # FastMCP сървър
from supabase import Client as SupabaseClient # Даваме му по-ясно име
import cohere

# --- NEW IMPORTS FOR PHASE 1 ---
from unstructured.partition.auto import partition
from langchain.text_splitter import RecursiveCharacterTextSplitter

# --- ПЕТО: ТВОИТЕ СОБСТВЕНИ МОДУЛИ (СЕГА ТРЯБВА ДА ВИЖДАТ .ENV ПРОМЕНЛИВИТЕ) ---
# Предполага се, че тези модули са в същата директория 'src' или са инсталирани правилно.
# Използвай относителни импорти (с точка), ако са в същия пакет.
from .utils import (
    get_supabase_client,
    add_documents_to_supabase,
    search_documents,
    search_documents_with_text,  # type: ignore
    RerankerType,
    rerank_documents,
    calculate_sha256,
    get_document_hash_info,
    upsert_document_hash,
    extract_entities_from_text_llm,
    enhanced_semantic_search,  # type: ignore
    small_to_big_retrieval,    # type: ignore
    agent_based_retrieval,     # type: ignore
    smart_rag_query,           # type: ignore
    ultra_smart_rag_query,     # type: ignore
    get_rag_performance_summary # type: ignore
)
from crawl4ai import AsyncWebCrawler, BrowserConfig, CacheMode, CrawlResult
from .entity_utils import program_name_normalizer_instance, ProgramNameNormalizer
from .gazetteer_utils import load_program_gazetteer, GAZETTEER_DEFAULT_PATH

# --- Шесто: Глобални променливи, които се инициализират от .env (СЛЕД като е зареден) ---
module_logger.debug("--- Reading settings from environment after .env load (crawl4ai_mcp.py) ---")

OPENAI_API_KEY_MCP = os.getenv("OPENAI_API_KEY")
if not OPENAI_API_KEY_MCP:
    module_logger.warning("CRITICAL: OPENAI_API_KEY NOT FOUND in environment after .env load attempt. OpenAI calls will likely fail.")
else:
    # Показваме само част от ключа от съображения за сигурност
    module_logger.info(f"OPENAI_API_KEY found by crawl4ai_mcp.py: '{OPENAI_API_KEY_MCP[:5]}...{OPENAI_API_KEY_MCP[-4:]}'")

LLM_MODEL_FOR_QUERY_ENTITIES_MCP = os.getenv(
    "MODEL_CHOICE_QUERY_ENTITIES", 
    os.getenv("MODEL_CHOICE_ENTITY_EXTRACTION", "gpt-4o-mini") # Резервна стойност
)
DEFAULT_ENTITY_TYPES_QUERY_MCP_STR = os.getenv("DEFAULT_ENTITY_TYPES_FOR_QUERY_MCP", '["PROGRAM_NAME", "YEAR"]')
try:
    DEFAULT_ENTITY_TYPES_QUERY_MCP = json.loads(DEFAULT_ENTITY_TYPES_QUERY_MCP_STR)
    if not isinstance(DEFAULT_ENTITY_TYPES_QUERY_MCP, list):
        raise ValueError("DEFAULT_ENTITY_TYPES_FOR_QUERY_MCP is not a list after JSON parsing.")
except (json.JSONDecodeError, ValueError) as e_json_parse:
    module_logger.error(
        f"Failed to parse DEFAULT_ENTITY_TYPES_FOR_QUERY_MCP ('{DEFAULT_ENTITY_TYPES_QUERY_MCP_STR}'): {e_json_parse}. "
        "Using fallback: ['PROGRAM_NAME', 'YEAR']"
    )
    DEFAULT_ENTITY_TYPES_QUERY_MCP = ["PROGRAM_NAME", "YEAR"]

# Числeни стойности с преобразуване и проверки
def get_env_int(var_name: str, default_val: int) -> int:
    val_str = os.getenv(var_name)
    if val_str:
        try: return int(val_str)
        except ValueError: module_logger.warning(f"Invalid int value for {var_name} ('{val_str}'). Using default: {default_val}.")
    return default_val

def get_env_float(var_name: str, default_val: float) -> float:
    val_str = os.getenv(var_name)
    if val_str:
        try: return float(val_str)
        except ValueError: module_logger.warning(f"Invalid float value for {var_name} ('{val_str}'). Using default: {default_val}.")
    return default_val

# --- ПРОМЕНЕНА СЕКЦИЯ С ПАРАМЕТРИ ---
# Параметри за първоначално търсене
DEFAULT_MATCH_COUNT_MCP = get_env_int("DEFAULT_MATCH_COUNT_MCP", 10) # Финален брой резултати
DEFAULT_MIN_PROGRAM_CONTAINMENT_THRESHOLD_MCP = get_env_float("DEFAULT_MIN_PROGRAM_CONTAINMENT_THRESHOLD_MCP", 0.60)
DEFAULT_MIN_SIMILARITY_THRESHOLD_MCP = get_env_float("DEFAULT_MIN_SIMILARITY_THRESHOLD_MCP", 0.1)
DEFAULT_WEIGHT_SIMILARITY_MCP = get_env_float("DEFAULT_WEIGHT_SIMILARITY_MCP", 0.25) # Актуализирано
DEFAULT_WEIGHT_PROGRAM_NAME_MCP = get_env_float("DEFAULT_WEIGHT_PROGRAM_NAME_MCP", 0.40) # Актуализирано
DEFAULT_WEIGHT_YEAR_MCP = get_env_float("DEFAULT_WEIGHT_YEAR_MCP", 0.10) # Актуализирано

# Нови параметри за Re-ranker
RERANK_CANDIDATE_COUNT_MCP = get_env_int("RERANK_CANDIDATE_COUNT", 25) # Брой кандидати за re-ranker-а
RERANK_TOP_N_MCP = get_env_int("RERANK_TOP_N", 5) # Колко от пренаредените да се върнат

module_logger.info(f"Loaded MCP settings: MODEL_QUERY_ENTITIES='{LLM_MODEL_FOR_QUERY_ENTITIES_MCP}', "
                   f"MATCH_COUNT={DEFAULT_MATCH_COUNT_MCP}, ENTITY_TYPES_QUERY={DEFAULT_ENTITY_TYPES_QUERY_MCP}, "
                   f"MIN_PROG_THRESH={DEFAULT_MIN_PROGRAM_CONTAINMENT_THRESHOLD_MCP}, MIN_SIM_THRESH={DEFAULT_MIN_SIMILARITY_THRESHOLD_MCP}, "
                   f"W_SIM={DEFAULT_WEIGHT_SIMILARITY_MCP}, W_PROG={DEFAULT_WEIGHT_PROGRAM_NAME_MCP}, W_YEAR={DEFAULT_WEIGHT_YEAR_MCP}, "
                   f"RERANK_CANDIDATES={RERANK_CANDIDATE_COUNT_MCP}, RERANK_TOP_N={RERANK_TOP_N_MCP}")
# --- КРАЙ НА ПРОМЕНЕНАТА СЕКЦИЯ С ПАРАМЕТРИ ---

# --- Дефиниция на Dataclasses ---
@dataclass
class Crawl4AIDocument:
    url: str
    raw_content: Union[str, bytes] # Може да е текст (HTML/MD) или байтове (PDF)
    doc_type: str # 'html_markdown', 'pdf', 'text_file_error', 'unknown_error' и т.н.
    text_content: Optional[str] = None # Текстовото съдържание след обработка (напр. от PDF)
    error_message: Optional[str] = None
    success: bool = True
    links: Dict[str, List[Dict[str, str]]] = field(default_factory=dict) # Линкове, извлечени от Crawl4AI

@dataclass
class Crawl4AIContext:
    # Ресурси, които се инициализират при стартиране на приложението
    crawler: Optional[AsyncWebCrawler] = None
    supabase_client: Optional[SupabaseClient] = None
    program_normalizer: Optional[ProgramNameNormalizer] = None
    gazetteer_data: Optional[List[Dict[str, Any]]] = None
    async_openai_client: Optional[AsyncOpenAI] = None
    cohere_client: Optional[cohere.Client] = None
    
    # Параметри по подразбиране за RAG заявки
    default_match_count: int = field(default_factory=lambda: DEFAULT_MATCH_COUNT_MCP)
    default_entity_types_query: List[str] = field(default_factory=lambda: list(DEFAULT_ENTITY_TYPES_QUERY_MCP))
    llm_model_query_entities: str = field(default_factory=lambda: LLM_MODEL_FOR_QUERY_ENTITIES_MCP)
    default_min_program_containment_threshold: float = field(default_factory=lambda: DEFAULT_MIN_PROGRAM_CONTAINMENT_THRESHOLD_MCP)
    default_min_similarity_threshold: float = field(default_factory=lambda: DEFAULT_MIN_SIMILARITY_THRESHOLD_MCP)
    default_weight_similarity: float = field(default_factory=lambda: DEFAULT_WEIGHT_SIMILARITY_MCP)
    default_weight_program_name: float = field(default_factory=lambda: DEFAULT_WEIGHT_PROGRAM_NAME_MCP)
    default_weight_year: float = field(default_factory=lambda: DEFAULT_WEIGHT_YEAR_MCP)
    
    # Нови параметри за Re-ranker в контекста
    rerank_candidate_count: int = field(default_factory=lambda: RERANK_CANDIDATE_COUNT_MCP)
    rerank_top_n: int = field(default_factory=lambda: RERANK_TOP_N_MCP)


# Глобална променлива за контекста на приложението
app_context: Optional[Crawl4AIContext] = None # Ще бъде инстанция на Crawl4AIContext

# --- Функции за управление на жизнения цикъл на приложението ---
async def initialize_application_resources():
    """Инициализира всички необходими ресурси за приложението при стартиране."""
    global app_context # За да можем да присвоим стойност на глобалната променлива
    module_logger.info("MCP APP LIFECYCLE: Starting resource initialization...")

    # 1. Инициализация на AsyncWebCrawler (без промяна)
    crawler_instance: Optional[AsyncWebCrawler] = None
    try:
        browser_config = BrowserConfig(headless=True, verbose=False)
        crawler_instance = AsyncWebCrawler(config=browser_config)
        await crawler_instance.__aenter__()
        module_logger.info("MCP APP LIFECYCLЕ: AsyncWebCrawler initialized and entered context.")
    except Exception as e_crawler_init:
        module_logger.critical(f"MCP APP LIFECYCLE: CRITICAL - Failed to initialize AsyncWebCrawler: {e_crawler_init}", exc_info=True)

    # 2. Инициализация на Supabase Client (без промяна)
    supabase_instance: Optional[SupabaseClient] = None
    try:
        supabase_instance = get_supabase_client()
        if supabase_instance:
            module_logger.info("MCP APP LIFECYCLE: Supabase client initialized via get_supabase_client().")
        else:
            module_logger.critical("MCP APP LIFECYCLE: CRITICAL - get_supabase_client() returned None.")
    except Exception as e_supabase_init:
        module_logger.critical(f"MCP APP LIFECYCLE: CRITICAL - Failed to initialize Supabase client: {e_supabase_init}", exc_info=True)

    # 3. Инициализация на AsyncOpenAI Client (без промяна)
    aio_openai_instance: Optional[AsyncOpenAI] = None
    if OPENAI_API_KEY_MCP:
        try:
            aio_openai_instance = AsyncOpenAI(api_key=OPENAI_API_KEY_MCP, max_retries=3)
            # --- FAIL-FAST: Валидация на OpenAI ключа ---
            await aio_openai_instance.models.list()
            module_logger.info("MCP APP LIFECYCLE: AsyncOpenAI client initialized and API key validated.")
            # --- КРАЙ на FAIL-FAST ---
        except Exception as e_openai_init:
            module_logger.critical(f"MCP APP LIFECYCLE: CRITICAL - Failed to initialize or validate AsyncOpenAI client: {e_openai_init}", exc_info=True)
            raise RuntimeError(f"Failed to initialize OpenAI client or validate API key: {e_openai_init}")
    else:
        module_logger.critical("MCP APP LIFECYCLE: CRITICAL - OPENAI_API_KEY_MCP not set. The application cannot function without it.")
        raise ValueError("OPENAI_API_KEY must be set in the environment.")
    
    # 4. Инициализация на Cohere Client ---
    cohere_api_key_mcp = os.getenv("COHERE_API_KEY")
    cohere_instance: Optional[cohere.Client] = None
    reranker_type_env = os.getenv("RERANKER_TYPE", "cohere").upper()

    if reranker_type_env == "COHERE":
        if cohere_api_key_mcp:
            try:
                cohere_instance = cohere.Client(cohere_api_key_mcp)
                # --- FAIL-FAST: Валидация на Cohere ключа (чрез заявка с празен query) ---
                # Cohere няма прост list_models, затова правим евтина заявка
                _ = cohere_instance.rerank(query="test", documents=["test"], top_n=1)
                module_logger.info("MCP APP LIFECYCLE: Cohere client initialized and API key validated.")
            except Exception as e_cohere_init:
                module_logger.critical(f"MCP APP LIFECYCLE: CRITICAL - Failed to initialize or validate Cohere client: {e_cohere_init}", exc_info=True)
                raise RuntimeError(f"Failed to initialize Cohere client or validate API key: {e_cohere_init}")
        else:
            module_logger.critical("MCP APP LIFECYCLE: CRITICAL - RERANKER_TYPE is COHERE, but COHERE_API_KEY is not set.")
            raise ValueError("COHERE_API_KEY must be set when RERANKER_TYPE is COHERE.")
    else:
        module_logger.info(f"MCP APP LIFECYCLE: Reranker type is '{reranker_type_env}'. Cohere client initialization skipped.")
    # --- КРАЙ НА НОВАТА СЕКЦИЯ ---

    # 5. Вземане на ProgramNameNormalizer (без промяна)
    current_program_normalizer_instance: Optional[ProgramNameNormalizer] = program_name_normalizer_instance
    if not current_program_normalizer_instance or not getattr(current_program_normalizer_instance, 'nlp', None):
        module_logger.critical("MCP APP LIFECYCLE: CRITICAL - ProgramNameNormalizer or its Stanza model (nlp) is not initialized. This is a fatal error.")
        raise RuntimeError("Stanza model (bg) required by ProgramNameNormalizer failed to load. The application cannot proceed.")
    else:
        module_logger.info("MCP APP LIFECYCLE: ProgramNameNormalizer instance and its Stanza model are successfully loaded.")

    # 6. Зареждане на Gazetteer Data (без промяна)
    loaded_gazetteer_data_list: List[Dict[str, Any]] = []
    if current_program_normalizer_instance:
        module_logger.info(f"MCP APP LIFECYCLE: Attempting to load gazetteer from: {GAZETTEER_DEFAULT_PATH}")
        try:
            loaded_gazetteer_data_list = await asyncio.to_thread(
                load_program_gazetteer,
                normalizer_instance=current_program_normalizer_instance,
                file_path=GAZETTEER_DEFAULT_PATH
            )
            module_logger.info(f"MCP APP LIFECYCLE: Gazetteer loaded. Number of canonical entries: {len(loaded_gazetteer_data_list)}")
        except Exception as e_gazetteer_load:
            module_logger.error(f"MCP APP LIFECYCLE: Failed to load gazetteer: {e_gazetteer_load}", exc_info=True)
            loaded_gazetteer_data_list = []
    else:
        module_logger.warning("MCP APP LIFECYCLE: Skipping gazetteer loading as ProgramNameNormalizer is not available.")
    
    # 7. Създаване на инстанция на Crawl4AIContext с всички инициализирани ресурси
    app_context = Crawl4AIContext(
        crawler=crawler_instance,
        supabase_client=supabase_instance,
        program_normalizer=current_program_normalizer_instance,
        gazetteer_data=loaded_gazetteer_data_list,
        async_openai_client=aio_openai_instance,
        cohere_client=cohere_instance
    )
    module_logger.info("MCP APP LIFECYCLE: Resource initialization complete. Global 'app_context' populated.")


async def cleanup_application_resources():
    """Почиства ресурсите на приложението при спиране."""
    global app_context
    module_logger.info("MCP APP LIFECYCLE: Starting resource cleanup...")
    if app_context:
        if app_context.crawler:
            try:
                module_logger.info("MCP APP LIFECYCLE: Cleaning up AsyncWebCrawler...")
                await app_context.crawler.__aexit__(None, None, None)
                module_logger.info("MCP APP LIFECYCLE: AsyncWebCrawler cleaned up.")
            except Exception as e_crawler_cleanup:
                module_logger.error(f"MCP APP LIFECYCLE: Error during AsyncWebCrawler cleanup: {e_crawler_cleanup}", exc_info=True)
        
        # Cohere и OpenAI клиентите не изискват изрично затваряне
        app_context = None
        module_logger.info("MCP APP LIFECYCLE: Global 'app_context' cleared.")
    else:
        module_logger.warning("MCP APP LIFECYCLE: app_context was already None during cleanup.")
    
    module_logger.info("MCP APP LIFECYCLE: Resource cleanup complete.")

# --- Конфигурация на FastMCP сървъра (без промяна) ---
mcp_host_setting = os.getenv("HOST", "0.0.0.0")
mcp_port_setting = get_env_int("PORT", 8051)

mcp = FastMCP(
    name="mcp-crawl4ai-rag",
    description="MCP server for RAG and web crawling with Crawl4AI, including PDF and entity processing.",
    host=mcp_host_setting,
    port=mcp_port_setting
)
module_logger.info(f"FastMCP server instance 'mcp' (version ?) initialized to run on http://{mcp_host_setting}:{mcp_port_setting}.")

def is_sitemap(url: str) -> bool:
    """Проверява дали URL е вероятно sitemap."""
    return url.lower().endswith('sitemap.xml') or 'sitemap' in urlparse(url).path.lower()

def is_txt(url: str) -> bool:
    """Проверява дали URL завършва на .txt."""
    return url.lower().endswith('.txt')

def parse_sitemap(sitemap_url: str) -> List[str]:
    """Извлича URL-и от sitemap.xml файл."""
    module_logger.info(f"Attempting to parse sitemap: {sitemap_url}")
    headers = {'User-Agent': 'Mozilla/5.0 (compatible; MCPCrawlBot/1.0; +http://example.com/bot)'}
    try:
        response = requests.get(sitemap_url, headers=headers, timeout=30)
        response.raise_for_status() 

        content_bytes = response.content
        try:
            iter_tree = ElementTree.iterparse(BytesIO(content_bytes))
            for _, el_node in iter_tree:
                if '}' in el_node.tag:
                    el_node.tag = el_node.tag.split('}', 1)[1]
            root_element = iter_tree.root  # type: ignore
            urls_found = [loc.text for loc in root_element.findall('.//loc') if loc.text and loc.text.strip()]
        except Exception: 
            module_logger.debug(f"Failed to strip namespace for sitemap {sitemap_url}, trying direct wildcard parsing.")
            tree = ElementTree.fromstring(content_bytes)
            urls_found = [loc.text for loc in tree.findall('.//{*}loc') if loc.text and loc.text.strip()]
        
        module_logger.info(f"Found {len(urls_found)} URLs in sitemap {sitemap_url}.")
        return urls_found
    except requests.exceptions.RequestException as e_requests:
        module_logger.error(f"Sitemap fetch error for {sitemap_url}: {e_requests}")
    except ElementTree.ParseError as e_xml_parse:
        module_logger.error(f"Sitemap XML parse error for {sitemap_url}: {e_xml_parse}")
    except Exception as e_general_sitemap:
        module_logger.error(f"Generic sitemap processing error for {sitemap_url}: {e_general_sitemap}", exc_info=True)
    return []

async def fetch_content_and_type(
    url: str
) -> Tuple[Optional[Union[str, bytes]], Optional[str], Optional[str], Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """
    Извлича съдържание от URL, определя типа му и връща съдържанието.
    За HTML страници използва собствена Playwright инстанция със stealth, за да заобикаля защити.
    """
    module_logger.info(f"fetch_content_and_type: Initiating fetch for URL: {url}")
    
    if not url.lower().startswith(('http://', 'https://')):
        module_logger.error(f"fetch_content_and_type: Invalid URL scheme for {url}. Must be http or https.")
        return None, "invalid_url_scheme", f"Invalid URL scheme for {url}. Must be http or https.", None, None

    is_definitely_pdf = False
    links_data: Optional[Dict[str, Any]] = {}

    # --- ПРОВЕРКА ЗА PDF (остава същата) ---
    if url.lower().endswith('.pdf'):
        is_definitely_pdf = True
    else:
        try:
            head_response = await asyncio.to_thread( 
                requests.head, url, timeout=15, allow_redirects=True,
                headers={'User-Agent': 'Mozilla/5.0 (compatible; MCPCrawlBot/1.0; +http://example.com/bot)'}
            )
            head_response.raise_for_status()
            content_type_header = head_response.headers.get('Content-Type', '').lower()
            if 'application/pdf' in content_type_header:
                is_definitely_pdf = True
        except Exception as e_head_request:
            module_logger.warning(f"fetch_content_and_type: HEAD request for {url} failed: {e_head_request}. Will proceed based on extension or stealth crawl.")

    # --- ИЗТЕГЛЯНЕ НА PDF (остава същото) ---
    if is_definitely_pdf:
        try:
            pdf_get_response = await asyncio.to_thread(
                requests.get, url, timeout=120, 
                headers={'User-Agent': 'Mozilla/5.0 (compatible; MCPCrawlBot/1.0; +http://example.com/bot)'}
            )
            pdf_get_response.raise_for_status()
            pdf_bytes = pdf_get_response.content
            if pdf_bytes:
                return pdf_bytes, "pdf", None, None, None
            else:
                return None, "pdf", "PDF download resulted in empty content.", None, None
        except Exception as e_pdf_req:
            return None, "pdf_download_error", f"Error downloading PDF {url}: {e_pdf_req}", None, None

    # --- ИЗТЕГЛЯНЕ НА TXT/MD (остава същото) ---
    elif is_txt(url) or url.lower().endswith(('.md', '.markdown')):
        try:
            txt_md_response = await asyncio.to_thread(
                requests.get, url, timeout=30,
                headers={'User-Agent': 'Mozilla/5.0 (compatible; MCPCrawlBot/1.0; +http://example.com/bot)'}
            )
            txt_md_response.raise_for_status()
            text_content = txt_md_response.content.decode('utf-8', errors='replace')
            return text_content, "html_markdown", None, None, None
        except Exception as e_txt_md:
            return None, "text_file_error", str(e_txt_md), None, None
            
    # --- НОВА, НЕЗАВИСИМА ЛОГИКА ЗА HTML СТРАНИЦИ ---
    else:
        async with async_playwright() as p:
            browser = None
            try:
                # Тук headless=True. Променете на False, за да видите какво се случва на екрана.
                browser = await p.chromium.launch(headless=True)
                
                # Използваме 'with' за page, за да сме сигурни, че се затваря
                async with await browser.new_page(
                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36'
                ) as page:

                    # Прилагаме "магията" за скриване
                    await stealth_async(page)
                    
                    module_logger.info(f"Navigating to {url} with a dedicated Playwright-Stealth instance...")
                    await page.goto(url, wait_until="networkidle", timeout=60000)
                    
                    # Кратко изчакване, за да се заредят всички динамични елементи
                    await page.wait_for_timeout(3000)

                    html_content = await page.content()
                    
                    links_list = await page.eval_on_selector_all("a[href]", """
                        (links) => links.map(link => ({
                            text: link.textContent.trim(),
                            href: link.href
                        }))
                    """)
                    
                    base_url_parsed = urlparse(url)
                    internal_links = [l for l in links_list if l['href'] and urlparse(l['href']).netloc == base_url_parsed.netloc]
                    external_links = [l for l in links_list if l['href'] and urlparse(l['href']).netloc != base_url_parsed.netloc]
                    links_data = {"internal": internal_links, "external": external_links}

                if not html_content:
                    return None, "html_crawl_error", "Stealth crawl resulted in empty HTML content.", links_data, None

                # 🚀 ФАЗА 1 ОПТИМИЗАЦИЯ: Използваме trafilatura за агресивно HTML изчистване
                try:
                    if trafilatura is not None:
                        clean_text = trafilatura.extract(html_content, favor_precision=True, include_comments=False, include_tables=True)
                    else:
                        clean_text = None
                    if clean_text and len(clean_text.strip()) > 50:
                        # Trafilatura успешно извлече чист текст
                        markdown_content = clean_text.strip()
                        module_logger.info(f"✅ Trafilatura extracted {len(markdown_content)} chars of clean text from {url}")
                    else:
                        # Fallback към markdownify ако trafilatura не успее
                        markdown_content = md(html_content).strip()
                        module_logger.info(f"⚠️ Trafilatura failed, using markdownify fallback for {url}")
                except Exception as e_trafilatura:
                    # Fallback към markdownify при грешка
                    markdown_content = md(html_content).strip()
                    module_logger.warning(f"⚠️ Trafilatura error for {url}: {e_trafilatura}, using markdownify fallback")

                # 🚀 ФАЗА 2 ОПТИМИЗАЦИЯ: Обогатяване с метаданни
                metadata = extract_page_metadata(url, html_content, markdown_content)
                module_logger.info(f"📊 Extracted metadata for {url}: type={metadata['document_type']}, quality={metadata['quality_score']}, programs={len(metadata['program_indicators'])}")

                if not markdown_content:
                     return None, "html_crawl_error", "Content extraction resulted in empty content.", links_data, None

                # Проверка за минимална дължина
                # ПРЕМАХВАМЕ Markdown линкове ([текст](url)) и изображения (![alt](url))
                text_for_length_check = re.sub(r'!?\[([^\]]*)\]\([^)]+\)', r'\1', markdown_content)
                text_for_length_check = re.sub(r'\s+', ' ', text_for_length_check).strip()
                min_meaningful_length = 50
                if len(text_for_length_check) < min_meaningful_length:
                     return markdown_content, "html_markdown_too_short", "Content very short after Markdown cleaning.", links_data, metadata

                module_logger.info(f"Successfully extracted {len(markdown_content)} chars of Markdown from {url} using dedicated Playwright-Stealth.")
                return markdown_content, "html_markdown", None, links_data, metadata

            except Exception as e_stealth_crawl:
                module_logger.error(f"Dedicated Playwright-Stealth crawl failed for {url}: {e_stealth_crawl}", exc_info=True)
                return None, "html_crawl_exception", f"Exception during stealth crawl: {e_stealth_crawl}", None, None
            finally:
                if browser:
                    await browser.close()
    
    return None, "unknown_fetch_error", "Could not determine content type or an unhandled error occurred during fetch.", None, None


async def crawl_recursive_internal_links(
    start_urls: List[str], max_depth: int = 1, max_concurrent: int = 5
) -> List[Crawl4AIDocument]:
    """Рекурсивно обхожда вътрешни линкове, започвайки от start_urls."""
    global app_context
    if not app_context or not app_context.crawler:
        module_logger.error("crawl_recursive_internal_links: Crawler instance not available. Cannot perform recursive crawl.")
        return []
    
    from collections import deque
    
    processed_documents: List[Crawl4AIDocument] = []
    visited_urls: set[str] = set()
    crawl_queue: deque[Tuple[str, int]] = deque() 

    for initial_url in start_urls:
        try:
            parsed_url = urlparse(initial_url)
            normalized_url = urljoin(initial_url, parsed_url.path).rstrip('/')
            if parsed_url.scheme == 'http':
                normalized_url = normalized_url.replace("http://", "https://", 1)
            
            if normalized_url and normalized_url not in visited_urls:
                 crawl_queue.append((normalized_url, 0))
                 visited_urls.add(normalized_url)
        except Exception as e_url_parse_init:
            module_logger.warning(f"Invalid start URL: '{initial_url}'. Error: {e_url_parse_init}. Skipping.")

    concurrency_semaphore = asyncio.Semaphore(max_concurrent)
    active_crawl_tasks: set[asyncio.Task] = set()

    async def _process_single_url_task(url_to_process: str, current_depth_level: int):
        nonlocal processed_documents
        async with concurrency_semaphore:
            raw_data, doc_type_detected, error_message_details, links_extracted, _ = await fetch_content_and_type(url_to_process)
            
            is_successful_fetch = not bool(error_message_details) and raw_data is not None
            document_text_content: Optional[str] = None
            if doc_type_detected == "html_markdown" and isinstance(raw_data, str):
                document_text_content = raw_data
            
            current_document_obj = Crawl4AIDocument(
                url=url_to_process, raw_content=raw_data if raw_data is not None else "",
                doc_type=doc_type_detected if doc_type_detected else "fetch_error_unknown_type",
                text_content=document_text_content, error_message=error_message_details,
                success=is_successful_fetch, links=links_extracted if links_extracted else {}
            )
            processed_documents.append(current_document_obj)

            if current_document_obj.success and current_document_obj.doc_type == "html_markdown" and \
               current_depth_level < max_depth and current_document_obj.links:
                
                base_domain = urlparse(url_to_process).netloc
                internal_links_list = current_document_obj.links.get("internal", [])
                
                for link_info_dict in internal_links_list:
                    link_href = link_info_dict.get("href")
                    if link_href:
                        try:
                            absolute_next_url = urljoin(url_to_process, link_href)
                            parsed_next_url_obj = urlparse(absolute_next_url)
                            if parsed_next_url_obj.scheme not in ['http', 'https'] or parsed_next_url_obj.netloc != base_domain:
                                continue
                            
                            normalized_next_url = urljoin(absolute_next_url, parsed_next_url_obj.path).rstrip('/')
                            if parsed_next_url_obj.scheme == 'http':
                                normalized_next_url = normalized_next_url.replace("http://", "https://", 1)

                            # 🚀 ФАЗА 1 ОПТИМИЗАЦИЯ: Филтриране на URL-и преди добавяне в опашката
                            if normalized_next_url and normalized_next_url not in visited_urls:
                                if should_crawl_url(normalized_next_url):
                                    visited_urls.add(normalized_next_url)
                                    crawl_queue.append((normalized_next_url, current_depth_level + 1))
                                    module_logger.debug(f"✅ URL approved for crawling: {normalized_next_url}")
                                else:
                                    module_logger.debug(f"🚫 URL filtered out: {normalized_next_url}")
                        except Exception as e_link_processing:
                             module_logger.warning(f"Error processing internal link '{link_href}': {e_link_processing}.")
    
    while crawl_queue or active_crawl_tasks:
        while crawl_queue and len(active_crawl_tasks) < max_concurrent:
            url_to_visit, depth = crawl_queue.popleft()
            new_task = asyncio.create_task(_process_single_url_task(url_to_visit, depth))
            active_crawl_tasks.add(new_task)
            new_task.add_done_callback(active_crawl_tasks.discard) 
        
        if active_crawl_tasks:
            _, _ = await asyncio.wait(
                active_crawl_tasks, 
                return_when=asyncio.FIRST_COMPLETED if crawl_queue else asyncio.ALL_COMPLETED
            )
        else:
            break 
            
    return processed_documents

# --- Дефиниции на MCP Инструменти ---

@mcp.tool()
async def smart_crawl_url(
    ctx: Context,  # pylint: disable=unused-argument
    url: str,
    max_depth: int = 0,
    chunk_size: int = 512, # MODIFIED: Default chunk_size is now smaller and more effective for BGE
    chunk_overlap: int = 50, # NEW: Added chunk_overlap parameter
    force_recrawl: bool = False
) -> str:
    """
    MODIFIED: Интелигентно обхожда URL, използва unstructured за парсване и langchain за
    разделяне на текст, и го записва в Supabase.
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "smart_crawl_url: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "url": url, "error": error_message}, indent=2, ensure_ascii=False)

    documents_for_processing: List[Tuple[str, Union[str, bytes], str, Optional[Dict[str, Any]], Optional[Dict[str, Any]]]] = []
    
    # --- Логиката за събиране на документи остава същата ---
    if is_sitemap(url):
        sitemap_urls = await asyncio.to_thread(parse_sitemap, url)
        if not sitemap_urls:
            return json.dumps({"success": False, "url": url, "error": f"No URLs found in sitemap {url}."}, indent=2, ensure_ascii=False)
        fetch_results = await asyncio.gather(*[fetch_content_and_type(u) for u in sitemap_urls], return_exceptions=True)
        for i, res in enumerate(fetch_results):
            if not isinstance(res, Exception) and res and res[0]:  # type: ignore
                raw, dtype, _, links, metadata = res  # type: ignore
                if raw and dtype: documents_for_processing.append((sitemap_urls[i], raw, dtype, links, metadata))
    elif is_txt(url) or url.lower().endswith(('.md', '.markdown')):
        raw, dtype, err, links, metadata = await fetch_content_and_type(url)
        if err or not raw or dtype != "html_markdown":
            return json.dumps({"success": False, "url": url, "error": f"Failed to fetch: {err}"}, indent=2, ensure_ascii=False)
        documents_for_processing.append((url, raw, dtype, links, metadata))
    elif max_depth >= 0:
        if max_depth == 0:
            raw, dtype, err, links, metadata = await fetch_content_and_type(url)
            if err or not raw or not dtype:
                return json.dumps({"success": False, "url": url, "error": f"Failed to fetch: {err}"}, indent=2, ensure_ascii=False)
            documents_for_processing.append((url, raw, dtype, links, metadata))
        else:
            crawled_docs = await crawl_recursive_internal_links(start_urls=[url], max_depth=max_depth)
            for doc in crawled_docs:
                if doc.success and doc.raw_content and doc.doc_type not in ["error", "unknown_type_error", "fetch_error_unknown_type"]:
                    # За recursive crawling няма page metadata, така че предаваме None
                    documents_for_processing.append((doc.url, doc.raw_content, doc.doc_type, doc.links, None))
    else:
        return json.dumps({"success": False, "url": url, "error": f"Invalid max_depth: {max_depth}."}, indent=2, ensure_ascii=False)

    if not documents_for_processing:
        return json.dumps({"success": True, "url": url, "status_message": "No documents found for processing.", "documents_processed": 0}, indent=2, ensure_ascii=False)

    num_urls_processed = 0
    num_urls_skipped = 0
    total_chunks_added = 0
    
    # --- NEW: Инициализираме Text Splitter тук ---
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        length_function=len,
        is_separator_regex=False,
        separators=["\n\n", "\n", ". ", " ", ""] # Добър набор от разделители
    )
    module_logger.info(f"Initialized RecursiveCharacterTextSplitter with chunk_size={chunk_size}, chunk_overlap={chunk_overlap}")


    for doc_url, doc_raw_content, doc_content_type, _, page_metadata in documents_for_processing:
        current_content_hash = calculate_sha256(doc_raw_content)

        if not force_recrawl:
            db_hash_info = await asyncio.to_thread(get_document_hash_info, app_context.supabase_client, doc_url)
            if db_hash_info and db_hash_info.get('content_hash') == current_content_hash:
                module_logger.info(f"Skipping unchanged document: {doc_url}")
                num_urls_skipped += 1
                continue
        
        num_urls_processed += 1
        try:
            # Изтриваме старите фрагменти за този URL
            module_logger.info(f"Deleting old chunks for URL: {doc_url}")
            await asyncio.to_thread(app_context.supabase_client.table("crawled_pages").delete().eq("url", doc_url).execute)
        except Exception as e_delete:
            module_logger.error(f"Exception deleting old chunks for {doc_url}: {e_delete}", exc_info=True)

        # --- NEW: Логика за парсване с unstructured и разделяне с langchain ---
        document_chunks: List[str] = []
        db_chunk_metadata_list: List[Dict[str, Any]] = []

        try:
            # Използваме unstructured за всички типове файлове, той сам ще избере правилния парсър
            # Трябва да го изпълним в отделна нишка, защото е синхронна и CPU-интензивна операция
            file_like_object = BytesIO(doc_raw_content) if isinstance(doc_raw_content, bytes) else BytesIO(doc_raw_content.encode('utf-8'))  # type: ignore
            
            elements = await asyncio.to_thread(
                partition,
                file=file_like_object,
                # Подаваме content_type, за да помогнем на unstructured, ако е възможно
                content_type='application/pdf' if doc_content_type == 'pdf' else 'text/html',
                strategy="auto" # Позволяваме на unstructured да избере най-добрата стратегия
            )
            module_logger.info(f"Unstructured partitioned document {doc_url} into {len(elements)} elements.")

            # Обхождаме елементите и ги разделяме на още по-малки фрагменти, ако е нужно
            for el in elements:
                # Използваме сплитера върху текста на всеки елемент
                chunks_from_element = text_splitter.split_text(el.text)
                for chunk_text in chunks_from_element:
                    if not chunk_text or not chunk_text.strip():
                        continue
                    
                    document_chunks.append(chunk_text)
                    
                    # 🚀 ФАЗА 2 ОПТИМИЗАЦИЯ: Обогатени метаданни с page metadata
                    chunk_meta = {
                        "source_url": doc_url,
                        "source_domain": urlparse(doc_url).netloc or "unknown",
                        "content_type_original": doc_content_type,
                        "unstructured_element_type": str(type(el)).split('.')[-1][:-2],
                        "page_number": getattr(el.metadata, 'page_number', None),
                        "filename": getattr(el.metadata, 'filename', None),
                        "parent_id": getattr(el.metadata, 'parent_id', None)
                    }

                    # Добавяме page metadata ако е налично
                    if page_metadata:
                        chunk_meta.update({
                            "document_type": page_metadata.get('document_type', 'unknown'),
                            "quality_score": page_metadata.get('quality_score', 0.5),
                            "publication_date": page_metadata.get('publication_date'),
                            "program_indicators": page_metadata.get('program_indicators', []),
                            "url_indicators": page_metadata.get('url_indicators', [])
                        })
                    db_chunk_metadata_list.append(chunk_meta)

        except Exception as e_partition:
            module_logger.error(f"Failed to partition or chunk document {doc_url}: {e_partition}", exc_info=True)
            await asyncio.to_thread(upsert_document_hash, app_context.supabase_client, doc_url, current_content_hash)
            continue
        # --- END OF NEW LOGIC ---

        if not document_chunks:
            module_logger.warning(f"No chunks were generated for document {doc_url} after processing. Skipping.")
            await asyncio.to_thread(upsert_document_hash, app_context.supabase_client, doc_url, current_content_hash)
            continue
            
        db_chunk_urls = [doc_url] * len(document_chunks)
        db_chunk_indices = list(range(len(document_chunks)))
        
        if db_chunk_urls:
            await add_documents_to_supabase(
                app_ctx=app_context,
                urls=db_chunk_urls,
                chunk_numbers=db_chunk_indices, 
                contents=document_chunks,
                metadatas=db_chunk_metadata_list,
                batch_size=20 # Може да се наложи да се намали, ако фрагментите са много
            )
            total_chunks_added += len(db_chunk_urls)
            await asyncio.to_thread(upsert_document_hash, app_context.supabase_client, doc_url, current_content_hash)

    final_report_message = (f"Smart crawl for '{url}' completed. "
        f"URLs processed: {num_urls_processed}. "
        f"URLs skipped (unchanged): {num_urls_skipped}. "
        f"Total chunks stored: {total_chunks_added}.")
    
    return json.dumps({
        "success": True, "initial_url": url, "status_message": final_report_message, 
        "urls_considered": len(documents_for_processing),
        "urls_processed": num_urls_processed,
        "urls_skipped": num_urls_skipped, 
        "chunks_stored": total_chunks_added
    }, indent=2, ensure_ascii=False)


@mcp.tool()
async def perform_rag_query(
    ctx: Context,
    query: str,
    source: Optional[str] = None,
    match_count: Optional[int] = None, # Сега това е ФИНАЛНИЯТ брой резултати
    query_entities_filter: Optional[List[Dict[str, Any]]] = None,
    extract_entities_from_query_if_missing: bool = True,
    use_reranker: bool = False, # <-- 6. НОВ ПАРАМЕТЪР ЗА КОНТРОЛ НА RE-RANKER-А
    min_program_containment_threshold: Optional[float] = None,
    min_similarity_threshold: Optional[float] = None,
    weight_similarity: Optional[float] = None,
    weight_program_name: Optional[float] = None,
    weight_year: Optional[float] = None
) -> str:
    """
    Извършва RAG заявка. Опционално използва Cohere Re-ranker за подобряване на релевантността.
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "perform_rag_query: Application context or Supabase client not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "query": query, "error": error_message}, indent=2, ensure_ascii=False)

    module_logger.info(f"MCP Tool 'perform_rag_query' initiated. Query: '{query[:70]}...'. Use Reranker: {use_reranker}")
    
    # --- 7. НОВА ЛОГИКА ЗА ОПРЕДЕЛЯНЕ НА БРОЯ РЕЗУЛТАТИ ---
    final_match_count = match_count if match_count is not None else app_context.default_match_count
    
    # Ако използваме re-ranker, първоначално извличаме повече кандидати
    initial_search_count = app_context.rerank_candidate_count if use_reranker else final_match_count
    
    # Ако е зададен match_count, той определя крайния резултат, дори и за re-ranker-a
    reranker_top_n = final_match_count if use_reranker else app_context.rerank_top_n

    module_logger.debug(
        f"Search counts: final_match_count={final_match_count}, initial_search_count={initial_search_count}, "
        f"reranker_top_n={reranker_top_n}"
    )
    # --- КРАЙ НА НОВАТА ЛОГИКА ---
    
    # Вземане на параметри за скориране от контекста (без промяна)
    min_prog_thr_to_use = min_program_containment_threshold if min_program_containment_threshold is not None else app_context.default_min_program_containment_threshold
    min_sim_thr_to_use = min_similarity_threshold if min_similarity_threshold is not None else app_context.default_min_similarity_threshold
    w_sim_to_use = weight_similarity if weight_similarity is not None else app_context.default_weight_similarity
    w_prog_to_use = weight_program_name if weight_program_name is not None else app_context.default_weight_program_name
    w_year_to_use = weight_year if weight_year is not None else app_context.default_weight_year

    # Извличане и обработка на същности от заявката (без промяна)
    final_entities_for_sql_query: List[Dict[str, Any]] = [] 
    raw_extracted_query_entities: List[Dict[str, Any]] = []

    if query_entities_filter:
        raw_extracted_query_entities = query_entities_filter
    elif extract_entities_from_query_if_missing:
        if app_context.async_openai_client and app_context.program_normalizer:
            raw_extracted_query_entities = await extract_entities_from_text_llm(
                text_content=query, entity_types=app_context.default_entity_types_query,
                llm_model=app_context.llm_model_query_entities, async_openai_client=app_context.async_openai_client,
                url_for_logging=f"query_extraction_for_'{query[:30]}...'", extraction_context="user_query"
            )
        else:
            module_logger.warning("perform_rag_query: Cannot extract entities - clients not available in app_context.")
            raw_extracted_query_entities = []
    else:
        raw_extracted_query_entities = []

    if raw_extracted_query_entities:
        if app_context.gazetteer_data and app_context.program_normalizer:
            for entity_dict_from_query in raw_extracted_query_entities:
                current_enriched_entity = entity_dict_from_query.copy()
                if isinstance(entity_dict_from_query, dict) and entity_dict_from_query.get("type") == "PROGRAM_NAME":
                    entity_lemmas_set = set(entity_dict_from_query.get("lemmatized_tokens_sorted_unique", []))
                    if not entity_lemmas_set:
                        final_entities_for_sql_query.append(current_enriched_entity)
                        continue
                    best_match_canonical_name_for_query_entity = None
                    highest_containment_score_for_query_entity = 0.0
                    GAZETTEER_QUERY_ENTITY_MATCH_THRESHOLD = 0.7
                    for gazetteer_entry_item in app_context.gazetteer_data:
                        if not gazetteer_entry_item.get("normalized_lemmatized_forms"): continue
                        for gaz_form_lemmas_list_item in gazetteer_entry_item.get("normalized_lemmatized_forms", []):
                            gaz_form_lemmas_set_item = set(gaz_form_lemmas_list_item)
                            if not gaz_form_lemmas_set_item: continue
                            intersection_size_val = len(entity_lemmas_set.intersection(gaz_form_lemmas_set_item))
                            current_score_val = intersection_size_val / len(gaz_form_lemmas_set_item) if gaz_form_lemmas_set_item else 0.0
                            if current_score_val > highest_containment_score_for_query_entity:
                                highest_containment_score_for_query_entity = current_score_val
                                best_match_canonical_name_for_query_entity = gazetteer_entry_item.get("canonical_name")
                    if best_match_canonical_name_for_query_entity and highest_containment_score_for_query_entity >= GAZETTEER_QUERY_ENTITY_MATCH_THRESHOLD: 
                        current_enriched_entity["gazetteer_canonical_name"] = best_match_canonical_name_for_query_entity
                        current_enriched_entity["gazetteer_match_score"] = round(highest_containment_score_for_query_entity, 4)
                final_entities_for_sql_query.append(current_enriched_entity)
        else:
            final_entities_for_sql_query = raw_extracted_query_entities
    else:
        final_entities_for_sql_query = []

    query_canonical_program_names_for_sql_param: Optional[List[str]] = None
    temp_canonical_names_list: List[str] = []
    if final_entities_for_sql_query:
        for q_entity in final_entities_for_sql_query:
            if isinstance(q_entity, dict) and q_entity.get("type") == "PROGRAM_NAME" and q_entity.get("gazetteer_canonical_name"):
                temp_canonical_names_list.append(str(q_entity["gazetteer_canonical_name"]))
        if temp_canonical_names_list:
            query_canonical_program_names_for_sql_param = sorted(list(set(temp_canonical_names_list)))
    
    sql_source_filters: Optional[List[str]] = None
    if source and source.strip():
        sql_source_filters = [s_item.strip() for s_item in source.split(',') if s_item.strip()]

    retrieved_search_results: List[Dict[str, Any]] = []
    reranker_was_used = False

    try:
        # Първоначално търсене
        retrieved_search_results = await asyncio.to_thread(
            search_documents, 
            client=app_context.supabase_client, 
            query=query,
            match_count=initial_search_count,
            source_filters=sql_source_filters,
            query_entities_filter=final_entities_for_sql_query,
            query_program_canonical_names=query_canonical_program_names_for_sql_param, 
            min_program_containment_threshold=min_prog_thr_to_use,
            min_similarity_threshold=min_sim_thr_to_use,
            weight_similarity=w_sim_to_use,
            weight_program_name=w_prog_to_use,
            weight_year=w_year_to_use
        )
        
        module_logger.info(f"perform_rag_query: Initial search returned {len(retrieved_search_results)} candidates.")

        # --- 9. НОВ БЛОК ЗА RE-RANKING ---
        if use_reranker:
            reranker_to_use_str = os.getenv("RERANKER_TYPE", "cohere").upper()
            try:
                reranker_to_use = RerankerType[reranker_to_use_str]
            except KeyError:
                module_logger.warning(f"Invalid RERANKER_TYPE '{reranker_to_use_str}' in environment. Defaulting to COHERE.")
                reranker_to_use = RerankerType.COHERE

            if reranker_to_use != RerankerType.NONE:
                module_logger.info(f"perform_rag_query: Proceeding with {reranker_to_use.value} re-ranking.")
                reranked_results = await asyncio.to_thread(
                    rerank_documents,
                    query=query,
                    documents=retrieved_search_results,
                    reranker_type=reranker_to_use,
                    top_n=reranker_top_n,
                    cohere_client=app_context.cohere_client,
                    cross_encoder_model=None # MCP сървърът не използва локален модел за момента
                )
                retrieved_search_results = reranked_results
                reranker_was_used = True
                module_logger.info(f"perform_rag_query: Re-ranking complete. Final result count: {len(retrieved_search_results)}.")
            else:
                module_logger.info("perform_rag_query: Reranker is set to NONE. Skipping re-ranking.")
                retrieved_search_results = retrieved_search_results[:final_match_count]
        else:
            retrieved_search_results = retrieved_search_results[:final_match_count]
        
        num_results = len(retrieved_search_results) if retrieved_search_results is not None else 0
        
        scoring_params_actually_used = {
            "min_program_containment_threshold": min_prog_thr_to_use,
            "min_similarity_threshold": min_sim_thr_to_use,
            "weight_similarity": w_sim_to_use,
            "weight_program_name": w_prog_to_use,
            "weight_year": w_year_to_use
        }
        
        response_payload = {
            "success": True,
            "query": query,
            "source_filter_applied": sql_source_filters if sql_source_filters else None,
            "query_entities_processed_for_sql": final_entities_for_sql_query, 
            "query_canonical_program_names_sent_to_sql": query_canonical_program_names_for_sql_param,
            "reranker_used": reranker_was_used, 
            "scoring_parameters_used": scoring_params_actually_used,
            "results": retrieved_search_results if retrieved_search_results is not None else [],
            "count": num_results
        }
        return json.dumps(response_payload, indent=2, ensure_ascii=False)

    except Exception as e_rag_search:
        module_logger.error(f"Exception in 'perform_rag_query': {e_rag_search}", exc_info=True)
        return json.dumps({"success": False, "query": query, "error": str(e_rag_search)}, indent=2, ensure_ascii=False)


@mcp.tool()
async def enhanced_rag_query(
    ctx: Context,
    query: str,
    source: Optional[str] = None,
    match_count: Optional[int] = None,
    num_query_variations: int = 3,
    use_metadata_routing: bool = True,
    similarity_threshold: float = 0.7,
    use_reranker: bool = True
) -> str:
    """
    PHASE 2: Подобрено RAG търсене с Multi-Query Transformation и Metadata Routing.

    Този инструмент използва най-новите техники за подобряване на recall и precision:
    - Генерира алтернативни версии на заявката с GPT-4o-mini
    - Извлича метаданни от заявката за автоматично филтриране
    - Комбинира резултатите с Reciprocal Rank Fusion (RRF)
    - Прилага Cohere reranker за финално подобряване

    Args:
        query: Заявката за търсене
        source: Опционален филтър по източник
        match_count: Финален брой резултати (по подразбиране 10)
        num_query_variations: Брой алтернативни заявки за генериране (по подразбиране 3)
        use_metadata_routing: Дали да използва автоматично извличане на метаданни
        similarity_threshold: Праг за семантична прилика (по подразбиране 0.7)
        use_reranker: Дали да използва Cohere reranker за финално подобряване
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "enhanced_rag_query: Application context or Supabase client not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "query": query, "error": error_message}, indent=2, ensure_ascii=False)

    module_logger.info(f"MCP Tool 'enhanced_rag_query' initiated. Query: '{query[:70]}...'. "
                      f"Variations: {num_query_variations}, Metadata routing: {use_metadata_routing}")

    try:
        # Импортираме новите функции от utils
        from utils import enhanced_semantic_search, rerank_documents, RerankerType

        # Настройки
        final_match_count = match_count if match_count is not None else app_context.default_match_count
        max_results_per_query = 20  # Повече кандидати за всяка заявка

        # Подготвяме async OpenAI клиент
        async_openai_client = None
        if app_context.async_openai_client:
            async_openai_client = app_context.async_openai_client
        else:
            module_logger.warning("enhanced_rag_query: OpenAI client not available. Multi-query will be limited.")

        # Изпълняваме подобреното семантично търсене
        enhanced_results = await enhanced_semantic_search(
            query=query,
            supabase_client=app_context.supabase_client,
            async_openai_client=async_openai_client,
            num_query_variations=num_query_variations,
            similarity_threshold=similarity_threshold,
            max_results_per_query=max_results_per_query,
            final_top_k=final_match_count * 2 if use_reranker else final_match_count,  # Повече кандидати за reranker
            use_metadata_routing=use_metadata_routing
        )

        module_logger.info(f"enhanced_rag_query: Enhanced search returned {len(enhanced_results)} results.")

        # Прилагаме source филтър ако е зададен
        if source:
            enhanced_results = [
                doc for doc in enhanced_results
                if doc.get('metadata', {}).get('source', '').lower() == source.lower()
            ]
            module_logger.info(f"enhanced_rag_query: After source filter '{source}': {len(enhanced_results)} results.")

        # Финален reranking ако е поискан
        final_results = enhanced_results
        reranker_was_used = False

        if use_reranker and len(enhanced_results) > 0:
            reranker_to_use_str = os.getenv("RERANKER_TYPE", "cohere").upper()
            try:
                reranker_to_use = RerankerType[reranker_to_use_str]
            except KeyError:
                module_logger.warning(f"Invalid RERANKER_TYPE '{reranker_to_use_str}'. Defaulting to COHERE.")
                reranker_to_use = RerankerType.COHERE

            if reranker_to_use != RerankerType.NONE:
                module_logger.info(f"enhanced_rag_query: Applying final {reranker_to_use.value} re-ranking.")
                reranked_results = await asyncio.to_thread(
                    rerank_documents,
                    query=query,
                    documents=enhanced_results,
                    reranker_type=reranker_to_use,
                    top_n=final_match_count,
                    cohere_client=app_context.cohere_client,
                    cross_encoder_model=None
                )
                final_results = reranked_results
                reranker_was_used = True
                module_logger.info(f"enhanced_rag_query: Final re-ranking complete. Result count: {len(final_results)}.")

        # Ограничаваме до финалния брой резултати
        final_results = final_results[:final_match_count]

        response_payload = {
            "success": True,
            "query": query,
            "method": "enhanced_multi_query_with_rrf",
            "num_query_variations_used": num_query_variations,
            "metadata_routing_used": use_metadata_routing,
            "source_filter_applied": source,
            "reranker_used": reranker_was_used,
            "similarity_threshold": similarity_threshold,
            "results": final_results,
            "count": len(final_results)
        }

        module_logger.info(f"enhanced_rag_query: Completed successfully. Returning {len(final_results)} results.")
        return json.dumps(response_payload, indent=2, ensure_ascii=False)

    except Exception as e_enhanced_search:
        module_logger.error(f"Exception in 'enhanced_rag_query': {e_enhanced_search}", exc_info=True)
        return json.dumps({"success": False, "query": query, "error": str(e_enhanced_search)}, indent=2, ensure_ascii=False)


@mcp.tool()
async def small_to_big_rag_query(
    ctx: Context,
    query: str,
    source: Optional[str] = None,
    match_count: Optional[int] = None,
    similarity_threshold: float = 0.7,
    initial_chunk_count: int = 20,
    context_expansion_strategy: str = "parent_document"
) -> str:
    """
    Phase 3.1: Small-to-Big Retrieval for Enhanced RAG Performance

    This advanced retrieval method:
    1. Searches on small chunks for precision
    2. Expands to larger contexts (parent documents or surrounding chunks)
    3. Uses Cohere reranker on expanded contexts for final ranking
    4. Returns top-k results with rich context

    Args:
        query: The search query
        source: Optional source filter (domain)
        match_count: Number of final results to return (default: app context setting)
        similarity_threshold: Minimum similarity threshold for initial retrieval
        initial_chunk_count: Number of initial chunks to retrieve before expansion
        context_expansion_strategy: "parent_document", "surrounding_chunks", or "hybrid"

    Returns:
        JSON response with enhanced results containing expanded contexts
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "small_to_big_rag_query: Application context or Supabase client not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "query": query, "error": error_message}, indent=2, ensure_ascii=False)

    module_logger.info(f"MCP Tool 'small_to_big_rag_query' initiated. Query: '{query[:70]}...'. "
                      f"Strategy: {context_expansion_strategy}, Initial chunks: {initial_chunk_count}")

    try:
        # Settings
        final_match_count = match_count if match_count is not None else app_context.default_match_count

        # Get OpenAI client for query variations
        async_openai_client = None
        if app_context.async_openai_client:
            async_openai_client = app_context.async_openai_client
        else:
            module_logger.warning("small_to_big_rag_query: OpenAI client not available. Using basic retrieval.")

        # Execute small-to-big retrieval
        small_to_big_results = await small_to_big_retrieval(
            query=query,
            supabase_client=app_context.supabase_client,
            async_openai_client=async_openai_client,
            similarity_threshold=similarity_threshold,
            initial_chunk_count=initial_chunk_count,
            final_top_k=final_match_count * 2,  # Get more candidates for source filtering
            context_expansion_strategy=context_expansion_strategy
        )

        module_logger.info(f"small_to_big_rag_query: Small-to-big retrieval returned {len(small_to_big_results)} results.")

        # Apply source filter if specified
        if source:
            small_to_big_results = [
                doc for doc in small_to_big_results
                if doc.get('metadata', {}).get('source', '').lower() == source.lower()
            ]
            module_logger.info(f"small_to_big_rag_query: After source filter '{source}': {len(small_to_big_results)} results.")

        # Limit to final count
        final_results = small_to_big_results[:final_match_count]

        response_payload = {
            "success": True,
            "query": query,
            "method": "small_to_big_retrieval",
            "context_expansion_strategy": context_expansion_strategy,
            "initial_chunk_count": initial_chunk_count,
            "similarity_threshold": similarity_threshold,
            "source_filter": source,
            "results": final_results,
            "count": len(final_results)
        }

        module_logger.info(f"small_to_big_rag_query: Completed successfully. Returning {len(final_results)} results.")
        return json.dumps(response_payload, indent=2, ensure_ascii=False)

    except Exception as e_small_to_big:
        module_logger.error(f"Exception in 'small_to_big_rag_query': {e_small_to_big}", exc_info=True)
        return json.dumps({"success": False, "query": query, "error": str(e_small_to_big)}, indent=2, ensure_ascii=False)


@mcp.tool()
async def agent_based_rag_query(
    ctx: Context,
    query: str,
    source: Optional[str] = None,
    match_count: Optional[int] = None,
    similarity_threshold: float = 0.7,
    max_iterations: int = 3,
    enable_multi_step: bool = True
) -> str:
    """
    Phase 3.2: Agent-based Retrieval for Complex Query Processing

    This intelligent agent system:
    1. Analyzes query complexity and decomposes complex queries into sub-queries
    2. Performs multi-step retrieval operations with reasoning chains
    3. Synthesizes results from multiple documents and searches
    4. Uses different retrieval strategies based on query type

    Args:
        query: The search query (required)
        source: Optional source filter (e.g., domain name)
        match_count: Number of results to return (default: 5)
        similarity_threshold: Minimum similarity threshold (default: 0.7)
        max_iterations: Maximum number of retrieval iterations (default: 3)
        enable_multi_step: Whether to enable multi-step reasoning (default: True)

    Returns:
        JSON response with agent-based retrieval results and reasoning chain
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "agent_based_rag_query: Application context or Supabase client not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "query": query, "error": error_message}, indent=2, ensure_ascii=False)

    module_logger.info(f"MCP Tool 'agent_based_rag_query' initiated. Query: '{query[:70]}...'. "
                      f"Max iterations: {max_iterations}, Multi-step: {enable_multi_step}")

    try:
        # Settings
        final_match_count = match_count if match_count is not None else app_context.default_match_count

        # Get OpenAI client for LLM operations
        async_openai_client = None
        if app_context.async_openai_client:
            async_openai_client = app_context.async_openai_client
        else:
            module_logger.warning("agent_based_rag_query: OpenAI client not available. Using simplified agent mode.")

        # Execute agent-based retrieval
        agent_results = await agent_based_retrieval(
            query=query,
            supabase_client=app_context.supabase_client,
            async_openai_client=async_openai_client,
            max_iterations=max_iterations,
            similarity_threshold=similarity_threshold,
            final_top_k=final_match_count * 2,  # Get more candidates for source filtering
            enable_multi_step=enable_multi_step
        )

        module_logger.info(f"agent_based_rag_query: Agent-based retrieval returned {len(agent_results)} results.")

        # Apply source filter if specified
        if source:
            agent_results = [
                doc for doc in agent_results
                if doc.get('metadata', {}).get('source', '').lower() == source.lower()
            ]
            module_logger.info(f"agent_based_rag_query: After source filter '{source}': {len(agent_results)} results.")

        # Limit to final count
        final_results = agent_results[:final_match_count]

        # Extract agent metadata if available
        agent_analysis = {}
        if final_results and final_results[0].get('agent_metadata'):
            agent_metadata = final_results[0]['agent_metadata']
            agent_analysis = {
                "total_reasoning_steps": agent_metadata.get('total_steps', 0),
                "reasoning_chain": agent_metadata.get('reasoning_chain', []),
                "synthesis_method": agent_metadata.get('synthesis_method', 'unknown'),
                "deduplication_ratio": round(agent_metadata.get('deduplication_ratio', 1.0), 3)
            }

        response_payload = {
            "success": True,
            "method": "agent_based_retrieval",
            "query": query,
            "parameters": {
                "max_iterations": max_iterations,
                "similarity_threshold": similarity_threshold,
                "enable_multi_step": enable_multi_step
            },
            "agent_analysis": agent_analysis,
            "source_filter": source,
            "results": final_results,
            "count": len(final_results)
        }

        module_logger.info(f"agent_based_rag_query: Completed successfully. Returning {len(final_results)} results.")
        return json.dumps(response_payload, indent=2, ensure_ascii=False)

    except Exception as e_agent_based:
        module_logger.error(f"Exception in 'agent_based_rag_query': {e_agent_based}", exc_info=True)
        return json.dumps({"success": False, "query": query, "error": str(e_agent_based)}, indent=2, ensure_ascii=False)


@mcp.tool()
async def get_available_sources(ctx: Context) -> str:
    """Извлича списък с уникалните източници (домейни) от индексираните документи."""
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "get_available_sources: Application context or Supabase client not initialized."
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    supabase_instance_tool = app_context.supabase_client
    try:
        rpc_response = await asyncio.to_thread(
            supabase_instance_tool.rpc('get_distinct_sources_from_metadata').execute
        )
        if hasattr(rpc_response, 'data') and rpc_response.data is not None:
            sources_list = sorted([item['source'] for item in rpc_response.data if item and isinstance(item, dict) and item.get('source')])
            return json.dumps({"success": True, "sources": sources_list, "count": len(sources_list)}, indent=2, ensure_ascii=False)
        else:
            raise ValueError("RPC failed or no data, attempting fallback.")

    except Exception as e_rpc_call:
        module_logger.warning(f"Error during RPC call for sources: {e_rpc_call}. Attempting fallback.")
        try:
            all_metadata_response = await asyncio.to_thread(
                supabase_instance_tool.table('crawled_pages').select('metadata').execute  # type: ignore
            )
            if hasattr(all_metadata_response, 'data') and all_metadata_response.data is not None:
                unique_sources_set: set[str] = set()
                for record in all_metadata_response.data:
                    if record and isinstance(record, dict) and record.get('metadata') and isinstance(record.get('metadata'), dict) and record['metadata'].get('source'):
                        unique_sources_set.add(str(record['metadata']['source']))
                
                sources_list_fallback = sorted(list(unique_sources_set))
                return json.dumps({"success": True, "sources": sources_list_fallback, "count": len(sources_list_fallback), "retrieval_method": "fallback_direct_query"}, indent=2, ensure_ascii=False)
            else:
                return json.dumps({"success": False, "error": "Failed to retrieve sources using both RPC and direct fallback query."}, indent=2, ensure_ascii=False)
        except Exception as e_fallback_query:
            return json.dumps({"success": False, "error": f"Original RPC error: {str(e_rpc_call)}. Fallback query error: {str(e_fallback_query)}"}, indent=2, ensure_ascii=False)

@mcp.tool()
async def smart_rag_query_tool(
    ctx: Context,
    query: str,
    source: Optional[str] = None,
    match_count: Optional[int] = None,
    similarity_threshold: float = 0.7,
    force_strategy: Optional[str] = None,
    include_metadata: bool = False
) -> str:
    """
    Phase 3.3: Smart RAG Query - Intelligent automatic routing

    Automatically classifies queries and routes them to the optimal retrieval strategy.
    This is the recommended tool for all RAG queries as it provides the best performance.

    Args:
        query: The search query in Bulgarian or English
        source: Optional filter by source domain (e.g., "esif.bg")
        match_count: Number of results to return (default: 5)
        similarity_threshold: Minimum similarity threshold (default: 0.7)
        force_strategy: Force specific strategy ("enhanced_semantic", "small_to_big", "agent_based")
        include_metadata: Include detailed routing metadata in response

    Returns:
        JSON string with results and routing information
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "smart_rag_query_tool: Application context or Supabase client not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"MCP Tool 'smart_rag_query_tool' called with query: '{query[:50]}...'")

        # Validate parameters
        final_top_k = match_count if match_count is not None else 5
        final_top_k = max(1, min(final_top_k, 20))  # Limit between 1-20

        if force_strategy and force_strategy not in ["enhanced_semantic", "small_to_big", "agent_based"]:
            return json.dumps({
                "success": False,
                "error": f"Invalid force_strategy: {force_strategy}. Must be one of: enhanced_semantic, small_to_big, agent_based"
            }, indent=2, ensure_ascii=False)

        # Execute smart RAG query
        result = await smart_rag_query(
            query=query,
            supabase_client=app_context.supabase_client,
            async_openai_client=app_context.async_openai_client,
            similarity_threshold=similarity_threshold,
            final_top_k=final_top_k,
            force_strategy=force_strategy,
            include_metadata=include_metadata
        )

        # Filter by source if specified
        if source and result.get("results"):
            filtered_results = []
            for doc in result["results"]:
                doc_source = doc.get("source", "")
                if source.lower() in doc_source.lower():
                    filtered_results.append(doc)
            result["results"] = filtered_results
            result["total_results"] = len(filtered_results)
            result["filtered_by_source"] = source

        # Format response
        response = {
            "success": True,
            "query": query,
            "strategy_used": result.get("strategy_used", "unknown"),
            "total_results": result.get("total_results", 0),
            "avg_similarity": result.get("avg_similarity", 0.0),
            "execution_time": result.get("execution_time", 0.0),
            "results": result.get("results", [])
        }

        if include_metadata and "routing_metadata" in result:
            response["routing_metadata"] = result["routing_metadata"]

        if "error" in result:
            response["error"] = result["error"]
            response["success"] = False

        module_logger.info(f"smart_rag_query_tool: Completed successfully with {response['total_results']} results "
                          f"using {response['strategy_used']} strategy")

        return json.dumps(response, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"smart_rag_query_tool: Error during smart RAG query: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)


@mcp.tool()
async def ultra_smart_rag_query_tool(
    ctx: Context,
    query: str,
    source: Optional[str] = None,
    match_count: Optional[int] = None,
    similarity_threshold: float = 0.7,
    enable_all_optimizations: bool = True,
    include_metadata: bool = False
) -> str:
    """
    Phase 4: Ultra Smart RAG Query - Maximum optimization for 99% accuracy

    This is the most advanced RAG tool combining all Phase 4 optimizations:
    - Content enhancement with quality scoring
    - Hybrid search (semantic + keyword)
    - Advanced ranking algorithms (Phase 4.3)
    - Query understanding and intent detection (Phase 4.4)
    - Intelligent multi-stage reranking (Phase 4.5)
    - Entity extraction and enrichment
    - Semantic tagging and classification

    Args:
        query: The search query in Bulgarian or English
        source: Optional filter by source domain (e.g., "esif.bg")
        match_count: Number of results to return (default: 5)
        similarity_threshold: Minimum similarity threshold (default: 0.7)
        enable_all_optimizations: Enable all Phase 4 optimizations (default: True)
        include_metadata: Include detailed metadata in response (default: False)

    Returns:
        JSON string with ultra-optimized results
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "ultra_smart_rag_query_tool: Application context or Supabase client not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"🚀 MCP Tool 'ultra_smart_rag_query_tool' called with query: '{query[:50]}...'")

        # Validate parameters
        final_top_k = match_count if match_count is not None else 5
        final_top_k = max(1, min(final_top_k, 20))  # Limit between 1-20

        # Execute ultra smart RAG query
        result = await ultra_smart_rag_query(
            query=query,
            supabase_client=app_context.supabase_client,
            async_openai_client=app_context.async_openai_client,
            similarity_threshold=similarity_threshold,
            final_top_k=final_top_k,
            enable_all_optimizations=enable_all_optimizations
        )

        # Filter by source if specified
        if source and result.get("results"):
            filtered_results = []
            for doc in result["results"]:
                doc_source = doc.get("source", "")
                if source.lower() in doc_source.lower():
                    filtered_results.append(doc)
            result["results"] = filtered_results
            result["total_results"] = len(filtered_results)
            result["filtered_by_source"] = source

        # Format response with Phase 4 enhancements
        response = {
            "success": True,
            "query": query,
            "phase": "4.0",
            "strategy_used": result.get("strategy_used", "unknown"),
            "optimizations_enabled": result.get("optimizations_enabled", False),
            "total_results": result.get("total_results", 0),
            "avg_similarity": result.get("avg_similarity", 0.0),
            "avg_quality_score": result.get("avg_quality_score", 0.5),
            "execution_time": result.get("execution_time", 0.0),
            "results": []
        }

        # Enhanced result formatting
        for doc in result.get("results", []):
            enhanced_doc = {
                "id": doc.get("id"),
                "title": doc.get("title", "No title"),
                "content": doc.get("content", ""),
                "url": doc.get("url", ""),
                "similarity": doc.get("similarity", 0.0),
                "quality_score": doc.get("quality_score", 0.5),
                "content_type": doc.get("content_type", "unknown"),
                "metadata": doc.get("metadata", {})
            }

            # Add Phase 4 enhancements if available
            if include_metadata:
                enhanced_doc.update({
                    "key_concepts": doc.get("key_concepts", []),
                    "semantic_tags": doc.get("semantic_tags", []),
                    "entities": doc.get("entities", []),
                    "enhancement_metadata": doc.get("enhancement_metadata", {})
                })

            response["results"].append(enhanced_doc)

        if "error" in result:
            response["error"] = result["error"]
            response["success"] = False

        module_logger.info(f"ultra_smart_rag_query_tool: Completed successfully with {response['total_results']} results "
                          f"(avg quality: {response['avg_quality_score']:.3f})")

        return json.dumps(response, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"ultra_smart_rag_query_tool: Error during ultra smart RAG query: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)


@mcp.tool()
async def get_rag_performance_tool(ctx: Context) -> str:
    """
    Phase 3.3: Get RAG Performance Summary

    Returns comprehensive performance statistics for all RAG strategies,
    including recommendations for optimization.

    Returns:
        JSON string with performance statistics and recommendations
    """
    global app_context
    if not app_context:
        error_message = "get_rag_performance_tool: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info("MCP Tool 'get_rag_performance_tool' called")

        # Get performance summary
        performance_summary = await get_rag_performance_summary()

        # Format response
        response = {
            "success": True,
            "performance_summary": performance_summary
        }

        if "error" in performance_summary:
            response["success"] = False
            response["error"] = performance_summary["error"]

        module_logger.info("get_rag_performance_tool: Completed successfully")

        return json.dumps(response, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"get_rag_performance_tool: Error getting performance summary: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)


@mcp.tool()
async def consultant_agent_query(
    ctx: Context,
    query: str,
    client_profile: Optional[str] = None,
    include_recommendations: bool = True,
    include_insights: bool = True,
    final_top_k: int = 5
) -> str:
    """
    Phase 5.0: Smart Consultant Agent - Professional European funding consultation

    This tool mimics the behavior of professional European funding consultants:
    - Intelligent query decomposition for complex multi-part questions
    - Multi-dimensional analysis (topic + time + location + budget)
    - Professional recommendations with next steps and warnings
    - Strategic insights and market overview
    - Comparative analysis between programs

    Args:
        query: Complex consultant-level query in Bulgarian or English
        client_profile: Optional client context (e.g., "МСП в транспорта, 50 служители")
        include_recommendations: Generate professional recommendations
        include_insights: Generate strategic consultant insights
        final_top_k: Number of programs to analyze (1-10)

    Returns:
        Comprehensive consultant analysis with recommendations and insights
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "consultant_agent_query: Application context or Supabase client not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"🧠 MCP Tool 'consultant_agent_query' called with query: '{query[:50]}...'")

        # Import consultant agent
        from .consultant_agent import smart_consultant

        # Validate parameters
        final_top_k = max(1, min(final_top_k, 10))  # Limit between 1-10

        # Parse client profile if provided
        client_context = None
        if client_profile:
            client_context = {"profile": client_profile}

        # Step 1: Analyze query like a consultant
        consultant_query = await smart_consultant.analyze_consultant_query(
            query=query,
            async_openai_client=app_context.async_openai_client,
            client_context=client_context
        )

        module_logger.info(f"📊 Query analyzed as: {consultant_query.query_type.value} "
                          f"with {len(consultant_query.sub_queries)} sub-queries")

        # Step 2: Execute consultant-level search
        consultant_result = await smart_consultant.execute_consultant_search(
            consultant_query=consultant_query,
            supabase_client=app_context.supabase_client,
            async_openai_client=app_context.async_openai_client,
            final_top_k=final_top_k
        )

        # Step 3: Format response for user consumption
        if consultant_result.get("success"):
            response_data = {
                "success": True,
                "consultant_analysis": consultant_result["query_analysis"],
                "programs_found": len(consultant_result["results"]),
                "results": consultant_result["results"]
            }

            # Add recommendations if requested
            if include_recommendations and consultant_result.get("recommendations"):
                response_data["professional_recommendations"] = [
                    {
                        "program": rec.program_name,
                        "relevance": f"{rec.relevance_score:.1%}",
                        "eligibility": f"{rec.eligibility_match:.1%}",
                        "urgency": rec.deadline_urgency,
                        "budget_fit": rec.budget_fit,
                        "reason": rec.recommendation_reason,
                        "next_steps": rec.next_steps,
                        "warnings": rec.warnings
                    }
                    for rec in consultant_result["recommendations"]
                ]

            # Add insights if requested
            if include_insights and consultant_result.get("consultant_insights"):
                response_data["strategic_insights"] = consultant_result["consultant_insights"]

            response_data["metadata"] = consultant_result["metadata"]

            module_logger.info(f"✅ Consultant query completed successfully: "
                             f"{len(consultant_result['results'])} programs analyzed")

            return json.dumps(response_data, indent=2, ensure_ascii=False)
        else:
            error_msg = consultant_result.get("error", "Unknown consultant error")
            module_logger.error(f"❌ Consultant query failed: {error_msg}")
            return json.dumps({
                "success": False,
                "error": error_msg,
                "query_analysis": consultant_result.get("query_analysis", {})
            }, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"consultant_agent_query: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": "Consultant analysis failed. Please try again.",
            "technical_error": str(e)
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def incremental_content_update(ctx: Context) -> str:
    """
    🚀 ФАЗА 5.1: Интелигентни инкрементални обновления
    Проверява за промени в съдържанието и обновява само променените страници.
    Оптимизирано за Supabase free plan с минимизиране на bandwidth.
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "incremental_content_update: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info("🚀 Започвам интелигентни инкрементални обновления...")

        # Import incremental updates module
        from .incremental_updates import create_incremental_update_tool

        # Извършване на интелигентно обновление
        result = await create_incremental_update_tool()

        module_logger.info(f"✅ Инкрементални обновления завършени: {result['details']}")

        return json.dumps(result, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"incremental_content_update: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def check_content_freshness_tool(
    ctx: Context,
    urls: Optional[List[str]] = None,
    max_urls: int = 20
) -> str:
    """
    📊 Проверява актуалността на съдържанието
    Анализира кои страници са остарели и се нуждаят от обновление.
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "check_content_freshness_tool: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"📊 Проверявам актуалността на съдържанието...")

        # Import incremental updates module
        from .incremental_updates import check_content_freshness, SmartContentMonitor

        # Ако не са подадени URL-и, вземи от базата данни
        if not urls:
            async with SmartContentMonitor(app_context.supabase_client) as monitor:
                urls = await monitor.get_urls_for_update(max_urls=max_urls)

        if not urls:
            return json.dumps({
                "success": True,
                "message": "Няма URL-и за проверка",
                "freshness_report": {
                    "total_urls": 0,
                    "fresh_content": 0,
                    "stale_content": 0,
                    "errors": 0,
                    "details": []
                }
            }, indent=2, ensure_ascii=False)

        # Проверка на актуалността
        freshness_report = await check_content_freshness(urls[:max_urls])

        module_logger.info(f"✅ Проверка на актуалността завършена: "
                          f"{freshness_report['stale_content']} остарели от {freshness_report['total_urls']}")

        return json.dumps({
            "success": True,
            "message": f"Проверени {freshness_report['total_urls']} URL-и",
            "freshness_report": freshness_report
        }, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"check_content_freshness_tool: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def apply_contextual_retrieval_upgrade(
    ctx: Context,
    max_chunks: int = 50
) -> str:
    """
    🚀 ФАЗА 5.2: Contextual Retrieval (Anthropic 2024)
    Прилага революционната техника за 67% подобрение на RAG точността.
    Добавя контекст към chunks преди embedding за подобрена семантична точност.
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "apply_contextual_retrieval_upgrade: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"🚀 Започвам Contextual Retrieval upgrade за {max_chunks} chunks...")

        # Import contextual retrieval module
        from .contextual_retrieval import apply_contextual_retrieval_upgrade as apply_upgrade

        # Прилагане на contextual retrieval upgrade
        result = await apply_upgrade(max_chunks=max_chunks)

        if result.get("success"):
            module_logger.info(f"✅ Contextual Retrieval upgrade завършен: "
                              f"{result['updated_chunks']} chunks обновени с "
                              f"{result['total_context_tokens']} контекстуални токена")
        else:
            module_logger.error(f"❌ Contextual Retrieval upgrade неуспешен: {result.get('error')}")

        return json.dumps(result, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"apply_contextual_retrieval_upgrade: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def validate_contextual_retrieval_performance(ctx: Context) -> str:
    """
    📊 Валидира производителността на Contextual Retrieval системата
    Анализира покритието с контекст и средния брой контекстуални токени.
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "validate_contextual_retrieval_performance: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info("📊 Валидирам производителността на Contextual Retrieval...")

        # Import contextual retrieval module
        from .contextual_retrieval import validate_contextual_retrieval_performance as validate_performance

        # Валидация на производителността
        result = await validate_performance()

        module_logger.info(f"✅ Contextual Retrieval валидация завършена: "
                          f"{result.get('context_coverage', 0):.1f}% покритие с контекст")

        return json.dumps({
            "success": True,
            "message": "Contextual Retrieval валидация завършена",
            "performance_metrics": result
        }, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"validate_contextual_retrieval_performance: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def multi_vector_search(
    ctx: Context,
    query: str,
    semantic_weight: float = 0.4,
    topic_weight: float = 0.2,
    temporal_weight: float = 0.15,
    geographic_weight: float = 0.1,
    budget_weight: float = 0.1,
    program_type_weight: float = 0.05,
    limit: int = 10
) -> str:
    """
    🎯 ФАЗА 5.3: Multi-Vector Retrieval System
    Многомерно търсене което комбинира семантично търсене с филтриране по:
    - Теми/области (topic_weight)
    - Време/години (temporal_weight)
    - География/региони (geographic_weight)
    - Бюджет/финансиране (budget_weight)
    - Тип програма (program_type_weight)
    - Семантично сходство (semantic_weight)

    Параметри:
    - query: Заявката за търсене
    - semantic_weight: Тегло за семантично сходство (по подразбиране 0.4)
    - topic_weight: Тегло за тематично съответствие (по подразбиране 0.2)
    - temporal_weight: Тегло за времево съответствие (по подразбиране 0.15)
    - geographic_weight: Тегло за географско съответствие (по подразбиране 0.1)
    - budget_weight: Тегло за бюджетно съответствие (по подразбиране 0.1)
    - program_type_weight: Тегло за тип програма (по подразбиране 0.05)
    - limit: Максимален брой резултати (по подразбиране 10)
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "multi_vector_search: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"🎯 Започвам Multi-Vector Search за: {query[:50]}...")

        # Import multi-vector retrieval module
        from .multi_vector_retrieval import perform_multi_vector_search

        # Извършване на многомерно търсене
        result = await perform_multi_vector_search(
            query_text=query,
            semantic_weight=semantic_weight,
            topic_weight=topic_weight,
            temporal_weight=temporal_weight,
            geographic_weight=geographic_weight,
            budget_weight=budget_weight,
            program_type_weight=program_type_weight,
            limit=limit
        )

        if result.get("success"):
            module_logger.info(f"✅ Multi-Vector Search завършен: {result.get('results_count')} резултата")
        else:
            module_logger.error(f"❌ Multi-Vector Search неуспешен: {result.get('error')}")

        return json.dumps(result, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"multi_vector_search: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def analyze_multi_vector_performance(ctx: Context, test_queries: Optional[list] = None) -> str:
    """
    📊 Анализира производителността на Multi-Vector Retrieval системата
    Тества различни комбинации от тегла и измерва точността.

    Параметри:
    - test_queries: Списък с тестови заявки (по подразбиране използва предефинирани)
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "analyze_multi_vector_performance: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info("📊 Анализирам производителността на Multi-Vector Retrieval...")

        # Предефинирани тестови заявки ако не са подадени
        if not test_queries:
            test_queries = [
                "иновации в София 2024",
                "туризъм Пловдив ОПРР",
                "зелена енергия Interreg",
                "МСП дигитализация бюджет",
                "образование Horizon Europe"
            ]

        # Import multi-vector retrieval module
        from .multi_vector_retrieval import perform_multi_vector_search

        performance_results = []

        # Тестване на различни конфигурации на тегла
        weight_configs = [
            {"name": "Semantic Focus", "semantic": 0.6, "topic": 0.2, "temporal": 0.1, "geographic": 0.05, "budget": 0.03, "program_type": 0.02},
            {"name": "Topic Focus", "semantic": 0.3, "topic": 0.4, "temporal": 0.15, "geographic": 0.1, "budget": 0.03, "program_type": 0.02},
            {"name": "Balanced", "semantic": 0.4, "topic": 0.2, "temporal": 0.15, "geographic": 0.1, "budget": 0.1, "program_type": 0.05},
            {"name": "Geographic Focus", "semantic": 0.3, "topic": 0.15, "temporal": 0.1, "geographic": 0.3, "budget": 0.1, "program_type": 0.05}
        ]

        for config in weight_configs:
            config_results = []

            for query in test_queries:
                result = await perform_multi_vector_search(
                    query_text=query,
                    semantic_weight=config["semantic"],
                    topic_weight=config["topic"],
                    temporal_weight=config["temporal"],
                    geographic_weight=config["geographic"],
                    budget_weight=config["budget"],
                    program_type_weight=config["program_type"],
                    limit=5
                )

                if result.get("success"):
                    avg_score = sum(r["total_score"] for r in result["results"]) / len(result["results"]) if result["results"] else 0
                    config_results.append({
                        "query": query,
                        "avg_score": round(avg_score, 3),
                        "results_count": result["results_count"]
                    })

            performance_results.append({
                "config_name": config["name"],
                "weights": {k: v for k, v in config.items() if k != "name"},
                "query_results": config_results,
                "avg_performance": round(sum(r["avg_score"] for r in config_results) / len(config_results), 3) if config_results else 0
            })

        # Сортиране по производителност
        performance_results.sort(key=lambda x: x["avg_performance"], reverse=True)

        module_logger.info(f"✅ Multi-Vector Performance анализ завършен")

        return json.dumps({
            "success": True,
            "message": "Multi-Vector Retrieval производителност анализирана",
            "test_queries_count": len(test_queries),
            "weight_configs_tested": len(weight_configs),
            "performance_results": performance_results,
            "best_config": performance_results[0] if performance_results else None
        }, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"analyze_multi_vector_performance: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def get_system_health_dashboard(ctx: Context) -> str:
    """
    📊 ФАЗА 5.4: Real-Time Monitoring Dashboard
    Получава пълен dashboard за здравето на системата включително:
    - Производителност на RAG заявки
    - Свежест на данните в Supabase
    - Системни метрики и алерти
    - Препоръки за оптимизация
    - Тенденции в производителността
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "get_system_health_dashboard: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info("📊 Генерирам System Health Dashboard...")

        # Import monitoring dashboard module
        from .monitoring_dashboard import get_system_health_dashboard as get_dashboard

        # Получаване на dashboard данни
        result = await get_dashboard()

        if result.get("success"):
            module_logger.info(f"✅ System Health Dashboard генериран успешно")
            dashboard = result["dashboard"]

            # Логиране на важни метрики
            module_logger.info(f"📊 Общ статус: {dashboard['overall_status']}")
            module_logger.info(f"⏱️ Uptime: {dashboard['uptime_hours']:.1f} часа")
            module_logger.info(f"🚨 Алерти: {len(dashboard['alerts'])}")
            module_logger.info(f"💡 Препоръки: {len(dashboard['recommendations'])}")
        else:
            module_logger.error(f"❌ System Health Dashboard неуспешен: {result.get('error')}")

        return json.dumps(result, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"get_system_health_dashboard: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def record_performance_metric(
    ctx: Context,
    metric_type: str,
    value: float,
    operation_type: str = "general"
) -> str:
    """
    📈 Записва метрика за производителност в мониторинг системата

    Параметри:
    - metric_type: Тип метрика ("rag_query", "crawling", "database")
    - value: Стойност на метриката (време в секунди, процент успеваемост и др.)
    - operation_type: Тип операция за по-детайлно проследяване
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "record_performance_metric: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"📈 Записвам performance metric: {metric_type} = {value}")

        # Import monitoring dashboard module
        from .monitoring_dashboard import get_global_monitor

        monitor = get_global_monitor()

        # Записване на метриката според типа
        if metric_type == "rag_query":
            await monitor.record_rag_query_performance(
                query_time=value,
                success=True,  # Предполагаме успех ако се записва
                query_type=operation_type
            )
        elif metric_type == "crawling":
            await monitor.record_crawling_performance(
                success_rate=value,
                pages_crawled=1,  # Примерна стойност
                errors=0
            )
        elif metric_type == "database":
            await monitor.record_database_performance(
                response_time=value,
                operation_type=operation_type
            )
        else:
            return json.dumps({
                "success": False,
                "error": f"Неизвестен metric_type: {metric_type}. Поддържани: rag_query, crawling, database"
            }, indent=2, ensure_ascii=False)

        module_logger.info(f"✅ Performance metric записана успешно")

        return json.dumps({
            "success": True,
            "message": f"Performance metric записана: {metric_type} = {value}",
            "metric_type": metric_type,
            "value": value,
            "operation_type": operation_type
        }, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"record_performance_metric: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def get_performance_trends(ctx: Context, hours: int = 24) -> str:
    """
    📈 Получава тенденции в производителността за зададен период

    Параметри:
    - hours: Брой часове назад за анализ (по подразбиране 24)
    """
    global app_context
    if not app_context or not app_context.supabase_client:
        error_message = "get_performance_trends: Application context not initialized."
        module_logger.error(error_message)
        return json.dumps({"success": False, "error": error_message}, indent=2, ensure_ascii=False)

    try:
        module_logger.info(f"📈 Получавам performance trends за последните {hours} часа...")

        # Import monitoring dashboard module
        from .monitoring_dashboard import get_global_monitor

        monitor = get_global_monitor()
        trends = await monitor.get_performance_trends(hours=hours)

        module_logger.info(f"✅ Performance trends получени: {trends.get('metrics_count', 0)} метрики")

        return json.dumps({
            "success": True,
            "message": f"Performance trends за последните {hours} часа",
            "trends": trends
        }, indent=2, ensure_ascii=False)

    except Exception as e:
        error_message = f"get_performance_trends: Unexpected error: {str(e)}"
        module_logger.error(error_message, exc_info=True)
        return json.dumps({
            "success": False,
            "error": error_message
        }, indent=2, ensure_ascii=False)

@mcp.tool()
async def long_sleep_test(ctx: Context, sleep_duration: int = 10) -> str:
    """Тестов инструмент, който просто изчаква зададен брой секунди."""
    module_logger.info(f"MCP Tool 'long_sleep_test' called. Sleeping for {sleep_duration} seconds...")
    await asyncio.sleep(sleep_duration)
    response_message = f"Slept for {sleep_duration} seconds. Test operation complete."
    return json.dumps({"success": True, "message": response_message, "duration_slept": sleep_duration}, indent=2, ensure_ascii=False)

# --- Основна функция за стартиране на MCP сървъра (без промяна) ---
async def main_mcp_server_with_lifecycle_management():
    """Основна функция за стартиране на MCP сървъра."""
    module_logger.info("--- Main function for MCP server (with lifecycle management) entered ---")
    try:
        await initialize_application_resources() 
        transport_protocol_env = os.getenv("TRANSPORT", "sse").lower()
        module_logger.info(f"MCP Transport protocol selected: '{transport_protocol_env}'")

        if transport_protocol_env == 'sse':
            await mcp.run_sse_async()
        elif transport_protocol_env == 'stdio':
            await mcp.run_stdio_async()
        else:
            await mcp.run_sse_async()
            
    except KeyboardInterrupt:
        module_logger.info("MCP APP LIFECYCLE: KeyboardInterrupt received. Initiating shutdown...")
    except SystemExit as e_sysexit:
        module_logger.critical(f"MCP APP LIFECYCLE: SystemExit called: {e_sysexit}. Application will terminate.")
    except Exception as e_main_server_loop:
        module_logger.critical(f"MCP APP LIFECYCLE: Unhandled exception in main loop: {e_main_server_loop}", exc_info=True)
    finally:
        module_logger.info("MCP APP LIFECYCLE: Server loop terminated. Proceeding to cleanup...")
        await cleanup_application_resources() 
    
    module_logger.info("--- Main function for MCP server (with lifecycle management) finished ---")

# --- Точка на влизане в скрипта (без промяна) ---
if __name__ == "__main__":
    module_logger.info(f"--- Script '{__file__}' execution started as __main__ ---")
    try:
        asyncio.run(main_mcp_server_with_lifecycle_management())
    except Exception as e_top_level_run:
        module_logger.critical(f"CRITICAL UNHANDLED EXCEPTION at top level asyncio.run: {e_top_level_run}", exc_info=True)
        if app_context:
            module_logger.info("Attempting emergency resource cleanup...")
            asyncio.run(cleanup_application_resources())

    module_logger.info(f"--- Script '{__file__}' execution finished (__main__) ---")

# --- END OF FILE: src/crawl4ai_mcp.py ---