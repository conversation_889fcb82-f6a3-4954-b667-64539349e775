"""
🚀 ФАЗА 5.1: СИСТЕМА ЗА ИНКРЕМЕНТАЛНИ ОБНОВЛЕНИЯ
Интелигентна система за детекция на промени и оптимизация на Supabase storage/bandwidth

Автор: Augment Agent (Автономно развитие)
Дата: 2025-07-04
"""

import asyncio
import logging
import hashlib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Set, Any
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
from urllib.parse import urlparse, urljoin

from .utils import (
    get_supabase_client, 
    calculate_sha256, 
    get_document_hash_info, 
    upsert_document_hash
)

# Настройка на логинг
logger = logging.getLogger(__name__)

class ContentType(Enum):
    """Типове съдържание за различни стратегии за обновление"""
    STATIC_PAGE = "static_page"          # Статични страници - рядко се променят
    NEWS_ARTICLE = "news_article"        # Новини - често се променят
    PROGRAM_INFO = "program_info"        # Информация за програми - средно се променя
    DEADLINE_INFO = "deadline_info"      # Крайни срокове - критично за актуалност
    CONTACT_INFO = "contact_info"        # Контактна информация - рядко се променя

class UpdatePriority(Enum):
    """Приоритет за обновление"""
    CRITICAL = "critical"    # Ежедневно
    HIGH = "high"           # Всеки 3 дни
    MEDIUM = "medium"       # Седмично
    LOW = "low"            # Месечно

@dataclass
class ContentChangeInfo:
    """Информация за промяна в съдържанието"""
    url: str
    old_hash: Optional[str]
    new_hash: str
    content_type: ContentType
    priority: UpdatePriority
    last_modified: Optional[datetime]
    content_size: int
    change_detected: bool
    http_status: int
    error_message: Optional[str] = None

@dataclass
class BatchUpdateResult:
    """Резултат от batch обновление"""
    total_urls: int
    changed_urls: int
    unchanged_urls: int
    error_urls: int
    bandwidth_saved: int  # bytes
    processing_time: float
    changes: List[ContentChangeInfo]

class SmartContentMonitor:
    """
    🧠 Интелигентен мониторинг на съдържанието с оптимизация за Supabase free plan
    """
    
    def __init__(self, supabase_client):
        self.supabase_client = supabase_client
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Конфигурация за различни типове съдържание
        self.content_type_config = {
            ContentType.STATIC_PAGE: {
                "update_interval": timedelta(days=30),
                "priority": UpdatePriority.LOW,
                "keywords": ["за нас", "контакти", "общи условия"]
            },
            ContentType.NEWS_ARTICLE: {
                "update_interval": timedelta(days=1),
                "priority": UpdatePriority.CRITICAL,
                "keywords": ["новини", "съобщения", "актуално"]
            },
            ContentType.PROGRAM_INFO: {
                "update_interval": timedelta(days=7),
                "priority": UpdatePriority.MEDIUM,
                "keywords": ["програма", "схема", "мярка"]
            },
            ContentType.DEADLINE_INFO: {
                "update_interval": timedelta(days=1),
                "priority": UpdatePriority.CRITICAL,
                "keywords": ["срок", "кандидатстване", "прием"]
            },
            ContentType.CONTACT_INFO: {
                "update_interval": timedelta(days=30),
                "priority": UpdatePriority.LOW,
                "keywords": ["контакт", "адрес", "телефон"]
            }
        }
    
    async def __aenter__(self):
        """Async context manager вход"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (compatible; MCPCrawlBot/1.0; +http://example.com/bot)'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager изход"""
        if self.session:
            await self.session.close()
    
    def classify_content_type(self, url: str, content: str = "") -> ContentType:
        """
        🎯 Класифицира типа съдържание на базата на URL и съдържание
        """
        url_lower = url.lower()
        content_lower = content.lower()
        
        # Проверка за ключови думи в URL и съдържание
        for content_type, config in self.content_type_config.items():
            keywords = config["keywords"]
            if any(keyword in url_lower or keyword in content_lower for keyword in keywords):
                return content_type
        
        # По подразбиране - информация за програми
        return ContentType.PROGRAM_INFO
    
    async def check_url_changes_batch(self, urls: List[str]) -> List[ContentChangeInfo]:
        """
        🚀 Batch проверка за промени в множество URL-и
        Оптимизирано за минимизиране на HTTP заявки и bandwidth
        """
        if not self.session:
            raise RuntimeError("SmartContentMonitor трябва да се използва като async context manager")
        
        changes = []
        
        # Групиране на URL-ите по домейн за оптимизация
        domain_groups = self._group_urls_by_domain(urls)
        
        for domain, domain_urls in domain_groups.items():
            logger.info(f"Проверка на {len(domain_urls)} URL-и от домейн {domain}")
            
            # Batch проверка за всеки домейн
            domain_changes = await self._check_domain_urls(domain_urls)
            changes.extend(domain_changes)
            
            # Кратка пауза между домейните за да не претоварим сървърите
            await asyncio.sleep(1)
        
        return changes
    
    def _group_urls_by_domain(self, urls: List[str]) -> Dict[str, List[str]]:
        """Групира URL-ите по домейн за оптимизация"""
        domain_groups = {}
        for url in urls:
            try:
                domain = urlparse(url).netloc
                if domain not in domain_groups:
                    domain_groups[domain] = []
                domain_groups[domain].append(url)
            except Exception as e:
                logger.warning(f"Невалиден URL {url}: {e}")
        return domain_groups
    
    async def _check_domain_urls(self, urls: List[str]) -> List[ContentChangeInfo]:
        """Проверява URL-ите от един домейн"""
        changes = []
        
        # Concurrent проверка с ограничение за да не претоварим сървъра
        semaphore = asyncio.Semaphore(5)  # Максимум 5 едновременни заявки
        
        tasks = [self._check_single_url_change(url, semaphore) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, ContentChangeInfo):
                changes.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Грешка при проверка на URL: {result}")
        
        return changes
    
    async def _check_single_url_change(self, url: str, semaphore: asyncio.Semaphore) -> ContentChangeInfo:
        """
        🔍 Проверява един URL за промени с оптимизация за bandwidth
        """
        async with semaphore:
            try:
                # Получаване на текущия hash от базата данни
                old_hash_info = await asyncio.to_thread(get_document_hash_info, self.supabase_client, url)
                old_hash = old_hash_info.get('content_hash') if old_hash_info else None
                
                # HEAD заявка първо за проверка на Last-Modified и Content-Length
                head_info = await self._get_head_info(url)
                
                if head_info and old_hash_info:
                    # Проверка дали има промяна на базата на Last-Modified
                    last_modified_db = old_hash_info.get('last_crawled_at') if old_hash_info else None
                    if last_modified_db and head_info.get('last_modified'):
                        if head_info['last_modified'] <= last_modified_db:
                            logger.info(f"URL {url} не е променен според Last-Modified header")
                            return ContentChangeInfo(
                                url=url,
                                old_hash=old_hash,
                                new_hash=old_hash or "",
                                content_type=ContentType.STATIC_PAGE,
                                priority=UpdatePriority.LOW,
                                last_modified=head_info.get('last_modified'),
                                content_size=head_info.get('content_length', 0),
                                change_detected=False,
                                http_status=head_info.get('status', 200)
                            )
                
                # Ако няма Last-Modified или има съмнение за промяна, правим пълна заявка
                content, status_code = await self._fetch_content(url)
                
                if content is None:
                    return ContentChangeInfo(
                        url=url,
                        old_hash=old_hash,
                        new_hash="",
                        content_type=ContentType.STATIC_PAGE,
                        priority=UpdatePriority.LOW,
                        last_modified=None,
                        content_size=0,
                        change_detected=False,
                        http_status=status_code,
                        error_message=f"Неуспешно изтегляне, HTTP {status_code}"
                    )
                
                # Изчисляване на новия hash
                new_hash = calculate_sha256(content)
                
                # Класифициране на съдържанието
                content_type = self.classify_content_type(url, content)
                priority = self.content_type_config[content_type]["priority"]
                
                # Проверка за промяна
                change_detected = old_hash != new_hash
                
                return ContentChangeInfo(
                    url=url,
                    old_hash=old_hash,
                    new_hash=new_hash,
                    content_type=content_type,
                    priority=priority,
                    last_modified=head_info.get('last_modified') if head_info else None,
                    content_size=len(content.encode('utf-8')),
                    change_detected=change_detected,
                    http_status=status_code
                )
                
            except Exception as e:
                logger.error(f"Грешка при проверка на {url}: {e}")
                return ContentChangeInfo(
                    url=url,
                    old_hash=old_hash,
                    new_hash="",
                    content_type=ContentType.STATIC_PAGE,
                    priority=UpdatePriority.LOW,
                    last_modified=None,
                    content_size=0,
                    change_detected=False,
                    http_status=500,
                    error_message=str(e)
                )
    
    async def _get_head_info(self, url: str) -> Optional[Dict[str, Any]]:
        """Получава HEAD информация за оптимизация на bandwidth"""
        try:
            async with self.session.head(url) as response:
                last_modified = None
                if 'Last-Modified' in response.headers:
                    from email.utils import parsedate_to_datetime
                    last_modified = parsedate_to_datetime(response.headers['Last-Modified'])
                
                content_length = None
                if 'Content-Length' in response.headers:
                    content_length = int(response.headers['Content-Length'])
                
                return {
                    'status': response.status,
                    'last_modified': last_modified,
                    'content_length': content_length
                }
        except Exception as e:
            logger.debug(f"HEAD заявка неуспешна за {url}: {e}")
            return None
    
    async def _fetch_content(self, url: str) -> Tuple[Optional[str], int]:
        """Изтегля съдържанието на URL"""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    return content, response.status
                else:
                    return None, response.status
        except Exception as e:
            logger.error(f"Грешка при изтегляне на {url}: {e}")
            return None, 500

    async def get_urls_for_update(self, max_urls: int = 100) -> List[str]:
        """
        📋 Получава списък с URL-и които трябва да бъдат проверени за обновления
        Базирано на приоритет и време от последното обновление
        """
        try:
            # Заявка за получаване на URL-и които трябва да бъдат обновени
            query = """
            SELECT DISTINCT url, last_crawled_at, content_hash
            FROM document_content_hashes
            WHERE
                last_crawled_at < NOW() - INTERVAL '1 day'  -- Поне 1 ден стари
                OR last_crawled_at IS NULL                   -- Никога не са crawl-вани
            ORDER BY
                CASE
                    WHEN last_crawled_at IS NULL THEN 0     -- Най-висок приоритет
                    ELSE EXTRACT(EPOCH FROM (NOW() - last_crawled_at))
                END DESC
            LIMIT %s
            """

            response = await asyncio.to_thread(
                self.supabase_client.rpc,
                'execute_sql',
                {'query': query, 'params': [max_urls]}
            )

            if response.data:
                return [row['url'] for row in response.data]
            else:
                # Fallback - получаване на всички уникални URL-и от crawled_pages
                response = await asyncio.to_thread(
                    self.supabase_client.table("crawled_pages")
                    .select("url")
                    .limit(max_urls)
                    .execute
                )
                return list(set(row['url'] for row in response.data))

        except Exception as e:
            logger.error(f"Грешка при получаване на URL-и за обновление: {e}")
            return []

    async def perform_batch_update(self, urls: List[str]) -> BatchUpdateResult:
        """
        🚀 Извършва batch обновление на множество URL-и
        """
        start_time = datetime.now()

        # Проверка за промени
        changes = await self.check_url_changes_batch(urls)

        # Статистики
        total_urls = len(urls)
        changed_urls = sum(1 for change in changes if change.change_detected)
        unchanged_urls = sum(1 for change in changes if not change.change_detected and change.error_message is None)
        error_urls = sum(1 for change in changes if change.error_message is not None)

        # Изчисляване на спестен bandwidth (за URL-ите които не са се променили)
        bandwidth_saved = sum(
            change.content_size for change in changes
            if not change.change_detected and change.error_message is None
        )

        processing_time = (datetime.now() - start_time).total_seconds()

        # Обновяване на hash-овете в базата данни за променените URL-и
        for change in changes:
            if change.change_detected and change.error_message is None:
                await asyncio.to_thread(
                    upsert_document_hash,
                    self.supabase_client,
                    change.url,
                    change.new_hash
                )

        result = BatchUpdateResult(
            total_urls=total_urls,
            changed_urls=changed_urls,
            unchanged_urls=unchanged_urls,
            error_urls=error_urls,
            bandwidth_saved=bandwidth_saved,
            processing_time=processing_time,
            changes=changes
        )

        logger.info(f"Batch обновление завършено: {changed_urls}/{total_urls} променени URL-и, "
                   f"{bandwidth_saved/1024:.1f}KB спестен bandwidth")

        return result

    async def schedule_smart_updates(self) -> BatchUpdateResult:
        """
        🧠 Интелигентно планиране на обновления базирано на приоритет и тип съдържание
        """
        # Получаване на URL-и за обновление
        urls_to_check = await self.get_urls_for_update(max_urls=50)  # Ограничение за free plan

        if not urls_to_check:
            logger.info("Няма URL-и за проверка")
            return BatchUpdateResult(0, 0, 0, 0, 0, 0.0, [])

        logger.info(f"Започвам интелигентно обновление на {len(urls_to_check)} URL-и")

        # Извършване на batch обновление
        result = await self.perform_batch_update(urls_to_check)

        return result

# Utility функции за интеграция с MCP сървъра

async def create_incremental_update_tool():
    """
    🛠️ Създава MCP tool за инкрементални обновления
    """
    from .utils import get_supabase_client

    supabase_client = get_supabase_client()

    async with SmartContentMonitor(supabase_client) as monitor:
        result = await monitor.schedule_smart_updates()

        return {
            "success": True,
            "message": f"Обновени {result.changed_urls} от {result.total_urls} URL-и",
            "details": {
                "changed_urls": result.changed_urls,
                "unchanged_urls": result.unchanged_urls,
                "error_urls": result.error_urls,
                "bandwidth_saved_kb": round(result.bandwidth_saved / 1024, 2),
                "processing_time_seconds": round(result.processing_time, 2)
            }
        }

async def check_content_freshness(urls: List[str]) -> Dict[str, Any]:
    """
    📊 Проверява актуалността на съдържанието за дадени URL-и
    """
    from .utils import get_supabase_client

    supabase_client = get_supabase_client()

    async with SmartContentMonitor(supabase_client) as monitor:
        changes = await monitor.check_url_changes_batch(urls)

        freshness_report = {
            "total_urls": len(urls),
            "fresh_content": sum(1 for c in changes if not c.change_detected),
            "stale_content": sum(1 for c in changes if c.change_detected),
            "errors": sum(1 for c in changes if c.error_message),
            "details": [asdict(change) for change in changes]
        }

        return freshness_report
