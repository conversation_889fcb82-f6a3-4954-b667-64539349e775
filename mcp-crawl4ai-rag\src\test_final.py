#!/usr/bin/env python3
"""
Final test to verify all systems work together
"""
import sys
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('../.env')

from utils import smart_rag_query, get_supabase_client

async def test_rag():
    print("🧪 Testing integrated RAG system...")

    supabase = get_supabase_client()
    result = await smart_rag_query('Кои са програмите за иновации в предприятията?', supabase)
    
    print(f"✅ Strategy: {result['strategy_used']}")
    print(f"✅ Results count: {len(result['results'])}")
    print(f"✅ Execution time: {result['execution_time']:.2f}s")
    
    if result['results']:
        first_result = result['results'][0]
        content = first_result.get('content', '')[:200]
        url = first_result.get('url', 'No URL')
        score = first_result.get('similarity', first_result.get('score', 'N/A'))

        print(f"✅ First result content: {content}...")
        print(f"✅ URL: {url}")
        print(f"✅ Score: {score}")

        # Show program name if available
        program_name = first_result.get('metadata', {}).get('program_name', 'N/A')
        print(f"✅ Program: {program_name}")

    print("\n🎉 All systems working correctly!")

if __name__ == "__main__":
    asyncio.run(test_rag())
