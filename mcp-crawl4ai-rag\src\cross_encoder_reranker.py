#!/usr/bin/env python3
"""
Cross-encoder Reranking Implementation
Phase 8.2: Highest priority improvement (⭐⭐⭐⭐⭐) for RAG accuracy

This module implements cross-encoder reranking using ms-marco-MiniLM-L6-v2 model
to significantly improve the relevance ranking of search results.
"""

import logging
import time
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import asyncio

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CrossEncoderConfig:
    """Configuration for Cross-encoder Reranking"""
    model_name: str = "cross-encoder/ms-marco-MiniLM-L6-v2"
    max_length: int = 512
    batch_size: int = 8
    top_k_rerank: int = 20  # Rerank top 20 results
    final_k: int = 10       # Return top 10 after reranking
    score_threshold: float = 0.1  # Minimum reranking score
    enable_caching: bool = True
    cache_ttl: int = 3600   # Cache TTL in seconds

@dataclass
class RerankResult:
    """Result from cross-encoder reranking"""
    id: str
    url: str
    content: str
    metadata: Dict[str, Any]
    original_score: float
    rerank_score: float
    final_score: float
    rank_position: int

class CrossEncoderReranker:
    """
    Cross-encoder reranking implementation using ms-marco-MiniLM-L6-v2
    
    This reranker takes initial search results and reranks them using a
    cross-encoder model that considers query-document pairs jointly.
    """
    
    def __init__(self, config: Optional[CrossEncoderConfig] = None):
        self.config = config or CrossEncoderConfig()
        self.model = None
        self.cache = {} if self.config.enable_caching else None
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize the cross-encoder model"""
        try:
            logger.info(f"🔧 Initializing Cross-encoder model: {self.config.model_name}")
            
            from sentence_transformers import CrossEncoder
            
            self.model = CrossEncoder(
                self.config.model_name,
                max_length=self.config.max_length
            )
            
            logger.info(f"✅ Cross-encoder model loaded successfully")
            
        except ImportError:
            logger.error("❌ sentence-transformers not installed. Install with: pip install sentence-transformers")
            raise
        except Exception as e:
            logger.error(f"❌ Failed to load cross-encoder model: {e}")
            raise
    
    def _create_query_document_pairs(self, query: str, documents: List[Dict[str, Any]]) -> List[Tuple[str, str]]:
        """Create query-document pairs for cross-encoder scoring"""
        pairs = []
        
        for doc in documents:
            # Extract content for scoring
            content = doc.get('content', '')
            
            # Truncate content if too long
            if len(content) > 1000:  # Reasonable limit for cross-encoder
                content = content[:1000] + "..."
            
            pairs.append((query, content))
        
        return pairs
    
    def _get_cache_key(self, query: str, doc_id: str) -> str:
        """Generate cache key for query-document pair"""
        return f"{hash(query)}_{doc_id}"
    
    def _batch_score(self, pairs: List[Tuple[str, str]]) -> List[float]:
        """Score query-document pairs in batches"""
        if not self.model:
            logger.error("❌ Cross-encoder model not initialized")
            return [0.0] * len(pairs)
        
        try:
            # Score all pairs at once (model handles batching internally)
            scores = self.model.predict(pairs)
            
            # Convert to list if numpy array
            if hasattr(scores, 'tolist'):
                scores_list = scores.tolist()
            else:
                scores_list = list(scores)

            return scores_list
            
        except Exception as e:
            logger.error(f"❌ Cross-encoder scoring failed: {e}")
            return [0.0] * len(pairs)
    
    async def rerank_results(
        self, 
        query: str, 
        results: List[Dict[str, Any]], 
        top_k: Optional[int] = None
    ) -> List[RerankResult]:
        """
        Rerank search results using cross-encoder model
        
        Args:
            query: Search query
            results: Initial search results
            top_k: Number of results to return (uses config.final_k if None)
            
        Returns:
            List of reranked results
        """
        if not results:
            logger.warning("🔍 No results to rerank")
            return []
        
        start_time = time.time()
        final_k = top_k or self.config.final_k
        
        logger.info(f"🔄 Reranking {len(results)} results for query: '{query[:50]}...'")
        
        # Limit to top_k_rerank for efficiency
        candidates = results[:self.config.top_k_rerank]
        
        # Create query-document pairs
        pairs = self._create_query_document_pairs(query, candidates)
        
        # Check cache for existing scores
        cached_scores = []
        uncached_pairs = []
        uncached_indices = []
        
        if self.cache:
            for i, (q, doc_content) in enumerate(pairs):
                doc_id = candidates[i].get('id', str(i))
                cache_key = self._get_cache_key(query, doc_id)
                
                if cache_key in self.cache:
                    cached_scores.append((i, self.cache[cache_key]))
                else:
                    uncached_pairs.append((q, doc_content))
                    uncached_indices.append(i)
        else:
            uncached_pairs = pairs
            uncached_indices = list(range(len(pairs)))
        
        # Score uncached pairs
        if uncached_pairs:
            logger.info(f"🧠 Scoring {len(uncached_pairs)} query-document pairs with cross-encoder")
            new_scores = self._batch_score(uncached_pairs)
            
            # Update cache
            if self.cache:
                for idx, score in zip(uncached_indices, new_scores):
                    doc_id = candidates[idx].get('id', str(idx))
                    cache_key = self._get_cache_key(query, doc_id)
                    self.cache[cache_key] = score
        else:
            new_scores = []
        
        # Combine cached and new scores
        all_scores = [0.0] * len(candidates)
        
        # Fill in cached scores
        for idx, score in cached_scores:
            all_scores[idx] = score
        
        # Fill in new scores
        for i, score in enumerate(new_scores):
            idx = uncached_indices[i]
            all_scores[idx] = score
        
        # Create reranked results
        reranked_results = []
        
        for i, (doc, rerank_score) in enumerate(zip(candidates, all_scores)):
            # Get original score (try different field names)
            original_score = (
                doc.get('hybrid_score', 0.0) or 
                doc.get('similarity', 0.0) or 
                doc.get('score', 0.0)
            )
            
            # Calculate final score (weighted combination)
            # 70% rerank score + 30% original score
            final_score = 0.7 * rerank_score + 0.3 * original_score
            
            rerank_result = RerankResult(
                id=doc.get('id', str(i)),
                url=doc.get('url', ''),
                content=doc.get('content', ''),
                metadata=doc.get('metadata', {}),
                original_score=original_score,
                rerank_score=rerank_score,
                final_score=final_score,
                rank_position=i + 1
            )
            
            reranked_results.append(rerank_result)
        
        # Sort by final score (descending)
        reranked_results.sort(key=lambda x: x.final_score, reverse=True)
        
        # Filter by score threshold and limit to final_k
        filtered_results = [
            r for r in reranked_results 
            if r.rerank_score >= self.config.score_threshold
        ][:final_k]
        
        # Update rank positions
        for i, result in enumerate(filtered_results):
            result.rank_position = i + 1
        
        elapsed_time = time.time() - start_time
        
        logger.info(f"✅ Reranking completed: {len(filtered_results)} results in {elapsed_time:.3f}s")
        if filtered_results:
            logger.info(f"📊 Score improvements: avg rerank={sum(r.rerank_score for r in filtered_results)/len(filtered_results):.3f}")
        else:
            logger.info("📊 Score improvements: No results after filtering")
        
        return filtered_results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get reranker statistics"""
        return {
            "model_name": self.config.model_name,
            "cache_size": len(self.cache) if self.cache else 0,
            "config": {
                "max_length": self.config.max_length,
                "batch_size": self.config.batch_size,
                "top_k_rerank": self.config.top_k_rerank,
                "final_k": self.config.final_k,
                "score_threshold": self.config.score_threshold
            }
        }

# Global reranker instance
_reranker_instance = None

def get_reranker(config: Optional[CrossEncoderConfig] = None) -> CrossEncoderReranker:
    """Get global reranker instance (singleton pattern)"""
    global _reranker_instance
    
    if _reranker_instance is None:
        _reranker_instance = CrossEncoderReranker(config)
    
    return _reranker_instance

async def rerank_search_results(
    query: str, 
    results: List[Dict[str, Any]], 
    top_k: int = 10,
    config: Optional[CrossEncoderConfig] = None
) -> List[Dict[str, Any]]:
    """
    Convenience function to rerank search results
    
    Args:
        query: Search query
        results: Initial search results
        top_k: Number of results to return
        config: Optional reranker configuration
        
    Returns:
        List of reranked results in original format
    """
    reranker = get_reranker(config)
    reranked = await reranker.rerank_results(query, results, top_k)
    
    # Convert back to original format
    output_results = []
    for r in reranked:
        result = {
            'id': r.id,
            'url': r.url,
            'content': r.content,
            'metadata': r.metadata,
            'similarity': r.original_score,
            'rerank_score': r.rerank_score,
            'final_score': r.final_score,
            'rank_position': r.rank_position
        }
        output_results.append(result)
    
    return output_results

if __name__ == "__main__":
    # Test the reranker
    async def test_reranker():
        # Sample test data
        query = "европейски фондове за иновации"
        
        sample_results = [
            {
                'id': '1',
                'url': 'https://example.com/1',
                'content': 'Програма за иновации и предприемачество с европейско финансиране',
                'metadata': {},
                'similarity': 0.8
            },
            {
                'id': '2', 
                'url': 'https://example.com/2',
                'content': 'Информация за туризъм и културни дейности',
                'metadata': {},
                'similarity': 0.6
            }
        ]
        
        reranked = await rerank_search_results(query, sample_results)
        
        print("🔄 Reranking Test Results:")
        for r in reranked:
            print(f"  📄 {r['id']}: original={r['similarity']:.3f}, rerank={r['rerank_score']:.3f}, final={r['final_score']:.3f}")
    
    asyncio.run(test_reranker())
