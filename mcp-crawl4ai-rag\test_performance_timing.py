#!/usr/bin/env python3
"""
Test Performance Timing - Анализира защо standard RAG е по-бавен от advanced RAG
"""

import asyncio
import sys
import os
import time
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append('src')

from utils import enhanced_semantic_search, ultra_smart_rag_query
from supabase import create_client, Client

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_performance_comparison():
    """Тества timing разликата между enhanced_semantic_search и ultra_smart_rag_query"""
    
    # Initialize Supabase
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        logger.error("SUPABASE_URL или SUPABASE_SERVICE_KEY не са зададени")
        return
    
    supabase: Client = create_client(url, key)
    
    test_query = "Какви са условията за кандидатстване по програма за регионално развитие?"
    
    logger.info("🔍 ЗАПОЧВАМ PERFORMANCE ТЕСТ")
    logger.info("=" * 60)
    
    # Test 1: enhanced_semantic_search (standard RAG)
    logger.info("📊 ТЕСТ 1: enhanced_semantic_search (Standard RAG)")
    start_time = time.time()
    
    try:
        standard_results = await enhanced_semantic_search(
            query=test_query,
            supabase_client=supabase,
            async_openai_client=None,
            similarity_threshold=0.62,
            final_top_k=5
        )
        standard_time = time.time() - start_time
        logger.info(f"✅ Standard RAG: {standard_time:.2f}s, {len(standard_results)} results")
    except Exception as e:
        logger.error(f"❌ Standard RAG failed: {e}")
        standard_time = 0
        standard_results = []
    
    logger.info("-" * 60)
    
    # Test 2: ultra_smart_rag_query (advanced RAG)
    logger.info("📊 ТЕСТ 2: ultra_smart_rag_query (Advanced RAG)")
    start_time = time.time()
    
    try:
        advanced_result = await ultra_smart_rag_query(
            query=test_query,
            supabase_client=supabase,
            async_openai_client=None,
            similarity_threshold=0.62,
            final_top_k=5
        )
        advanced_time = time.time() - start_time
        advanced_results = advanced_result.get('results', [])
        logger.info(f"✅ Advanced RAG: {advanced_time:.2f}s, {len(advanced_results)} results")
    except Exception as e:
        logger.error(f"❌ Advanced RAG failed: {e}")
        advanced_time = 0
        advanced_results = []
    
    logger.info("=" * 60)
    logger.info("📈 PERFORMANCE АНАЛИЗ:")
    logger.info(f"Standard RAG (enhanced_semantic_search): {standard_time:.2f}s")
    logger.info(f"Advanced RAG (ultra_smart_rag_query): {advanced_time:.2f}s")
    
    if standard_time > 0 and advanced_time > 0:
        ratio = standard_time / advanced_time
        logger.info(f"Ratio (Standard/Advanced): {ratio:.2f}x")
        
        if ratio > 1:
            logger.warning(f"⚠️ ПАРАДОКС ПОТВЪРДЕН: Standard RAG е {ratio:.2f}x по-бавен от Advanced RAG!")
        else:
            logger.info(f"✅ Advanced RAG е {1/ratio:.2f}x по-бавен от Standard RAG (очаквано)")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_performance_comparison())
