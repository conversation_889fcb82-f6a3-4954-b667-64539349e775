Example 1:
User Query: "Търся информация за програма 'Иновации и конкурентоспособност' за 2023 година."
JSON Output:
{
  "entities": [
    {"name": "Иновации и конкурентоспособност", "type": "PROGRAM_NAME"},
    {"name": "2023", "type": "YEAR"}
  ]
}

Example 2:
User Query: "Кой финансира проекти за енергийна ефективност?"
JSON Output:
{
  "entities": [
    {"name": "енергийна ефективност", "type": "PROGRAM_NAME"}
  ]
}

Example 3:
User Query: "Кандидатствах по програма за млади фермери миналата година."
JSON Output:
{
  "entities": [
    {"name": "млади фермери", "type": "PROGRAM_NAME"}
  ]
}