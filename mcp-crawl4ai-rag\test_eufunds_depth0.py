import os
import asyncio
import json
from fastmcp.client import Client
from fastmcp.client.transports import SSETransport
from fastmcp.exceptions import ToolError

# --- Настройка на логирането ---
import logging

logger = logging.getLogger("index_single_url")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(name)s:%(funcName)s:%(lineno)d] - %(message)s'
)
# ----------------------------------

async def main():
    base_server_url = os.getenv("MCP_SERVER_BASE_URL", "http://localhost:8051")
    sse_path = "/sse"
    sse_connection_url = f"{base_server_url}{sse_path}"

    logger.info(f"Ще се свързва към MCP SSE на: {sse_connection_url}")

    transport = SSETransport(url=sse_connection_url)
    client = Client(transport=transport)

    final_result_payload_text = None
    result_content_list = None

    try:
        async with client:
            logger.info(f"Успешно свързване към MCP сървъра (чрез транспорт на {sse_connection_url})")

            tool_name = "smart_crawl_url"

            # --- URL И ПАРАМЕТРИ ЗА ТЕСТ ---
            # ЦЕЛЕНАСОЧЕНО CRAWLING за ОПТТИ и ОПОС програми

            # ОПОС (Екологични) програми
            url_to_test = "https://www.eufunds.bg/bg/opos"
            current_max_depth = 1

            params = {
                "url": url_to_test,
                "max_depth": current_max_depth,
                "chunk_size": 3000,
                "force_recrawl": True
            }

            logger.info(f"Извикване на инструмент '{tool_name}' за URL: '{url_to_test}' с параметри: {json.dumps(params, indent=2, ensure_ascii=False)}")

            try:
                result_content_list = await client.call_tool(
                    name=tool_name, arguments=params
                )
            except ToolError as tce:
                logger.error(f"!!! Възникна ToolError по време на client.call_tool за URL {url_to_test} !!!")
                logger.error(f"!!! Тип на грешката: {type(tce)}")
                logger.error(f"!!! Аргументи на грешката: {tce.args}")
                # ToolError в FastMCP няма response атрибут - използваме str() за детайли
                final_result_payload_text = str(tce)
                logger.error(f"!!! ToolError детайли: {final_result_payload_text}")
            except Exception as e_inner:
                logger.error(f"!!! Възникна НЕ-ToolError грешка по време на client.call_tool за URL {url_to_test}: {type(e_inner).__name__} - {e_inner}", exc_info=True)

            logger.info(f"\n--- Суров резултат от client.call_tool за URL: {url_to_test} ---")
            if result_content_list and isinstance(result_content_list, list) and len(result_content_list) > 0:
                item = result_content_list[0]
                # FastMCP връща различни типове обекти - обработваме ги безопасно
                try:
                    # Използваме getattr за безопасен достъп до атрибути
                    text_content = getattr(item, 'text', None)
                    if text_content:
                        logger.info(f"Съдържание на item.text (първите 1000 символа): {text_content[:1000]}...")
                        final_result_payload_text = text_content
                    elif isinstance(item, dict) and "error" in item:
                        logger.warning(f"Инструментът '{tool_name}' върна речник с грешка: {item}")
                        final_result_payload_text = json.dumps(item)
                    elif isinstance(item, str):
                        logger.info(f"Инструментът '{tool_name}' върна директно string резултат")
                        final_result_payload_text = item
                    else:
                        logger.warning(f"Резултатният елемент от '{tool_name}' има неочакван формат. Тип: {type(item)}, Елемент: {item}")
                        final_result_payload_text = str(item)
                except Exception as e_item_processing:
                    logger.error(f"Грешка при обработка на резултата: {e_item_processing}")
                    final_result_payload_text = str(item)
            elif result_content_list is not None:
                logger.warning(f"Инструментът '{tool_name}' върна празен списък с резултати.")

            if final_result_payload_text is not None:
                logger.info(f"\n--- Обобщение на резултата (от item.text или от ToolError.response.text) за URL: {url_to_test} ---")
                try:
                    parsed_result = json.loads(final_result_payload_text)
                    logger.info(json.dumps(parsed_result, indent=2, ensure_ascii=False))
                    if not parsed_result.get("success", False):
                        logger.warning(f"Обхождането на {url_to_test} НЕ беше успешно според отговора: {parsed_result.get('message', parsed_result.get('error', 'Няма съобщение за грешка.'))}")
                    else:
                        logger.info(f"Обхождането на {url_to_test} изглежда УСПЕШНО приключи.")
                except json.JSONDecodeError as e_json_final:
                    logger.error(f"Грешка при парсване на ФИНАЛНИЯ JSON от payload за {url_to_test}: {e_json_final}")
                    logger.error(f"Суров финален payload (първите 1000 символа): {final_result_payload_text[:1000]}...")
                except Exception as e_parse_final:
                    logger.error(f"Друга грешка при обработка на ФИНАЛНИЯ payload за {url_to_test}: {e_parse_final}")
            else:
                logger.warning(f"Не е получен финален текстов payload за обработка за URL: {url_to_test}.")

            logger.info(f"--- Приключи обработка на URL: {url_to_test} ---")

    except ConnectionRefusedError:
        logger.critical(f"КРИТИЧНА ГРЕШКА: Не мога да се свържа с MCP сървъра на {sse_connection_url}. Уверете се, че сървърът работи.")
    except Exception as e_outer_main:
        logger.critical(f"Възникна неочаквана ГЛАВНА грешка в main функцията: {type(e_outer_main).__name__} - {e_outer_main}", exc_info=True)
    finally:
        logger.info("--- Скриптът за индексиране (единичен URL) приключи опита си за работа ---")


if __name__ == "__main__":
    from dotenv import load_dotenv
    from pathlib import Path

    try:
        current_script_path = Path(__file__).resolve()
        dotenv_path = current_script_path.parent.parent / '.env'
        if not dotenv_path.exists():
            dotenv_path = current_script_path.parent / '.env'
        if dotenv_path.exists():
            logger.info(f"Зареждане на .env от {dotenv_path} за скрипта за индексиране.")
            load_dotenv(dotenv_path, override=True)
        else:
            logger.warning(f"Не е намерен .env файл на очакваните места ({current_script_path.parent.parent / '.env'} или {current_script_path.parent / '.env'}). Ще се разчита на системни променливи.")
    except Exception as e_dotenv_load:
        logger.warning(f"Неуспешно зареждане на .env в скрипта за индексиране: {e_dotenv_load}")

    logger.info("--- Стартиране на скрипта за индексиране (единичен URL) ---")
    asyncio.run(main())
    logger.info("--- Скриптът за индексиране (единичен URL) приключи ---")
