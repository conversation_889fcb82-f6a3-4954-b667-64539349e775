#!/usr/bin/env python3
"""
РЕАЛИСТИЧЕН RAG ТЕСТ - базиран на данните които ИМАМЕ в Supabase
Този тест е създаден да оцени RAG системата с въпроси за които ЗНАЕМ отговорите в базата данни.
"""

import asyncio
import json
from src.utils import ultra_smart_rag_query
from supabase import create_client, Client
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# РЕАЛИСТИЧНИ ТЕСТОВИ ВЪПРОСИ базирани на данните в Supabase
REALISTIC_TEST_QUERIES = [
    {
        "id": "test_1",
        "query": "Кои са програмите за развитие на регионите в България?",
        "expected_programs": ["Програма Развитие на регионите 2021-2027", "ОПРР", "Развитие на регионите"],
        "expected_urls": ["https://www.eufunds.bg/bg/oprd"],
        "description": "Търси информация за регионални програми - имаме много данни за ОПРР"
    },
    {
        "id": "test_2", 
        "query": "Какви са процедурите за подкрепа на индустриални паркове?",
        "expected_content": ["BG16FFPR003-4.005", "индустриални и логистични паркове", "чиста енергия"],
        "expected_urls": ["https://www.eufunds.bg/bg/oprd"],
        "description": "Търси конкретна процедура която имаме в данните"
    },
    {
        "id": "test_3",
        "query": "Кои са транспортните програми в България?",
        "expected_programs": ["ОПТТИ", "Транспортна свързаност", "АМ Европа"],
        "expected_urls": ["https://www.eufunds.bg/bg/optti"],
        "description": "Търси транспортни програми - имаме данни за ОПТТИ"
    },
    {
        "id": "test_4",
        "query": "Какво е Interreg програмата с Северна Македония?",
        "expected_programs": ["Interreg VI-A ИПП България Северна Македония", "Програма Interreg VI-A IPA България Северна Македония 2021-2027"],
        "expected_content": ["Interreg", "Северна Македония", "2021-2027"],
        "description": "Търси конкретна Interreg програма която имаме в данните"
    },
    {
        "id": "test_5",
        "query": "Какви са програмите за околна среда?",
        "expected_programs": ["ОПОС", "Програма Околна среда 2021-2027"],
        "expected_urls": ["https://www.eufunds.bg/bg/opos"],
        "description": "Търси екологични програми - имаме данни за ОПОС"
    },
    {
        "id": "test_6",
        "query": "Кои са програмите за малки и средни предприятия?",
        "expected_programs": ["Оперативна програма Иновации и конкурентоспособност", "Подкрепа за малки и средни предприятия"],
        "expected_content": ["малки и средни предприятия", "МСП", "иновации", "конкурентоспособност"],
        "expected_urls": ["https://www.eufunds.bg/bg/page/2"],
        "description": "Търси МСП програми - имаме данни в новините"
    },
    {
        "id": "test_7",
        "query": "Какво е програмата за управление на границите?",
        "expected_programs": ["Програма по Инструмента за финансова подкрепа за управлението на границите и визовата политика"],
        "expected_content": ["граници", "визова политика", "финансова подкрепа"],
        "description": "Търси специфична програма която се споменава често в данните"
    },
    {
        "id": "test_8",
        "query": "Какви са мерките за справедлив преход?",
        "expected_content": ["Справедлив преход", "Приоритет 4", "Стара Загора"],
        "expected_urls": ["https://www.eufunds.bg/bg/oprd"],
        "description": "Търси информация за справедлив преход - имаме конкретни данни"
    }
]

async def evaluate_realistic_rag():
    """Оценява RAG системата с реалистични въпроси базирани на наличните данни"""
    
    print("ЗАПОЧВА РЕАЛИСТИЧНА RAG ОЦЕНКА")
    print("=" * 60)
    
    total_tests = len(REALISTIC_TEST_QUERIES)
    passed_tests = 0
    detailed_results = []
    
    for test_case in REALISTIC_TEST_QUERIES:
        print(f"ТЕСТ {test_case['id']}: {test_case['description']}")
        print(f"ВЪПРОС: {test_case['query']}")
        
        try:
            # Изпълни RAG заявката с Phase 4.7 подобрения
            result = await ultra_smart_rag_query(
                query=test_case['query'],
                supabase_client=supabase,
                final_top_k=5,
                similarity_threshold=0.3,
                adaptive_threshold=True,
                multi_pass_search=True,
                query_expansion=True,
                # Phase 4.7 optimizations enabled
                intelligent_clustering=True,
                dynamic_learning=True,
                performance_optimization=True
            )
            
            # Анализирай резултата
            found_programs = []
            found_urls = []
            found_content = []
            
            if result and 'results' in result:
                for doc in result['results']:
                    # Проверка за програми
                    if 'expected_programs' in test_case:
                        for program in test_case['expected_programs']:
                            if program.lower() in doc.get('content', '').lower():
                                found_programs.append(program)
                    
                    # Проверка за URL-и
                    if 'expected_urls' in test_case:
                        doc_url = doc.get('url', '')
                        for expected_url in test_case['expected_urls']:
                            if expected_url in doc_url:
                                found_urls.append(doc_url)
                    
                    # Проверка за съдържание
                    if 'expected_content' in test_case:
                        for content in test_case['expected_content']:
                            if content.lower() in doc.get('content', '').lower():
                                found_content.append(content)
            
            # Оценка на теста
            score = 0
            max_score = 0
            
            if 'expected_programs' in test_case:
                max_score += 1
                if found_programs:
                    score += 1
                    print(f"НАМЕРЕНИ ПРОГРАМИ: {found_programs}")
                else:
                    print(f"НЕ СА НАМЕРЕНИ ОЧАКВАНИ ПРОГРАМИ: {test_case['expected_programs']}")
            
            if 'expected_urls' in test_case:
                max_score += 1
                if found_urls:
                    score += 1
                    print(f"НАМЕРЕНИ URL-И: {found_urls}")
                else:
                    print(f"НЕ СА НАМЕРЕНИ ОЧАКВАНИ URL-И: {test_case['expected_urls']}")
            
            if 'expected_content' in test_case:
                max_score += 1
                if found_content:
                    score += 1
                    print(f"НАМЕРЕНО СЪДЪРЖАНИЕ: {found_content}")
                else:
                    print(f"НЕ Е НАМЕРЕНО ОЧАКВАНО СЪДЪРЖАНИЕ: {test_case['expected_content']}")
            
            # Изчисли процент успех за този тест
            test_success = (score / max_score) * 100 if max_score > 0 else 0
            
            if test_success >= 50:  # Поне 50% от критериите са изпълнени
                passed_tests += 1
                print(f"ТЕСТ УСПЕШЕН: {test_success:.1f}%")
            else:
                print(f"ТЕСТ НЕУСПЕШЕН: {test_success:.1f}%")
            
            # Запази детайлни резултати
            detailed_results.append({
                'test_id': test_case['id'],
                'query': test_case['query'],
                'success_rate': test_success,
                'found_programs': found_programs,
                'found_urls': found_urls,
                'found_content': found_content,
                'total_documents': len(result.get('results', [])) if result else 0
            })
            
        except Exception as e:
            print(f"ГРЕШКА В ТЕСТА: {str(e)}")
            detailed_results.append({
                'test_id': test_case['id'],
                'query': test_case['query'],
                'success_rate': 0,
                'error': str(e)
            })
    
    # Финални резултати
    overall_accuracy = (passed_tests / total_tests) * 100
    
    print("\n" + "=" * 60)
    print("ФИНАЛНИ РЕЗУЛТАТИ")
    print("=" * 60)
    print(f"ОБЩ УСПЕХ: {passed_tests}/{total_tests} теста ({overall_accuracy:.1f}%)")
    
    if overall_accuracy >= 75:
        print("ОТЛИЧЕН РЕЗУЛТАТ! RAG системата работи много добре с наличните данни!")
    elif overall_accuracy >= 50:
        print("ДОБЪР РЕЗУЛТАТ! RAG системата работи прилично с наличните данни.")
    elif overall_accuracy >= 25:
        print("СРЕДЕН РЕЗУЛТАТ. Нужни са подобрения в RAG системата.")
    else:
        print("СЛАБ РЕЗУЛТАТ. RAG системата се нуждае от сериозни подобрения.")
    
    # Запази резултатите
    with open('realistic_rag_evaluation_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'overall_accuracy': overall_accuracy,
            'passed_tests': passed_tests,
            'total_tests': total_tests,
            'detailed_results': detailed_results
        }, f, ensure_ascii=False, indent=2)
    
    print(f"Детайлни резултати запазени в: realistic_rag_evaluation_results.json")
    
    return overall_accuracy

if __name__ == "__main__":
    asyncio.run(evaluate_realistic_rag())
