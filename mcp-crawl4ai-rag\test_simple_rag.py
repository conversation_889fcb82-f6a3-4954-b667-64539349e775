#!/usr/bin/env python3
"""
Прост и директен тест на RAG функционалността
"""
import asyncio
import json
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def simple_rag_test():
    print('🎯 ПРОСТ ДИРЕКТЕН ТЕСТ НА RAG СИСТЕМАТА')
    print('=' * 50)
    
    try:
        # Test 1: Import check
        print('\n1️⃣ Проверка на импортите...')
        from utils import get_supabase_client
        from crawl4ai_mcp import app_context
        
        print('✅ Импортите са успешни')
        
        # Test 2: Supabase connection
        print('\n2️⃣ Проверка на Supabase връзката...')
        supabase = get_supabase_client()
        
        # Simple query to test connection
        result = supabase.table('crawled_pages').select('id').limit(1).execute()
        print(f'✅ Supabase връзка работи - намерени {len(result.data)} записа')
        
        # Test 3: Check if we have data
        print('\n3️⃣ Проверка на данните в базата...')
        count_result = supabase.table('crawled_pages').select('id', count='exact').execute()
        total_pages = count_result.count
        print(f'📊 Общо страници в базата: {total_pages}')
        
        if total_pages == 0:
            print('❌ Няма данни в базата за тестване')
            return False
        
        # Test 4: Test RPC function directly
        print('\n4️⃣ Тест на RPC функцията...')
        test_query = 'програми за иновации'
        
        rpc_result = supabase.rpc('match_crawled_pages_v4_debug', {
            'query_text': test_query,
            'match_count': 3,
            'p_weight_similarity': 0.4,
            'p_weight_program_name': 0.4,
            'p_weight_year': 0.2
        }).execute()
        
        if rpc_result.data:
            print(f'✅ RPC функция работи - намерени {len(rpc_result.data)} резултата')
            
            # Show first result
            first_result = rpc_result.data[0]
            print(f'📄 Първи резултат:')
            print(f'   Score: {first_result.get("score", "N/A")}')
            print(f'   Title: {first_result.get("title", "N/A")[:60]}...')
            print(f'   URL: {first_result.get("url", "N/A")}')
            
            return True
        else:
            print('❌ RPC функцията не върна резултати')
            return False
            
    except Exception as e:
        print(f'❌ Грешка при тестването: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(simple_rag_test())
    print(f'\n🏁 ФИНАЛЕН РЕЗУЛТАТ: {"✅ УСПЕХ" if result else "❌ НЕУСПЕХ"}')
