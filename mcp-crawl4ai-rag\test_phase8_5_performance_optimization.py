#!/usr/bin/env python3
"""
Phase 8.5: Performance Optimization Tests
Testing cascaded multi-stage execution, caching, and optimized processing
Based on external LLM recommendations for Bulgarian content optimization
"""

import asyncio
import time
import logging
from typing import List, Dict, Any
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import (
    get_supabase_client,
    search_crawled_pages_advanced_query,
    search_documents_with_text_hybrid_reranked
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerformanceOptimizationTester:
    """Test suite for Phase 8.5 Performance Optimization"""
    
    def __init__(self):
        self.client = get_supabase_client()
        self.test_queries = [
            "програми за малки и средни предприятия в България",
            "европейски фондове за иновации и развитие",
            "финансиране на стартъп компании в България",
            "дигитална трансформация на бизнеса",
            "зелена енергия и устойчиво развитие"
        ]
        
    async def test_cascaded_execution(self) -> Dict[str, Any]:
        """Test 1: Cascaded Multi-stage Execution"""
        logger.info("🧪 TEST 1: Cascaded Multi-stage Execution")
        
        query = self.test_queries[0]
        start_time = time.time()
        
        try:
            # Stage 1: Fast initial search with fewer results
            stage1_start = time.time()
            stage1_results = await search_documents_with_text_hybrid_reranked(
                self.client, query, match_count=5, rerank_top_k=10
            )
            stage1_time = time.time() - stage1_start
            
            # Stage 2: Advanced processing if stage 1 has good results
            stage2_results = []
            stage2_time = 0
            
            if stage1_results and len(stage1_results) >= 3:
                # Good initial results - proceed with advanced processing
                stage2_start = time.time()
                stage2_results = await search_crawled_pages_advanced_query(
                    query, match_count=10, enable_query_expansion=True,
                    enable_hyde=True, enable_multi_query=True
                )
                stage2_time = time.time() - stage2_start
            
            total_time = time.time() - start_time
            
            # Combine results intelligently
            final_results = self._combine_cascaded_results(stage1_results, stage2_results)
            
            logger.info(f"🎯 Stage 1: {len(stage1_results)} results in {stage1_time:.3f}s")
            logger.info(f"🚀 Stage 2: {len(stage2_results)} results in {stage2_time:.3f}s")
            logger.info(f"📊 Final: {len(final_results)} results in {total_time:.3f}s")
            
            return {
                'success': True,
                'stage1_results': len(stage1_results),
                'stage1_time': stage1_time,
                'stage2_results': len(stage2_results),
                'stage2_time': stage2_time,
                'final_results': len(final_results),
                'total_time': total_time,
                'efficiency_gain': stage1_time / total_time if total_time > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ Cascaded execution failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def _combine_cascaded_results(self, stage1: List[Dict], stage2: List[Dict]) -> List[Dict]:
        """Intelligently combine results from cascaded stages"""
        if not stage2:
            return stage1
        
        # Use stage2 results as primary, supplement with unique stage1 results
        seen_urls = {result.get('url', '') for result in stage2}
        combined = list(stage2)
        
        for result in stage1:
            if result.get('url', '') not in seen_urls:
                combined.append(result)
                if len(combined) >= 15:  # Limit total results
                    break
        
        return combined
    
    async def test_query_caching(self) -> Dict[str, Any]:
        """Test 2: Query Expansion Caching"""
        logger.info("🧪 TEST 2: Query Expansion Caching")
        
        query = self.test_queries[1]
        
        try:
            # First run - cold cache
            start_time = time.time()
            results1 = await search_crawled_pages_advanced_query(
                query, match_count=10, enable_query_expansion=True,
                enable_hyde=False, enable_multi_query=False
            )
            cold_time = time.time() - start_time
            
            # Second run - warm cache (should be faster)
            start_time = time.time()
            results2 = await search_crawled_pages_advanced_query(
                query, match_count=10, enable_query_expansion=True,
                enable_hyde=False, enable_multi_query=False
            )
            warm_time = time.time() - start_time
            
            cache_efficiency = (cold_time - warm_time) / cold_time if cold_time > 0 else 0
            
            logger.info(f"❄️ Cold cache: {len(results1)} results in {cold_time:.3f}s")
            logger.info(f"🔥 Warm cache: {len(results2)} results in {warm_time:.3f}s")
            logger.info(f"⚡ Cache efficiency: {cache_efficiency:.1%}")
            
            return {
                'success': True,
                'cold_time': cold_time,
                'warm_time': warm_time,
                'cache_efficiency': cache_efficiency,
                'results_consistent': len(results1) == len(results2)
            }
            
        except Exception as e:
            logger.error(f"❌ Query caching test failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_optimized_hyde(self) -> Dict[str, Any]:
        """Test 3: Optimized HyDE Document Generation"""
        logger.info("🧪 TEST 3: Optimized HyDE Document Generation")
        
        query = self.test_queries[2]
        
        try:
            # Test with HyDE enabled
            start_time = time.time()
            hyde_results = await search_crawled_pages_advanced_query(
                query, match_count=10, enable_query_expansion=False,
                enable_hyde=True, enable_multi_query=False
            )
            hyde_time = time.time() - start_time
            
            # Test without HyDE
            start_time = time.time()
            no_hyde_results = await search_crawled_pages_advanced_query(
                query, match_count=10, enable_query_expansion=False,
                enable_hyde=False, enable_multi_query=False
            )
            no_hyde_time = time.time() - start_time
            
            hyde_overhead = (hyde_time - no_hyde_time) / no_hyde_time if no_hyde_time > 0 else 0
            
            logger.info(f"🎯 With HyDE: {len(hyde_results)} results in {hyde_time:.3f}s")
            logger.info(f"🔄 Without HyDE: {len(no_hyde_results)} results in {no_hyde_time:.3f}s")
            logger.info(f"⏱️ HyDE overhead: {hyde_overhead:.1%}")
            
            return {
                'success': True,
                'hyde_time': hyde_time,
                'no_hyde_time': no_hyde_time,
                'hyde_overhead': hyde_overhead,
                'hyde_results': len(hyde_results),
                'no_hyde_results': len(no_hyde_results)
            }
            
        except Exception as e:
            logger.error(f"❌ Optimized HyDE test failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def test_batch_processing(self) -> Dict[str, Any]:
        """Test 4: Batch Query Processing"""
        logger.info("🧪 TEST 4: Batch Query Processing")
        
        try:
            # Sequential processing
            sequential_start = time.time()
            sequential_results = []
            for query in self.test_queries[:3]:
                results = await search_documents_with_text_hybrid_reranked(
                    self.client, query, match_count=5, rerank_top_k=10
                )
                sequential_results.append(len(results))
            sequential_time = time.time() - sequential_start
            
            # Concurrent processing
            concurrent_start = time.time()
            tasks = [
                search_documents_with_text_hybrid_reranked(
                    self.client, query, match_count=5, rerank_top_k=10
                )
                for query in self.test_queries[:3]
            ]
            concurrent_results_raw = await asyncio.gather(*tasks)
            concurrent_results = [len(results) for results in concurrent_results_raw]
            concurrent_time = time.time() - concurrent_start
            
            speedup = sequential_time / concurrent_time if concurrent_time > 0 else 0
            
            logger.info(f"🔄 Sequential: {sequential_results} in {sequential_time:.3f}s")
            logger.info(f"⚡ Concurrent: {concurrent_results} in {concurrent_time:.3f}s")
            logger.info(f"🚀 Speedup: {speedup:.1f}x")
            
            return {
                'success': True,
                'sequential_time': sequential_time,
                'concurrent_time': concurrent_time,
                'speedup': speedup,
                'results_consistent': sequential_results == concurrent_results
            }
            
        except Exception as e:
            logger.error(f"❌ Batch processing test failed: {e}")
            return {'success': False, 'error': str(e)}

async def main():
    """Run all Phase 8.5 Performance Optimization tests"""
    logger.info("🚀 STARTING PHASE 8.5: PERFORMANCE OPTIMIZATION TESTS")
    logger.info("=" * 80)
    
    tester = PerformanceOptimizationTester()
    results = {}
    
    # Test 1: Cascaded Multi-stage Execution
    logger.info("\n" + "=" * 20 + " Cascaded Multi-stage Execution " + "=" * 20)
    results['cascaded'] = await tester.test_cascaded_execution()
    if results['cascaded']['success']:
        logger.info("✅ PASSED - Cascaded Multi-stage Execution")
    else:
        logger.error("❌ FAILED - Cascaded Multi-stage Execution")
    
    # Test 2: Query Expansion Caching
    logger.info("\n" + "=" * 20 + " Query Expansion Caching " + "=" * 20)
    results['caching'] = await tester.test_query_caching()
    if results['caching']['success']:
        logger.info("✅ PASSED - Query Expansion Caching")
    else:
        logger.error("❌ FAILED - Query Expansion Caching")
    
    # Test 3: Optimized HyDE Document Generation
    logger.info("\n" + "=" * 20 + " Optimized HyDE Generation " + "=" * 20)
    results['hyde'] = await tester.test_optimized_hyde()
    if results['hyde']['success']:
        logger.info("✅ PASSED - Optimized HyDE Generation")
    else:
        logger.error("❌ FAILED - Optimized HyDE Generation")
    
    # Test 4: Batch Query Processing
    logger.info("\n" + "=" * 20 + " Batch Query Processing " + "=" * 20)
    results['batch'] = await tester.test_batch_processing()
    if results['batch']['success']:
        logger.info("✅ PASSED - Batch Query Processing")
    else:
        logger.error("❌ FAILED - Batch Query Processing")
    
    # Final Summary
    logger.info("\n" + "=" * 80)
    logger.info("🏁 PHASE 8.5 PERFORMANCE OPTIMIZATION - FINAL RESULTS")
    logger.info("=" * 80)
    
    passed_tests = sum(1 for result in results.values() if result.get('success', False))
    total_tests = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
        logger.info(f"{status} - {test_name.title()}")
    
    logger.info(f"\n📊 OVERALL RESULTS:")
    logger.info(f"   Tests passed: {passed_tests}/{total_tests}")
    logger.info(f"   Success rate: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        logger.info("🎉 PHASE 8.5 PERFORMANCE OPTIMIZATION: COMPLETE SUCCESS!")
    elif passed_tests >= total_tests * 0.75:
        logger.info("✅ PHASE 8.5 PERFORMANCE OPTIMIZATION: MOSTLY SUCCESSFUL")
    else:
        logger.info("⚠️ PHASE 8.5 PERFORMANCE OPTIMIZATION: NEEDS IMPROVEMENT")

if __name__ == "__main__":
    asyncio.run(main())
