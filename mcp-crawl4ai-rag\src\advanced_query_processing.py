#!/usr/bin/env python3
"""
Advanced Query Processing Engine
Phase 8.4: Query Expansion, HyDE Pattern, and Multi-query Transformation

Based on external LLM recommendations for achieving 90%+ RAG accuracy.
Priority: ⭐⭐⭐ (High Impact)
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
import re
import openai
from openai import AsyncOpenAI
from functools import lru_cache
import hashlib
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class QueryProcessingConfig:
    """Configuration for advanced query processing"""
    
    # Query Expansion Settings
    enable_query_expansion: bool = True
    max_synonyms: int = 3
    expansion_weight: float = 0.3
    
    # HyDE Pattern Settings
    enable_hyde: bool = True
    hyde_temperature: float = 0.7
    hyde_max_tokens: int = 200
    
    # Multi-query Transformation
    enable_multi_query: bool = True
    max_query_variants: int = 3
    variant_weight: float = 0.4
    
    # Bulgarian Language Support
    bulgarian_synonyms: Optional[Dict[str, List[str]]] = None
    
    def __post_init__(self):
        if self.bulgarian_synonyms is None:
            self.bulgarian_synonyms = {
                # European funding terms
                "европейски": ["ЕС", "европейски съюз", "общностни"],
                "фондове": ["финансиране", "средства", "програми", "субсидии"],
                "иновации": ["нововъведения", "технологии", "развитие", "модернизация"],
                "развитие": ["растеж", "прогрес", "усъвършенстване", "подобрение"],
                
                # Business terms
                "предприятия": ["фирми", "компании", "бизнес", "организации"],
                "малки": ["микро", "мини", "дребни"],
                "средни": ["МСП", "средни предприятия"],
                
                # Technology terms
                "дигитален": ["цифров", "електронен", "онлайн", "виртуален"],
                "трансформация": ["промяна", "преобразуване", "модернизация"],
                "технологии": ["техника", "иновации", "решения", "системи"],
                
                # Sustainability terms
                "устойчив": ["екологичен", "зелен", "възобновяем", "дълготраен"],
                "околна среда": ["екология", "природа", "климат", "биосфера"],
                "зелен": ["екологичен", "чист", "възобновяем", "устойчив"]
            }

class AdvancedQueryProcessor:
    """Advanced query processing with expansion, HyDE, and multi-query transformation"""

    def __init__(self, config: Optional[QueryProcessingConfig] = None):
        self.config = config or QueryProcessingConfig()
        self.client = None

        # Performance optimization caches
        self._query_expansion_cache = {}
        self._hyde_document_cache = {}
        self._query_variants_cache = {}
        self._cache_ttl = timedelta(hours=1)  # Cache TTL: 1 hour
        self._initialize_openai()
        
        logger.info("🔧 Advanced Query Processor initialized")
        logger.info(f"   Query Expansion: {'✅' if self.config.enable_query_expansion else '❌'}")
        logger.info(f"   HyDE Pattern: {'✅' if self.config.enable_hyde else '❌'}")
        logger.info(f"   Multi-query: {'✅' if self.config.enable_multi_query else '❌'}")
    
    def _initialize_openai(self):
        """Initialize OpenAI client for HyDE and query generation"""
        try:
            import os
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.client = AsyncOpenAI(api_key=api_key)
                logger.info("✅ OpenAI client initialized for advanced query processing")
            else:
                logger.warning("⚠️ OpenAI API key not found - HyDE pattern disabled")
                self.config.enable_hyde = False
        except Exception as e:
            logger.error(f"❌ Failed to initialize OpenAI client: {e}")
            self.config.enable_hyde = False

    def _get_cache_key(self, text: str) -> str:
        """Generate cache key from text"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _is_cache_valid(self, timestamp: datetime) -> bool:
        """Check if cache entry is still valid"""
        return datetime.now() - timestamp < self._cache_ttl

    def _get_cached_result(self, cache: dict, key: str, cache_name: str):
        """Get cached result if valid"""
        if key in cache:
            cached_entry = cache[key]
            if self._is_cache_valid(cached_entry['timestamp']):
                logger.info(f"🔥 Using cached {cache_name} for key: {key[:8]}...")
                return cached_entry['result']
            else:
                # Remove expired entry
                del cache[key]
        return None

    def _cache_result(self, cache: dict, key: str, result: Any, cache_name: str):
        """Cache result with timestamp"""
        cache[key] = {
            'result': result,
            'timestamp': datetime.now()
        }
        logger.info(f"❄️ Cached new {cache_name} for key: {key[:8]}...")

    def expand_query_with_synonyms(self, query: str) -> List[str]:
        """Expand query with Bulgarian synonyms (with caching)"""
        if not self.config.enable_query_expansion:
            return [query]

        # Check cache first
        cache_key = self._get_cache_key(query)
        cached_result = self._get_cached_result(self._query_expansion_cache, cache_key, "query expansion")
        if cached_result is not None:
            return cached_result

        expanded_queries = [query]  # Original query first

        # Find and replace synonyms
        words = re.findall(r'\b\w+\b', query.lower())

        for word in words:
            if self.config.bulgarian_synonyms and word in self.config.bulgarian_synonyms:
                synonyms = self.config.bulgarian_synonyms[word][:self.config.max_synonyms]

                for synonym in synonyms:
                    # Create variant by replacing the word
                    expanded_query = re.sub(
                        r'\b' + re.escape(word) + r'\b',
                        synonym,
                        query,
                        flags=re.IGNORECASE
                    )
                    if expanded_query != query and expanded_query not in expanded_queries:
                        expanded_queries.append(expanded_query)

        result = expanded_queries[:self.config.max_query_variants + 1]

        # Cache the result
        self._cache_result(self._query_expansion_cache, cache_key, result, "query expansion")

        logger.info(f"📝 Query expansion: {len(result)} variants generated")
        return result
    
    async def generate_hyde_document(self, query: str) -> Optional[str]:
        """Generate hypothetical document using HyDE pattern (with caching)"""
        if not self.config.enable_hyde or not self.client:
            return None

        # Check cache first
        cache_key = self._get_cache_key(query)
        cached_result = self._get_cached_result(self._hyde_document_cache, cache_key, "HyDE document")
        if cached_result is not None:
            return cached_result

        try:
            # Detect language for appropriate prompt
            is_bulgarian = any(char in 'абвгдежзийклмнопрстуфхцчшщъьюя' for char in query.lower())
            
            if is_bulgarian:
                prompt = f"""Напиши кратък хипотетичен документ, който би отговорил на следната заявка за европейско финансиране:

Заявка: {query}

Документът трябва да съдържа:
- Конкретна информация за програми и фондове
- Критерии за кандидатстване
- Размери на финансирането
- Срокове и процедури

Документ:"""
            else:
                prompt = f"""Write a brief hypothetical document that would answer the following European funding query:

Query: {query}

The document should contain:
- Specific information about programs and funds
- Application criteria
- Funding amounts
- Deadlines and procedures

Document:"""
            
            response = await self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=self.config.hyde_temperature,
                max_tokens=self.config.hyde_max_tokens
            )
            
            hyde_doc = response.choices[0].message.content
            if hyde_doc:
                hyde_doc = hyde_doc.strip()
            else:
                hyde_doc = ""

            # Cache the result
            self._cache_result(self._hyde_document_cache, cache_key, hyde_doc, "HyDE document")

            logger.info(f"🎯 HyDE document generated: {len(hyde_doc)} characters")
            return hyde_doc

        except Exception as e:
            logger.error(f"❌ HyDE generation failed: {e}")
            return None
    
    async def generate_query_variants(self, query: str) -> List[str]:
        """Generate multiple query variants using LLM (with caching)"""
        if not self.config.enable_multi_query or not self.client:
            return [query]

        # Check cache first
        cache_key = self._get_cache_key(query)
        cached_result = self._get_cached_result(self._query_variants_cache, cache_key, "query variants")
        if cached_result is not None:
            return cached_result

        try:
            # Detect language for appropriate prompt
            is_bulgarian = any(char in 'абвгдежзийклмнопрстуфхцчшщъьюя' for char in query.lower())
            
            if is_bulgarian:
                prompt = f"""Генерирай {self.config.max_query_variants} различни варианта на следната заявка за европейско финансиране. Всеки вариант трябва да търси същата информация, но с различни думи и фрази:

Оригинална заявка: {query}

Варианти:
1."""
            else:
                prompt = f"""Generate {self.config.max_query_variants} different variants of the following European funding query. Each variant should seek the same information but use different words and phrases:

Original query: {query}

Variants:
1."""
            
            response = await self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": prompt}],
                temperature=0.8,
                max_tokens=300
            )
            
            content = response.choices[0].message.content
            if content:
                content = content.strip()
            else:
                content = ""
            
            # Parse variants from response
            variants = [query]  # Original first
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if re.match(r'^\d+\.', line):
                    variant = re.sub(r'^\d+\.\s*', '', line).strip()
                    if variant and variant != query:
                        variants.append(variant)
            
            result = variants[:self.config.max_query_variants + 1]

            # Cache the result
            self._cache_result(self._query_variants_cache, cache_key, result, "query variants")

            logger.info(f"🔄 Query variants generated: {len(result)} total")
            return result

        except Exception as e:
            logger.error(f"❌ Query variant generation failed: {e}")
            return [query]
    
    async def process_query_advanced(self, query: str) -> Dict[str, Any]:
        """Process query with all advanced techniques"""
        start_time = time.time()
        
        logger.info(f"🚀 Advanced query processing: '{query[:50]}...'")
        
        # 1. Query Expansion with Synonyms
        expanded_queries = self.expand_query_with_synonyms(query)
        
        # 2. Generate HyDE document
        hyde_doc = await self.generate_hyde_document(query)
        
        # 3. Generate query variants
        query_variants = await self.generate_query_variants(query)
        
        # Combine all queries (remove duplicates)
        all_queries = list(dict.fromkeys(expanded_queries + query_variants))
        
        processing_time = time.time() - start_time
        
        result = {
            'original_query': query,
            'expanded_queries': expanded_queries,
            'query_variants': query_variants,
            'all_queries': all_queries,
            'hyde_document': hyde_doc,
            'processing_time': processing_time,
            'total_queries': len(all_queries)
        }
        
        logger.info(f"✅ Advanced processing completed in {processing_time:.3f}s")
        logger.info(f"   📝 Expanded queries: {len(expanded_queries)}")
        logger.info(f"   🔄 Query variants: {len(query_variants)}")
        logger.info(f"   🎯 HyDE document: {'✅' if hyde_doc else '❌'}")
        logger.info(f"   📊 Total queries: {len(all_queries)}")
        
        return result

    def clear_caches(self):
        """Clear all performance optimization caches"""
        self._query_expansion_cache.clear()
        self._hyde_document_cache.clear()
        self._query_variants_cache.clear()
        logger.info("🧹 Advanced query processing caches cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'query_expansion_cache_size': len(self._query_expansion_cache),
            'hyde_document_cache_size': len(self._hyde_document_cache),
            'query_variants_cache_size': len(self._query_variants_cache),
            'cache_ttl_hours': self._cache_ttl.total_seconds() / 3600
        }

# Global instance
_query_processor = None

def get_advanced_query_processor() -> AdvancedQueryProcessor:
    """Get global advanced query processor instance"""
    global _query_processor
    if _query_processor is None:
        _query_processor = AdvancedQueryProcessor()
    return _query_processor

async def process_query_with_advanced_techniques(query: str) -> Dict[str, Any]:
    """Convenience function for advanced query processing"""
    processor = get_advanced_query_processor()
    return await processor.process_query_advanced(query)
