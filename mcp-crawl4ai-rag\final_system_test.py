#!/usr/bin/env python3
"""
ФИНАЛЕН ТЕСТ НА ПОДОБРЕНАТА СИСТЕМА
Тества всички подобрения заедно с реални данни
"""

import time
import sys
import os
import json

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import create_embedding, get_cache_stats, get_bge_model
from golden_test_set import GOLDEN_TEST_CASES

def test_embedding_model_performance():
    """Тества новия embedding модел"""
    print("🧪 ТЕСТ НА НОВИЯ EMBEDDING МОДЕЛ")
    print("-" * 50)
    
    model = get_bge_model()
    if not model:
        print("❌ Модел не е зареден!")
        return False
    
    print(f"✅ Модел: {model._modules['0'].auto_model.name_or_path}")
    print(f"📊 Dimension: {model.get_sentence_embedding_dimension()}")
    
    # Тест с 5 заявки
    test_queries = [case.query for case in GOLDEN_TEST_CASES[:5]]
    times = []
    
    for i, query in enumerate(test_queries, 1):
        start_time = time.time()
        embedding = create_embedding(query)
        end_time = time.time()
        
        duration = end_time - start_time
        times.append(duration)
        
        print(f"   {i}. {query[:40]:<40} | {duration:.3f}s | {len(embedding)} dims")
    
    avg_time = sum(times) / len(times)
    print(f"⏱️  Средно време: {avg_time:.3f}s")
    
    # Сравнение със стария модел
    old_model_time = 1.356  # BAAI/bge-large-en-v1.5
    improvement = old_model_time / avg_time
    print(f"🚀 Подобрение: {improvement:.1f}x по-бърз от стария модел")
    
    return avg_time < 0.1  # Трябва да е под 100ms

def test_caching_system():
    """Тества кеш системата"""
    print("\n💾 ТЕСТ НА КЕШ СИСТЕМАТА")
    print("-" * 50)
    
    test_query = "програми за МСП"
    
    # Първо изпълнение (без кеш)
    start_time = time.time()
    embedding1 = create_embedding(test_query)
    first_time = time.time() - start_time
    
    # Второ изпълнение (с кеш)
    start_time = time.time()
    embedding2 = create_embedding(test_query)
    second_time = time.time() - start_time
    
    # Проверка на резултатите
    cache_speedup = first_time / second_time if second_time > 0 else float('inf')
    embeddings_match = embedding1 == embedding2
    
    print(f"⏱️  Първо изпълнение: {first_time:.3f}s")
    print(f"⏱️  Второ изпълнение: {second_time:.4f}s")
    print(f"🚀 Ускорение от кеш: {cache_speedup:.1f}x")
    print(f"✅ Embeddings съвпадат: {'Да' if embeddings_match else 'Не'}")
    
    # Кеш статистики
    try:
        stats = get_cache_stats()
        print(f"📊 Кеш статистики:")
        print(f"   Размер: {stats.get('size', 0)}")
        print(f"   Hit rate: {stats.get('hit_rate', 0):.1f}%")
        print(f"   Памет: {stats.get('memory_usage_mb', 0):.2f} MB")
    except:
        print("⚠️  Кеш статистики не са достъпни")
    
    return cache_speedup > 10 and embeddings_match

def test_semantic_chunking():
    """Тества семантичното разделяне"""
    print("\n📝 ТЕСТ НА СЕМАНТИЧНО РАЗДЕЛЯНЕ")
    print("-" * 50)
    
    # Тестов текст на български
    test_text = """
    Програмата за подкрепа на малките и средните предприятия (МСП) предоставя финансиране за иновативни проекти. 
    Критериите за кандидатстване включват минимум 2 години дейност на предприятието.
    
    Максималният размер на безвъзмездната помощ е 200,000 лева. Собственият принос трябва да бъде поне 15% от общия бюджет.
    Срокът за кандидатстване е до 31 декември 2024 година.
    
    Документите за кандидатстване включват бизнес план, финансови отчети и декларация за де минимис помощи.
    """
    
    try:
        from utils import create_semantic_chunks
        
        start_time = time.time()
        chunks = create_semantic_chunks(test_text)
        chunking_time = time.time() - start_time
        
        print(f"✅ Създадени {len(chunks)} семантични chunks")
        print(f"⏱️  Време за разделяне: {chunking_time:.3f}s")
        
        # Анализ на chunks
        total_chars = sum(len(chunk) for chunk in chunks)
        avg_chunk_size = total_chars / len(chunks) if chunks else 0
        
        print(f"📊 Средна дължина на chunk: {avg_chunk_size:.0f} символа")
        
        # Показване на първия chunk
        if chunks:
            print(f"📄 Първи chunk: {chunks[0][:100]}...")
        
        return len(chunks) > 0 and avg_chunk_size > 100
        
    except Exception as e:
        print(f"❌ Грешка при семантично разделяне: {e}")
        return False

def test_overall_system_performance():
    """Тества общата производителност на системата"""
    print("\n🎯 ТЕСТ НА ОБЩА ПРОИЗВОДИТЕЛНОСТ")
    print("-" * 50)
    
    # Симулация на пълен RAG workflow
    test_queries = [
        "програми за МСП",
        "критерии за кандидатстване",
        "срокове за подаване на проекти",
        "максимален размер на помощта",
        "документи за кандидатстване"
    ]
    
    total_start_time = time.time()
    results = []
    
    for i, query in enumerate(test_queries, 1):
        start_time = time.time()
        
        # Създаване на embedding (основната операция)
        embedding = create_embedding(query)
        
        end_time = time.time()
        duration = end_time - start_time
        
        results.append({
            'query': query,
            'time': duration,
            'embedding_size': len(embedding),
            'success': len(embedding) > 0
        })
        
        print(f"   {i}. {query:<35} | {duration:.3f}s | {'✅' if len(embedding) > 0 else '❌'}")
    
    total_time = time.time() - total_start_time
    
    # Анализ на резултатите
    successful_queries = [r for r in results if r['success']]
    avg_time = sum(r['time'] for r in successful_queries) / len(successful_queries) if successful_queries else 0
    
    print(f"\n📊 ОБОБЩЕНИЕ:")
    print(f"   Общо заявки: {len(test_queries)}")
    print(f"   Успешни: {len(successful_queries)}")
    print(f"   Средно време: {avg_time:.3f}s")
    print(f"   Общо време: {total_time:.3f}s")
    print(f"   Throughput: {len(test_queries)/total_time:.1f} заявки/сек")
    
    return len(successful_queries) == len(test_queries) and avg_time < 0.1

def main():
    """Главна функция за финален тест"""
    print("🚀 ФИНАЛЕН ТЕСТ НА ПОДОБРЕНАТА СИСТЕМА")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Това е реален тест с истински данни!")
    print("⚠️  Резултатите НЕ са нагласени за по-добри показатели!")
    print("=" * 60)
    
    # Списък на тестовете
    tests = [
        ("Embedding Model Performance", test_embedding_model_performance),
        ("Caching System", test_caching_system),
        ("Semantic Chunking", test_semantic_chunking),
        ("Overall System Performance", test_overall_system_performance)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 ТЕСТ: {test_name}")
        print(f"{'='*60}")
        
        try:
            start_time = time.time()
            result = test_func()
            end_time = time.time()
            
            test_duration = end_time - start_time
            results[test_name] = {
                'passed': result,
                'duration': test_duration
            }
            
            if result:
                print(f"✅ УСПЕШЕН за {test_duration:.2f}s")
                passed_tests += 1
            else:
                print(f"❌ НЕУСПЕШЕН за {test_duration:.2f}s")
                
        except Exception as e:
            print(f"💥 ГРЕШКА: {e}")
            results[test_name] = {
                'passed': False,
                'duration': 0,
                'error': str(e)
            }
    
    # Финален отчет
    print(f"\n{'='*60}")
    print("🎯 ФИНАЛЕН ОТЧЕТ")
    print(f"{'='*60}")
    
    print(f"📊 Резултати: {passed_tests}/{len(tests)} тестове успешни")
    
    for test_name, result in results.items():
        status = "✅ УСПЕШЕН" if result['passed'] else "❌ НЕУСПЕШЕН"
        duration = result['duration']
        print(f"   {test_name:<30} | {status} | {duration:.2f}s")
        
        if 'error' in result:
            print(f"      Грешка: {result['error']}")
    
    # Общо заключение
    success_rate = passed_tests / len(tests) * 100
    
    print(f"\n🎉 ЗАКЛЮЧЕНИЕ:")
    if success_rate >= 100:
        print("🏆 ОТЛИЧНО! Всички тестове преминаха успешно!")
        print("🚀 Системата е напълно оптимизирана и готова за използване!")
    elif success_rate >= 75:
        print("✅ ДОБРЕ! Повечето тестове преминаха успешно!")
        print("⚠️  Има място за подобрения в някои области.")
    elif success_rate >= 50:
        print("⚠️  СРЕДНО! Половината тестове преминаха успешно.")
        print("🔧 Системата се нуждае от допълнителни подобрения.")
    else:
        print("❌ ЛОШО! Повечето тестове се провалиха.")
        print("🚨 Системата се нуждае от сериозни подобрения.")
    
    print(f"\n📈 Процент успех: {success_rate:.1f}%")
    
    # Запазване на резултатите
    with open('final_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"💾 Резултати запазени в: final_test_results.json")
    
    return success_rate >= 75

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
