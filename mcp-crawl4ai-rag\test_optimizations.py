#!/usr/bin/env python3
"""
Тест за проверка на оптимизациите в Phase 1: Critical Optimizations
Проверява дали HyDE и query variations са деактивирани и дали latency е намален.
"""

import asyncio
import time
import logging
from src.utils import ultra_smart_rag_query
from src.crawl4ai_mcp import get_supabase_client

# Настройка на logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_optimized_rag():
    """Тест на оптимизираната RAG система"""
    
    print("🧪 Тестване на Phase 1 оптимизации...")
    print("=" * 60)
    
    # Тест заявки
    test_queries = [
        "програми за малки и средни предприятия",
        "финансиране на иновации в България", 
        "европейски фондове за образование"
    ]
    
    client = get_supabase_client()
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Тест {i}: '{query}'")
        print("-" * 40)
        
        start_time = time.time()
        
        try:
            # Извикване на оптимизираната функция
            results = await ultra_smart_rag_query(
                query=query,
                supabase_client=client,
                final_top_k=5,
                query_expansion=False,  # Изрично деактивирано
                enable_all_optimizations=False  # Изрично деактивирано
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"⏱️  Време за изпълнение: {duration:.2f} секунди")
            print(f"📊 Брой резултати: {len(results)}")
            
            # Проверка за качество
            if results:
                print(f"📊 Резултати тип: {type(results)}")
                if isinstance(results, list) and results and isinstance(results[0], dict):
                    avg_similarity = sum(r.get('similarity', 0) for r in results) / len(results)
                    print(f"📈 Средна similarity: {avg_similarity:.3f}")

                    # Показване на топ резултат
                    top_result = results[0]
                    print(f"🏆 Топ резултат: {top_result.get('title', 'N/A')[:50]}...")
                    print(f"   Similarity: {top_result.get('similarity', 0):.3f}")
                else:
                    print(f"📊 Резултати са в неочакван формат: {str(results)[:100]}...")
            
            # Проверка за успех на оптимизацията
            if duration < 5.0:  # Очакваме под 5 секунди
                print("✅ УСПЕХ: Latency е намален!")
            else:
                print("⚠️  ВНИМАНИЕ: Latency все още е висок")
                
        except Exception as e:
            print(f"❌ ГРЕШКА: {e}")
            logger.error(f"Грешка при тест {i}: {e}", exc_info=True)
    
    print("\n" + "=" * 60)
    print("🏁 Тестването завърши!")

if __name__ == "__main__":
    asyncio.run(test_optimized_rag())
