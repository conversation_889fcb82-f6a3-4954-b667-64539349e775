#!/usr/bin/env python3
"""
🚀 ФАЗА 3 ТЕСТ: Flexible Evaluation Metrics
Тестваме с SuccessRate@3, MRR и Content Relevance вместо строга Precision@1
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils import get_supabase_client
from openai import AsyncOpenAI

# Реалистични тестови случаи базирани на съществуващи данни
REALISTIC_TESTS = [
    {
        "query": "европейски фондове за околна среда",
        "relevant_keywords": ["околна", "среда", "environment", "екология", "зелен", "климат"],
        "category": "environment"
    },
    {
        "query": "процедури за финансиране",
        "relevant_keywords": ["процедура", "финансиране", "кандидатстване", "проект", "заявление"],
        "category": "procedures"
    },
    {
        "query": "програма за човешки ресурси",
        "relevant_keywords": ["човешки", "ресурси", "образование", "обучение", "заетост", "кариера"],
        "category": "human_resources"
    },
    {
        "query": "иновации и технологии",
        "relevant_keywords": ["иновации", "технологии", "изследвания", "развитие", "наука"],
        "category": "innovation"
    },
    {
        "query": "транспортна инфраструктура",
        "relevant_keywords": ["транспорт", "инфраструктура", "пътища", "железопътен", "мобилност"],
        "category": "transport"
    },
    {
        "query": "регионално развитие",
        "relevant_keywords": ["регионален", "развитие", "местен", "община", "територия"],
        "category": "regional"
    },
    {
        "query": "малки и средни предприятия",
        "relevant_keywords": ["МСП", "предприятия", "бизнес", "стартъп", "предприемачество"],
        "category": "sme"
    },
    {
        "query": "цифрова трансформация",
        "relevant_keywords": ["цифров", "дигитален", "технологии", "IT", "информационен"],
        "category": "digital"
    }
]

def calculate_content_relevance(content, keywords):
    """Изчислява релевантността на съдържанието спрямо ключовите думи"""
    if not content:
        return 0.0
    
    content_lower = content.lower()
    matches = sum(1 for keyword in keywords if keyword.lower() in content_lower)
    return matches / len(keywords)

def calculate_mrr(results, relevant_keywords):
    """Изчислява Mean Reciprocal Rank"""
    for i, result in enumerate(results, 1):
        relevance = calculate_content_relevance(result.get('content', ''), relevant_keywords)
        if relevance > 0.3:  # Праг за релевантност
            return 1.0 / i
    return 0.0

def calculate_success_at_k(results, relevant_keywords, k=3):
    """Изчислява Success Rate @ K"""
    for result in results[:k]:
        relevance = calculate_content_relevance(result.get('content', ''), relevant_keywords)
        if relevance > 0.3:
            return 1.0
    return 0.0

async def test_flexible_evaluation():
    """Тестваме с гъвкави метрики за оценка"""
    print("🚀 ФАЗА 4 ТЕСТ: Pre-filtering + Flexible Evaluation Metrics")
    print("=" * 80)
    
    # Инициализираме клиенти
    print("📋 Инициализираме клиенти...")
    supabase = get_supabase_client()
    openai_client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    total_tests = len(REALISTIC_TESTS)
    precision_at_1 = 0
    success_at_3 = 0
    total_mrr = 0
    total_relevance = 0
    
    print(f"🎯 Стартираме {total_tests} реалистични теста...")
    print("-" * 80)
    
    for i, test_case in enumerate(REALISTIC_TESTS, 1):
        query = test_case["query"]
        keywords = test_case["relevant_keywords"]
        category = test_case["category"]
        
        print(f"\n🔍 ТЕСТ {i}/{total_tests}: '{query}' ({category})")
        
        try:
            # Генерираме embedding
            embedding_response = await openai_client.embeddings.create(
                model="text-embedding-3-large",
                input=query,
                dimensions=1024
            )
            query_embedding = embedding_response.data[0].embedding
            
            # Тестваме с новата filtered функция
            result = supabase.rpc('match_crawled_pages_v6_filtered', {
                'p_query_embedding': query_embedding,
                'p_weight_similarity': 0.4,  # Увеличаваме similarity
                'p_weight_program_name': 0.2,
                'p_weight_year': 0.1,
                'p_weight_metadata': 0.3,  # Намаляваме metadata тежестта
                'p_match_count': 10
            }).execute()
            
            if result.data:
                # Изчисляваме метриките
                top_result = result.data[0]
                top_relevance = calculate_content_relevance(top_result.get('content', ''), keywords)
                
                # Precision@1
                if top_relevance > 0.3:
                    precision_at_1 += 1
                    print(f"   ✅ Precision@1: УСПЕХ (релевантност: {top_relevance:.2f})")
                else:
                    print(f"   ❌ Precision@1: НЕУСПЕХ (релевантност: {top_relevance:.2f})")
                
                # Success@3
                success_3 = calculate_success_at_k(result.data, keywords, 3)
                success_at_3 += success_3
                if success_3 > 0:
                    print(f"   ✅ Success@3: УСПЕХ")
                else:
                    print(f"   ❌ Success@3: НЕУСПЕХ")
                
                # MRR
                mrr = calculate_mrr(result.data, keywords)
                total_mrr += mrr
                print(f"   📊 MRR: {mrr:.3f}")
                
                # Средна релевантност на топ 3
                top3_relevance = sum(calculate_content_relevance(r.get('content', ''), keywords) for r in result.data[:3]) / 3
                total_relevance += top3_relevance
                print(f"   📊 Средна релевантност топ 3: {top3_relevance:.3f}")
                
                # Показваме топ 3 резултата с релевантност
                print(f"   📋 Топ 3 резултата:")
                for j, page in enumerate(result.data[:3], 1):
                    relevance = calculate_content_relevance(page.get('content', ''), keywords)
                    metadata = page.get('metadata') or {}
                    doc_type = metadata.get('document_type', 'N/A')
                    print(f"      #{j} Score: {page['final_score']:.3f} | Relevance: {relevance:.3f} | Type: {doc_type}")
                    print(f"          {page['url']}")
            else:
                print(f"   ❌ Няма намерени резултати")
                
        except Exception as e:
            print(f"   ❌ ГРЕШКА: {e}")
    
    # Изчисляваме финалните метрики
    precision_1_pct = (precision_at_1 / total_tests) * 100
    success_3_pct = (success_at_3 / total_tests) * 100
    avg_mrr = total_mrr / total_tests
    avg_relevance = total_relevance / total_tests
    
    print(f"\n📈 ФИНАЛНИ РЕЗУЛТАТИ - FLEXIBLE EVALUATION")
    print("=" * 80)
    print(f"📊 Precision@1: {precision_1_pct:.1f}% ({precision_at_1}/{total_tests})")
    print(f"🎯 Success@3: {success_3_pct:.1f}% ({success_at_3}/{total_tests})")
    print(f"🔄 Mean Reciprocal Rank: {avg_mrr:.3f}")
    print(f"📝 Средна релевантност: {avg_relevance:.3f}")
    
    # Общ успех индекс
    overall_success = (precision_1_pct * 0.4 + success_3_pct * 0.4 + avg_mrr * 100 * 0.2)
    print(f"\n🏆 ОБЩ УСПЕХ ИНДЕКС: {overall_success:.1f}%")
    
    # Сравнение с предишни резултати
    print(f"\n🚀 АНАЛИЗ НА ПОДОБРЕНИЯТА:")
    print(f"   📈 Golden Standard (строг): 20.0%")
    print(f"   📈 Success@3 (гъвкав): {success_3_pct:.1f}%")
    print(f"   📈 Общ успех: {overall_success:.1f}%")
    
    if overall_success >= 70:
        print(f"🎉 ОТЛИЧНО! Системата работи много добре!")
    elif overall_success >= 50:
        print(f"✅ ДОБРЕ! Системата работи задоволително!")
    else:
        print(f"⚠️ Нужни са още подобрения.")
    
    print("🚀 ФАЗА 3 завършена!")

if __name__ == "__main__":
    asyncio.run(test_flexible_evaluation())
