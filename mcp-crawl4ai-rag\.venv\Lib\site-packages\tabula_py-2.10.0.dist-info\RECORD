tabula/__init__.py,sha256=eC3sEpp4pbQHwpmvVE8eJZL_A4MkZVb-BzDkMDNfkV8,325
tabula/__pycache__/__init__.cpython-313.pyc,,
tabula/__pycache__/backend.cpython-313.pyc,,
tabula/__pycache__/file_util.cpython-313.pyc,,
tabula/__pycache__/io.cpython-313.pyc,,
tabula/__pycache__/template.cpython-313.pyc,,
tabula/__pycache__/util.cpython-313.pyc,,
tabula/backend.py,sha256=BbiLvBoNsY2_cBA7bvvqtlzxA8vEcDtigyPOleuUZJI,4550
tabula/errors/__init__.py,sha256=ucHUd0_2_isb6ahe1Ml7F31D6HiMJOUZ7rlq9s0dMYw,454
tabula/errors/__pycache__/__init__.cpython-313.pyc,,
tabula/file_util.py,sha256=5KJNnt8lXqH2WgnAEIb8SrTOl3BFIbB1n7kClHiFzEM,3909
tabula/io.py,sha256=pksOsG3EytDVzs2m2TJCd3Cp8NnS35bFAdwjekGknzQ,44671
tabula/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tabula/tabula-1.0.5-jar-with-dependencies.jar,sha256=IWHj__ZZOdfOCBJHnPnKNoYNtWl_f8H6ARYe1AkqB0U,13334394
tabula/template.py,sha256=KFVsRXXr5u17_CweBX27fSkJ8ZfG2cuYzd4XykNU6Mg,2313
tabula/util.py,sha256=aHhrhYPg5btuftFfaLuwsTLx3MXFjeIhvzERqKGr0co,9485
tabula_py-2.10.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
tabula_py-2.10.0.dist-info/LICENSE,sha256=HiGi1V0yIKVXjQPkcjyHfHmRFsSKKRoAGe68JlIc1Yw,1080
tabula_py-2.10.0.dist-info/METADATA,sha256=RaB8nifxubvEJPwSkPLO1DD3q9GdeVGQkAGSQVwx8pk,7555
tabula_py-2.10.0.dist-info/RECORD,,
tabula_py-2.10.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
tabula_py-2.10.0.dist-info/WHEEL,sha256=OVMc5UfuAQiSplgO0_WdW7vXVGAt9Hdd6qtN4HotdyA,91
tabula_py-2.10.0.dist-info/top_level.txt,sha256=5M5pAlbKJLwYXe8JeWG74i3H1Iwc3Px8jmsWko8z97A,7
