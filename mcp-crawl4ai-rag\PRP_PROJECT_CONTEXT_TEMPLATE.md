# Base PRP Template v2 - MCP Crawl4AI RAG Project Context

**name**: "Base PRP Template v2 - Context-Rich with Validation Loops"  
**description**: Template optimized for AI agents to implement features with sufficient context and self-validation capabilities to achieve working code through iterative refinement.

## Core Principles
- **Context is King**: Include ALL necessary documentation, examples, and caveats
- **Validation Loops**: Provide executable tests/lints the AI can run and fix
- **Information Dense**: Use keywords and patterns from the codebase
- **Progressive Success**: Start simple, validate, then enhance
- **Global rules**: Be sure to follow all rules in CLAUDE.md

## Goal
**What needs to be built**: Advanced RAG (Retrieval-Augmented Generation) system serving as an AI consultant for European funding programs with 99% accuracy. The system should replace human consultants for Bulgarian/European funding program guidance through intelligent document retrieval and response generation.

## Why
**Business value and user impact**:
- Replace expensive human consultants with AI system for European funding programs
- Provide 24/7 access to funding program information for Bulgarian businesses
- Achieve 99% accuracy in funding program guidance and recommendations
- Enable scalable consultation services for thousands of users simultaneously

**Integration with existing features**:
- Built on MCP (Model Context Protocol) for seamless AI agent integration
- Uses Supabase for scalable vector storage and retrieval
- Integrates with Crawl4AI for automated data collection from eufunds.bg
- Compatible with multiple LLM providers (OpenAI, Cohere, local models)

**Problems this solves and for whom**:
- **For Businesses**: Complex navigation of EU funding programs simplified
- **For Consultants**: Automated research and initial program matching
- **For Government**: Reduced burden on human advisors, better program accessibility
- **For AI Developers**: Production-ready RAG system with proven 100% test success

## What
**User-visible behavior and technical requirements**:
- **Input**: Natural language queries about EU funding programs in Bulgarian/English
- **Output**: Accurate, contextual responses with program details, URLs, and specific procedures
- **Performance**: 3-5 second response time with 100% accuracy on realistic test scenarios
- **Coverage**: 367+ documents covering ОПРР, ОПТТИ, ОПОС, Interreg, МСП programs
- **Interface**: MCP server with SSE endpoint for real-time AI agent communication

## Success Criteria
✅ **100% success rate on all 8 realistic test scenarios**  
✅ **Sub-3 second average response time**  
✅ **367+ high-quality documents in Supabase database**  
✅ **Phase 4.7 intelligent clustering and optimization working**  
✅ **Production-ready MCP server with proper error handling**  
✅ **Comprehensive test coverage with automated validation**

## All Needed Context

### Documentation & References
**MUST READ - Include these in your context window**

- **file**: `PROJECT_CONTEXT.md`
  **why**: Complete system overview, architecture, and current Phase 4.7 status
  **critical**: Understanding of RAG evolution phases and current production readiness

- **file**: `src/utils.py`
  **why**: Core RAG engine with `ultra_smart_rag_query()` function - main implementation patterns
  **critical**: Phase 4.7 clustering logic, adaptive thresholds, multi-pass search patterns

- **file**: `src/crawl4ai_mcp.py`
  **why**: MCP server structure, tool definitions, error handling patterns
  **critical**: FastMCP patterns, async context management, tool parameter validation

- **file**: `README.md`
  **why**: Production deployment guide, performance metrics, system capabilities
  **critical**: Current 100% success rate achievements and Phase 4.7 features

- **file**: `crawled_pages.sql`
  **why**: Database schema, RPC functions, vector search implementation
  **critical**: Supabase vector operations, embedding dimensions (1024 for BGE-M3)

- **file**: `test_realistic_rag_evaluation.py`
  **why**: Testing framework, success criteria, realistic test scenarios
  **critical**: 100% success requirement, test data patterns, validation methodology

### Current Codebase Tree
```
mcp-crawl4ai-rag/
├── src/
│   ├── crawl4ai_mcp.py          # Main MCP server with FastMCP
│   ├── utils.py                 # Core RAG engine (ultra_smart_rag_query)
│   ├── hybrid_search.py         # Hybrid semantic + BM25 search
│   ├── intelligent_reranking.py # Multi-stage result reranking
│   ├── content_enhancement.py   # spaCy-based content processing
│   ├── advanced_ranking.py      # Context-aware ranking algorithms
│   ├── query_understanding.py   # Query analysis and intent classification
│   └── entity_utils.py          # Bulgarian entity extraction
├── test_realistic_rag_evaluation.py  # Main testing framework
├── test_eufunds_depth0.py            # Data collection script
├── test_single_msp.py                # МСП-specific testing
├── PROJECT_CONTEXT.md               # Comprehensive project documentation
├── README.md                        # Production-ready documentation
├── requirements.txt                 # 87 optimized dependencies
├── crawled_pages.sql               # Database schema
└── realistic_rag_evaluation_results.json  # Latest test results
```

### Desired Codebase Tree with Files to be Added
**Current system is production-ready. New features should extend existing patterns:**
```
src/
├── [existing files remain unchanged]
├── new_feature.py              # Follow existing module patterns
└── tests/
    └── test_new_feature.py     # 100% success rate requirement
```

### Known Gotchas of our Codebase & Library Quirks

**CRITICAL: RAG System Patterns**
- Always use `ultra_smart_rag_query` as the main entry point for Phase 4.7+
- Clustering results must maintain diversity across program types (ОПРР, ОПТТИ, ОПОС, Interreg)
- Bulgarian text processing requires special handling in entity_utils.py
- Performance optimization flags must be backward compatible

**CRITICAL: Testing Patterns**
- All tests must achieve 100% success rate to maintain production readiness
- Use realistic test data from actual Supabase content
- Test expectations must align with actual database content (see МСП programs fix)
- Environment variables: `SUPABASE_SERVICE_KEY` not `SUPABASE_KEY`

**CRITICAL: MCP Server Patterns**
- FastMCP requires async functions for all tools
- Context management must be properly handled in lifecycle hooks
- Tool parameters must include proper type hints and validation
- Error handling must preserve user-friendly messages while logging technical details

**CRITICAL: Supabase Integration**
- BGE-large-en-v1.5 embeddings are 1024-dimensional (not 1536)
- RPC functions require non-null weight parameters between 0.0 and 1.0
- Vector similarity uses cosine distance (1 - cosine_similarity)
- Metadata filtering uses JSONB containment operator (@>)

**CRITICAL: Dependencies**
- sentence-transformers for BGE-M3 model loading
- spaCy for content enhancement (English model used for Bulgarian)
- Cohere for reranking (requires API key)
- OpenAI for query analysis (optional, graceful degradation)

## Implementation Blueprint

### Data Models and Structure
**Core data models ensure type safety and consistency:**

**Supabase Schema (`crawled_pages` table)**:
```sql
- id: bigint (primary key)
- url: text (unique)
- content: text (full page content)
- metadata: jsonb (title, description, program_type)
- embedding: vector(1024) (BGE-large-en-v1.5 embeddings)
- created_at: timestamp
- updated_at: timestamp
```

**RAG Result Structure**:
```python
{
    "id": int,
    "url": str,
    "content": str,
    "metadata": dict,
    "similarity": float,
    "quality_score": float,
    "cluster_id": Optional[int]
}
```

**Query Analysis Structure**:
```python
{
    "intent": str,  # "factual", "procedural", "comparative"
    "complexity": str,  # "simple", "medium", "complex"
    "confidence": float,  # 0.0 to 1.0
    "entities": List[str],
    "program_types": List[str]  # ["ОПРР", "ОПТТИ", "ОПОС", "Interreg", "МСП"]
}
```

### List of Tasks to be Completed

**Task 1: Core RAG Engine Setup**
MODIFY `src/utils.py`:
- FIND pattern: `async def ultra_smart_rag_query`
- PRESERVE existing Phase 4.7 clustering logic
- MAINTAIN backward compatibility with all existing parameters

CREATE new features following pattern:
- MIRROR pattern from: `src/hybrid_search.py`
- MODIFY class name and core logic
- KEEP error handling pattern identical

**Task 2: MCP Server Integration**
MODIFY `src/crawl4ai_mcp.py`:
- FIND pattern: `@mcp.tool()` decorators
- INJECT new tools following existing async patterns
- PRESERVE FastMCP lifecycle management

**Task 3: Testing Implementation**
CREATE `test_new_feature.py`:
- MIRROR pattern from: `test_realistic_rag_evaluation.py`
- ACHIEVE 100% success rate requirement
- VALIDATE against actual Supabase data

**Task 4: Documentation Updates**
MODIFY `README.md` and `PROJECT_CONTEXT.md`:
- UPDATE performance metrics if improved
- DOCUMENT new capabilities
- MAINTAIN production-ready status

### Per Task Pseudocode

**Task 1 - RAG Engine Enhancement**
```python
# Pseudocode with CRITICAL details
async def enhanced_rag_feature(query: str, **kwargs) -> List[Dict]:
    # PATTERN: Always validate input first (see existing validation)
    if not query or not query.strip():
        raise ValueError("Query cannot be empty")

    # CRITICAL: Use existing BGE-M3 embedding model
    embeddings = await create_embeddings_batch([query])  # 1024-dim

    # PATTERN: Use existing Supabase RPC with proper weights
    results = await supabase_client.rpc(
        "match_crawled_pages_v4_debug",
        {
            "query_embedding": embeddings[0],
            "p_weight_similarity": 0.4,  # CRITICAL: non-null weights
            "p_weight_program_name": 0.3,
            "p_weight_year": 0.3,
            "match_count": kwargs.get("limit", 20)
        }
    )

    # PATTERN: Apply Phase 4.7 clustering and optimization
    clustered_results = await apply_intelligent_clustering(results)

    # CRITICAL: Maintain diversity across program types
    final_results = ensure_program_diversity(clustered_results)

    return final_results[:kwargs.get("final_top_k", 5)]
```

**Task 2 - MCP Tool Integration**
```python
# MCP tool following FastMCP patterns
@mcp.tool()
async def new_rag_tool(
    ctx: Context,
    query: str,
    program_type: Optional[str] = None,
    similarity_threshold: float = 0.7
) -> str:
    """
    New RAG functionality with proper MCP integration.

    Args:
        query: User query in Bulgarian or English
        program_type: Optional filter for ОПРР, ОПТТИ, ОПОС, etc.
        similarity_threshold: Minimum similarity for results
    """
    # PATTERN: Validate parameters
    if not query.strip():
        return json.dumps({"error": "Query cannot be empty"}, ensure_ascii=False)

    try:
        # CRITICAL: Use global app_context for Supabase client
        global app_context
        if not app_context or not app_context.supabase_client:
            return json.dumps({"error": "Database not available"}, ensure_ascii=False)

        # PATTERN: Call core RAG function
        results = await enhanced_rag_feature(
            query=query,
            supabase_client=app_context.supabase_client,
            program_type=program_type,
            similarity_threshold=similarity_threshold
        )

        # PATTERN: Format response for user consumption
        return format_rag_response(results, query)

    except Exception as e:
        # PATTERN: Log technical details, return user-friendly message
        logger.error(f"RAG tool error: {e}")
        return json.dumps({"error": "Search failed. Please try again."}, ensure_ascii=False)
```

### Integration Points

**DATABASE**:
- **schema**: Use existing `crawled_pages` table structure
- **rpc**: Leverage `match_crawled_pages_v4_debug` with proper weight parameters
- **indexes**: Utilize existing vector and metadata indexes

**CONFIG**:
- **add to**: Environment variables following existing patterns
- **pattern**: `NEW_FEATURE_SETTING = os.getenv('NEW_FEATURE_SETTING', 'default_value')`

**ROUTES**:
- **add to**: `src/crawl4ai_mcp.py` as new `@mcp.tool()` functions
- **pattern**: Follow existing async tool patterns with proper error handling

### Validation Loop

**Level 1: Syntax & Style**
```bash
# Run these FIRST - fix any errors before proceeding
ruff check src/ --fix  # Auto-fix what's possible
mypy src/            # Type checking

# Expected: No errors. If errors, READ the error and fix.
```

**Level 2: Unit Tests**
```python
# CREATE test_new_feature.py with these test cases:
def test_happy_path():
    """Basic functionality works with realistic data"""
    result = await new_rag_feature("Кои са програмите за МСП?")
    assert len(result) > 0
    assert result[0]["similarity"] > 0.5

def test_program_type_filtering():
    """Program type filtering works correctly"""
    result = await new_rag_feature("транспорт", program_type="ОПТТИ")
    assert all("ОПТТИ" in r["metadata"].get("program_type", "") for r in result)

def test_empty_query_handling():
    """Empty queries are handled gracefully"""
    with pytest.raises(ValueError):
        await new_rag_feature("")

def test_database_unavailable():
    """Handles database connection issues"""
    with mock.patch('supabase_client.rpc', side_effect=Exception("DB Error")):
        result = await new_rag_feature("test query")
        assert "error" in result or len(result) == 0
```

```bash
# Run and iterate until passing:
python -m pytest test_new_feature.py -v
# CRITICAL: Must achieve 100% success rate
```

**Level 3: Integration Test**
```bash
# Start the MCP server
python -m src.crawl4ai_mcp

# Test the new tool via MCP protocol
# Expected: Proper JSON response with relevant results
# If error: Check logs for stack trace and fix root cause
```

**Level 4: Realistic RAG Evaluation**
```bash
# Run comprehensive test suite
python test_realistic_rag_evaluation.py

# CRITICAL: Must maintain 100% success rate on all 8 test scenarios
# If any test fails: Fix the issue, don't modify test expectations
```

### Final Validation Checklist
✅ **All tests pass**: `python -m pytest tests/ -v`
✅ **No linting errors**: `ruff check src/`
✅ **No type errors**: `mypy src/`
✅ **Realistic RAG evaluation**: 100% success rate maintained
✅ **MCP server starts**: No import or initialization errors
✅ **Database connectivity**: Supabase operations work correctly
✅ **Performance maintained**: Response times under 5 seconds
✅ **Documentation updated**: README and PROJECT_CONTEXT reflect changes

### Anti-Patterns to Avoid
❌ **Don't modify existing RAG logic** without preserving backward compatibility
❌ **Don't skip the 100% test success requirement** - this is production critical
❌ **Don't ignore Supabase RPC parameter requirements** (weight parameters must be non-null)
❌ **Don't use sync functions** in the async MCP context
❌ **Don't hardcode values** that should use existing environment variable patterns
❌ **Don't break the established Phase 4.7 clustering** and optimization logic
❌ **Don't modify result structures** without updating all dependent code
❌ **Don't add dependencies** without updating requirements.txt following existing patterns
❌ **Don't change embedding dimensions** (BGE-M3 uses 1024, not 1536)
❌ **Don't modify test expectations** to make tests pass - fix the code instead
