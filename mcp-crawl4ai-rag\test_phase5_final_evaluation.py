#!/usr/bin/env python3
"""
🚀 ФАЗА 5 ФИНАЛЕН ТЕСТ: Comprehensive Evaluation System
Финално тестване на всички подобрения с множество метрики
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils import get_supabase_client
from openai import AsyncOpenAI

# Comprehensive test suite
COMPREHENSIVE_TESTS = [
    {
        "query": "европейски фондове за околна среда",
        "relevant_keywords": ["околна", "среда", "environment", "екология", "зелен", "климат"],
        "category": "environment",
        "difficulty": "medium"
    },
    {
        "query": "процедури за финансиране на проекти",
        "relevant_keywords": ["процедура", "финансиране", "кандидатстване", "проект", "заявление"],
        "category": "procedures",
        "difficulty": "easy"
    },
    {
        "query": "програма за развитие на човешките ресурси",
        "relevant_keywords": ["човешки", "ресурси", "образование", "обучение", "заетост", "кариера"],
        "category": "human_resources",
        "difficulty": "medium"
    },
    {
        "query": "иновации и технологии за МСП",
        "relevant_keywords": ["иновации", "технологии", "изследвания", "развитие", "наука", "МСП"],
        "category": "innovation",
        "difficulty": "hard"
    },
    {
        "query": "транспортна инфраструктура и мобилност",
        "relevant_keywords": ["транспорт", "инфраструктура", "пътища", "железопътен", "мобилност"],
        "category": "transport",
        "difficulty": "medium"
    },
    {
        "query": "регионално развитие и местни общности",
        "relevant_keywords": ["регионален", "развитие", "местен", "община", "територия"],
        "category": "regional",
        "difficulty": "hard"
    },
    {
        "query": "подкрепа за малки и средни предприятия",
        "relevant_keywords": ["МСП", "предприятия", "бизнес", "стартъп", "предприемачество"],
        "category": "sme",
        "difficulty": "medium"
    },
    {
        "query": "цифрова трансформация и дигитализация",
        "relevant_keywords": ["цифров", "дигитален", "технологии", "IT", "информационен"],
        "category": "digital",
        "difficulty": "hard"
    },
    {
        "query": "образование и професионално обучение",
        "relevant_keywords": ["образование", "обучение", "професионален", "квалификация", "умения"],
        "category": "education",
        "difficulty": "easy"
    },
    {
        "query": "енергийна ефективност и възобновяеми източници",
        "relevant_keywords": ["енергия", "ефективност", "възобновяем", "слънчев", "вятър"],
        "category": "energy",
        "difficulty": "hard"
    }
]

def calculate_content_relevance(content, keywords):
    """Изчислява релевантността на съдържанието спрямо ключовите думи"""
    if not content:
        return 0.0
    
    content_lower = content.lower()
    matches = sum(1 for keyword in keywords if keyword.lower() in content_lower)
    return matches / len(keywords)

def calculate_multiple_metrics(results, relevant_keywords):
    """Изчислява множество метрики за оценка"""
    metrics = {
        'precision_at_1': 0.0,
        'precision_at_3': 0.0,
        'success_at_3': 0.0,
        'success_at_5': 0.0,
        'mrr': 0.0,
        'avg_relevance_top3': 0.0,
        'avg_relevance_top5': 0.0
    }
    
    if not results:
        return metrics
    
    # Precision@1
    top_relevance = calculate_content_relevance(results[0].get('content', ''), relevant_keywords)
    if top_relevance > 0.3:
        metrics['precision_at_1'] = 1.0
    
    # Precision@3 и Success@3
    top3_relevant = 0
    for result in results[:3]:
        relevance = calculate_content_relevance(result.get('content', ''), relevant_keywords)
        if relevance > 0.3:
            top3_relevant += 1
            if metrics['success_at_3'] == 0.0:
                metrics['success_at_3'] = 1.0
    
    metrics['precision_at_3'] = top3_relevant / 3.0
    
    # Success@5
    for result in results[:5]:
        relevance = calculate_content_relevance(result.get('content', ''), relevant_keywords)
        if relevance > 0.3:
            metrics['success_at_5'] = 1.0
            break
    
    # MRR
    for i, result in enumerate(results[:10], 1):
        relevance = calculate_content_relevance(result.get('content', ''), relevant_keywords)
        if relevance > 0.3:
            metrics['mrr'] = 1.0 / i
            break
    
    # Average relevance
    if len(results) >= 3:
        metrics['avg_relevance_top3'] = sum(calculate_content_relevance(r.get('content', ''), relevant_keywords) for r in results[:3]) / 3
    if len(results) >= 5:
        metrics['avg_relevance_top5'] = sum(calculate_content_relevance(r.get('content', ''), relevant_keywords) for r in results[:5]) / 5
    
    return metrics

async def test_comprehensive_evaluation():
    """Comprehensive evaluation с множество метрики"""
    print("🚀 ФАЗА 5 ФИНАЛЕН ТЕСТ: Comprehensive Evaluation System")
    print("=" * 90)
    
    # Инициализираме клиенти
    print("📋 Инициализираме клиенти...")
    supabase = get_supabase_client()
    openai_client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    total_tests = len(COMPREHENSIVE_TESTS)
    cumulative_metrics = {
        'precision_at_1': 0.0,
        'precision_at_3': 0.0,
        'success_at_3': 0.0,
        'success_at_5': 0.0,
        'mrr': 0.0,
        'avg_relevance_top3': 0.0,
        'avg_relevance_top5': 0.0
    }
    
    difficulty_results = {'easy': [], 'medium': [], 'hard': []}
    
    print(f"🎯 Стартираме {total_tests} comprehensive теста...")
    print("-" * 90)
    
    for i, test_case in enumerate(COMPREHENSIVE_TESTS, 1):
        query = test_case["query"]
        keywords = test_case["relevant_keywords"]
        category = test_case["category"]
        difficulty = test_case["difficulty"]
        
        print(f"\n🔍 ТЕСТ {i}/{total_tests}: '{query}' ({category} - {difficulty})")
        
        try:
            # Генерираме embedding
            embedding_response = await openai_client.embeddings.create(
                model="text-embedding-3-large",
                input=query,
                dimensions=1024
            )
            query_embedding = embedding_response.data[0].embedding
            
            # Тестваме с финалната filtered функция
            result = supabase.rpc('match_crawled_pages_v6_filtered', {
                'p_query_embedding': query_embedding,
                'p_weight_similarity': 0.4,
                'p_weight_program_name': 0.2,
                'p_weight_year': 0.1,
                'p_weight_metadata': 0.3,
                'p_match_count': 10
            }).execute()
            
            if result.data:
                # Изчисляваме всички метрики
                metrics = calculate_multiple_metrics(result.data, keywords)
                
                # Добавяме към кумулативните резултати
                for key in cumulative_metrics:
                    cumulative_metrics[key] += metrics[key]
                
                # Добавяме към резултатите по трудност
                difficulty_results[difficulty].append(metrics)
                
                # Показваме резултатите
                print(f"   📊 P@1: {metrics['precision_at_1']:.0f} | S@3: {metrics['success_at_3']:.0f} | S@5: {metrics['success_at_5']:.0f} | MRR: {metrics['mrr']:.3f}")
                print(f"   📝 Avg Rel Top3: {metrics['avg_relevance_top3']:.3f} | Top5: {metrics['avg_relevance_top5']:.3f}")
                
                # Показваме топ 3 резултата
                print(f"   📋 Топ 3:")
                for j, page in enumerate(result.data[:3], 1):
                    relevance = calculate_content_relevance(page.get('content', ''), keywords)
                    metadata = page.get('metadata') or {}
                    doc_type = metadata.get('document_type', 'N/A')
                    print(f"      #{j} Score: {page['final_score']:.3f} | Rel: {relevance:.3f} | Type: {doc_type}")
            else:
                print(f"   ❌ Няма намерени резултати")
                
        except Exception as e:
            print(f"   ❌ ГРЕШКА: {e}")
    
    # Изчисляваме финалните метрики
    final_metrics = {key: (value / total_tests) * 100 for key, value in cumulative_metrics.items()}
    final_metrics['mrr'] = (cumulative_metrics['mrr'] / total_tests) * 100  # MRR като процент
    
    print(f"\n📈 ФИНАЛНИ РЕЗУЛТАТИ - COMPREHENSIVE EVALUATION")
    print("=" * 90)
    print(f"📊 Precision@1: {final_metrics['precision_at_1']:.1f}%")
    print(f"📊 Precision@3: {final_metrics['precision_at_3']:.1f}%")
    print(f"🎯 Success@3: {final_metrics['success_at_3']:.1f}%")
    print(f"🎯 Success@5: {final_metrics['success_at_5']:.1f}%")
    print(f"🔄 Mean Reciprocal Rank: {final_metrics['mrr']:.1f}%")
    print(f"📝 Avg Relevance Top3: {final_metrics['avg_relevance_top3']:.1f}%")
    print(f"📝 Avg Relevance Top5: {final_metrics['avg_relevance_top5']:.1f}%")
    
    # Общ успех индекс
    overall_success = (
        final_metrics['precision_at_1'] * 0.3 +
        final_metrics['success_at_3'] * 0.3 +
        final_metrics['success_at_5'] * 0.2 +
        final_metrics['mrr'] * 0.2
    )
    print(f"\n🏆 ОБЩ УСПЕХ ИНДЕКС: {overall_success:.1f}%")
    
    # Анализ по трудност
    print(f"\n📊 АНАЛИЗ ПО ТРУДНОСТ:")
    for difficulty in ['easy', 'medium', 'hard']:
        if difficulty_results[difficulty]:
            avg_success = sum(m['success_at_3'] for m in difficulty_results[difficulty]) / len(difficulty_results[difficulty]) * 100
            print(f"   {difficulty.upper()}: {avg_success:.1f}% success rate")
    
    # Сравнение с предишни резултати
    print(f"\n🚀 ЕВОЛЮЦИЯ НА СИСТЕМАТА:")
    print(f"   📈 Baseline (10%): +{overall_success - 10:.0f}% подобрение")
    print(f"   📈 Phase 1 (20%): +{overall_success - 20:.0f}% подобрение")
    print(f"   📈 Phase 2-3 (18.8%): +{overall_success - 18.8:.0f}% подобрение")
    
    if overall_success >= 50:
        print(f"🎉 ОТЛИЧНО! Системата работи много добре! Цел постигната!")
    elif overall_success >= 40:
        print(f"✅ ДОБРЕ! Системата работи задоволително!")
    else:
        print(f"⚠️ Нужни са още подобрения.")
    
    print("🚀 ФАЗА 5 и PHASE 6.0 PRECISION OPTIMIZATION завършени!")

if __name__ == "__main__":
    asyncio.run(test_comprehensive_evaluation())
