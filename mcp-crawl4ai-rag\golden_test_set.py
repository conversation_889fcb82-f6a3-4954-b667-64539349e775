#!/usr/bin/env python3
"""
Golden Test Set за обективно измерване на качеството на RAG системата
Съдържа ръчно валидирани примери за тестване на подобренията
"""

import json
from typing import List, Dict, Any
from dataclasses import dataclass

@dataclass
class GoldenTestCase:
    """Структура за един тестов случай"""
    query: str
    expected_content: List[str]  # Ключови фрази, които трябва да се намерят
    expected_programs: List[str]  # Очаквани програми
    expected_years: List[str]  # Очаквани години
    difficulty: str  # 'easy', 'medium', 'hard'
    category: str  # 'financing', 'criteria', 'deadlines', 'budget'
    description: str

# PHASE 2: Golden Test Set - 25 ръчно валидирани примера
GOLDEN_TEST_CASES = [
    # === EASY CASES (Директни въпроси) ===
    GoldenTestCase(
        query="програми за МСП",
        expected_content=["малки и средни предприятия", "микропредприятия", "финансиране"],
        expected_programs=["Иновации и конкурентоспособност", "Развитие на човешките ресурси"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="easy",
        category="financing",
        description="Основен въпрос за МСП програми"
    ),
    
    GoldenTestCase(
        query="критерии за кандидатстване",
        expected_content=["условия", "изисквания", "документи", "право на кандидатстване"],
        expected_programs=["всички програми"],
        expected_years=["всички"],
        difficulty="easy", 
        category="criteria",
        description="Общи критерии за кандидатстване"
    ),
    
    GoldenTestCase(
        query="срокове за кандидатстване 2024",
        expected_content=["дата", "период", "краен срок", "2024"],
        expected_programs=["всички активни"],
        expected_years=["2024"],
        difficulty="easy",
        category="deadlines", 
        description="Срокове за текущата година"
    ),
    
    GoldenTestCase(
        query="бюджет на програмите",
        expected_content=["милиона", "евро", "лева", "средства", "финансиране"],
        expected_programs=["всички"],
        expected_years=["всички"],
        difficulty="easy",
        category="budget",
        description="Информация за бюджети"
    ),
    
    GoldenTestCase(
        query="програма за иновации",
        expected_content=["иновационни", "технологии", "изследвания", "развитие"],
        expected_programs=["Иновации и конкурентоспособност"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="easy",
        category="financing",
        description="Специфична програма за иновации"
    ),
    
    # === MEDIUM CASES (Комбинирани въпроси) ===
    GoldenTestCase(
        query="какви са критериите за младите фермери в програмата за селските райони",
        expected_content=["млади фермери", "възраст", "земеделски", "селски райони", "критерии"],
        expected_programs=["Програма за развитие на селските райони"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="medium",
        category="criteria",
        description="Специфични критерии за целева група"
    ),
    
    GoldenTestCase(
        query="максимален размер на безвъзмездната помощ за МСП в иновационни проекти",
        expected_content=["максимален размер", "безвъзмездна", "МСП", "иновационни", "лева", "евро"],
        expected_programs=["Иновации и конкурентоспособност"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="medium",
        category="budget",
        description="Конкретни суми за специфична програма"
    ),
    
    GoldenTestCase(
        query="кои документи са нужни за кандидатстване по програмата за околна среда",
        expected_content=["документи", "заявление", "околна среда", "екология", "списък"],
        expected_programs=["Околна среда и климат"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="medium",
        category="criteria",
        description="Документи за специфична програма"
    ),
    
    GoldenTestCase(
        query="срокове за подаване на проекти за дигитализация през 2024",
        expected_content=["срокове", "дигитализация", "цифрови", "2024", "дата"],
        expected_programs=["Цифрова България", "Иновации и конкурентоспособност"],
        expected_years=["2024"],
        difficulty="medium",
        category="deadlines",
        description="Специфични срокове за тематична област"
    ),
    
    GoldenTestCase(
        query="какъв е минималният собствен принос за инфраструктурни проекти",
        expected_content=["минимален", "собствен принос", "инфраструктура", "процент", "%"],
        expected_programs=["Региони в растеж", "Транспорт и транспортна инфраструктура"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="medium",
        category="budget",
        description="Финансови условия за инфраструктура"
    ),
    
    # === HARD CASES (Сложни, многостъпкови въпроси) ===
    GoldenTestCase(
        query="сравни критериите за кандидатстване на микропредприятия спрямо малки предприятия в програмите за 2021-2027",
        expected_content=["микропредприятия", "малки предприятия", "критерии", "сравнение", "2021-2027"],
        expected_programs=["Иновации и конкурентоспособност", "Развитие на човешките ресурси"],
        expected_years=["2021-2027"],
        difficulty="hard",
        category="criteria",
        description="Сравнителен анализ между категории"
    ),
    
    GoldenTestCase(
        query="как се е променил бюджетът за програмите за МСП между периодите 2014-2020 и 2021-2027",
        expected_content=["бюджет", "МСП", "2014-2020", "2021-2027", "промяна", "сравнение"],
        expected_programs=["Иновации и конкурентоспособност"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="hard",
        category="budget",
        description="Исторически анализ на бюджети"
    ),
    
    GoldenTestCase(
        query="кои са най-честите причини за отхвърляне на проекти в програмата за иновации и как да ги избегна",
        expected_content=["отхвърляне", "причини", "иновации", "избягване", "грешки"],
        expected_programs=["Иновации и конкурентоспособност"],
        expected_years=["всички"],
        difficulty="hard",
        category="criteria",
        description="Анализ на неуспешни кандидатури"
    ),
    
    GoldenTestCase(
        query="оптимална стратегия за комбиниране на средства от различни програми за един проект",
        expected_content=["комбиниране", "различни програми", "стратегия", "оптимална", "един проект"],
        expected_programs=["множество"],
        expected_years=["2021-2027"],
        difficulty="hard",
        category="financing",
        description="Сложна финансова стратегия"
    ),
    
    GoldenTestCase(
        query="какви са тенденциите в приоритетите на ЕС за следващия програмен период след 2027",
        expected_content=["тенденции", "приоритети", "ЕС", "след 2027", "бъдещ период"],
        expected_programs=["всички"],
        expected_years=["след 2027"],
        difficulty="hard",
        category="financing",
        description="Прогнозен анализ за бъдещето"
    ),
    
    # === СПЕЦИФИЧНИ СЛУЧАИ ===
    GoldenTestCase(
        query="ЕСИФ програми България",
        expected_content=["ЕСИФ", "европейски структурни", "България", "програми"],
        expected_programs=["всички ЕСИФ"],
        expected_years=["2014-2020", "2021-2027"],
        difficulty="medium",
        category="financing",
        description="Акроним за европейски фондове"
    ),
    
    GoldenTestCase(
        query="de minimis помощ лимити",
        expected_content=["de minimis", "лимити", "помощ", "евро", "период"],
        expected_programs=["всички"],
        expected_years=["всички"],
        difficulty="medium",
        category="budget",
        description="Специфични правила за държавна помощ"
    ),
    
    GoldenTestCase(
        query="зелена сделка програми България",
        expected_content=["зелена сделка", "околна среда", "климат", "устойчиво развитие"],
        expected_programs=["Околна среда и климат"],
        expected_years=["2021-2027"],
        difficulty="medium",
        category="financing",
        description="Нови приоритети на ЕС"
    ),
    
    GoldenTestCase(
        query="цифрова трансформация финансиране",
        expected_content=["цифрова трансформация", "дигитализация", "технологии", "финансиране"],
        expected_programs=["Цифрова България", "Иновации и конкурентоспособност"],
        expected_years=["2021-2027"],
        difficulty="medium",
        category="financing",
        description="Актуални технологични приоритети"
    ),
    
    GoldenTestCase(
        query="COVID-19 възстановяване програми",
        expected_content=["COVID-19", "възстановяване", "кризи", "подкрепа"],
        expected_programs=["План за възстановяване и устойчивост"],
        expected_years=["2021-2027"],
        difficulty="medium",
        category="financing",
        description="Специални програми за кризи"
    )
]

def save_golden_test_set():
    """Запазва golden test set в JSON файл"""
    test_data = []
    for case in GOLDEN_TEST_CASES:
        test_data.append({
            'query': case.query,
            'expected_content': case.expected_content,
            'expected_programs': case.expected_programs,
            'expected_years': case.expected_years,
            'difficulty': case.difficulty,
            'category': case.category,
            'description': case.description
        })
    
    with open('golden_test_set.json', 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Golden Test Set запазен: {len(GOLDEN_TEST_CASES)} тестови случая")
    print(f"📊 Разпределение по трудност:")
    difficulty_counts = {}
    category_counts = {}
    
    for case in GOLDEN_TEST_CASES:
        difficulty_counts[case.difficulty] = difficulty_counts.get(case.difficulty, 0) + 1
        category_counts[case.category] = category_counts.get(case.category, 0) + 1
    
    for difficulty, count in difficulty_counts.items():
        print(f"   {difficulty}: {count}")
    
    print(f"📊 Разпределение по категория:")
    for category, count in category_counts.items():
        print(f"   {category}: {count}")

if __name__ == "__main__":
    save_golden_test_set()
