#!/usr/bin/env python3
"""
Debug script за проверка на BGE модел размерности
"""

import os
import sys
from sentence_transformers import SentenceTransformer
import numpy as np

def test_bge_model():
    """Тестване на BGE модел размерности"""
    
    print("=== BGE MODEL DIMENSION DEBUG ===")
    
    # Зареждане на модел
    model_name = "BAAI/bge-large-en-v1.5"
    print(f"Зареждане на модел: {model_name}")
    
    try:
        model = SentenceTransformer(model_name)
        print(f"Модел зареден успешно")
        
        # Тест текстове
        test_texts = [
            "This is a test sentence.",
            "Another test sentence for embedding.",
            "Short text",
            "A longer text that contains more information and should be processed correctly by the BGE model to generate proper embeddings."
        ]
        
        print(f"\nТестване с {len(test_texts)} текста...")
        
        for i, text in enumerate(test_texts):
            print(f"\nТекст {i+1}: '{text[:50]}...'")
            
            # Генериране на embedding
            embedding = model.encode([text])
            
            print(f"  - Тип: {type(embedding)}")
            print(f"  - Shape: {embedding.shape}")
            print(f"  - Размерност: {embedding.shape[1] if len(embedding.shape) > 1 else len(embedding)}")
            print(f"  - Първи 5 стойности: {embedding[0][:5]}")
            print(f"  - Последни 5 стойности: {embedding[0][-5:]}")
            
            # Проверка за NaN или inf
            has_nan = np.isnan(embedding).any()
            has_inf = np.isinf(embedding).any()
            print(f"  - Има NaN: {has_nan}")
            print(f"  - Има Inf: {has_inf}")
            
        # Batch тест
        print(f"\n=== BATCH TEST ===")
        batch_embeddings = model.encode(test_texts)
        print(f"Batch shape: {batch_embeddings.shape}")
        print(f"Batch размерност: {batch_embeddings.shape[1]}")
        
        # Проверка на модел информация
        print(f"\n=== MODEL INFO ===")
        print(f"Model max_seq_length: {getattr(model, 'max_seq_length', 'N/A')}")
        print(f"Model device: {getattr(model, 'device', 'N/A')}")
        
        # Проверка на конфигурация
        if hasattr(model, '_modules'):
            for name, module in model._modules.items():
                print(f"Module {name}: {type(module)}")
                if hasattr(module, 'config'):
                    config = module.config
                    if hasattr(config, 'hidden_size'):
                        print(f"  - hidden_size: {config.hidden_size}")
                    if hasattr(config, 'max_position_embeddings'):
                        print(f"  - max_position_embeddings: {config.max_position_embeddings}")
        
        return True
        
    except Exception as e:
        print(f"ГРЕШКА: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_bge_model()
    if success:
        print("\n✅ BGE модел тест завърши успешно")
    else:
        print("\n❌ BGE модел тест неуспешен")
        sys.exit(1)
