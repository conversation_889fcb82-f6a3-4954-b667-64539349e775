"""
Multi-Step Retrieval Pipeline
Комбинира Groq reranking, Program extraction и Bulgarian NLP
за постигане на 100% точност в RAG системата
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import time

# Импорти на новите модули - FIXED: absolute imports
try:
    from groq_reranker import GroqReranker, create_groq_reranker
except ImportError:
    try:
        from src.groq_reranker import GroqReranker, create_groq_reranker
    except ImportError:
        GroqReranker = None
        create_groq_reranker = None

try:
    from program_name_extractor import BulgarianProgramExtractor
except ImportError:
    try:
        from src.program_name_extractor import BulgarianProgramExtractor
    except ImportError:
        BulgarianProgramExtractor = None

try:
    from bulgarian_nlp import BulgarianNLP
except ImportError:
    try:
        from src.bulgarian_nlp import BulgarianNLP
    except ImportError:
        BulgarianNLP = None

# Импорти на съществуващи модули
# Забележка: Избягваме циркулярен импорт - ще импортираме ultra_smart_rag_query динамично

@dataclass
class PipelineConfig:
    """Конфигурация за multi-step pipeline"""
    use_groq_reranking: bool = True
    use_program_extraction: bool = True
    use_bulgarian_nlp: bool = True
    enable_query_expansion: bool = True
    enable_content_enhancement: bool = True
    max_documents: int = 10
    similarity_threshold: float = 0.3
    program_confidence_threshold: float = 0.6

@dataclass
class PipelineResult:
    """Резултат от multi-step pipeline"""
    documents: List[Dict[str, Any]]
    query_analysis: Dict[str, Any]
    program_matches: Dict[str, Any]
    processing_time: float
    pipeline_stages: List[str]
    success_metrics: Dict[str, float]

class MultiStepRetrievalPipeline:
    """
    Многостъпков RAG pipeline с интелигентно обработване
    
    Стъпки:
    1. Query Analysis & Expansion (Bulgarian NLP)
    2. Initial Retrieval (Existing RAG)
    3. Program Name Extraction
    4. Content Enhancement
    5. Groq Reranking
    6. Final Validation & Scoring
    """
    
    def __init__(self, config: Optional[PipelineConfig] = None):
        self.config = config or PipelineConfig()
        self.logger = logging.getLogger(__name__)
        
        # Инициализирай компонентите
        self._initialize_components()
        
        self.logger.info("✅ Multi-Step Retrieval Pipeline инициализиран")
    
    def _initialize_components(self):
        """Инициализира всички компоненти на pipeline-а"""
        
        # Groq Reranker
        if self.config.use_groq_reranking:
            try:
                self.groq_reranker = create_groq_reranker()
                self.logger.info("✅ Groq Reranker зареден")
            except Exception as e:
                self.logger.warning(f"⚠️ Groq Reranker грешка: {e} - деактивиран")
                self.config.use_groq_reranking = False
        
        # Program Name Extractor
        if self.config.use_program_extraction:
            try:
                self.program_extractor = BulgarianProgramExtractor()
                self.logger.info("✅ Program Name Extractor зареден")
            except Exception as e:
                self.logger.warning(f"⚠️ Program Extractor грешка: {e} - деактивиран")
                self.config.use_program_extraction = False
        
        # Bulgarian NLP
        if self.config.use_bulgarian_nlp:
            try:
                self.bulgarian_nlp = BulgarianNLP()
                self.logger.info("✅ Bulgarian NLP зареден")
            except Exception as e:
                self.logger.warning(f"⚠️ Bulgarian NLP грешка: {e} - деактивиран")
                self.config.use_bulgarian_nlp = False
    
    def process_query(self, query: str, **kwargs) -> PipelineResult:
        """
        Главна функция за обработка на заявка
        """
        start_time = time.time()
        pipeline_stages = []
        
        self.logger.info(f"🚀 Започвам multi-step обработка на: '{query}'")
        
        # Stage 1: Query Analysis & Expansion
        query_analysis = self._stage1_query_analysis(query)
        pipeline_stages.append("query_analysis")
        
        # Stage 2: Initial Retrieval
        initial_docs = self._stage2_initial_retrieval(query, query_analysis, **kwargs)
        pipeline_stages.append("initial_retrieval")
        
        # Stage 3: Program Name Extraction
        program_matches = self._stage3_program_extraction(initial_docs, query)
        pipeline_stages.append("program_extraction")
        
        # Stage 4: Content Enhancement
        enhanced_docs = self._stage4_content_enhancement(initial_docs, query_analysis, program_matches)
        pipeline_stages.append("content_enhancement")
        
        # Stage 5: Groq Reranking
        reranked_docs = self._stage5_groq_reranking(enhanced_docs, query)
        pipeline_stages.append("groq_reranking")
        
        # Stage 6: Final Validation & Scoring
        final_docs, success_metrics = self._stage6_final_validation(reranked_docs, query, query_analysis)
        pipeline_stages.append("final_validation")
        
        processing_time = time.time() - start_time
        
        result = PipelineResult(
            documents=final_docs,
            query_analysis=query_analysis,
            program_matches=program_matches,
            processing_time=processing_time,
            pipeline_stages=pipeline_stages,
            success_metrics=success_metrics
        )
        
        self.logger.info(f"✅ Pipeline завършен за {processing_time:.2f}s с {len(final_docs)} документа")
        
        return result
    
    def _stage1_query_analysis(self, query: str) -> Dict[str, Any]:
        """Stage 1: Анализ и разширяване на заявката"""
        analysis = {
            'original_query': query,
            'expanded_terms': [query],
            'keywords': [],
            'complexity': 1.0,
            'language': 'bg',
            'method': 'basic'
        }
        
        if self.config.use_bulgarian_nlp:
            try:
                # NLP анализ
                nlp_result = self.bulgarian_nlp.process_text(query)
                
                # Извлечи ключови думи
                keywords = self.bulgarian_nlp.extract_keywords(query, max_keywords=5)
                
                # Морфологично разширяване
                if self.config.enable_query_expansion:
                    expanded_terms = self.bulgarian_nlp.expand_query_with_morphology(query)
                else:
                    expanded_terms = [query]
                
                analysis.update({
                    'keywords': keywords,
                    'expanded_terms': expanded_terms,
                    'entities': [{'text': e.text, 'label': e.label} for e in nlp_result['entities']],
                    'method': nlp_result['method']
                })
                
                self.logger.info(f"🔍 Query анализ: {len(keywords)} ключови думи, {len(expanded_terms)} термина")
                
            except Exception as e:
                self.logger.warning(f"⚠️ Query анализ грешка: {e}")
        
        # Groq query complexity analysis
        if self.config.use_groq_reranking:
            try:
                groq_analysis = self.groq_reranker.analyze_query_complexity(query)
                analysis['complexity'] = groq_analysis.get('complexity_score', 1.0)
                analysis['groq_analysis'] = groq_analysis
            except Exception as e:
                self.logger.warning(f"⚠️ Groq анализ грешка: {e}")
        
        return analysis
    
    def _stage2_initial_retrieval(self, query: str, query_analysis: Dict[str, Any], **kwargs) -> List[Dict[str, Any]]:
        """Stage 2: Първоначално извличане с RAG"""
        try:
            # Използвай разширените термини ако са налични
            search_query = query
            if self.config.enable_query_expansion and query_analysis.get('expanded_terms'):
                # Комбинирай оригиналната заявка с разширените термини
                expanded_terms = query_analysis['expanded_terms'][:3]  # Топ 3
                search_query = f"{query} {' '.join(expanded_terms)}"
            
            # Извикай съществуващата RAG функция (динамичен импорт)
            try:
                # Try relative import first
                from .utils import ultra_smart_rag_query, get_supabase_client
                import asyncio

                rag_result = asyncio.run(ultra_smart_rag_query(
                    query=search_query,
                    supabase_client=get_supabase_client(),
                    similarity_threshold=self.config.similarity_threshold,
                    final_top_k=self.config.max_documents
                ))
            except ImportError:
                # Fallback to absolute import
                try:
                    from src.utils import ultra_smart_rag_query, get_supabase_client
                    import asyncio

                    rag_result = asyncio.run(ultra_smart_rag_query(
                        query=search_query,
                        supabase_client=get_supabase_client(),
                        similarity_threshold=self.config.similarity_threshold,
                        final_top_k=self.config.max_documents
                    ))
                except ImportError:
                    # Final fallback - използвай основна RAG функция
                    from src.utils import get_supabase_client
                    import asyncio

                # Основна RAG заявка без циркулярен импорт
                rag_result = {
                    'documents': [],
                    'total_found': 0,
                    'processing_time': 0.0,
                    'method': 'fallback_basic_rag'
                }
            
            documents = rag_result.get('results', [])
            
            self.logger.info(f"📚 Първоначално извличане: {len(documents)} документа")
            
            return documents
            
        except Exception as e:
            self.logger.error(f"❌ Грешка в първоначално извличане: {e}")
            return []
    
    def _stage3_program_extraction(self, documents: List[Dict[str, Any]], query: str) -> Dict[str, Any]:
        """Stage 3: Извличане на програмни имена"""
        program_matches = {
            'total_programs': 0,
            'by_document': {},
            'unique_programs': set(),
            'confidence_scores': []
        }
        
        if not self.config.use_program_extraction:
            return program_matches
        
        try:
            # Извлечи програми от всички документи
            extraction_results = self.program_extractor.extract_from_documents(documents, query)
            
            for doc_id, matches in extraction_results.items():
                program_matches['by_document'][doc_id] = [
                    {
                        'name': match.exact_name,
                        'confidence': match.confidence,
                        'type': match.match_type
                    }
                    for match in matches
                ]
                
                # Събери статистики
                for match in matches:
                    if match.confidence >= self.config.program_confidence_threshold:
                        program_matches['unique_programs'].add(match.exact_name)
                        program_matches['confidence_scores'].append(match.confidence)
            
            program_matches['total_programs'] = len(program_matches['unique_programs'])
            program_matches['unique_programs'] = list(program_matches['unique_programs'])
            
            self.logger.info(f"🏷️ Извлечени програми: {program_matches['total_programs']} уникални")
            
        except Exception as e:
            self.logger.warning(f"⚠️ Program extraction грешка: {e}")
        
        return program_matches
    
    def _stage4_content_enhancement(self, documents: List[Dict[str, Any]], 
                                   query_analysis: Dict[str, Any], 
                                   program_matches: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Stage 4: Обогатяване на съдържанието"""
        enhanced_docs = []
        
        for i, doc in enumerate(documents):
            enhanced_doc = doc.copy()
            
            # Добави програмна информация
            if self.config.use_program_extraction:
                try:
                    doc_programs = self.program_extractor.enhance_rag_results([doc], query_analysis['original_query'])
                    if doc_programs:
                        enhanced_doc.update(doc_programs[0])
                except Exception as e:
                    self.logger.warning(f"⚠️ Program enhancement грешка за документ {i}: {e}")
            
            # Добави NLP анализ
            if self.config.use_bulgarian_nlp and self.config.enable_content_enhancement:
                try:
                    content = doc.get('content', '')
                    if content:
                        content_keywords = self.bulgarian_nlp.extract_keywords(content, max_keywords=5)
                        enhanced_doc['content_keywords'] = content_keywords
                        
                        # Изчисли keyword overlap с query
                        query_keywords = set(query_analysis.get('keywords', []))
                        content_keywords_set = set(content_keywords)
                        overlap = len(query_keywords.intersection(content_keywords_set))
                        
                        if query_keywords:
                            enhanced_doc['keyword_overlap_score'] = overlap / len(query_keywords)
                        else:
                            enhanced_doc['keyword_overlap_score'] = 0.0
                        
                except Exception as e:
                    self.logger.warning(f"⚠️ Content enhancement грешка за документ {i}: {e}")
            
            enhanced_docs.append(enhanced_doc)
        
        self.logger.info(f"🔧 Content enhancement: {len(enhanced_docs)} документа обогатени")
        
        return enhanced_docs
    
    def _stage5_groq_reranking(self, documents: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """Stage 5: Groq reranking"""
        if not self.config.use_groq_reranking:
            return documents
        
        try:
            reranked_docs = self.groq_reranker.stage3_reranking(query, documents)
            self.logger.info(f"🤖 Groq reranking: {len(reranked_docs)} документа преранжирани")
            return reranked_docs
            
        except Exception as e:
            self.logger.warning(f"⚠️ Groq reranking грешка: {e}")
            return documents
    
    def _stage6_final_validation(self, documents: List[Dict[str, Any]], 
                                query: str, 
                                query_analysis: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], Dict[str, float]]:
        """Stage 6: Финална валидация и scoring"""
        
        # Изчисли финални scores
        for doc in documents:
            final_score = self._calculate_final_score(doc, query_analysis)
            doc['final_pipeline_score'] = final_score
        
        # Сортирай по финален score
        documents.sort(key=lambda d: d.get('final_pipeline_score', 0), reverse=True)
        
        # Филтрирай по threshold
        filtered_docs = [
            doc for doc in documents 
            if doc.get('similarity_score', 0) >= self.config.similarity_threshold
        ]
        
        # Изчисли success metrics
        success_metrics = self._calculate_success_metrics(filtered_docs, query_analysis)
        
        self.logger.info(f"✅ Финална валидация: {len(filtered_docs)} документа останаха")
        
        return filtered_docs, success_metrics
    
    def _calculate_final_score(self, doc: Dict[str, Any], query_analysis: Dict[str, Any]) -> float:
        """Изчислява финален score за документ"""
        
        # Базов similarity score
        similarity_score = doc.get('similarity_score', 0.0)
        
        # Program name score
        program_score = doc.get('program_name_score', 0.0)
        
        # Keyword overlap score
        keyword_score = doc.get('keyword_overlap_score', 0.0)
        
        # Groq scores
        groq_score = doc.get('groq_score', doc.get('groq_fallback_score', 0.0))
        if isinstance(groq_score, str):
            groq_score = 0.0
        
        # Нормализирай Groq score (ако е в скала 1-10)
        if groq_score > 1.0:
            groq_score = groq_score / 10.0
        
        # Weighted combination
        final_score = (
            similarity_score * 0.4 +
            program_score * 0.25 +
            keyword_score * 0.15 +
            groq_score * 0.2
        )
        
        return min(final_score, 1.0)
    
    def _calculate_success_metrics(self, documents: List[Dict[str, Any]], 
                                  query_analysis: Dict[str, Any]) -> Dict[str, float]:
        """Изчислява success metrics за pipeline-а"""
        
        if not documents:
            return {
                'document_count': 0,
                'avg_similarity': 0.0,
                'avg_program_score': 0.0,
                'avg_final_score': 0.0,
                'has_programs': 0.0
            }
        
        # Основни метрики
        similarities = [doc.get('similarity_score', 0) for doc in documents]
        program_scores = [doc.get('program_name_score', 0) for doc in documents]
        final_scores = [doc.get('final_pipeline_score', 0) for doc in documents]
        
        # Програмни метрики
        docs_with_programs = sum(1 for doc in documents if doc.get('extracted_programs'))
        
        return {
            'document_count': len(documents),
            'avg_similarity': sum(similarities) / len(similarities) if similarities else 0.0,
            'avg_program_score': sum(program_scores) / len(program_scores) if program_scores else 0.0,
            'avg_final_score': sum(final_scores) / len(final_scores) if final_scores else 0.0,
            'has_programs': docs_with_programs / len(documents) if documents else 0.0
        }
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """Връща информация за pipeline конфигурацията"""
        return {
            'config': {
                'use_groq_reranking': self.config.use_groq_reranking,
                'use_program_extraction': self.config.use_program_extraction,
                'use_bulgarian_nlp': self.config.use_bulgarian_nlp,
                'enable_query_expansion': self.config.enable_query_expansion,
                'enable_content_enhancement': self.config.enable_content_enhancement
            },
            'components': {
                'groq_available': hasattr(self, 'groq_reranker'),
                'program_extractor_available': hasattr(self, 'program_extractor'),
                'bulgarian_nlp_available': hasattr(self, 'bulgarian_nlp')
            }
        }


# Factory функция
def create_multi_step_pipeline(config: Optional[PipelineConfig] = None) -> MultiStepRetrievalPipeline:
    """Factory функция за създаване на pipeline"""
    return MultiStepRetrievalPipeline(config)


def test_multi_step_pipeline():
    """Тест функция за multi-step pipeline"""
    
    # Създай pipeline
    pipeline = create_multi_step_pipeline()
    
    print("🧪 Тестване на Multi-Step Pipeline...")
    print(f"📊 Pipeline Info: {pipeline.get_pipeline_info()}")
    
    # Тестова заявка
    test_query = "Кои са програмите за развитие на регионите в България?"
    
    print(f"\n🔍 Тестова заявка: '{test_query}'")
    
    # Обработи заявката
    try:
        result = pipeline.process_query(test_query)
        
        print(f"\n✅ Pipeline резултат:")
        print(f"  📄 Документи: {len(result.documents)}")
        print(f"  ⏱️ Време: {result.processing_time:.2f}s")
        print(f"  🔧 Стъпки: {', '.join(result.pipeline_stages)}")
        print(f"  📊 Метрики: {result.success_metrics}")
        
        # Покажи топ 3 документа
        for i, doc in enumerate(result.documents[:3]):
            title = doc.get('title', 'Без заглавие')[:50]
            score = doc.get('final_pipeline_score', 0)
            programs = doc.get('extracted_programs', [])
            
            print(f"\n  📄 Документ {i+1}: {title}...")
            print(f"     Score: {score:.3f}")
            if programs:
                program_names = [p['name'] for p in programs[:2]]
                print(f"     Програми: {', '.join(program_names)}")
        
    except Exception as e:
        print(f"❌ Pipeline грешка: {e}")


if __name__ == "__main__":
    test_multi_step_pipeline()
