#!/usr/bin/env python3
"""
Phase 8.2: Cross-encoder Reranking Testing
Testing the highest priority improvement (⭐⭐⭐⭐⭐) recommended by external LLM models
"""

import os
import asyncio
import time
from typing import List, Dict, Any
from supabase import create_client

# Test queries for comprehensive evaluation
TEST_QUERIES = [
    "европейски фондове за иновации",
    "програми за малки предприятия",
    "финансиране на научни изследвания",
    "ОПИК програма за дигитализация",
    "Хоризонт Европа за стартъп компании",
    "зелена сделка европейски фондове",
    "дигитална трансформация финансиране ЕС",
    "устойчиво развитие програми България"
]

def test_cross_encoder_reranker_integration():
    """Test cross-encoder reranker integration in utils.py"""
    print("\n🧪 Testing Cross-encoder Reranker Integration...")
    
    try:
        from src.utils import search_documents_with_text_hybrid_reranked
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        async def run_test():
            test_query = "европейски фондове за иновации"
            
            print(f"🔍 Testing query: '{test_query}'")
            
            # Test with reranking enabled
            start_time = time.time()
            reranked_results = await search_documents_with_text_hybrid_reranked(
                client=supabase,
                query_text=test_query,
                match_count=5,
                enable_reranking=True,
                rerank_top_k=10
            )
            rerank_time = time.time() - start_time
            
            if reranked_results:
                avg_final_score = sum(r.get('final_score', 0) for r in reranked_results) / len(reranked_results)
                avg_rerank_score = sum(r.get('rerank_score', 0) for r in reranked_results) / len(reranked_results)
                
                print(f"   ✅ Reranked: {len(reranked_results)} results")
                print(f"   📊 Avg final score: {avg_final_score:.3f}")
                print(f"   🧠 Avg rerank score: {avg_rerank_score:.3f}")
                print(f"   ⏱️ Time: {rerank_time:.3f}s")
                
                return True
            else:
                print("   ❌ No reranked results")
                return False
        
        return asyncio.run(run_test())
        
    except Exception as e:
        print(f"❌ Cross-encoder integration test failed: {e}")
        return False

def test_reranking_vs_hybrid_comparison():
    """Compare reranked results vs regular hybrid search"""
    print("\n🧪 Testing Reranking vs Hybrid Comparison...")
    
    try:
        from src.utils import search_documents_with_text_hybrid_reranked, search_documents_with_text_hybrid
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        async def run_comparison():
            test_query = "програми за малки предприятия"
            
            print(f"🔍 Comparing query: '{test_query}'")
            
            # Test regular hybrid search
            start_time = time.time()
            hybrid_results = search_documents_with_text_hybrid(
                supabase, test_query, match_count=5
            )
            hybrid_time = time.time() - start_time
            
            # Test reranked hybrid search
            start_time = time.time()
            reranked_results = await search_documents_with_text_hybrid_reranked(
                client=supabase,
                query_text=test_query,
                match_count=5,
                enable_reranking=True
            )
            rerank_time = time.time() - start_time
            
            if hybrid_results and reranked_results:
                # Calculate average scores
                hybrid_avg = sum(r.get('hybrid_score', 0) for r in hybrid_results) / len(hybrid_results)
                rerank_avg = sum(r.get('final_score', 0) for r in reranked_results) / len(reranked_results)
                
                improvement = ((rerank_avg - hybrid_avg) / hybrid_avg * 100) if hybrid_avg > 0 else 0
                
                print(f"   📊 Hybrid: {len(hybrid_results)} results, avg score: {hybrid_avg:.3f}, time: {hybrid_time:.3f}s")
                print(f"   🧠 Reranked: {len(reranked_results)} results, avg score: {rerank_avg:.3f}, time: {rerank_time:.3f}s")
                print(f"   📈 Improvement: {improvement:+.1f}%")
                
                # Success if reranking shows improvement
                return improvement > 0
            else:
                print("   ❌ Missing results for comparison")
                return False
        
        return asyncio.run(run_comparison())
        
    except Exception as e:
        print(f"❌ Reranking comparison test failed: {e}")
        return False

def test_performance_across_queries():
    """Test reranking performance across multiple queries"""
    print("\n🧪 Testing Performance Across Multiple Queries...")
    
    try:
        from src.utils import search_documents_with_text_hybrid_reranked
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        async def run_performance_test():
            successful_queries = 0
            total_time = 0
            total_score = 0
            
            for i, query in enumerate(TEST_QUERIES[:5], 1):  # Test first 5 queries
                print(f"🔍 Query {i}/5: '{query[:30]}...'")
                
                start_time = time.time()
                results = await search_documents_with_text_hybrid_reranked(
                    client=supabase,
                    query_text=query,
                    match_count=5,
                    enable_reranking=True
                )
                query_time = time.time() - start_time
                
                if results:
                    avg_score = sum(r.get('final_score', 0) for r in results) / len(results)
                    successful_queries += 1
                    total_time += query_time
                    total_score += avg_score
                    
                    print(f"   ✅ {len(results)} results, avg score: {avg_score:.3f}, time: {query_time:.3f}s")
                else:
                    print(f"   ❌ No results")
            
            if successful_queries > 0:
                avg_time = total_time / successful_queries
                avg_score = total_score / successful_queries
                success_rate = (successful_queries / 5) * 100
                
                print(f"\n📊 PERFORMANCE SUMMARY:")
                print(f"   Success Rate: {success_rate:.1f}%")
                print(f"   Average Score: {avg_score:.3f}")
                print(f"   Average Time: {avg_time:.3f}s")
                
                # Success if >80% success rate and reasonable performance
                return success_rate >= 80 and avg_score > 1.0
            else:
                print("   ❌ No successful queries")
                return False
        
        return asyncio.run(run_performance_test())
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

def test_reranking_configuration():
    """Test different reranking configurations"""
    print("\n🧪 Testing Reranking Configuration Options...")
    
    try:
        from src.utils import search_documents_with_text_hybrid_reranked
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        async def run_config_test():
            test_query = "финансиране на научни изследвания"
            
            configs = [
                {"rerank_top_k": 10, "match_count": 5, "name": "Standard (10→5)"},
                {"rerank_top_k": 20, "match_count": 5, "name": "Extended (20→5)"},
                {"rerank_top_k": 15, "match_count": 10, "name": "Balanced (15→10)"}
            ]
            
            best_score = 0
            best_config = None
            
            for config in configs:
                print(f"🔧 Testing {config['name']}...")
                
                start_time = time.time()
                results = await search_documents_with_text_hybrid_reranked(
                    client=supabase,
                    query_text=test_query,
                    match_count=config['match_count'],
                    rerank_top_k=config['rerank_top_k'],
                    enable_reranking=True
                )
                config_time = time.time() - start_time
                
                if results:
                    avg_score = sum(r.get('final_score', 0) for r in results) / len(results)
                    print(f"   📊 {len(results)} results, avg score: {avg_score:.3f}, time: {config_time:.3f}s")
                    
                    if avg_score > best_score:
                        best_score = avg_score
                        best_config = config['name']
                else:
                    print(f"   ❌ No results")
            
            if best_config:
                print(f"\n🏆 BEST CONFIGURATION: {best_config} with score {best_score:.3f}")
                return True
            else:
                print("   ❌ No successful configurations")
                return False
        
        return asyncio.run(run_config_test())
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run all Phase 8.2 tests"""
    print("🚀 Phase 8.2: Cross-encoder Reranking Testing")
    print("=" * 60)
    
    tests = [
        ("Cross-encoder Integration", test_cross_encoder_reranker_integration),
        ("Reranking vs Hybrid", test_reranking_vs_hybrid_comparison),
        ("Performance Across Queries", test_performance_across_queries),
        ("Configuration Testing", test_reranking_configuration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}...")
        try:
            if test_func():
                print(f"✅ PASS {test_name}")
                passed += 1
            else:
                print(f"❌ FAIL {test_name}")
        except Exception as e:
            print(f"❌ ERROR {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 PHASE 8.2 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ PASS {test_name}" if passed == total else f"❌ FAIL {test_name}")
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Cross-encoder reranking is working perfectly!")
    else:
        print("⚠️ Some tests failed. Review issues before proceeding.")

if __name__ == "__main__":
    main()
