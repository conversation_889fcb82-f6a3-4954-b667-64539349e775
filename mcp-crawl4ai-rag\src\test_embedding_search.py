#!/usr/bin/env python3
"""
Директен тест на embedding search функционалност
"""

import os
import sys
import asyncio
import json
from dotenv import load_dotenv
from supabase import create_client, Client

# Добавяне на src директорията към path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import create_embedding

async def test_embedding_search():
    """Тестване на embedding search функционалност"""
    
    print("=== EMBEDDING SEARCH TEST ===")
    
    # Зареждане на environment variables
    load_dotenv()
    
    # Supabase конфигурация
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        print("❌ Липсват Supabase credentials")
        return
    
    # Създаване на клиент
    supabase: Client = create_client(url, key)
    
    # Тест запитвания
    test_queries = [
        "европейски фондове за иновации",
        "финансиране за малки и средни предприятия",
        "програми за дигитализация",
        "подкрепа за стартъпи",
        "зелени технологии финансиране"
    ]
    
    print(f"Тестване с {len(test_queries)} запитвания...")
    
    total_score = 0
    successful_queries = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Тест {i}: '{query}' ---")
        
        try:
            # Създаване на embedding за запитването
            print("Създаване на embedding...")
            query_embedding = create_embedding(query)
            
            if not query_embedding:
                print("❌ Неуспешно създаване на embedding")
                continue
            
            print(f"✅ Embedding създаден: {len(query_embedding)} размерности")
            
            # Търсене в базата данни
            print("Търсене в базата данни...")
            response = supabase.rpc('match_crawled_pages_debug_simple', {
                'p_query_embedding': query_embedding,
                'p_similarity_threshold': 0.1,
                'p_limit': 5
            }).execute()
            
            if response.data:
                results = response.data
                print(f"✅ Намерени {len(results)} резултата")

                # Debug: показване на първия резултат
                if results:
                    print(f"DEBUG: Първи резултат: {results[0]}")

                # Изчисляване на оценки
                scores = []
                for result in results:
                    if result is None:
                        continue

                    similarity = result.get('similarity', 0)
                    score = similarity * 100  # Конвертиране в проценти
                    scores.append(score)

                    page_url = result.get('page_url') or 'N/A'
                    content_preview = result.get('content_preview') or 'N/A'

                    print(f"   📄 {page_url[:60]}...")
                    print(f"      Сходство: {score:.1f}%")
                    print(f"      Съдържание: {content_preview[:100]}...")
                
                if scores:
                    avg_score = sum(scores) / len(scores)
                    print(f"📊 Средна оценка: {avg_score:.1f}%")
                    
                    total_score += avg_score
                    successful_queries += 1
                else:
                    print("⚠️ Няма резултати с оценки")
            else:
                print("❌ Няма резултати от базата данни")
                
        except Exception as e:
            print(f"❌ Грешка: {e}")
            import traceback
            traceback.print_exc()
    
    # Обобщение
    print(f"\n=== ОБОБЩЕНИЕ ===")
    print(f"Успешни запитвания: {successful_queries}/{len(test_queries)}")
    
    if successful_queries > 0:
        overall_avg = total_score / successful_queries
        print(f"Обща средна оценка: {overall_avg:.1f}%")
        
        # Оценка на качеството
        if overall_avg >= 70:
            print("🎉 ОТЛИЧНО качество (≥70%)")
        elif overall_avg >= 50:
            print("✅ ДОБРО качество (50-69%)")
        elif overall_avg >= 30:
            print("⚠️ СРЕДНО качество (30-49%)")
        else:
            print("❌ НИСКО качество (<30%)")
    else:
        print("❌ Няма успешни запитвания")
    
    return successful_queries, total_score / successful_queries if successful_queries > 0 else 0

if __name__ == "__main__":
    success_count, avg_score = asyncio.run(test_embedding_search())
    
    print(f"\n🏁 ФИНАЛЕН РЕЗУЛТАТ:")
    print(f"   Успешност: {success_count}/5 запитвания")
    print(f"   Средна оценка: {avg_score:.1f}%")
    
    if success_count >= 4 and avg_score >= 50:
        print("✅ Embedding search системата работи добре!")
    else:
        print("⚠️ Embedding search системата се нуждае от подобрения")
