"""
🎯 Test Multi-modal Document Processing Capabilities
==================================================

Comprehensive tests for the new multi-modal processing system:
- PDF processing with OCR and table extraction
- Image analysis with Bulgarian text recognition
- Form detection and field extraction
- Integration with RAG system

Author: Augment Agent
Date: 2025-01-05
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, List
import tempfile
import requests
from io import BytesIO

# Add src to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from multimodal_processor import MultiModalProcessor, MultiModalConfig, ProcessingResult
    MULTIMODAL_AVAILABLE = True
except ImportError:
    MULTIMODAL_AVAILABLE = False
    print("❌ Multi-modal processor not available")

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiModalTester:
    """
    🎯 Comprehensive Multi-Modal Testing Suite
    
    Tests all aspects of multi-modal document processing:
    1. PDF processing with text extraction and OCR
    2. Image processing with Bulgarian text recognition
    3. Table extraction from documents
    4. Form detection and field extraction
    5. Integration with existing RAG pipeline
    """
    
    def __init__(self):
        self.config = MultiModalConfig(
            ocr_language="bul+eng",
            enhance_images=True,
            extract_tables=True,
            use_vision_model=bool(os.getenv("OPENAI_API_KEY")),
            use_bulgarian_nlp=True,
            max_file_size_mb=10  # Smaller for testing
        )
        
        if MULTIMODAL_AVAILABLE:
            self.processor = MultiModalProcessor(self.config)
            logger.info("✅ Multi-modal processor initialized for testing")
        else:
            self.processor = None
            logger.error("❌ Multi-modal processor not available")
    
    async def test_pdf_processing(self) -> Dict[str, Any]:
        """Test PDF processing capabilities"""
        logger.info("🔍 Testing PDF processing...")
        
        if not self.processor:
            return {"success": False, "error": "Processor not available"}
        
        # Test with a sample EU program PDF (if available)
        test_urls = [
            "https://www.eufunds.bg/sites/default/files/uploads/opik/documents/OPIK_2021-2027_BG.pdf",
            "https://ec.europa.eu/info/sites/default/files/about_the_european_commission/eu_budget/multiannual-financial-framework-2021-2027_en.pdf"
        ]
        
        results = []
        
        for url in test_urls:
            try:
                logger.info(f"📄 Testing PDF from: {url}")
                
                # Download PDF
                response = requests.get(url, timeout=30)
                if response.status_code != 200:
                    logger.warning(f"⚠️ Failed to download PDF from {url}")
                    continue
                
                pdf_content = response.content
                
                # Process with multi-modal processor
                result = await self.processor.process_document(
                    content=pdf_content,
                    content_type="application/pdf",
                    source_url=url
                )
                
                test_result = {
                    "url": url,
                    "success": result.success,
                    "processing_time": result.processing_time,
                    "text_length": len(result.extracted_text),
                    "tables_count": len(result.tables),
                    "images_count": len(result.images),
                    "forms_count": len(result.forms),
                    "confidence_score": result.confidence_score,
                    "metadata": result.metadata
                }
                
                if result.error_message:
                    test_result["error"] = result.error_message
                
                results.append(test_result)
                
                logger.info(f"✅ PDF processing result: "
                          f"success={result.success}, "
                          f"text={len(result.extracted_text)} chars, "
                          f"tables={len(result.tables)}, "
                          f"time={result.processing_time:.2f}s")
                
                # Test only first PDF to avoid long processing
                break
                
            except Exception as e:
                logger.error(f"❌ PDF processing failed for {url}: {e}")
                results.append({
                    "url": url,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "test_type": "pdf_processing",
            "success": len([r for r in results if r.get("success")]) > 0,
            "results": results,
            "summary": f"Tested {len(results)} PDFs, {len([r for r in results if r.get('success')])} successful"
        }
    
    async def test_image_processing(self) -> Dict[str, Any]:
        """Test image processing with OCR"""
        logger.info("🔍 Testing image processing...")
        
        if not self.processor:
            return {"success": False, "error": "Processor not available"}
        
        # Test with sample images containing Bulgarian text
        test_urls = [
            "https://www.eufunds.bg/sites/default/files/styles/large/public/2021-06/OPIK%202021-2027%20-%20infographic.jpg",
            "https://ec.europa.eu/regional_policy/images/thematic-objectives/to1_en.jpg"
        ]
        
        results = []
        
        for url in test_urls:
            try:
                logger.info(f"🖼️ Testing image from: {url}")
                
                # Download image
                response = requests.get(url, timeout=30)
                if response.status_code != 200:
                    logger.warning(f"⚠️ Failed to download image from {url}")
                    continue
                
                image_content = response.content
                
                # Process with multi-modal processor
                result = await self.processor.process_document(
                    content=image_content,
                    content_type="image/jpeg",
                    source_url=url
                )
                
                test_result = {
                    "url": url,
                    "success": result.success,
                    "processing_time": result.processing_time,
                    "text_length": len(result.extracted_text),
                    "tables_count": len(result.tables),
                    "forms_count": len(result.forms),
                    "confidence_score": result.confidence_score,
                    "extracted_text_preview": result.extracted_text[:200] + "..." if len(result.extracted_text) > 200 else result.extracted_text,
                    "metadata": result.metadata
                }
                
                if result.error_message:
                    test_result["error"] = result.error_message
                
                results.append(test_result)
                
                logger.info(f"✅ Image processing result: "
                          f"success={result.success}, "
                          f"text={len(result.extracted_text)} chars, "
                          f"time={result.processing_time:.2f}s")
                
                # Test only first image to avoid long processing
                break
                
            except Exception as e:
                logger.error(f"❌ Image processing failed for {url}: {e}")
                results.append({
                    "url": url,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "test_type": "image_processing",
            "success": len([r for r in results if r.get("success")]) > 0,
            "results": results,
            "summary": f"Tested {len(results)} images, {len([r for r in results if r.get('success')])} successful"
        }
    
    async def test_table_extraction(self) -> Dict[str, Any]:
        """Test table extraction capabilities"""
        logger.info("🔍 Testing table extraction...")
        
        if not self.processor:
            return {"success": False, "error": "Processor not available"}
        
        # Create a simple test PDF with table content
        try:
            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
            from reportlab.lib import colors
            
            # Create test PDF with table
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
                doc = SimpleDocTemplate(tmp_file.name, pagesize=letter)
                
                # Sample EU program data table
                data = [
                    ['Програма', 'Бюджет (млн. лв.)', 'Период', 'Статус'],
                    ['ОПИК', '1200', '2021-2027', 'Активна'],
                    ['ОПРР', '800', '2021-2027', 'Активна'],
                    ['ОПОС', '600', '2021-2027', 'В подготовка'],
                    ['Хоризонт Европа', '2000', '2021-2027', 'Активна']
                ]
                
                table = Table(data)
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 14),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                doc.build([table])
                
                # Read the generated PDF
                with open(tmp_file.name, 'rb') as f:
                    pdf_content = f.read()
                
                # Process with multi-modal processor
                result = await self.processor.process_document(
                    content=pdf_content,
                    content_type="application/pdf",
                    source_url="test_table.pdf"
                )
                
                # Clean up
                os.unlink(tmp_file.name)
                
                return {
                    "test_type": "table_extraction",
                    "success": result.success and len(result.tables) > 0,
                    "tables_found": len(result.tables),
                    "text_length": len(result.extracted_text),
                    "processing_time": result.processing_time,
                    "tables_data": result.tables if result.tables else [],
                    "extracted_text_preview": result.extracted_text[:300] + "..." if len(result.extracted_text) > 300 else result.extracted_text,
                    "error": result.error_message
                }
                
        except ImportError:
            logger.warning("⚠️ ReportLab not available for table test")
            return {
                "test_type": "table_extraction",
                "success": False,
                "error": "ReportLab not available for generating test PDF"
            }
        except Exception as e:
            logger.error(f"❌ Table extraction test failed: {e}")
            return {
                "test_type": "table_extraction",
                "success": False,
                "error": str(e)
            }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run comprehensive multi-modal tests"""
        logger.info("🚀 Starting comprehensive multi-modal tests...")
        
        if not MULTIMODAL_AVAILABLE:
            return {
                "success": False,
                "error": "Multi-modal processor not available",
                "missing_dependencies": "Check if required packages are installed"
            }
        
        test_results = {}
        
        # Test 1: PDF Processing
        try:
            pdf_result = await self.test_pdf_processing()
            test_results["pdf_processing"] = pdf_result
        except Exception as e:
            test_results["pdf_processing"] = {"success": False, "error": str(e)}
        
        # Test 2: Image Processing
        try:
            image_result = await self.test_image_processing()
            test_results["image_processing"] = image_result
        except Exception as e:
            test_results["image_processing"] = {"success": False, "error": str(e)}
        
        # Test 3: Table Extraction
        try:
            table_result = await self.test_table_extraction()
            test_results["table_extraction"] = table_result
        except Exception as e:
            test_results["table_extraction"] = {"success": False, "error": str(e)}
        
        # Calculate overall success
        successful_tests = len([t for t in test_results.values() if t.get("success")])
        total_tests = len(test_results)
        
        overall_result = {
            "overall_success": successful_tests > 0,
            "successful_tests": successful_tests,
            "total_tests": total_tests,
            "success_rate": f"{(successful_tests/total_tests)*100:.1f}%" if total_tests > 0 else "0%",
            "test_results": test_results,
            "summary": f"Multi-modal testing completed: {successful_tests}/{total_tests} tests passed"
        }
        
        logger.info(f"✅ Multi-modal testing completed: {successful_tests}/{total_tests} tests passed")
        
        return overall_result

async def main():
    """Main test function"""
    print("🎯 Multi-modal Document Processing Test Suite")
    print("=" * 50)
    
    tester = MultiModalTester()
    results = await tester.run_all_tests()
    
    print("\n📊 Test Results:")
    print("=" * 50)
    print(f"Overall Success: {results['overall_success']}")
    print(f"Success Rate: {results['success_rate']}")
    print(f"Tests Passed: {results['successful_tests']}/{results['total_tests']}")
    
    for test_name, test_result in results.get("test_results", {}).items():
        print(f"\n{test_name.upper()}:")
        print(f"  Success: {test_result.get('success', False)}")
        if test_result.get('error'):
            print(f"  Error: {test_result['error']}")
        if test_result.get('summary'):
            print(f"  Summary: {test_result['summary']}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())
