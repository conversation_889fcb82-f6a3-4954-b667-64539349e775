-- Database Index Optimization for crawled_pages table
-- Замяна на ivfflat с HNSW индекси за по-бързо търсене
-- Изпълнете тези заявки в Supabase SQL Editor

-- ============================================================
-- СТЪПКА 1: АНАЛИЗ НА ТЕКУЩИ ИНДЕКСИ
-- ============================================================

-- Проверка на текущи индекси
SELECT 
    indexname, 
    indexdef,
    schemaname,
    tablename
FROM pg_indexes 
WHERE tablename = 'crawled_pages'
ORDER BY indexname;

-- Проверка на размер на таблица
SELECT 
    pg_size_pretty(pg_total_relation_size('crawled_pages')) as table_size,
    pg_size_pretty(pg_relation_size('crawled_pages')) as data_size,
    pg_size_pretty(pg_total_relation_size('crawled_pages') - pg_relation_size('crawled_pages')) as index_size;

-- Брой записи
SELECT COUNT(*) as total_records FROM crawled_pages;

-- ============================================================
-- СТЪПКА 2: ПРЕМАХВАНЕ НА СТАРИ ИНДЕКСИ
-- ============================================================

-- Премахване на стария ivfflat индекс (ако съществува)
DROP INDEX IF EXISTS crawled_pages_embedding_idx;

-- Премахване на други стари vector индекси
DROP INDEX IF EXISTS crawled_pages_embedding_vector_cosine_ops_idx;

-- ============================================================
-- СТЪПКА 3: СЪЗДАВАНЕ НА НОВИ HNSW ИНДЕКСИ
-- ============================================================

-- HNSW индекс за cosine similarity (основен)
-- m=16: брой връзки на възел (по-високо = по-точно, но по-бавно)
-- ef_construction=64: размер на динамичния списък (по-високо = по-точно, но по-бавно)
CREATE INDEX IF NOT EXISTS crawled_pages_embedding_hnsw_cosine_idx 
ON crawled_pages 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- HNSW индекс за inner product (за dot product similarity)
CREATE INDEX IF NOT EXISTS crawled_pages_embedding_hnsw_ip_idx 
ON crawled_pages 
USING hnsw (embedding vector_ip_ops) 
WITH (m = 16, ef_construction = 64);

-- ============================================================
-- СТЪПКА 4: ОПТИМИЗАЦИЯ НА METADATA ИНДЕКСИ
-- ============================================================

-- Премахване на стария metadata индекс
DROP INDEX IF EXISTS idx_crawled_pages_metadata;

-- Нов оптимизиран metadata индекс с jsonb_path_ops
CREATE INDEX IF NOT EXISTS idx_crawled_pages_metadata_optimized 
ON crawled_pages 
USING gin (metadata jsonb_path_ops);

-- Специфични индекси за често използвани metadata полета
CREATE INDEX IF NOT EXISTS idx_crawled_pages_source 
ON crawled_pages ((metadata->>'source'));

CREATE INDEX IF NOT EXISTS idx_crawled_pages_title 
ON crawled_pages ((metadata->>'title'));

CREATE INDEX IF NOT EXISTS idx_crawled_pages_program_type 
ON crawled_pages ((metadata->>'program_type'));

-- ============================================================
-- СТЪПКА 5: ДОПЪЛНИТЕЛНИ PERFORMANCE ИНДЕКСИ
-- ============================================================

-- Composite индекс за URL и chunk_number (за уникалност и търсене)
CREATE INDEX IF NOT EXISTS idx_crawled_pages_url_chunk 
ON crawled_pages (url, chunk_number);

-- Индекс за temporal queries
CREATE INDEX IF NOT EXISTS idx_crawled_pages_created_at 
ON crawled_pages (created_at DESC);

-- Индекс за content length (за филтриране по размер)
CREATE INDEX IF NOT EXISTS idx_crawled_pages_content_length 
ON crawled_pages (length(content));

-- ============================================================
-- СТЪПКА 6: НАСТРОЙКИ ЗА HNSW PERFORMANCE
-- ============================================================

-- Настройка на hnsw.ef_search за по-добра точност при търсене
-- Това влияе на всички HNSW заявки в сесията
-- По-високо ef_search = по-точни резултати, но по-бавно
SET hnsw.ef_search = 40;

-- За постоянна настройка (изисква SUPERUSER права):
-- ALTER SYSTEM SET hnsw.ef_search = 40;
-- SELECT pg_reload_conf();

-- ============================================================
-- СТЪПКА 7: СТАТИСТИКИ И АНАЛИЗ
-- ============================================================

-- Обновяване на статистики за query planner
ANALYZE crawled_pages;

-- Проверка на новите индекси
SELECT 
    indexname, 
    indexdef,
    pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
FROM pg_indexes 
WHERE tablename = 'crawled_pages'
ORDER BY indexname;

-- Статистики за embedding колоната
SELECT 
    attname,
    n_distinct,
    correlation,
    most_common_vals,
    most_common_freqs
FROM pg_stats 
WHERE tablename = 'crawled_pages' 
AND attname = 'embedding';

-- ============================================================
-- СТЪПКА 8: ТЕСТВАНЕ НА PERFORMANCE
-- ============================================================

-- Тест 1: Обикновена vector similarity заявка
EXPLAIN (ANALYZE, BUFFERS) 
SELECT id, url, metadata, 
       1 - (embedding <=> '[0.1,0.2,0.3]'::vector) as similarity
FROM crawled_pages 
ORDER BY embedding <=> '[0.1,0.2,0.3]'::vector 
LIMIT 10;

-- Тест 2: Заявка с metadata филтър
EXPLAIN (ANALYZE, BUFFERS)
SELECT id, url, metadata,
       1 - (embedding <=> '[0.1,0.2,0.3]'::vector) as similarity
FROM crawled_pages 
WHERE metadata->>'source' = 'eufunds'
ORDER BY embedding <=> '[0.1,0.2,0.3]'::vector 
LIMIT 10;

-- ============================================================
-- СТЪПКА 9: MAINTENANCE ЗАЯВКИ
-- ============================================================

-- Седмично maintenance
-- VACUUM ANALYZE crawled_pages;

-- Месечно maintenance  
-- REINDEX INDEX crawled_pages_embedding_hnsw_cosine_idx;
-- REINDEX INDEX crawled_pages_embedding_hnsw_ip_idx;

-- Дневно maintenance
-- ANALYZE crawled_pages;

-- ============================================================
-- СТЪПКА 10: МОНИТОРИРАНЕ НА PERFORMANCE
-- ============================================================

-- Проверка на index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE tablename = 'crawled_pages'
ORDER BY idx_scan DESC;

-- Проверка на table statistics
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del
FROM pg_stat_user_tables 
WHERE tablename = 'crawled_pages';

-- ============================================================
-- ЗАБЕЛЕЖКИ ЗА ОПТИМИЗАЦИЯ
-- ============================================================

/*
1. HNSW параметри:
   - m=16: добър баланс между точност и скорост
   - ef_construction=64: добро качество на индекса
   - ef_search=40: добра точност при търсене

2. За по-голяма точност (но по-бавно):
   - Увеличете ef_search до 80-100
   - Увеличете m до 32
   - Увеличете ef_construction до 128

3. За по-висока скорост (но по-ниска точност):
   - Намалете ef_search до 20-30
   - Намалете m до 8
   - Намалете ef_construction до 32

4. Мониторирайте performance с:
   - pg_stat_user_indexes
   - pg_stat_user_tables
   - EXPLAIN ANALYZE на заявките

5. Редовно maintenance:
   - VACUUM ANALYZE седмично
   - REINDEX месечно
   - ANALYZE дневно
*/
