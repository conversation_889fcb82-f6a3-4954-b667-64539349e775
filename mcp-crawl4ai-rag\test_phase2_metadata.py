#!/usr/bin/env python3
"""
🚀 ФАЗА 2 ТЕСТ: Тестване на метаданни обогатяване
Тества новата функционалност за извличане и съхраняване на метаданни
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Добавяме src директорията към Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Импортираме модулите
import src.crawl4ai_mcp as crawl4ai_mcp
from src.utils import get_supabase_client

async def test_metadata_extraction():
    """Тества извличането на метаданни от различни типове страници"""
    
    print("🚀 ФАЗА 2 ТЕСТ: Започваме тестване на метаданни обогатяване")
    print("=" * 70)
    
    # Инициализираме ресурсите
    print("📋 Инициализираме приложението...")
    await crawl4ai_mcp.initialize_application_resources()
    
    # Тестови URL-и с различни типове страници
    test_urls = [
        {
            "url": "https://eufunds.bg/node/opos/1/2024/bg",
            "description": "Процедура страница (очакваме document_type='procedure', quality_score=0.9)"
        },
        {
            "url": "https://eufunds.bg/term/1/bg",
            "description": "Категория страница (очакваме document_type='category', quality_score=0.2)"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_urls, 1):
        url = test_case["url"]
        description = test_case["description"]
        
        print(f"\n📊 ТЕСТ {i}: {description}")
        print(f"🔗 URL: {url}")
        print("-" * 50)
        
        try:
            # Извършваме crawl с force_recrawl=True за да видим новите метаданни
            # Създаваме mock context
            class MockContext:
                pass

            ctx = MockContext()
            result_json = await crawl4ai_mcp.smart_crawl_url(
                ctx=ctx,
                url=url,
                max_depth=0,
                chunk_size=1000,
                chunk_overlap=100,
                force_recrawl=True
            )
            
            result = json.loads(result_json)
            
            if result.get("success"):
                print(f"✅ Успешен crawl:")
                print(f"   📄 URLs processed: {result.get('urls_processed', 0)}")
                print(f"   📦 Chunks stored: {result.get('chunks_stored', 0)}")
                print(f"   📝 Status: {result.get('status_message', 'N/A')}")
                
                results.append({
                    "url": url,
                    "success": True,
                    "chunks_stored": result.get('chunks_stored', 0),
                    "description": description
                })
            else:
                print(f"❌ Неуспешен crawl: {result.get('error', 'Unknown error')}")
                results.append({
                    "url": url,
                    "success": False,
                    "error": result.get('error', 'Unknown error'),
                    "description": description
                })
                
        except Exception as e:
            print(f"❌ Грешка при crawl: {e}")
            results.append({
                "url": url,
                "success": False,
                "error": str(e),
                "description": description
            })
    
    # Проверяваме метаданните в базата данни
    print(f"\n🔍 ПРОВЕРКА НА МЕТАДАННИ В БАЗАТА ДАННИ")
    print("=" * 70)
    
    try:
        supabase = get_supabase_client()
        
        # Вземаме последните записи с метаданни
        response = supabase.table("crawled_pages").select("url, metadata").order("created_at", desc=True).limit(10).execute()
        
        if response.data:
            print(f"📊 Намерени {len(response.data)} записа с метаданни:")
            
            for record in response.data:
                url = record.get("url", "N/A")
                metadata = record.get("metadata", {})
                
                print(f"\n🔗 URL: {url}")
                if metadata:
                    print(f"   📋 Document Type: {metadata.get('document_type', 'N/A')}")
                    print(f"   ⭐ Quality Score: {metadata.get('quality_score', 'N/A')}")
                    print(f"   📅 Publication Date: {metadata.get('publication_date', 'N/A')}")
                    print(f"   🏷️ Program Indicators: {len(metadata.get('program_indicators', []))}")
                    print(f"   🔗 URL Indicators: {metadata.get('url_indicators', [])}")
                else:
                    print("   ⚠️ Няма метаданни")
        else:
            print("❌ Няма намерени записи в базата данни")
            
    except Exception as e:
        print(f"❌ Грешка при проверка на базата данни: {e}")
    
    # Обобщение на резултатите
    print(f"\n📈 ОБОБЩЕНИЕ НА ТЕСТОВЕТЕ")
    print("=" * 70)
    
    successful_tests = sum(1 for r in results if r["success"])
    total_tests = len(results)
    
    print(f"✅ Успешни тестове: {successful_tests}/{total_tests}")
    print(f"📦 Общо chunks съхранени: {sum(r.get('chunks_stored', 0) for r in results if r['success'])}")
    
    if successful_tests == total_tests:
        print("🎉 Всички тестове преминаха успешно!")
        print("🚀 ФАЗА 2 метаданни обогатяване работи правилно!")
    else:
        print("⚠️ Някои тестове не преминаха успешно")
        for r in results:
            if not r["success"]:
                print(f"   ❌ {r['url']}: {r.get('error', 'Unknown error')}")
    
    # Почистваме ресурсите
    print(f"\n🧹 Почистваме ресурсите...")
    await crawl4ai_mcp.cleanup_application_resources()
    
    return results

if __name__ == "__main__":
    asyncio.run(test_metadata_extraction())
