#!/usr/bin/env python3
"""
Knowledge Graph Integration with RAG System
Интегрира knowledge graph в RAG системата за по-точни резултати
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

try:
    from .knowledge_graph import EUProgramsKnowledgeGraph, TemporalKnowledgeGraph
    from .utils import get_supabase_client
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(__file__))
    from knowledge_graph import EUProgramsKnowledgeGraph, TemporalKnowledgeGraph
    from utils import get_supabase_client

logger = logging.getLogger(__name__)

@dataclass
class KnowledgeGraphRAGResult:
    """Резултат от RAG заявка обогатена с knowledge graph данни"""
    original_results: List[Dict[str, Any]]
    kg_enhanced_results: List[Dict[str, Any]]
    knowledge_graph_insights: Dict[str, Any]
    temporal_alerts: List[Dict[str, Any]]
    eligibility_analysis: Dict[str, Any]
    improvement_score: float

class KnowledgeGraphRAGEnhancer:
    """
    Обогатява RAG резултати с knowledge graph insights
    Специално за LLM консултиране по EU програми
    """
    
    def __init__(self):
        self.supabase = get_supabase_client()
        self.kg = None
        self.temporal_kg = None
        self.is_initialized = False
        
    async def initialize(self) -> Dict[str, Any]:
        """Инициализира knowledge graph системата"""
        
        if self.is_initialized:
            return {"status": "already_initialized"}
        
        logger.info("🔗 ИНИЦИАЛИЗИРАНЕ НА KNOWLEDGE GRAPH RAG ENHANCER")
        start_time = time.time()
        
        try:
            # Инициализирай основния knowledge graph
            self.kg = EUProgramsKnowledgeGraph(self.supabase)
            kg_result = await self.kg.build_knowledge_graph()
            
            if kg_result['status'] != 'success':
                return {"status": "error", "error": "Failed to build knowledge graph"}
            
            # Инициализирай temporal knowledge graph
            self.temporal_kg = TemporalKnowledgeGraph(self.supabase)
            await self.temporal_kg.build_knowledge_graph()
            await self.temporal_kg.add_temporal_entities()
            
            self.is_initialized = True
            init_time = time.time() - start_time
            
            logger.info(f"✅ Knowledge Graph RAG Enhancer инициализиран: {init_time:.2f}s")
            
            return {
                "status": "success",
                "initialization_time": init_time,
                "knowledge_graph_stats": kg_result,
                "temporal_entities": len(self.temporal_kg.temporal_entities)
            }
            
        except Exception as e:
            logger.error(f"❌ Грешка при инициализация: {e}", exc_info=True)
            return {"status": "error", "error": str(e)}
    
    async def enhance_rag_results(
        self, 
        query: str,
        original_results: List[Dict[str, Any]],
        organization_type: Optional[str] = None
    ) -> KnowledgeGraphRAGResult:
        """
        Обогатява RAG резултати с knowledge graph данни
        
        Args:
            query: Оригиналната заявка
            original_results: Резултати от RAG системата
            organization_type: Тип организация за персонализация
        """
        
        if not self.is_initialized:
            await self.initialize()
        
        logger.info(f"🔍 ОБОГАТЯВАНЕ НА RAG РЕЗУЛТАТИ С KNOWLEDGE GRAPH")
        logger.info(f"Query: {query}")
        logger.info(f"Original results: {len(original_results)}")
        
        start_time = time.time()
        
        try:
            # Анализирай заявката за entity extraction
            query_entities = await self._extract_query_entities(query)
            logger.info(f"Извлечени entities: {query_entities}")
            
            # Обогати резултатите с knowledge graph данни
            enhanced_results = await self._enhance_with_kg_data(original_results, query_entities)
            
            # Генерирай knowledge graph insights
            kg_insights = await self._generate_kg_insights(query, query_entities, organization_type)
            
            # Вземи temporal alerts
            temporal_alerts = await self.temporal_kg.get_deadline_alerts(days_threshold=60)
            
            # Направи eligibility analysis ако има организация
            eligibility_analysis = {}
            if organization_type:
                eligibility_analysis = await self._analyze_eligibility(organization_type, query_entities)
            
            # Изчисли improvement score
            improvement_score = self._calculate_improvement_score(original_results, enhanced_results)
            
            enhancement_time = time.time() - start_time
            logger.info(f"✅ RAG обогатяване завърши: {enhancement_time:.2f}s")
            logger.info(f"Improvement score: {improvement_score:.2f}")
            
            return KnowledgeGraphRAGResult(
                original_results=original_results,
                kg_enhanced_results=enhanced_results,
                knowledge_graph_insights=kg_insights,
                temporal_alerts=temporal_alerts,
                eligibility_analysis=eligibility_analysis,
                improvement_score=improvement_score
            )
            
        except Exception as e:
            logger.error(f"❌ Грешка при обогатяване: {e}", exc_info=True)
            
            # Fallback към оригиналните резултати
            return KnowledgeGraphRAGResult(
                original_results=original_results,
                kg_enhanced_results=original_results,
                knowledge_graph_insights={"error": str(e)},
                temporal_alerts=[],
                eligibility_analysis={},
                improvement_score=0.0
            )
    
    async def _extract_query_entities(self, query: str) -> Dict[str, List[str]]:
        """Извлича entities от заявката"""
        
        entities = {
            'programs': [],
            'organizations': [],
            'criteria': [],
            'sectors': []
        }
        
        query_lower = query.lower()
        
        # Програми
        program_keywords = {
            'опик': 'ОПИК',
            'опрр': 'ОПРР', 
            'опос': 'ОПОС',
            'хоризонт': 'Хоризонт Европа',
            'еразъм': 'Еразъм+',
            'life': 'LIFE',
            'creative': 'Creative Europe'
        }
        
        for keyword, program in program_keywords.items():
            if keyword in query_lower:
                entities['programs'].append(program)
        
        # Организации
        org_keywords = {
            'мсп': 'МСП',
            'малки и средни предприятия': 'МСП',
            'нпо': 'НПО',
            'неправителствени': 'НПО',
            'университет': 'университет',
            'община': 'община',
            'стартъп': 'стартъп',
            'микропредприятие': 'микропредприятие'
        }
        
        for keyword, org_type in org_keywords.items():
            if keyword in query_lower:
                entities['organizations'].append(org_type)
        
        # Сектори
        sector_keywords = {
            'дигитализация': 'технологии',
            'иновации': 'технологии',
            'култура': 'култура',
            'образование': 'образование',
            'околна среда': 'околна_среда',
            'здравеопазване': 'здравеопазване'
        }
        
        for keyword, sector in sector_keywords.items():
            if keyword in query_lower:
                entities['sectors'].append(sector)
        
        return entities
    
    async def _enhance_with_kg_data(
        self, 
        original_results: List[Dict[str, Any]], 
        query_entities: Dict[str, List[str]]
    ) -> List[Dict[str, Any]]:
        """Обогатява резултатите с knowledge graph данни"""
        
        enhanced_results = []
        
        for result in original_results:
            enhanced_result = result.copy()
            
            # Добави knowledge graph metadata
            enhanced_result['kg_metadata'] = {
                'related_programs': [],
                'eligibility_info': {},
                'requirements': [],
                'similar_opportunities': []
            }
            
            # Намери свързани програми
            for program in query_entities.get('programs', []):
                program_id = f"prog_{program.lower().replace(' ', '_')}"
                if program_id in self.kg.entities['programs']:
                    
                    # Вземи requirements за програмата
                    requirements = await self.kg.query_knowledge_graph(
                        "program_requirements", 
                        program_id
                    )
                    
                    enhanced_result['kg_metadata']['related_programs'].append({
                        'program_name': program,
                        'program_id': program_id,
                        'requirements': requirements.get('requirements', [])
                    })
            
            # Добави eligibility информация за организации
            for org_type in query_entities.get('organizations', []):
                org_id = f"org_{org_type.lower().replace(' ', '_')}"
                if org_id in self.kg.entities['organizations']:
                    
                    # Вземи eligible програми
                    eligible_programs = await self.kg.query_knowledge_graph(
                        "eligible_programs",
                        org_id,
                        {"min_strength": 0.5}
                    )
                    
                    enhanced_result['kg_metadata']['eligibility_info'][org_type] = eligible_programs
            
            # Добави relevance score базирано на knowledge graph
            kg_relevance = self._calculate_kg_relevance(result, query_entities)
            enhanced_result['kg_relevance_score'] = kg_relevance
            
            # Комбинирай с оригиналния score ако има
            if 'score' in result:
                enhanced_result['combined_score'] = (result['score'] + kg_relevance) / 2
            else:
                enhanced_result['combined_score'] = kg_relevance
            
            enhanced_results.append(enhanced_result)
        
        # Сортирай по combined_score
        enhanced_results.sort(key=lambda x: x.get('combined_score', 0), reverse=True)
        
        return enhanced_results
    
    def _calculate_kg_relevance(self, result: Dict[str, Any], query_entities: Dict[str, List[str]]) -> float:
        """Изчислява relevance score базирано на knowledge graph"""
        
        relevance = 0.5  # Base score
        
        content = result.get('content', '').lower()
        
        # Бонус за споменаване на програми
        for program in query_entities.get('programs', []):
            if program.lower() in content:
                relevance += 0.2
        
        # Бонус за споменаване на организации
        for org_type in query_entities.get('organizations', []):
            if org_type.lower() in content:
                relevance += 0.15
        
        # Бонус за споменаване на сектори
        for sector in query_entities.get('sectors', []):
            if sector.lower() in content:
                relevance += 0.1
        
        return min(relevance, 1.0)
    
    async def _generate_kg_insights(
        self, 
        query: str, 
        query_entities: Dict[str, List[str]], 
        organization_type: Optional[str]
    ) -> Dict[str, Any]:
        """Генерира insights от knowledge graph"""
        
        insights = {
            'query_analysis': query_entities,
            'recommended_programs': [],
            'eligibility_summary': {},
            'requirements_overview': [],
            'strategic_recommendations': []
        }
        
        # Препоръки за програми базирано на query entities
        if organization_type:
            org_id = f"org_{organization_type.lower().replace(' ', '_')}"
            if org_id in self.kg.entities['organizations']:
                
                # Вземи най-подходящите програми
                compatibility = await self.kg.query_knowledge_graph(
                    "program_compatibility",
                    org_id
                )
                
                top_programs = compatibility.get('program_compatibility', [])[:5]
                insights['recommended_programs'] = top_programs
                
                # Eligibility summary
                insights['eligibility_summary'] = {
                    'organization_type': organization_type,
                    'highly_compatible_programs': compatibility.get('highly_compatible_programs', 0),
                    'total_programs_analyzed': compatibility.get('total_programs_analyzed', 0)
                }
        
        # Стратегически препоръки
        insights['strategic_recommendations'] = await self._generate_strategic_recommendations(
            query_entities, organization_type
        )
        
        return insights
    
    async def _generate_strategic_recommendations(
        self, 
        query_entities: Dict[str, List[str]], 
        organization_type: Optional[str]
    ) -> List[str]:
        """Генерира стратегически препоръки"""
        
        recommendations = []
        
        if organization_type == "МСП":
            recommendations.extend([
                "Фокусирайте се на ОПИК за иновации и дигитализация",
                "Разгледайте възможности за партньорство с университети",
                "Подгответе документация за съфинансиране"
            ])
        elif organization_type == "НПО":
            recommendations.extend([
                "ОПОС предлага отлични възможности за социални проекти",
                "Creative Europe за културни инициативи",
                "Изградете капацитет за проектно управление"
            ])
        elif organization_type == "университет":
            recommendations.extend([
                "Хоризонт Европа за научни изследвания",
                "Еразъм+ за образователни програми",
                "Развийте международни партньорства"
            ])
        
        # Добави препоръки базирано на query entities
        if 'дигитализация' in ' '.join(query_entities.get('sectors', [])):
            recommendations.append("Акцентирайте върху дигиталните компетенции и технологии")
        
        if 'иновации' in ' '.join(query_entities.get('sectors', [])):
            recommendations.append("Подчертайте иновативните аспекти на проекта")
        
        return recommendations
    
    async def _analyze_eligibility(
        self, 
        organization_type: str, 
        query_entities: Dict[str, List[str]]
    ) -> Dict[str, Any]:
        """Анализира eligibility за дадена организация"""
        
        org_id = f"org_{organization_type.lower().replace(' ', '_')}"
        
        if org_id not in self.kg.entities['organizations']:
            return {"error": f"Организация {organization_type} не е намерена"}
        
        # Вземи eligible програми
        eligible_programs = await self.kg.query_knowledge_graph(
            "eligible_programs",
            org_id,
            {"min_strength": 0.3}
        )
        
        # Вземи подобни организации
        similar_orgs = await self.kg.query_knowledge_graph(
            "similar_organizations",
            org_id
        )
        
        return {
            'organization_type': organization_type,
            'eligible_programs': eligible_programs,
            'similar_organizations': similar_orgs,
            'analysis_timestamp': time.time()
        }
    
    def _calculate_improvement_score(
        self, 
        original_results: List[Dict[str, Any]], 
        enhanced_results: List[Dict[str, Any]]
    ) -> float:
        """Изчислява improvement score от knowledge graph обогатяването"""
        
        if not original_results or not enhanced_results:
            return 0.0
        
        # Брой резултати с kg_metadata
        enhanced_count = sum(1 for r in enhanced_results if 'kg_metadata' in r)
        enhancement_ratio = enhanced_count / len(enhanced_results)
        
        # Средна kg_relevance_score
        kg_scores = [r.get('kg_relevance_score', 0) for r in enhanced_results]
        avg_kg_score = sum(kg_scores) / len(kg_scores) if kg_scores else 0
        
        # Комбиниран improvement score
        improvement_score = (enhancement_ratio * 0.6 + avg_kg_score * 0.4)
        
        return improvement_score

# Global instance
kg_rag_enhancer = KnowledgeGraphRAGEnhancer()

async def enhance_rag_with_knowledge_graph(
    query: str,
    original_results: List[Dict[str, Any]],
    organization_type: Optional[str] = None
) -> KnowledgeGraphRAGResult:
    """
    Convenience function за обогатяване на RAG резултати
    """
    return await kg_rag_enhancer.enhance_rag_results(query, original_results, organization_type)
