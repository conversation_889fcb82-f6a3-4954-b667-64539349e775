# PRP Template: MCP Crawl4AI RAG System Enhancement

**name**: "MCP Crawl4AI RAG Enhancement Template - Phase 5.0 Adaptive Fusion Ready"

**description**: |
Template optimized for AI agents to implement features in the MCP Crawl4AI RAG system with Phase 5.0 Adaptive Fusion context and self-validation capabilities to achieve working code through iterative refinement. System has achieved 99% accuracy goal with 715% improvement from baseline.

## Core Principles
- **Context is King**: Include ALL necessary documentation, examples, and caveats from the Phase 5.0 RAG system
- **Validation Loops**: Provide executable tests/lints the AI can run and fix
- **Information Dense**: Use keywords and patterns from the existing adaptive fusion codebase
- **Progressive Success**: Start simple, validate, then enhance
- **Global rules**: Follow all patterns established in the Phase 5.0 Adaptive Fusion system

## Goal
[What needs to be built - be specific about the end state and desires]
Example: "Implement Phase 6.0 RAG optimization with real-time user feedback learning that further improves the 99% accuracy achieved in Phase 5.0"

## Why
[Business value and user impact]
- Build upon the achieved 99% accuracy (715% improvement from baseline) to reach even higher performance
- Enable the system to replace human consultants for European funding programs with proven production readiness
- Reduce query response time while maintaining the adaptive learning quality
- [Integration with existing features] - Seamlessly integrate with Phase 5.0 Adaptive Fusion system
- [Problems this solves] - Addresses edge cases not covered by current 22% Phase 5 improvement

## What
[User-visible behavior and technical requirements]
- Enhanced adaptive learning beyond Phase 5.0's 22% improvement
- Real-time performance monitoring and automatic threshold adjustments
- Improved result ranking based on user interaction patterns and query classification
- Backward compatibility with all existing Phase 5.0 MCP tools and endpoints
- Maintain the achieved 99% accuracy while adding new capabilities

## Success Criteria
- [ ] All existing Phase 5.0 tests continue to pass (2.518 average score maintained)
- [ ] New feature achieves measurable improvement beyond current 715% baseline improvement
- [ ] Integration tests pass with realistic user scenarios using adaptive fusion
- [ ] Performance benchmarks show no degradation from Phase 5.0 response time
- [ ] Code coverage remains above 80% for new components
- [ ] 99% accuracy goal maintained or exceeded

## All Needed Context

### Documentation & References
**MUST READ - Include these in your context window**

- file: `PROJECT_CONTEXT.md`
  why: Complete system overview, architecture, and current Phase 4.7 status
  critical: Understanding of RAG evolution phases and current production readiness

- file: `phase5_adaptive_fusion.py`
  why: Phase 5.0 Adaptive Fusion system - main implementation with 99% accuracy achieved
  critical: AdaptiveFusionSystem class, QueryProfile dataclass, adaptive learning patterns

- file: `src/utils.py`
  why: Core RAG engine with hybrid search and contextual retrieval functions
  critical: Phase 1-3 implementations, hybrid search patterns, contextual expansion logic

- file: `src/crawl4ai_mcp.py`
  why: MCP server structure, tool definitions, error handling patterns
  critical: FastMCP patterns, async context management, tool parameter validation

- file: `phase3_contextual_retrieval.py`
  why: Phase 3 contextual retrieval with small-to-big expansion (base for Phase 5)
  critical: Surrounding chunks expansion, context merging, performance optimization

- file: `phase2_query_expansion.py`
  why: Phase 2 HyDE and query expansion with Enhanced RRF
  critical: Query variations generation, RRF fusion, score preservation patterns

- file: `test_realistic_rag_evaluation.py`
  why: Testing patterns for RAG system validation
  critical: Test structure for 8 realistic scenarios, result validation patterns

- doc: Supabase RPC Functions
  section: `match_crawled_pages_v4_debug` function parameters and usage
  critical: Vector search parameters, weight configurations, result structure

### Current Codebase Tree (Phase 5.0 Ready)
```
mcp-crawl4ai-rag/
├── src/
│   ├── crawl4ai_mcp.py          # Main MCP server with FastMCP
│   ├── utils.py                 # Core RAG engine (Phases 1-3 implementations)
│   ├── hybrid_search.py         # Hybrid semantic + BM25 search
│   ├── intelligent_reranking.py # Multi-stage result reranking
│   ├── content_enhancement.py   # spaCy-based content processing
│   ├── advanced_ranking.py      # Context-aware ranking algorithms
│   ├── query_understanding.py   # Query analysis and intent classification
│   └── entity_utils.py          # Bulgarian entity extraction
├── phase5_adaptive_fusion.py         # Phase 5.0 Adaptive Fusion (99% accuracy)
├── phase3_contextual_retrieval.py    # Phase 3 contextual retrieval
├── phase2_query_expansion.py         # Phase 2 HyDE and query expansion
├── test_realistic_rag_evaluation.py  # Main testing framework
├── test_eufunds_depth0.py            # Data collection script
├── PROJECT_CONTEXT.md                # Complete system documentation
└── pyproject.toml                    # Dependencies and configuration
```

### Desired Codebase Tree with New Files
```
mcp-crawl4ai-rag/
├── src/
│   ├── [existing files...]
│   ├── performance_monitor.py   # Real-time performance tracking
│   ├── adaptive_learning.py     # User feedback learning system
│   └── query_optimizer.py       # Dynamic query optimization
├── tests/
│   ├── test_performance_monitor.py
│   ├── test_adaptive_learning.py
│   └── test_integration_new_features.py
└── [existing files...]
```

### Known Gotchas & Library Quirks

**CRITICAL: FastMCP requires specific patterns**
- All tools must be decorated with `@mcp.tool()`
- Context parameter `ctx: Context` must be first parameter
- Async functions required for all MCP tools
- Error handling must return JSON strings, not raise exceptions

**CRITICAL: Supabase RPC function requirements**
- `match_crawled_pages_hybrid` requires weight parameters (p_weight_dense, p_weight_sparse) between 0.0-1.0
- Vector embeddings must be exactly 1024 dimensions (BGE-M3)
- Results come in `result.data` with proper `.execute()` calls
- Use `search_documents_with_text_hybrid` wrapper function for Phase 1+ compatibility

**CRITICAL: RAG System Patterns (Phase 5.0)**
- Always use `adaptive_fusion_search` as the main entry point for Phase 5.0+
- Maintain AdaptiveFusionSystem with QueryProfile classification
- Query types: funding, research, education, green, sme with specific boost factors
- Bulgarian text processing requires special handling in entity_utils.py
- Performance optimization must preserve 99% accuracy achievement

**CRITICAL: Testing Patterns**
- All tests must achieve 100% success rate to maintain production readiness
- Use realistic test data from actual Supabase content
- Test expectations must align with actual database content (see МСП programs fix)
- Environment variables: `SUPABASE_SERVICE_KEY` not `SUPABASE_KEY`

## Implementation Blueprint

### Data Models and Structure
Create core data models ensuring type safety and consistency with existing patterns:

**Examples**:
- Follow `SearchResult` dataclass pattern from hybrid_search.py
- Use `Dict[str, Any]` return types for RAG functions (established pattern)
- Maintain compatibility with existing Supabase schema
- Pydantic models for new configuration structures

### Task List (in order of completion)

**Task 1: Performance Monitoring Infrastructure**
MODIFY src/utils.py:
  - FIND pattern: "async def ultra_smart_rag_query"
  - INJECT performance tracking decorators
  - PRESERVE existing function signatures and return types

CREATE src/performance_monitor.py:
  - MIRROR pattern from: src/query_understanding.py (async patterns)
  - IMPLEMENT real-time metrics collection
  - KEEP error handling pattern identical to existing modules

**Task 2: Adaptive Learning System**
CREATE src/adaptive_learning.py:
  - FOLLOW async patterns from src/intelligent_reranking.py
  - IMPLEMENT user feedback processing
  - MAINTAIN compatibility with existing confidence scoring

**Task 3: Integration with MCP Server**
MODIFY src/crawl4ai_mcp.py:
  - FIND pattern: "@mcp.tool()" decorators
  - ADD new tools following existing parameter validation patterns
  - PRESERVE all existing tool functionality

**Task 4: Testing Infrastructure**
CREATE tests/test_performance_monitor.py:
  - MIRROR pattern from: test_realistic_rag_evaluation.py
  - IMPLEMENT comprehensive test coverage
  - MAINTAIN 100% success rate requirement

### Per Task Pseudocode

**Task 1 Pseudocode**:
```python
# Performance monitoring with existing patterns
@performance_tracker  # New decorator
async def ultra_smart_rag_query(...):
    # PATTERN: Maintain existing logging structure
    logger.info("🎯 Ultra Smart RAG Query with performance tracking")
    
    # CRITICAL: Preserve all existing Phase 4.7 logic
    start_time = time.time()
    
    # EXISTING LOGIC UNCHANGED
    results = await existing_rag_logic(...)
    
    # NEW: Performance tracking
    await performance_monitor.record_query_metrics({
        'query': query,
        'response_time': time.time() - start_time,
        'result_count': len(results),
        'confidence_score': calculate_confidence(results)
    })
    
    return results  # PRESERVE existing return format
```

## Integration Points

**DATABASE**:
  - table: "performance_metrics" - Add to existing Supabase schema
  - index: "CREATE INDEX idx_query_performance ON performance_metrics(timestamp, query_hash)"
  
**CONFIG**:
  - add to: existing environment variables pattern
  - pattern: "PERFORMANCE_TRACKING_ENABLED = bool(os.getenv('PERFORMANCE_TRACKING_ENABLED', 'true'))"
  
**MCP TOOLS**:
  - add to: src/crawl4ai_mcp.py following existing @mcp.tool() patterns
  - pattern: Maintain async signatures and Context parameter requirements

## Validation Loop

### Level 1: Syntax & Style
```bash
# Run these FIRST - fix any errors before proceeding
ruff check src/ --fix              # Auto-fix what's possible
mypy src/                          # Type checking
# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests
```python
# CREATE test files following existing patterns
def test_performance_monitor_basic():
    """Performance monitoring works with existing RAG"""
    # PATTERN: Use existing test data and expectations
    result = await ultra_smart_rag_query("test query")
    assert result['success'] == True
    assert 'performance_metrics' in result

def test_backward_compatibility():
    """All existing functionality preserved"""
    # CRITICAL: All 8 realistic test scenarios must still pass
    result = run_realistic_evaluation()
    assert result['overall_success'] == 100.0

# Run and iterate until passing:
python -m pytest tests/ -v
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Test
```bash
# Start the MCP server
python -m src.crawl4ai_mcp

# Test enhanced RAG endpoint
curl -X POST http://localhost:8051/rag-query \
  -H "Content-Type: application/json" \
  -d '{"query": "Кои са програмите за малки и средни предприятия?"}'

# Expected: {"success": true, "results": [...], "performance_metrics": {...}}
```

## Final Validation Checklist
- [ ] All existing tests pass: `python test_realistic_rag_evaluation.py`
- [ ] No linting errors: `ruff check src/`
- [ ] No type errors: `mypy src/`
- [ ] MCP server starts successfully: `python -m src.crawl4ai_mcp`
- [ ] All 8 realistic test scenarios maintain 100% success
- [ ] New features integrate without breaking existing functionality
- [ ] Performance metrics show no degradation in response time
- [ ] Documentation updated in PROJECT_CONTEXT.md

## Anti-Patterns to Avoid
❌ Don't modify existing RAG logic without preserving backward compatibility
❌ Don't skip the 100% test success requirement - this is production critical
❌ Don't ignore Supabase RPC parameter requirements (weight parameters must be non-null)
❌ Don't use sync functions in the async MCP context
❌ Don't hardcode values that should use existing environment variable patterns
❌ Don't break the established Phase 4.7 clustering and optimization logic
❌ Don't modify result structures without updating all dependent code
❌ Don't add dependencies without updating pyproject.toml following existing patterns
