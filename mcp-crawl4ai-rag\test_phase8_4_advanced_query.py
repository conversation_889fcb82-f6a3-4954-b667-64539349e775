#!/usr/bin/env python3
"""
Phase 8.4: Advanced Query Processing Tests
Testing query expansion, HyDE pattern, and multi-query transformation

Based on external LLM recommendations for achieving 90%+ RAG accuracy.
Priority: ⭐⭐⭐ (High Impact)
"""

import asyncio
import logging
import time
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils import search_crawled_pages_advanced_query, search_documents_with_text_hybrid_reranked, get_supabase_client
from src.advanced_query_processing import get_advanced_query_processor, process_query_with_advanced_techniques

# Initialize client
client = get_supabase_client()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(levelname)s:%(name)s:%(message)s'
)
logger = logging.getLogger(__name__)

async def test_advanced_query_processing():
    """Test 1: Advanced Query Processing Engine"""
    logger.info("🧪 TEST 1: Advanced Query Processing Engine")
    
    try:
        # Test Bulgarian query processing
        bulgarian_query = "европейски фондове за иновации и развитие"
        
        result = await process_query_with_advanced_techniques(bulgarian_query)
        
        # Validate results
        original_query = result.get('original_query')
        expanded_queries = result.get('expanded_queries', [])
        query_variants = result.get('query_variants', [])
        all_queries = result.get('all_queries', [])
        hyde_doc = result.get('hyde_document')
        processing_time = result.get('processing_time', 0)
        
        logger.info(f"📝 Original query: {original_query}")
        logger.info(f"📝 Expanded queries: {len(expanded_queries)}")
        logger.info(f"🔄 Query variants: {len(query_variants)}")
        logger.info(f"📊 Total queries: {len(all_queries)}")
        logger.info(f"🎯 HyDE document: {'✅' if hyde_doc else '❌'}")
        logger.info(f"⏱️ Processing time: {processing_time:.3f}s")
        
        # Success criteria
        success = (
            len(expanded_queries) >= 1 and
            len(all_queries) >= 1 and
            processing_time < 10.0
        )
        
        if success:
            logger.info("✅ TEST 1 PASSED: Advanced query processing working")
            return True
        else:
            logger.error("❌ TEST 1 FAILED: Advanced query processing issues")
            return False
            
    except Exception as e:
        logger.error(f"❌ TEST 1 ERROR: {e}")
        return False

async def test_advanced_vs_standard_search():
    """Test 2: Advanced vs Standard Search Comparison"""
    logger.info("🧪 TEST 2: Advanced vs Standard Search Comparison")
    
    try:
        query = "програми за малки и средни предприятия в България"
        
        # Advanced search
        start_time = time.time()
        advanced_results = await search_crawled_pages_advanced_query(
            query_text=query,
            match_count=10,
            use_bulgarian_embeddings=True,
            rerank_top_k=20
        )
        advanced_time = time.time() - start_time
        
        # Standard search
        start_time = time.time()
        standard_results = await search_documents_with_text_hybrid_reranked(
            client=client,
            query_text=query,
            match_count=10,
            rerank_top_k=20
        )
        standard_time = time.time() - start_time
        
        # Calculate average scores (check multiple score fields)
        def get_score(result):
            return result.get('final_score', 0) or result.get('hybrid_score', 0) or result.get('similarity', 0) or result.get('score', 0) or 0.5
        
        advanced_avg_score = sum(get_score(r) for r in advanced_results) / max(len(advanced_results), 1)
        standard_avg_score = sum(get_score(r) for r in standard_results) / max(len(standard_results), 1)
        
        logger.info(f"🚀 Advanced search: {len(advanced_results)} results, avg score: {advanced_avg_score:.3f}, time: {advanced_time:.3f}s")
        logger.info(f"🔄 Standard search: {len(standard_results)} results, avg score: {standard_avg_score:.3f}, time: {standard_time:.3f}s")
        
        # Calculate improvement
        if standard_avg_score > 0:
            improvement = ((advanced_avg_score - standard_avg_score) / standard_avg_score) * 100
            logger.info(f"📈 Score improvement: {improvement:+.1f}%")
        
        # Check for advanced features in results
        advanced_features = 0
        for result in advanced_results:
            if result.get('query_weight') or result.get('source_query'):
                advanced_features += 1
        
        logger.info(f"🔧 Results with advanced features: {advanced_features}/{len(advanced_results)}")
        
        # Success criteria
        success = (
            len(advanced_results) > 0 and
            len(standard_results) > 0 and
            advanced_time < 15.0 and  # Reasonable time
            advanced_features > 0  # Some advanced features used
        )
        
        if success:
            logger.info("✅ TEST 2 PASSED: Advanced search working")
            return True
        else:
            logger.error("❌ TEST 2 FAILED: Advanced search issues")
            return False
            
    except Exception as e:
        logger.error(f"❌ TEST 2 ERROR: {e}")
        return False

async def test_multilingual_advanced_processing():
    """Test 3: Multilingual Advanced Processing"""
    logger.info("🧪 TEST 3: Multilingual Advanced Processing")
    
    try:
        test_queries = [
            # Bulgarian queries
            "европейски фондове за иновации и развитие",
            "програми за малки и средни предприятия в България",
            "финансиране на научни изследвания и развойна дейност",
            "дигитална трансформация и технологии",
            "устойчиво развитие и зелена икономика",
            
            # English queries
            "European funding programs for innovation",
            "digital transformation initiatives",
            "sustainable development projects"
        ]
        
        results_summary = []
        
        for query in test_queries:
            start_time = time.time()
            
            results = await search_crawled_pages_advanced_query(
                query_text=query,
                match_count=5,
                use_bulgarian_embeddings=True,
                rerank_top_k=20
            )
            
            search_time = time.time() - start_time
            
            # Calculate average score (check multiple score fields)
            def get_score(result):
                return result.get('final_score', 0) or result.get('hybrid_score', 0) or result.get('similarity', 0) or result.get('score', 0) or 0.5
            
            avg_score = sum(get_score(r) for r in results) / max(len(results), 1)
            
            # Detect query language
            is_bulgarian = any(char in 'абвгдежзийклмнопрстуфхцчшщъьюя' for char in query.lower())
            
            # Check for advanced features
            advanced_features = sum(1 for r in results if r.get('query_weight') or r.get('source_query'))
            
            success = len(results) > 0 and avg_score > 0.1 and search_time < 15.0
            
            results_summary.append({
                'query': query,
                'is_bulgarian': is_bulgarian,
                'results_count': len(results),
                'avg_score': avg_score,
                'search_time': search_time,
                'advanced_features': advanced_features,
                'success': success
            })
            
            logger.info(f"{'🇧🇬' if is_bulgarian else '🌍'} '{query[:40]}...' -> {len(results)} results, score: {avg_score:.3f}, time: {search_time:.3f}s, features: {advanced_features}")
        
        # Calculate summary statistics
        total_queries = len(results_summary)
        successful_queries = sum(1 for r in results_summary if r['success'])
        bulgarian_queries = sum(1 for r in results_summary if r['is_bulgarian'])
        english_queries = total_queries - bulgarian_queries
        
        avg_score_all = sum(r['avg_score'] for r in results_summary) / total_queries
        avg_time_all = sum(r['search_time'] for r in results_summary) / total_queries
        avg_features_all = sum(r['advanced_features'] for r in results_summary) / total_queries
        
        success_rate = successful_queries / total_queries
        
        logger.info(f"📊 MULTILINGUAL ADVANCED PROCESSING SUMMARY:")
        logger.info(f"   Total queries: {total_queries}")
        logger.info(f"   Bulgarian queries: {bulgarian_queries}")
        logger.info(f"   English queries: {english_queries}")
        logger.info(f"   Success rate: {success_rate:.1%}")
        logger.info(f"   Average score: {avg_score_all:.3f}")
        logger.info(f"   Average time: {avg_time_all:.3f}s")
        logger.info(f"   Average advanced features: {avg_features_all:.1f}")
        
        # Success criteria
        success = (
            success_rate >= 0.8 and  # 80%+ success rate
            avg_score_all >= 0.2 and  # Reasonable average score
            avg_time_all < 10.0 and  # Reasonable response time
            avg_features_all >= 1.0  # Advanced features being used
        )
        
        if success:
            logger.info("✅ TEST 3 PASSED: Multilingual advanced processing working")
            return True
        else:
            logger.error("❌ TEST 3 FAILED: Multilingual advanced processing issues")
            return False
            
    except Exception as e:
        logger.error(f"❌ TEST 3 ERROR: {e}")
        return False

async def test_performance_comparison():
    """Test 4: Performance Comparison with Previous Phases"""
    logger.info("🧪 TEST 4: Performance Comparison")
    
    try:
        test_query = "програми за малки и средни предприятия в България"
        
        # Phase 8.4: Advanced Query Processing
        start_time = time.time()
        phase84_results = await search_crawled_pages_advanced_query(
            query_text=test_query,
            match_count=10,
            use_bulgarian_embeddings=True,
            rerank_top_k=20
        )
        phase84_time = time.time() - start_time
        
        # Phase 8.2: Standard Hybrid with Reranking
        start_time = time.time()
        phase82_results = await search_documents_with_text_hybrid_reranked(
            client=client,
            query_text=test_query,
            match_count=10,
            rerank_top_k=20
        )
        phase82_time = time.time() - start_time
        
        # Calculate scores (check multiple score fields)
        def get_score(result):
            return result.get('final_score', 0) or result.get('hybrid_score', 0) or result.get('similarity', 0) or result.get('score', 0) or 0.5
        
        phase84_avg_score = sum(get_score(r) for r in phase84_results) / max(len(phase84_results), 1)
        phase82_avg_score = sum(get_score(r) for r in phase82_results) / max(len(phase82_results), 1)
        
        # Calculate improvement
        score_improvement = ((phase84_avg_score - phase82_avg_score) / max(phase82_avg_score, 0.001)) * 100
        time_overhead = ((phase84_time - phase82_time) / max(phase82_time, 0.001)) * 100
        
        logger.info(f"🚀 Phase 8.4 Advanced: {len(phase84_results)} results, avg score: {phase84_avg_score:.3f}, time: {phase84_time:.3f}s")
        logger.info(f"🔄 Phase 8.2 Standard: {len(phase82_results)} results, avg score: {phase82_avg_score:.3f}, time: {phase82_time:.3f}s")
        logger.info(f"📈 Score improvement: {score_improvement:+.1f}%")
        logger.info(f"⏱️ Time overhead: {time_overhead:+.1f}%")
        
        # Check advanced features usage
        advanced_features = sum(1 for r in phase84_results if r.get('query_weight') or r.get('source_query'))
        logger.info(f"🔧 Advanced features used: {advanced_features}/{len(phase84_results)}")
        
        # Success criteria
        success = (
            len(phase84_results) > 0 and
            len(phase82_results) > 0 and
            phase84_time < 20.0 and  # Reasonable time
            advanced_features > 0  # Advanced features being used
        )
        
        if success:
            logger.info("✅ TEST 4 PASSED: Performance comparison successful")
            return True
        else:
            logger.error("❌ TEST 4 FAILED: Performance comparison issues")
            return False
            
    except Exception as e:
        logger.error(f"❌ TEST 4 ERROR: {e}")
        return False

async def main():
    """Run all Phase 8.4 tests"""
    logger.info("🚀 STARTING PHASE 8.4: ADVANCED QUERY PROCESSING TESTS")
    logger.info("=" * 80)
    
    tests = [
        ("Advanced Query Processing Engine", test_advanced_query_processing),
        ("Advanced vs Standard Search", test_advanced_vs_standard_search),
        ("Multilingual Advanced Processing", test_multilingual_advanced_processing),
        ("Performance Comparison", test_performance_comparison)
    ]
    
    results = []
    total_time = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'=' * 20} {test_name} {'=' * 20}")
        start_time = time.time()
        
        try:
            success = await test_func()
            test_time = time.time() - start_time
            total_time += test_time
            
            results.append((test_name, success, test_time))
            
            if success:
                logger.info(f"✅ PASSED - {test_name} ({test_time:.2f}s)")
            else:
                logger.info(f"❌ FAILED - {test_name} ({test_time:.2f}s)")
                
        except Exception as e:
            test_time = time.time() - start_time
            total_time += test_time
            results.append((test_name, False, test_time))
            logger.error(f"❌ ERROR - {test_name} ({test_time:.2f}s): {e}")
    
    # Final summary
    logger.info("\n" + "=" * 80)
    logger.info("🏁 PHASE 8.4 ADVANCED QUERY PROCESSING - FINAL RESULTS")
    logger.info("=" * 80)
    
    passed_tests = sum(1 for _, success, _ in results if success)
    total_tests = len(results)
    
    for test_name, success, test_time in results:
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name} ({test_time:.2f}s)")
    
    logger.info(f"\n📊 OVERALL RESULTS:")
    logger.info(f"   Tests passed: {passed_tests}/{total_tests}")
    logger.info(f"   Success rate: {passed_tests/total_tests:.1%}")
    logger.info(f"   Total time: {total_time:.2f}s")
    
    if passed_tests >= 3:  # At least 3/4 tests should pass
        logger.info("🎉 PHASE 8.4 ADVANCED QUERY PROCESSING: SUCCESS!")
        logger.info("🚀 Query expansion, HyDE pattern, and multi-query transformation working!")
    else:
        logger.info("⚠️ PHASE 8.4 ADVANCED QUERY PROCESSING: NEEDS IMPROVEMENT")
        logger.info("🔧 Some advanced query processing features need attention")

if __name__ == "__main__":
    asyncio.run(main())
