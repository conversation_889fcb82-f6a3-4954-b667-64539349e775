#!/usr/bin/env python3
"""
Comprehensive RAG Evaluation System
Реални тестове за български RAG с benchmark datasets
"""

import asyncio
import json
import time
import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
from datetime import datetime

# Evaluation metrics
try:
    from rouge_score import rouge_scorer
    ROUGE_AVAILABLE = True
except ImportError:
    ROUGE_AVAILABLE = False
    
try:
    from bert_score import score as bert_score
    BERT_SCORE_AVAILABLE = True
except ImportError:
    BERT_SCORE_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class EvaluationQuery:
    """Структура за evaluation заявка"""
    id: str
    query: str
    expected_programs: List[str]  # Очаквани програми
    expected_keywords: List[str]  # Очаквани ключови думи
    difficulty: str  # "easy", "medium", "hard"
    category: str   # "funding", "eligibility", "deadlines", "procedures"
    language: str   # "bg", "en", "mixed"

@dataclass
class EvaluationResult:
    """Резултат от evaluation"""
    query_id: str
    query_text: str
    retrieved_docs: int
    processing_time: float
    
    # Accuracy metrics
    program_precision: float
    program_recall: float
    program_f1: float
    
    # Content quality metrics
    rouge_1: float
    rouge_2: float
    rouge_l: float
    bert_score_f1: float
    
    # Domain-specific metrics
    deadline_accuracy: float
    eligibility_accuracy: float
    source_attribution: float
    
    # Overall score
    overall_score: float
    
    # Additional info
    method_used: str
    errors: List[str]

class BulgarianRAGEvaluator:
    """Comprehensive RAG Evaluation System за български език"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.rouge_scorer = None
        self.evaluation_queries = []
        self.results = []
        
        # Initialize ROUGE if available
        if ROUGE_AVAILABLE:
            self.rouge_scorer = rouge_scorer.RougeScorer(
                ['rouge1', 'rouge2', 'rougeL'], 
                use_stemmer=True
            )
        
        # Load benchmark queries
        self._load_benchmark_queries()
    
    def _load_benchmark_queries(self):
        """Зарежда benchmark заявки за evaluation"""
        
        # РЕАЛНИ ЗАЯВКИ ЗА ЕС ПРОГРАМИ В БЪЛГАРИЯ
        benchmark_queries = [
            # EASY QUERIES
            EvaluationQuery(
                id="easy_001",
                query="Какви са програмите за малки и средни предприятия?",
                expected_programs=["ОПИК", "ОПРР", "ОПРЧР"],
                expected_keywords=["МСП", "малки предприятия", "средни предприятия", "финансиране"],
                difficulty="easy",
                category="funding",
                language="bg"
            ),
            EvaluationQuery(
                id="easy_002", 
                query="Кога е крайният срок за кандидатстване по ОПИК?",
                expected_programs=["ОПИК"],
                expected_keywords=["краен срок", "кандидатстване", "дата"],
                difficulty="easy",
                category="deadlines",
                language="bg"
            ),
            EvaluationQuery(
                id="easy_003",
                query="Какви документи са нужни за кандидатстване?",
                expected_programs=["общи изисквания"],
                expected_keywords=["документи", "изисквания", "кандидатстване"],
                difficulty="easy", 
                category="procedures",
                language="bg"
            ),
            
            # MEDIUM QUERIES
            EvaluationQuery(
                id="medium_001",
                query="Може ли НПО да кандидатства за финансиране на проект за дигитализация на културното наследство?",
                expected_programs=["ОПРЧР", "ОПИК"],
                expected_keywords=["НПО", "дигитализация", "културно наследство", "право на кандидатстване"],
                difficulty="medium",
                category="eligibility",
                language="bg"
            ),
            EvaluationQuery(
                id="medium_002",
                query="Каква е максималната сума за проект по програма за регионално развитие в Северозападен район?",
                expected_programs=["ОПРР"],
                expected_keywords=["максимална сума", "регионално развитие", "Северозападен район"],
                difficulty="medium",
                category="funding",
                language="bg"
            ),
            EvaluationQuery(
                id="medium_003",
                query="Какви са изискванията за съфинансиране при проекти за енергийна ефективност?",
                expected_programs=["ОПОС", "ОПРР"],
                expected_keywords=["съфинансиране", "енергийна ефективност", "изисквания"],
                difficulty="medium",
                category="procedures",
                language="bg"
            ),
            
            # HARD QUERIES
            EvaluationQuery(
                id="hard_001",
                query="Може ли консорциум от български и румънски организации да кандидатства за трансгранично сътрудничество в областта на туризма с фокус върху Дунавския регион?",
                expected_programs=["Interreg", "ОПРР"],
                expected_keywords=["консорциум", "трансгранично сътрудничество", "туризъм", "Дунавски регион", "румънски организации"],
                difficulty="hard",
                category="eligibility",
                language="bg"
            ),
            EvaluationQuery(
                id="hard_002",
                query="Каква е процедурата за изменение на бюджета на одобрен проект по ОПИК, ако възникне необходимост от преразпределение между дейностите поради COVID-19?",
                expected_programs=["ОПИК"],
                expected_keywords=["изменение на бюджет", "одобрен проект", "преразпределение", "COVID-19", "процедура"],
                difficulty="hard",
                category="procedures", 
                language="bg"
            ),
            EvaluationQuery(
                id="hard_003",
                query="Как се изчислява индикаторът за устойчивост при проекти за кръгова икономика финансирани от ОПОС?",
                expected_programs=["ОПОС"],
                expected_keywords=["индикатор", "устойчивост", "кръгова икономика", "изчисляване"],
                difficulty="hard",
                category="procedures",
                language="bg"
            )
        ]
        
        self.evaluation_queries = benchmark_queries
        self.logger.info(f"✅ Заредени {len(benchmark_queries)} benchmark заявки")
    
    async def evaluate_rag_system(
        self, 
        rag_function,
        supabase_client,
        async_openai_client=None,
        max_queries: Optional[int] = None
    ) -> Dict[str, Any]:
        """Оценява RAG системата с benchmark заявки"""
        
        print("🧪 ЗАПОЧВАМ COMPREHENSIVE RAG EVALUATION")
        print("=" * 60)

        print(f"🔍 Total evaluation queries available: {len(self.evaluation_queries)}")

        queries_to_test = self.evaluation_queries
        if max_queries:
            queries_to_test = queries_to_test[:max_queries]

        print(f"🔍 Queries to test: {len(queries_to_test)}")
        
        results = []
        total_start_time = time.time()
        
        for i, eval_query in enumerate(queries_to_test, 1):
            print(f"📊 Тест {i}/{len(queries_to_test)}: {eval_query.id}")
            print(f"🔍 Заявка: {eval_query.query}")
            
            try:
                # Execute RAG query
                start_time = time.time()
                
                rag_result = await rag_function(
                    query=eval_query.query,
                    supabase_client=supabase_client,
                    async_openai_client=async_openai_client,
                    final_top_k=5,
                    similarity_threshold=0.3  # Lower threshold for testing
                )

                # Debug: Print RAG result structure
                print(f"🔍 RAG result type: {type(rag_result)}")
                if isinstance(rag_result, dict):
                    print(f"🔍 RAG result keys: {list(rag_result.keys())}")
                    results_count = len(rag_result.get('results', []))
                    print(f"🔍 Results count: {results_count}")
                else:
                    print(f"🔍 RAG result: {rag_result}")
                
                processing_time = time.time() - start_time
                
                # Evaluate result
                eval_result = await self._evaluate_single_result(
                    eval_query, rag_result, processing_time
                )
                
                results.append(eval_result)

                print(f"✅ Резултат: {eval_result.overall_score:.3f} ({eval_result.method_used})")
                print("-" * 40)

            except Exception as e:
                print(f"❌ Грешка при тест {eval_query.id}: {e}")
                import traceback
                traceback.print_exc()
                
                # Create error result
                error_result = EvaluationResult(
                    query_id=eval_query.id,
                    query_text=eval_query.query,
                    retrieved_docs=0,
                    processing_time=0.0,
                    program_precision=0.0,
                    program_recall=0.0,
                    program_f1=0.0,
                    rouge_1=0.0,
                    rouge_2=0.0,
                    rouge_l=0.0,
                    bert_score_f1=0.0,
                    deadline_accuracy=0.0,
                    eligibility_accuracy=0.0,
                    source_attribution=0.0,
                    overall_score=0.0,
                    method_used="ERROR",
                    errors=[str(e)]
                )
                results.append(error_result)
        
        total_time = time.time() - total_start_time
        
        # Calculate aggregate metrics
        print(f"🔍 Calculating aggregate metrics for {len(results)} results")
        aggregate_metrics = self._calculate_aggregate_metrics(results)
        print(f"🔍 Aggregate metrics: {aggregate_metrics}")
        
        self.logger.info("=" * 60)
        self.logger.info("🎉 EVALUATION ЗАВЪРШИ!")
        self.logger.info(f"⏱️ Общо време: {total_time:.2f}s")
        self.logger.info(f"📊 Общ резултат: {aggregate_metrics['overall_average']:.3f}")
        
        return {
            "individual_results": [asdict(r) for r in results],
            "aggregate_metrics": aggregate_metrics,
            "total_time": total_time,
            "queries_tested": len(queries_to_test),
            "timestamp": datetime.now().isoformat()
        }

    async def _evaluate_single_result(
        self,
        eval_query: EvaluationQuery,
        rag_result: Dict[str, Any],
        processing_time: float
    ) -> EvaluationResult:
        """Оценява единичен резултат от RAG заявка"""

        # Extract documents from result - handle both dict and list formats
        if isinstance(rag_result, list):
            documents = rag_result
            method_used = 'direct_list'
        elif isinstance(rag_result, dict):
            documents = rag_result.get('results', []) or rag_result.get('documents', [])
            method_used = rag_result.get('strategy_used', rag_result.get('method', 'unknown'))
        else:
            documents = []
            method_used = 'unknown'

        # Calculate program accuracy
        program_metrics = self._calculate_program_accuracy(eval_query, documents)

        # Calculate content quality metrics
        content_metrics = self._calculate_content_quality(eval_query, documents)

        # Calculate domain-specific metrics
        domain_metrics = self._calculate_domain_metrics(eval_query, documents)

        # Calculate overall score
        overall_score = self._calculate_overall_score(
            program_metrics, content_metrics, domain_metrics
        )

        return EvaluationResult(
            query_id=eval_query.id,
            query_text=eval_query.query,
            retrieved_docs=len(documents),
            processing_time=processing_time,
            program_precision=program_metrics['precision'],
            program_recall=program_metrics['recall'],
            program_f1=program_metrics['f1'],
            rouge_1=content_metrics['rouge_1'],
            rouge_2=content_metrics['rouge_2'],
            rouge_l=content_metrics['rouge_l'],
            bert_score_f1=content_metrics['bert_score_f1'],
            deadline_accuracy=domain_metrics['deadline_accuracy'],
            eligibility_accuracy=domain_metrics['eligibility_accuracy'],
            source_attribution=domain_metrics['source_attribution'],
            overall_score=overall_score,
            method_used=method_used,
            errors=[]
        )

    def _calculate_program_accuracy(
        self,
        eval_query: EvaluationQuery,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Изчислява точността на програмите"""

        # Extract program names from documents
        found_programs = set()
        for doc in documents:
            # Method 1: Check extracted_programs field (from ProgramNameExtractor)
            extracted_programs = doc.get('extracted_programs', [])
            for prog in extracted_programs:
                if prog.get('confidence', 0) > 0.5:  # Only high-confidence matches
                    found_programs.add(prog['name'])

            # Method 2: Check content and URL for program mentions
            content = doc.get('content', '').lower()
            url = doc.get('url', '').lower()
            title = doc.get('title', '').lower()

            # Check for program mentions in content, title, URL
            for expected_program in eval_query.expected_programs:
                prog_lower = expected_program.lower()
                if (prog_lower in content or
                    prog_lower in url or
                    prog_lower in title or
                    any(prog_lower in prog['name'].lower() for prog in extracted_programs)):
                    found_programs.add(expected_program)

        expected_programs = set(eval_query.expected_programs)

        # Debug logging
        self.logger.info(f"Program Detection Debug:")
        self.logger.info(f"  Expected programs: {expected_programs}")
        self.logger.info(f"  Found programs: {found_programs}")
        self.logger.info(f"  Intersection: {found_programs & expected_programs}")

        # Calculate precision, recall, F1
        if len(found_programs) == 0:
            precision = 0.0
        else:
            precision = len(found_programs & expected_programs) / len(found_programs)

        if len(expected_programs) == 0:
            recall = 1.0
        else:
            recall = len(found_programs & expected_programs) / len(expected_programs)

        if precision + recall == 0:
            f1 = 0.0
        else:
            f1 = 2 * (precision * recall) / (precision + recall)

        self.logger.info(f"  Precision: {precision:.3f}, Recall: {recall:.3f}, F1: {f1:.3f}")

        return {
            'precision': precision,
            'recall': recall,
            'f1': f1
        }

    def _calculate_content_quality(
        self,
        eval_query: EvaluationQuery,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Изчислява качеството на съдържанието"""

        # Combine all document content
        combined_content = " ".join([
            doc.get('content', '') for doc in documents
        ])

        # Create reference text from expected keywords and programs
        reference_parts = []
        if eval_query.expected_keywords:
            reference_parts.extend(eval_query.expected_keywords)
        if eval_query.expected_programs:
            reference_parts.extend(eval_query.expected_programs)

        reference_text = " ".join(reference_parts) if reference_parts else eval_query.query

        rouge_scores = {'rouge_1': 0.0, 'rouge_2': 0.0, 'rouge_l': 0.0}
        bert_score_f1 = 0.0

        # Calculate ROUGE scores if available
        if self.rouge_scorer and combined_content and reference_text and len(combined_content.strip()) > 0:
            try:
                scores = self.rouge_scorer.score(reference_text, combined_content)
                rouge_scores = {
                    'rouge_1': scores['rouge1'].fmeasure,
                    'rouge_2': scores['rouge2'].fmeasure,
                    'rouge_l': scores['rougeL'].fmeasure
                }
                self.logger.info(f"ROUGE scores calculated: {rouge_scores}")
            except Exception as e:
                self.logger.warning(f"ROUGE calculation failed: {e}")

        # Calculate BERT Score if available
        if BERT_SCORE_AVAILABLE and combined_content and reference_text and len(combined_content.strip()) > 0:
            try:
                P, R, F1 = bert_score([combined_content], [reference_text], lang='bg')
                bert_score_f1 = F1.mean().item()
                self.logger.info(f"BERT Score F1: {bert_score_f1}")
            except Exception as e:
                self.logger.warning(f"BERT Score calculation failed: {e}")

        return {
            'rouge_1': rouge_scores['rouge_1'],
            'rouge_2': rouge_scores['rouge_2'],
            'rouge_l': rouge_scores['rouge_l'],
            'bert_score_f1': bert_score_f1
        }

    def _calculate_domain_metrics(
        self,
        eval_query: EvaluationQuery,
        documents: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        """Изчислява domain-specific метрики"""

        deadline_accuracy = 0.0
        eligibility_accuracy = 0.0
        source_attribution = 0.0

        # Check for deadline information
        if eval_query.category == "deadlines":
            deadline_keywords = ["срок", "дата", "до", "преди", "2024", "2025"]
            for doc in documents:
                content = doc.get('content', '').lower()
                if any(keyword in content for keyword in deadline_keywords):
                    deadline_accuracy = 1.0
                    break
        else:
            deadline_accuracy = 1.0  # Not applicable

        # Check for eligibility information
        if eval_query.category == "eligibility":
            eligibility_keywords = ["може", "право", "изисквания", "критерии", "условия"]
            for doc in documents:
                content = doc.get('content', '').lower()
                if any(keyword in content for keyword in eligibility_keywords):
                    eligibility_accuracy = 1.0
                    break
        else:
            eligibility_accuracy = 1.0  # Not applicable

        # Check source attribution
        valid_sources = 0
        for doc in documents:
            url = doc.get('url', '')
            if url and ('eufunds.bg' in url or 'esif.bg' in url):
                valid_sources += 1

        if len(documents) > 0:
            source_attribution = valid_sources / len(documents)

        return {
            'deadline_accuracy': deadline_accuracy,
            'eligibility_accuracy': eligibility_accuracy,
            'source_attribution': source_attribution
        }

    def _calculate_overall_score(
        self,
        program_metrics: Dict[str, float],
        content_metrics: Dict[str, float],
        domain_metrics: Dict[str, float]
    ) -> float:
        """Изчислява общия резултат"""

        # Weighted combination of metrics
        weights = {
            'program_f1': 0.3,
            'rouge_l': 0.2,
            'bert_score_f1': 0.2,
            'deadline_accuracy': 0.1,
            'eligibility_accuracy': 0.1,
            'source_attribution': 0.1
        }

        score = (
            weights['program_f1'] * program_metrics['f1'] +
            weights['rouge_l'] * content_metrics['rouge_l'] +
            weights['bert_score_f1'] * content_metrics['bert_score_f1'] +
            weights['deadline_accuracy'] * domain_metrics['deadline_accuracy'] +
            weights['eligibility_accuracy'] * domain_metrics['eligibility_accuracy'] +
            weights['source_attribution'] * domain_metrics['source_attribution']
        )

        return score

    def _calculate_aggregate_metrics(self, results: List[EvaluationResult]) -> Dict[str, float]:
        """Изчислява агрегирани метрики"""

        if not results:
            return {}

        # Calculate averages
        metrics = {
            'overall_average': np.mean([r.overall_score for r in results]),
            'program_f1_average': np.mean([r.program_f1 for r in results]),
            'rouge_l_average': np.mean([r.rouge_l for r in results]),
            'processing_time_average': np.mean([r.processing_time for r in results]),
            'retrieved_docs_average': np.mean([r.retrieved_docs for r in results]),
            'source_attribution_average': np.mean([r.source_attribution for r in results])
        }

        # Calculate by difficulty
        easy_results = [r for r in results if r.query_id.startswith('easy_')]
        medium_results = [r for r in results if r.query_id.startswith('medium_')]
        hard_results = [r for r in results if r.query_id.startswith('hard_')]

        if easy_results:
            metrics['easy_average'] = np.mean([r.overall_score for r in easy_results])
        if medium_results:
            metrics['medium_average'] = np.mean([r.overall_score for r in medium_results])
        if hard_results:
            metrics['hard_average'] = np.mean([r.overall_score for r in hard_results])

        return metrics


async def main():
    """Main function to run RAG evaluation"""
    print("🎯 Starting RAG Evaluation System...")

    # Load environment variables
    from dotenv import load_dotenv
    import os
    load_dotenv()

    # Initialize evaluator
    evaluator = BulgarianRAGEvaluator()
    print(f"🔍 Evaluator initialized with {len(evaluator.evaluation_queries)} queries")

    # Import required modules
    from utils import smart_rag_query, get_supabase_client
    from openai import AsyncOpenAI

    # Initialize clients
    supabase_client = get_supabase_client()
    openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Run comprehensive evaluation
    print("📊 Running comprehensive evaluation...")
    evaluation_data = await evaluator.evaluate_rag_system(
        rag_function=smart_rag_query,
        supabase_client=supabase_client,
        async_openai_client=openai_client,
        max_queries=3  # Limit for testing
    )

    results = evaluation_data.get('individual_results', [])

    # Calculate aggregate metrics
    print("📈 Calculating aggregate metrics...")
    # Convert dict results back to EvaluationResult objects if needed
    if results and isinstance(results[0], dict):
        from dataclasses import fields
        result_objects = []
        for r in results:
            # Create EvaluationResult from dict
            result_obj = EvaluationResult(**r)
            result_objects.append(result_obj)
        metrics = evaluator._calculate_aggregate_metrics(result_objects)
    else:
        metrics = evaluator._calculate_aggregate_metrics(results)

    # Print results
    print("\n" + "="*60)
    print("🏆 RAG EVALUATION RESULTS")
    print("="*60)

    print(f"📊 Total Queries: {len(results)}")
    print(f"⏱️  Average Processing Time: {metrics.get('avg_processing_time', 0):.2f}s")
    print(f"📄 Average Retrieved Docs: {metrics.get('avg_retrieved_docs', 0):.1f}")
    print(f"🎯 Overall Score: {metrics.get('overall_average', 0):.3f}")

    print("\n📈 Performance by Difficulty:")
    if 'easy_average' in metrics:
        print(f"  🟢 Easy: {metrics['easy_average']:.3f}")
    if 'medium_average' in metrics:
        print(f"  🟡 Medium: {metrics['medium_average']:.3f}")
    if 'hard_average' in metrics:
        print(f"  🔴 Hard: {metrics['hard_average']:.3f}")

    print("\n🔍 Detailed Metrics:")
    if 'avg_program_f1' in metrics:
        print(f"  📋 Program Detection F1: {metrics['avg_program_f1']:.3f}")
    if 'avg_rouge_l' in metrics:
        print(f"  📝 ROUGE-L Score: {metrics['avg_rouge_l']:.3f}")
    if 'avg_bert_score' in metrics:
        print(f"  🧠 BERT Score: {metrics['avg_bert_score']:.3f}")

    print("\n" + "="*60)
    print("✅ Evaluation completed successfully!")

    return results, metrics


if __name__ == "__main__":
    asyncio.run(main())
