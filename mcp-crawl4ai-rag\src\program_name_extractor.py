"""
Program Name Extractor - Gazetteer-based подход
Решава проблема с 50% точност при извличане на точни програмни имена
"""

import re
import logging
from typing import List, Dict, Any, Set, Tuple, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
import json

@dataclass
class ProgramMatch:
    """Резултат от намиране на програма"""
    exact_name: str
    confidence: float
    match_type: str  # 'exact', 'fuzzy', 'pattern', 'gazetteer'
    source_text: str
    position: Tuple[int, int]  # start, end позиция в текста

class BulgarianProgramExtractor:
    """
    Извлича точни имена на програми от български текст
    Използва gazetteer + pattern matching + fuzzy matching
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Gazetteer с известни програми
        self.known_programs = self._build_program_gazetteer()
        
        # Pattern-и за български административен език
        self.program_patterns = self._build_program_patterns()
        
        # Синоними и вариации
        self.program_synonyms = self._build_program_synonyms()
        
        self.logger.info(f"✅ Зареден gazetteer с {len(self.known_programs)} програми")
    
    def _build_program_gazetteer(self) -> Dict[str, List[str]]:
        """Изгражда gazetteer с известни програми"""
        return {
            'interreg': [
                'Програма Interreg VI-A IPA България Северна Македония 2021-2027',
                'Програма Interreg VI-A България-Сърбия 2021-2027',
                'Програма Interreg VI-A България-Румъния 2021-2027',
                'Програма Interreg VI-A България-Турция 2021-2027',
                'Програма Interreg VI-A България-Гърция 2021-2027',
                'Interreg VI-A IPA България Северна Македония',
                'Interreg България Северна Македония',
                'Interreg VI-A България-Сърбия',
                'Interreg България-Сърбия',
                'Interreg VI-A България-Румъния',
                'Interreg България-Румъния'
            ],
            'horizon': [
                'Програма Horizon Europe 2021-2027',
                'Horizon Europe 2021-2027',
                'Horizon Europe',
                'Програма Horizon 2020',
                'Horizon 2020'
            ],
            'regional': [
                'Програма Развитие на регионите 2021-2027',
                'Оперативна програма Развитие на регионите 2021-2027',
                'ОПРР 2021-2027',
                'ОПРР',
                'Програма за развитие на регионите',
                'Регионална програма за развитие'
            ],
            'transport': [
                'Оперативна програма Транспорт и транспортна инфраструктура 2021-2027',
                'ОПТТИ 2021-2027',
                'ОПТТИ',
                'Програма за транспорт и транспортна инфраструктура',
                'Транспортна програма'
            ],
            'environment': [
                'Оперативна програма Околна среда 2021-2027',
                'ОПОС 2021-2027',
                'ОПОС',
                'Програма за околна среда',
                'Екологична програма'
            ],
            'innovation': [
                'Оперативна програма Иновации и конкурентоспособност 2021-2027',
                'ОПИК 2021-2027',
                'ОПИК',
                'Програма за иновации и конкурентоспособност',
                'Иновационна програма'
            ],
            'human_resources': [
                'Оперативна програма Развитие на човешките ресурси 2021-2027',
                'ОПРЧР 2021-2027',
                'ОПРЧР',
                'Програма за развитие на човешките ресурси'
            ],
            'agriculture': [
                'Програма за развитие на селските райони 2021-2027',
                'ПРСР 2021-2027',
                'ПРСР',
                'Селска програма за развитие'
            ],
            'fisheries': [
                'Програма за морско дело и рибарство 2021-2027',
                'ПМДР 2021-2027',
                'ПМДР',
                'Рибарска програма'
            ],
            'border': [
                'Програма за финансова подкрепа за управление на границите',
                'Border Management Financial Support',
                'Програма за граничен контрол',
                'Гранична програма'
            ],
            'just_transition': [
                'Програма за справедлив преход',
                'Just Transition Programme',
                'Фонд за справедлив преход',
                'Програма за справедлива трансформация'
            ]
        }
    
    def _build_program_patterns(self) -> List[str]:
        """Изгражда regex pattern-и за програми"""
        return [
            # Основни програмни pattern-и
            r'Програма\s+([А-Я][А-Яа-я\s\-VI]+\d{4}[\-\d]*)',
            r'(Interreg\s+[А-Я\-VI]+\s+[А-Яа-я\s]+\d{4}[\-\d]*)',
            r'(Horizon\s+[А-Яа-я\s]*\d{4}[\-\d]*)',
            r'Оперативна програма\s+([А-Яа-я\s\"]+\d{4}[\-\d]*)',
            r'ОП([А-Я]{2,4})\s*\d{4}[\-\d]*',
            
            # Специфични pattern-и
            r'(ОПРР\s*\d{4}[\-\d]*)',
            r'(ОПТТИ\s*\d{4}[\-\d]*)',
            r'(ОПОС\s*\d{4}[\-\d]*)',
            r'(ОПИК\s*\d{4}[\-\d]*)',
            r'(ОПРЧР\s*\d{4}[\-\d]*)',
            r'(ПРСР\s*\d{4}[\-\d]*)',
            r'(ПМДР\s*\d{4}[\-\d]*)',
            
            # Международни програми
            r'(Interreg\s+VI[\-A]*\s+[А-Яа-я\s\-]+\d{4}[\-\d]*)',
            r'(Horizon\s+Europe\s*\d{4}[\-\d]*)',
            r'(Just\s+Transition\s+[А-Яа-я\s]*)',
            r'(Border\s+Management\s+[А-Яа-я\s]*)',
            
            # Общи pattern-и
            r'Програма\s+за\s+([А-Яа-я\s]+\d{4}[\-\d]*)',
            r'Фонд\s+за\s+([А-Яа-я\s]+)',
            r'Схема\s+([А-Яа-я\s\-]+\d{4}[\-\d]*)'
        ]
    
    def _build_program_synonyms(self) -> Dict[str, List[str]]:
        """Изгражда синоними за програми"""
        return {
            'развитие': ['развиване', 'усъвършенстване', 'подобряване'],
            'регион': ['региони', 'регионален', 'регионални', 'област', 'области'],
            'транспорт': ['транспортен', 'транспортна', 'транспортни', 'превоз'],
            'околна среда': ['екология', 'екологичен', 'природа', 'опазване'],
            'иновации': ['иновационен', 'иновативен', 'нововъведения'],
            'конкурентоспособност': ['конкурентен', 'конкуренция'],
            'човешки ресурси': ['кадри', 'персонал', 'заетост', 'образование'],
            'селски райони': ['село', 'селски', 'земеделие', 'агрикултура'],
            'рибарство': ['риболов', 'рибни ресурси', 'морско дело'],
            'граници': ['граничен', 'гранично', 'контрол', 'управление']
        }
    
    def extract_program_names(self, text: str, query: str = "") -> List[ProgramMatch]:
        """
        Главна функция за извличане на програмни имена
        """
        matches = []
        
        # 1. Gazetteer-based извличане (най-точно)
        gazetteer_matches = self._gazetteer_extraction(text)
        matches.extend(gazetteer_matches)
        
        # 2. Pattern-based извличане
        pattern_matches = self._pattern_extraction(text)
        matches.extend(pattern_matches)
        
        # 3. Fuzzy matching с известни програми
        fuzzy_matches = self._fuzzy_extraction(text, query)
        matches.extend(fuzzy_matches)
        
        # 4. Премахни дублирания и ранжирай
        unique_matches = self._deduplicate_and_rank(matches)
        
        self.logger.info(f"🔍 Извлечени {len(unique_matches)} програмни имена от текст")
        
        return unique_matches
    
    def _gazetteer_extraction(self, text: str) -> List[ProgramMatch]:
        """Gazetteer-based извличане на точни имена"""
        matches = []
        text_lower = text.lower()
        
        for category, programs in self.known_programs.items():
            for program in programs:
                program_lower = program.lower()
                
                # Търси точни съвпадения
                start_pos = text_lower.find(program_lower)
                if start_pos != -1:
                    end_pos = start_pos + len(program)
                    
                    match = ProgramMatch(
                        exact_name=program,
                        confidence=1.0,
                        match_type='gazetteer',
                        source_text=text[start_pos:end_pos],
                        position=(start_pos, end_pos)
                    )
                    matches.append(match)
        
        return matches
    
    def _pattern_extraction(self, text: str) -> List[ProgramMatch]:
        """Pattern-based извличане"""
        matches = []
        
        for pattern in self.program_patterns:
            regex_matches = re.finditer(pattern, text, re.IGNORECASE)
            
            for match in regex_matches:
                extracted_text = match.group(1) if match.groups() else match.group(0)
                
                program_match = ProgramMatch(
                    exact_name=extracted_text.strip(),
                    confidence=0.8,
                    match_type='pattern',
                    source_text=match.group(0),
                    position=match.span()
                )
                matches.append(program_match)
        
        return matches
    
    def _fuzzy_extraction(self, text: str, query: str) -> List[ProgramMatch]:
        """Fuzzy matching с известни програми"""
        matches = []
        
        # Раздели текста на изречения
        sentences = re.split(r'[.!?]\s+', text)
        
        for sentence in sentences:
            if len(sentence) < 20:  # Прескочи твърде къси изречения
                continue
                
            # Сравни с всички известни програми
            for category, programs in self.known_programs.items():
                for program in programs:
                    similarity = SequenceMatcher(None, sentence.lower(), program.lower()).ratio()
                    
                    if similarity > 0.6:  # Праг за fuzzy matching
                        match = ProgramMatch(
                            exact_name=program,
                            confidence=similarity,
                            match_type='fuzzy',
                            source_text=sentence,
                            position=(text.find(sentence), text.find(sentence) + len(sentence))
                        )
                        matches.append(match)
        
        return matches
    
    def _deduplicate_and_rank(self, matches: List[ProgramMatch]) -> List[ProgramMatch]:
        """Премахва дублирания и ранжира по confidence"""
        if not matches:
            return []
        
        # Групирай по exact_name
        grouped = {}
        for match in matches:
            key = match.exact_name.lower().strip()
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(match)
        
        # Избери най-добрия match за всяка група
        unique_matches = []
        for group in grouped.values():
            # Сортирай по confidence и match_type приоритет
            type_priority = {'gazetteer': 4, 'exact': 3, 'pattern': 2, 'fuzzy': 1}
            
            best_match = max(group, key=lambda m: (
                type_priority.get(m.match_type, 0),
                m.confidence
            ))
            unique_matches.append(best_match)
        
        # Сортирай по confidence (descending)
        unique_matches.sort(key=lambda m: m.confidence, reverse=True)
        
        return unique_matches
    
    def extract_from_documents(self, documents: List[Dict[str, Any]], query: str = "") -> Dict[str, List[ProgramMatch]]:
        """Извлича програмни имена от списък документи"""
        results = {}
        
        for i, doc in enumerate(documents):
            content = doc.get('content', '')
            title = doc.get('title', '')
            
            # Комбинирай заглавие и съдържание
            full_text = f"{title}\n{content}"
            
            # Извлечи програмни имена
            matches = self.extract_program_names(full_text, query)
            
            if matches:
                doc_id = doc.get('id', f'doc_{i}')
                results[doc_id] = matches
        
        return results
    
    def enhance_rag_results(self, documents: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """
        Обогатява RAG резултатите с извлечени програмни имена
        """
        enhanced_docs = []
        
        for doc in documents:
            enhanced_doc = doc.copy()
            
            # Извлечи програмни имена
            content = doc.get('content', '')
            title = doc.get('title', '')
            full_text = f"{title}\n{content}"
            
            matches = self.extract_program_names(full_text, query)
            
            # Добави извлечените имена към документа
            enhanced_doc['extracted_programs'] = [
                {
                    'name': match.exact_name,
                    'confidence': match.confidence,
                    'type': match.match_type
                }
                for match in matches
            ]
            
            # Изчисли program_name_score
            if matches:
                # Вземи най-високия confidence score
                max_confidence = max(match.confidence for match in matches)
                enhanced_doc['program_name_score'] = max_confidence
                
                # Добави програмните имена в summary
                program_names = [match.exact_name for match in matches[:3]]  # Топ 3
                enhanced_doc['program_summary'] = f"Програми: {', '.join(program_names)}"
            else:
                enhanced_doc['program_name_score'] = 0.0
                enhanced_doc['program_summary'] = "Няма идентифицирани програми"
            
            enhanced_docs.append(enhanced_doc)
        
        return enhanced_docs
    
    def get_program_statistics(self) -> Dict[str, Any]:
        """Връща статистики за gazetteer-а"""
        total_programs = sum(len(programs) for programs in self.known_programs.values())
        
        return {
            'total_programs': total_programs,
            'categories': len(self.known_programs),
            'category_breakdown': {
                category: len(programs) 
                for category, programs in self.known_programs.items()
            },
            'pattern_count': len(self.program_patterns)
        }


def test_program_extractor():
    """Тест функция за program extractor"""
    extractor = BulgarianProgramExtractor()
    
    # Тестови текстове
    test_texts = [
        "Програма Interreg VI-A IPA България Северна Македония 2021-2027 финансира трансгранични проекти",
        "ОПРР 2021-2027 подкрепя развитието на регионите в България",
        "Horizon Europe предоставя средства за научни изследвания",
        "Оперативна програма Транспорт и транспортна инфраструктура 2021-2027"
    ]
    
    print("🧪 Тестване на Program Name Extractor...")
    
    for i, text in enumerate(test_texts):
        print(f"\n📝 Тест {i+1}: {text[:50]}...")
        matches = extractor.extract_program_names(text)
        
        for match in matches:
            print(f"  ✅ {match.exact_name} (confidence: {match.confidence:.2f}, type: {match.match_type})")
    
    # Статистики
    stats = extractor.get_program_statistics()
    print(f"\n📊 Статистики: {stats}")


if __name__ == "__main__":
    test_program_extractor()
