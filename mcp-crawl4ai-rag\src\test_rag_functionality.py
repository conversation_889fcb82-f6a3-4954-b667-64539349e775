#!/usr/bin/env python3
"""
Тест на RAG функционалност след поправката
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Добавяне на src директорията към path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from crawl4ai_mcp import multi_vector_search

async def test_rag_functionality():
    """Тестване на RAG функционалност"""
    
    print("=== RAG FUNCTIONALITY TEST ===")
    
    # Зареждане на environment variables
    load_dotenv()
    
    # Тест запитвания
    test_queries = [
        "европейски фондове за иновации",
        "финансиране за малки и средни предприятия",
        "програми за дигитализация",
        "подкрепа за стартъпи",
        "зелени технологии финансиране"
    ]
    
    print(f"Тестване с {len(test_queries)} запитвания...")
    
    total_score = 0
    successful_queries = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Тест {i}: '{query}' ---")
        
        try:
            # Тестване на multi-vector search
            from mcp.server.fastmcp import Context
            import json
            ctx = Context()

            result_json = await multi_vector_search(
                ctx=ctx,
                query=query,
                limit=5
            )

            # Парсиране на JSON резултата
            result = json.loads(result_json)

            if result.get('success'):
                results = result.get('results', [])
                results_count = result.get('results_count', len(results))

                print(f"✅ Успешно: {results_count} резултата")

                # Изчисляване на средна оценка
                if results:
                    scores = [r.get('score', 0) for r in results if 'score' in r]
                    avg_score = sum(scores) / len(scores) if scores else 0
                    print(f"📊 Средна оценка: {avg_score:.1f}%")

                    # Показване на най-добрия резултат
                    best = results[0]
                    print(f"🏆 Най-добър: {best.get('title', 'N/A')[:50]}...")
                    print(f"   Оценка: {best.get('score', 0):.1f}%")
                    print(f"   URL: {best.get('url', 'N/A')[:60]}...")

                    total_score += avg_score
                    successful_queries += 1
                else:
                    print("⚠️ Няма резултати с оценки")

            else:
                print(f"❌ Неуспешно: {result.get('error', 'Неизвестна грешка')}")
                
        except Exception as e:
            print(f"❌ Грешка: {e}")
    
    # Обобщение
    print(f"\n=== ОБОБЩЕНИЕ ===")
    print(f"Успешни запитвания: {successful_queries}/{len(test_queries)}")
    
    if successful_queries > 0:
        overall_avg = total_score / successful_queries
        print(f"Обща средна оценка: {overall_avg:.1f}%")
        
        # Оценка на качеството
        if overall_avg >= 70:
            print("🎉 ОТЛИЧНО качество (≥70%)")
        elif overall_avg >= 50:
            print("✅ ДОБРО качество (50-69%)")
        elif overall_avg >= 30:
            print("⚠️ СРЕДНО качество (30-49%)")
        else:
            print("❌ НИСКО качество (<30%)")
    else:
        print("❌ Няма успешни запитвания")
    
    return successful_queries, total_score / successful_queries if successful_queries > 0 else 0

if __name__ == "__main__":
    success_count, avg_score = asyncio.run(test_rag_functionality())
    
    print(f"\n🏁 ФИНАЛЕН РЕЗУЛТАТ:")
    print(f"   Успешност: {success_count}/5 запитвания")
    print(f"   Средна оценка: {avg_score:.1f}%")
    
    if success_count >= 4 and avg_score >= 50:
        print("✅ RAG системата работи добре!")
    else:
        print("⚠️ RAG системата се нуждае от подобрения")
