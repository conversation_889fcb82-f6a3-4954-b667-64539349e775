# 🧪 РЪЧНО ТЕСТВАНЕ НА MCP CRAWL4AI RAG СИСТЕМАТА

## 📋 ПРЕГЛЕД
Този документ обяснява как да тествате системата ръчно без агент, за да потвърдите че всичко работи правилно.

## 🚀 СТЪПКА 1: СТАРТИРАНЕ НА MCP СЪРВЪРА

### Терминал 1 - Стартиране на сървъра:
```bash
# Отидете в директорията на проекта
cd C:\Users\<USER>\Desktop\evroprogrami\mcp-crawl4ai-rag

# Активирайте виртуалната среда (ако не е активна)
.venv\Scripts\activate

# Стартирайте MCP сървъра
python -m src.crawl4ai_mcp
```

**Очаквани резултати:**
- Сървърът трябва да стартира без грешки
- Трябва да видите съобщения за успешна инициализация
- Сървърът ще слуша за MCP заявки

## 🌐 СТЪПКА 2: CRAWLING НА НОВА СТРАНИЦА

### Терминал 2 - Тестване на crawling функционалност:
```bash
# Отворете нов терминал в същата директория
cd C:\Users\<USER>\Desktop\evroprogrami\mcp-crawl4ai-rag

# Активирайте виртуалната среда
.venv\Scripts\activate

# Тествайте crawling на нова страница
python -c "
import asyncio
from src.utils import crawl_and_store_page
from supabase import create_client
from dotenv import load_dotenv
import os

load_dotenv()
client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))

async def test_crawl():
    result = await crawl_and_store_page(
        client, 
        'https://www.eufunds.bg/bg/page/1',
        max_depth=1
    )
    print(f'Crawling резултат: {result}')

asyncio.run(test_crawl())
"
```

**Очаквани резултати:**
- Страницата трябва да бъде успешно crawl-ната
- Данните трябва да бъдат записани в Supabase
- Трябва да видите съобщение за успех

## 🔍 СТЪПКА 3: ТЕСТВАНЕ НА RAG ТЪРСЕНЕ

### Терминал 3 - Тестване на Phase 5 Adaptive Fusion:
```bash
# Отворете трети терминал
cd C:\Users\<USER>\Desktop\evroprogrami\mcp-crawl4ai-rag
.venv\Scripts\activate

# Тествайте RAG търсене с реална българска заявка
python -c "
from src.phase5_adaptive_fusion import adaptive_fusion_search
from supabase import create_client
from openai import OpenAI
from dotenv import load_dotenv
import os

load_dotenv()
client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))
openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Тест 1: Еразъм+ програми
print('🔍 ТЕСТ 1: Еразъм+ програми')
results = adaptive_fusion_search(
    client,
    'Еразъм+ програми за образование и обучение',
    openai_client,
    match_count=3
)
print(f'Брой резултати: {len(results)}')
for i, result in enumerate(results, 1):
    print(f'{i}. Score: {result.get(\"adaptive_score\", \"N/A\"):.3f} - {result.get(\"url\", \"N/A\")}')
print()

# Тест 2: МСП финансиране
print('🔍 ТЕСТ 2: МСП финансиране')
results = adaptive_fusion_search(
    client,
    'Програми за финансиране на малки и средни предприятия',
    openai_client,
    match_count=3
)
print(f'Брой резултати: {len(results)}')
for i, result in enumerate(results, 1):
    print(f'{i}. Score: {result.get(\"adaptive_score\", \"N/A\"):.3f} - {result.get(\"url\", \"N/A\")}')
print()

# Тест 3: ОПРР средства
print('🔍 ТЕСТ 3: ОПРР средства')
results = adaptive_fusion_search(
    client,
    'Как да кандидатствам за ОПРР средства за регионално развитие',
    openai_client,
    match_count=3
)
print(f'Брой резултати: {len(results)}')
for i, result in enumerate(results, 1):
    print(f'{i}. Score: {result.get(\"adaptive_score\", \"N/A\"):.3f} - {result.get(\"url\", \"N/A\")}')
"
```

**Очаквани резултати:**
- Всеки тест трябва да върне 3 резултата
- Adaptive scores трябва да са над 1.5 (добри резултати)
- URL-ите трябва да са релевантни към заявката

## 📊 СТЪПКА 4: ПРОВЕРКА НА БАЗАТА ДАННИ

### Терминал 4 - Проверка на Supabase данни:
```bash
cd C:\Users\<USER>\Desktop\evroprogrami\mcp-crawl4ai-rag
.venv\Scripts\activate

# Проверете колко документи има в базата
python -c "
from supabase import create_client
from dotenv import load_dotenv
import os

load_dotenv()
client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_KEY'))

# Проверете общия брой документи
result = client.table('crawled_pages').select('*', count='exact').execute()
print(f'📊 Общо документи в базата: {result.count}')

# Проверете последните 5 crawl-нати страници
recent = client.table('crawled_pages').select('url, created_at').order('created_at', desc=True).limit(5).execute()
print('📅 Последни 5 crawl-нати страници:')
for i, page in enumerate(recent.data, 1):
    print(f'{i}. {page[\"url\"]} - {page[\"created_at\"]}')
"
```

## 🎯 СТЪПКА 5: ТЕСТВАНЕ НА MCP ФУНКЦИИ

### Терминал 5 - Директно тестване на MCP функции:
```bash
cd C:\Users\<USER>\Desktop\evroprogrami\mcp-crawl4ai-rag
.venv\Scripts\activate

# Тествайте MCP функциите директно
python -c "
import asyncio
from src.crawl4ai_mcp import crawl_page, search_documents

async def test_mcp_functions():
    print('🧪 ТЕСТВАНЕ НА MCP ФУНКЦИИ')
    
    # Тест 1: Crawl функция
    print('1. Тестване на crawl_page...')
    crawl_result = await crawl_page('https://www.eufunds.bg/bg/page/2')
    print(f'Crawl резултат: {crawl_result[:100]}...')
    
    # Тест 2: Search функция
    print('2. Тестване на search_documents...')
    search_result = await search_documents('зелени технологии')
    print(f'Search резултати: {len(search_result)} документа')
    
    # Показване на първия резултат
    if search_result:
        first = search_result[0]
        print(f'Първи резултат: {first.get(\"url\", \"N/A\")}')
        print(f'Score: {first.get(\"adaptive_score\", \"N/A\")}')

asyncio.run(test_mcp_functions())
"
```

## ✅ КРИТЕРИИ ЗА УСПЕХ

### 🟢 Системата работи правилно ако:
1. **MCP сървърът стартира без грешки**
2. **Crawling функцията записва данни в Supabase**
3. **RAG търсенето връща релевантни резултати с високи scores (>1.5)**
4. **Базата данни съдържа актуални данни**
5. **MCP функциите работят директно**

### 🔴 Проблеми за внимание:
- Грешки при стартиране на сървъра
- Неуспешно crawling (връща грешки)
- Ниски adaptive scores (<1.0)
- Празни резултати от търсене
- Проблеми с връзката към Supabase

## 🛠️ ОТСТРАНЯВАНЕ НА ПРОБЛЕМИ

### Ако има проблеми:
1. **Проверете .env файла** - всички ключове трябва да са валидни
2. **Проверете интернет връзката** - за Supabase и OpenAI
3. **Рестартирайте сървъра** - понякога помага
4. **Проверете логовете** - за подробни съобщения за грешки

## 📝 ЗАКЛЮЧЕНИЕ

След изпълнение на всички стъпки трябва да имате пълна увереност че системата работи правилно и е готова за продукционна употреба. Всички тестове трябва да минат успешно и да показват високи резултати.

**Успех с тестването! 🚀**
