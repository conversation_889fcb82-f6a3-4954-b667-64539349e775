# LLM Консултация: Оптимизация на Слабите Места в RAG Системата

## Контекст на Проблема

Имаме високоефективна българска RAG система за европейски фондове с **100% общ успех** в реални тестове (8/8 теста), но с идентифицирани слаби места които искаме да оптимизираме.

## Текущо Състояние на Системата

### ✅ Силни Страни (Работят Отлично):
- **Hybrid Search**: BM25 + Dense vectors fusion с BGE-M3 embeddings
- **Cross-encoder Reranking**: ms-marco-MiniLM-L6-v2 модел
- **Cohere Reranking**: Професионален reranking API
- **Multi-stage Intelligent Reranking**: 5-етапна система
- **Content Enhancement**: Семантично обогатяване на резултатите
- **Bulgarian Language Support**: LaBSE embeddings за български

### ⚠️ Идентифицирани Слаби Места:

1. **Липса на OpenAI Client**
   - Пропуска Stage 3 от intelligent reranking
   - Няма LLM-базирано query analysis
   - Липсва advanced query complexity analysis

2. **Липсващ spaCy Model (en_core_web_sm)**
   - Основно NLP обработване не работи оптимално
   - Content enhancement е ограничено
   - Липсва advanced entity recognition

3. **Точност при Извличане на Точни Имена**
   - Намира релевантно съдържание (100% успех)
   - Но понякога не извлича точните имена на програми
   - Пример: Намира "Interreg, Северна Македония, 2021-2027" но не "Програма Interreg VI-A IPA България Северна Македония 2021-2027"

## Технически Детайли

### Архитектура:
- **Database**: Supabase с 367 документа
- **Embeddings**: BGE-M3 (1024 dimensions) + LaBSE за български
- **Search**: Hybrid (BM25 + Dense) с RRF fusion
- **Reranking**: Cross-encoder + Cohere + Multi-factor fusion
- **Language**: Основно български контент

### Текущи Резултати:
- **8/8 теста успешни** (100% общ успех)
- **7/8 теста перфектни** (100% точност)
- **1/8 тест частичен** (50% точност - намира съдържание, но не точни имена)

## Въпроси за Оптимизация

### 1. OpenAI Client Алтернативи
**Въпрос**: Как можем да заменим липсващия OpenAI client за Stage 3 reranking и query analysis? Кои open-source или по-евтини алтернативи препоръчвате за:
- LLM-базирано query complexity analysis
- Advanced query rewriting и expansion
- Semantic query understanding за български език

### 2. spaCy Model Оптимизация
**Въпрос**: Как да оптимизираме NLP обработването без en_core_web_sm модел? Препоръчвате ли:
- Български spaCy модел (bg_core_news_sm)?
- Алтернативни NLP библиотеки (Stanza, transformers)?
- Custom NLP pipeline за български контент?

### 3. Точно Извличане на Имена на Програми
**Въпрос**: Как да подобрим извличането на точни имена на програми от текст? Системата намира релевантното съдържание, но не винаги извлича точните официални имена. Препоръчвате ли:
- Named Entity Recognition (NER) специализиран за програми?
- Regex patterns за български програми?
- Post-processing с LLM за извличане на точни имена?
- Gazetteer-based approach с известни програми?

### 4. Подобряване на Точността при Частични Резултати
**Въпрос**: Как да превърнем 50% успеха в 100% при тестове като Interreg програмата? Системата намира правилното съдържание, но не извлича точните имена. Кои техники препоръчвате:
- Multi-step retrieval (първо намери, после извлечи имена)?
- Ensemble methods за комбиниране на различни подходи?
- Fine-tuning на модели за български административен език?

### 5. Performance vs Accuracy Trade-offs
**Въпрос**: Как да балансираме производителността с точността? Текущата система е бърза, но искаме да добавим:
- Допълнителни NLP стъпки
- По-сложно entity extraction
- Повече reranking етапи

Кои оптимизации препоръчвате за запазване на скоростта?

### 6. Специфични Подобрения за Български Език
**Въпрос**: Кои специфични техники препоръчвате за българския език в контекста на европейски фондове:
- Морфологичен анализ за български?
- Синоними и вариации на термини?
- Handling на кирилица vs латиница?
- Специализирани embeddings за административен български?

## Очаквани Резултати

Целим да постигнем:
- **100% точност** във всички тестове (не само 87.5%)
- **Запазване на високата производителност**
- **Подобрена точност при извличане на имена**
- **По-robust NLP обработване**

Моля, предоставете конкретни, практически препоръки с приоритизация и implementation details.
