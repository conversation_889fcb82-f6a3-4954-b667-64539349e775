#!/usr/bin/env python3
"""
Тест на Comprehensive RAG Evaluation System
РЕАЛНИ ТЕСТОВЕ за български RAG с benchmark datasets
"""

import asyncio
import sys
import os
import json
import time
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append('src')

from rag_evaluation import BulgarianRAGEvaluator
from utils import enhanced_semantic_search, ultra_smart_rag_query, get_supabase_client
from supabase import create_client, Client

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_rag_evaluation_system():
    """Тества RAG Evaluation System с реални заявки"""
    
    logger.info("🧪 ЗАПОЧВАМ РЕАЛЕН RAG EVALUATION ТЕСТ")
    logger.info("=" * 70)
    
    # Initialize Supabase
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        logger.error("❌ SUPABASE_URL или SUPABASE_SERVICE_KEY не са зададени")
        return
    
    supabase: Client = create_client(url, key)
    logger.info("✅ Supabase client инициализиран")
    
    # Initialize evaluator
    evaluator = BulgarianRAGEvaluator()
    logger.info(f"✅ Evaluator инициализиран с {len(evaluator.evaluation_queries)} benchmark заявки")
    
    # Test different RAG methods
    rag_methods = [
        {
            "name": "Enhanced Semantic Search",
            "function": enhanced_semantic_search,
            "description": "Стандартен семантичен search с BGE-M3"
        },
        {
            "name": "Ultra Smart RAG",
            "function": ultra_smart_rag_query,
            "description": "Разширен RAG с Multi-Step Pipeline"
        }
    ]
    
    all_results = {}
    
    for method in rag_methods:
        logger.info(f"🔬 ТЕСТВАМ: {method['name']}")
        logger.info(f"📝 Описание: {method['description']}")
        logger.info("-" * 50)
        
        try:
            start_time = time.time()
            
            # Run evaluation
            evaluation_result = await evaluator.evaluate_rag_system(
                rag_function=method['function'],
                supabase_client=supabase,
                async_openai_client=None,
                max_queries=6  # Test first 6 queries (2 easy, 2 medium, 2 hard)
            )
            
            method_time = time.time() - start_time
            
            # Store results
            all_results[method['name']] = evaluation_result
            
            # Display summary
            aggregate = evaluation_result['aggregate_metrics']
            logger.info(f"📊 РЕЗУЛТАТИ ЗА {method['name']}:")
            logger.info(f"   🎯 Общ резултат: {aggregate.get('overall_average', 0):.3f}")
            logger.info(f"   📈 Program F1: {aggregate.get('program_f1_average', 0):.3f}")
            logger.info(f"   📝 ROUGE-L: {aggregate.get('rouge_l_average', 0):.3f}")
            logger.info(f"   ⏱️ Средно време: {aggregate.get('processing_time_average', 0):.2f}s")
            logger.info(f"   📄 Средно docs: {aggregate.get('retrieved_docs_average', 0):.1f}")
            logger.info(f"   🔗 Source attribution: {aggregate.get('source_attribution_average', 0):.3f}")
            
            # Display by difficulty
            if 'easy_average' in aggregate:
                logger.info(f"   🟢 Easy queries: {aggregate['easy_average']:.3f}")
            if 'medium_average' in aggregate:
                logger.info(f"   🟡 Medium queries: {aggregate['medium_average']:.3f}")
            if 'hard_average' in aggregate:
                logger.info(f"   🔴 Hard queries: {aggregate['hard_average']:.3f}")
            
            logger.info(f"   ⏱️ Общо време за метода: {method_time:.2f}s")
            logger.info("✅ Метод завърши успешно!")
            
        except Exception as e:
            logger.error(f"❌ Грешка при тестване на {method['name']}: {e}", exc_info=True)
            all_results[method['name']] = {"error": str(e)}
        
        logger.info("=" * 50)
    
    # Compare methods
    logger.info("🏆 СРАВНЕНИЕ НА МЕТОДИТЕ:")
    logger.info("=" * 70)
    
    method_scores = {}
    for method_name, result in all_results.items():
        if 'error' not in result:
            score = result['aggregate_metrics'].get('overall_average', 0)
            method_scores[method_name] = score
            logger.info(f"📊 {method_name}: {score:.3f}")
    
    if method_scores:
        best_method = max(method_scores, key=method_scores.get)
        logger.info(f"🥇 НАЙ-ДОБЪР МЕТОД: {best_method} ({method_scores[best_method]:.3f})")
    
    # Save detailed results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    results_file = f"evaluation_results_{timestamp}.json"
    
    try:
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        logger.info(f"💾 Детайлни резултати запазени в: {results_file}")
    except Exception as e:
        logger.error(f"❌ Грешка при запазване на резултати: {e}")
    
    logger.info("=" * 70)
    logger.info("🎉 RAG EVALUATION ТЕСТ ЗАВЪРШИ!")
    
    return all_results

async def test_individual_queries():
    """Тества индивидуални заявки за debugging"""
    
    logger.info("🔍 ТЕСТВАМ ИНДИВИДУАЛНИ ЗАЯВКИ")
    logger.info("=" * 50)
    
    # Initialize Supabase
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    supabase: Client = create_client(url, key)
    
    # Test queries
    test_queries = [
        "Какви са програмите за малки и средни предприятия?",
        "Кога е крайният срок за кандидатстване по ОПИК?",
        "Може ли НПО да кандидатства за финансиране на проект за дигитализация?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        logger.info(f"🔍 Тест {i}: {query}")
        
        try:
            start_time = time.time()
            
            result = await enhanced_semantic_search(
                query=query,
                supabase_client=supabase,
                async_openai_client=None,
                similarity_threshold=0.62,
                final_top_k=3
            )
            
            processing_time = time.time() - start_time
            
            logger.info(f"⏱️ Време: {processing_time:.2f}s")
            logger.info(f"📄 Резултати: {len(result)}")
            
            for j, doc in enumerate(result[:2], 1):
                logger.info(f"   📄 Doc {j}:")
                logger.info(f"      URL: {doc.get('url', 'N/A')[:60]}...")
                logger.info(f"      Similarity: {doc.get('similarity', 0):.3f}")
                logger.info(f"      Content: {doc.get('content', '')[:100]}...")
            
        except Exception as e:
            logger.error(f"❌ Грешка при заявка {i}: {e}")
        
        logger.info("-" * 30)

if __name__ == "__main__":
    # Run comprehensive evaluation
    asyncio.run(test_rag_evaluation_system())
    
    # Uncomment to test individual queries
    # asyncio.run(test_individual_queries())
