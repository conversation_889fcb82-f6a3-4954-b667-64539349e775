#!/usr/bin/env python3
"""
Generate LaBSE embeddings for existing crawled pages
Phase 8.3: Bulgarian-specific Embeddings Implementation
"""

import asyncio
import logging
import time
from typing import List, Dict, Any
import numpy as np

# Import our modules
from src.utils import get_supabase_client
from src.bulgarian_embeddings import get_bulgarian_embedding_engine

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def generate_labse_embeddings_batch(batch_size: int = 10):
    """Generate LaBSE embeddings for crawled pages in batches"""
    
    logger.info("🚀 Starting LaBSE embeddings generation...")
    
    # Initialize clients
    client = get_supabase_client()
    engine = get_bulgarian_embedding_engine()
    
    # Get pages without LaBSE embeddings
    response = client.table('crawled_pages_labse').select('id, content').is_('embedding_labse', 'null').limit(batch_size).execute()
    
    if not response.data:
        logger.info("✅ All pages already have LaBSE embeddings!")
        return
    
    pages = response.data
    logger.info(f"📊 Processing {len(pages)} pages...")
    
    # Extract content for batch processing
    contents = [page['content'] for page in pages]
    page_ids = [page['id'] for page in pages]
    
    try:
        # Generate embeddings in batch
        logger.info(f"🔄 Creating LaBSE embeddings for {len(contents)} texts...")
        start_time = time.time()
        
        embeddings = engine.create_embeddings(contents)
        
        embedding_time = time.time() - start_time
        logger.info(f"✅ Created {len(embeddings)} LaBSE embeddings in {embedding_time:.3f}s")
        
        # Update database with embeddings
        update_count = 0
        for i, (page_id, embedding) in enumerate(zip(page_ids, embeddings)):
            try:
                # Convert numpy array to list for JSON serialization
                embedding_list = embedding.tolist()
                
                # Update the page with its embedding
                update_response = client.table('crawled_pages_labse').update({
                    'embedding_labse': embedding_list
                }).eq('id', page_id).execute()
                
                if update_response.data:
                    update_count += 1
                    if (i + 1) % 5 == 0:
                        logger.info(f"📝 Updated {i + 1}/{len(page_ids)} pages...")
                        
            except Exception as e:
                logger.error(f"❌ Failed to update page {page_id}: {e}")
                continue
        
        logger.info(f"✅ Successfully updated {update_count}/{len(pages)} pages with LaBSE embeddings")
        
        # Check remaining pages
        remaining_response = client.table('crawled_pages_labse').select('id', count='exact').is_('embedding_labse', 'null').execute()
        remaining_count = remaining_response.count
        
        logger.info(f"📊 Remaining pages without embeddings: {remaining_count}")
        
        return update_count, remaining_count
        
    except Exception as e:
        logger.error(f"❌ Failed to generate embeddings: {e}")
        return 0, len(pages)

async def main():
    """Main function to generate all LaBSE embeddings"""
    
    logger.info("🇧🇬 LaBSE Embeddings Generation - Phase 8.3")
    logger.info("=" * 60)
    
    total_processed = 0
    batch_size = 10
    max_batches = 20  # Limit to avoid exceeding Supabase limits
    
    for batch_num in range(1, max_batches + 1):
        logger.info(f"\n🔄 Processing batch {batch_num}/{max_batches}...")
        
        updated, remaining = await generate_labse_embeddings_batch(batch_size)
        total_processed += updated
        
        if remaining == 0:
            logger.info("🎉 All pages processed!")
            break
        
        if updated == 0:
            logger.warning("⚠️ No pages updated in this batch, stopping...")
            break
        
        # Small delay between batches
        await asyncio.sleep(1)
    
    logger.info(f"\n✅ COMPLETED: Processed {total_processed} pages with LaBSE embeddings")
    
    # Final statistics
    client = get_supabase_client()
    total_response = client.table('crawled_pages_labse').select('id', count='exact').execute()
    with_embeddings_response = client.table('crawled_pages_labse').select('id', count='exact').not_.is_('embedding_labse', 'null').execute()
    
    total_pages = total_response.count
    pages_with_embeddings = with_embeddings_response.count
    
    logger.info(f"📊 FINAL STATISTICS:")
    logger.info(f"   Total pages: {total_pages}")
    logger.info(f"   With LaBSE embeddings: {pages_with_embeddings}")
    logger.info(f"   Coverage: {pages_with_embeddings/total_pages*100:.1f}%")

if __name__ == "__main__":
    asyncio.run(main())
