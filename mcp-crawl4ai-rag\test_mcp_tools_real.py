#!/usr/bin/env python3
"""
РЕАЛЕН ТЕСТ НА MCP TOOLS
========================

Този тест проверява реалната функционалност на MCP tools чрез HTTP заявки:
1. Тестване на perform_rag_query
2. Тестване на enhanced_rag_query  
3. Тестване на consultant_agent_query
4. Тестване на multi_vector_search
5. Измерване на качеството и производителността
"""

import asyncio
import aiohttp
import json
import time
from typing import Dict, List, Any

# MCP Server URL
MCP_SERVER_URL = "http://localhost:8051"

async def initialize_mcp_session(session: aiohttp.ClientSession) -> str:
    """Initialize MCP session and return session_id"""

    # Initialize message
    init_message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "initialize",
        "params": {
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "clientInfo": {
                "name": "test-client",
                "version": "1.0.0"
            }
        }
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }

    try:
        async with session.post(f"{MCP_SERVER_URL}/sse",
                               json=init_message,
                               headers=headers) as response:

            if response.status == 200:
                result = await response.json()
                # Extract session_id from response
                if "session_id" in result:
                    return result["session_id"]
                else:
                    # Generate a simple session ID for testing
                    return "test-session-123"
            else:
                # Fallback session ID
                return "test-session-123"

    except Exception as e:
        # Fallback session ID
        return "test-session-123"


async def call_mcp_tool(session: aiohttp.ClientSession, session_id: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Call an MCP tool via HTTP with session_id"""

    # MCP protocol message
    message = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/call",
        "params": {
            "name": tool_name,
            "arguments": arguments
        }
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "X-Session-ID": session_id
    }

    try:
        async with session.post(f"{MCP_SERVER_URL}/sse",
                               json=message,
                               headers=headers) as response:

            if response.status == 200:
                result = await response.json()
                return result
            else:
                error_text = await response.text()
                return {
                    "error": f"HTTP {response.status}: {error_text}"
                }

    except Exception as e:
        return {
            "error": f"Request failed: {str(e)}"
        }


async def test_rag_tools():
    """Test all RAG-related MCP tools"""
    
    print('🎯 ТЕСТ НА MCP RAG TOOLS')
    print('=' * 50)
    
    # Test queries in Bulgarian
    test_queries = [
        "програми за иновации в малки и средни предприятия 2024",
        "финансиране за стартъп компании в България", 
        "европейски фондове за дигитализация",
        "ПРЧР програма за обучение",
        "ПКИП иновации предприятия"
    ]
    
    # Tools to test
    tools_to_test = [
        "perform_rag_query",
        "enhanced_rag_query", 
        "consultant_agent_query",
        "multi_vector_search"
    ]
    
    results_summary = []
    
    async with aiohttp.ClientSession() as session:

        # Initialize MCP session
        print('🔗 Инициализиране на MCP сесия...')
        session_id = await initialize_mcp_session(session)
        print(f'   ✅ Session ID: {session_id}')

        for tool_name in tools_to_test:
            print(f'\n🔧 Тестване на {tool_name}...')

            tool_results = []

            for query in test_queries:
                print(f'   🔍 Заявка: "{query[:40]}..."')

                start_time = time.time()

                # Prepare arguments based on tool
                if tool_name == "perform_rag_query":
                    arguments = {
                        "query": query,
                        "match_count": 5
                    }
                elif tool_name == "enhanced_rag_query":
                    arguments = {
                        "query": query,
                        "strategy": "enhanced_semantic",
                        "final_top_k": 5
                    }
                elif tool_name == "consultant_agent_query":
                    arguments = {
                        "query": query,
                        "max_iterations": 2,
                        "final_top_k": 5
                    }
                elif tool_name == "multi_vector_search":
                    arguments = {
                        "query": query,
                        "dimensions": ["semantic", "topic", "temporal"],
                        "final_top_k": 5
                    }

                # Call the tool
                result = await call_mcp_tool(session, session_id, tool_name, arguments)
                end_time = time.time()
                
                # Analyze result
                if "error" in result:
                    print(f'      ❌ Грешка: {result["error"]}')
                    tool_results.append({
                        'query': query,
                        'error': result["error"],
                        'response_time': end_time - start_time
                    })
                else:
                    # Try to extract content from MCP response
                    content = None
                    if "result" in result and "content" in result["result"]:
                        content = result["result"]["content"]
                    elif "result" in result:
                        content = result["result"]
                    
                    if content:
                        # Try to parse as JSON if it's a string
                        if isinstance(content, str):
                            try:
                                content = json.loads(content)
                            except:
                                pass
                        
                        # Count results
                        results_count = 0
                        if isinstance(content, dict):
                            if "results" in content:
                                results_count = len(content["results"])
                            elif "documents" in content:
                                results_count = len(content["documents"])
                        elif isinstance(content, list):
                            results_count = len(content)
                        
                        print(f'      ✅ Успех: {results_count} резултата ({end_time-start_time:.2f}s)')
                        
                        tool_results.append({
                            'query': query,
                            'results_count': results_count,
                            'response_time': end_time - start_time,
                            'success': True
                        })
                    else:
                        print(f'      ⚠️ Празен отговор ({end_time-start_time:.2f}s)')
                        tool_results.append({
                            'query': query,
                            'results_count': 0,
                            'response_time': end_time - start_time,
                            'success': False
                        })
            
            # Summarize tool performance
            successful = [r for r in tool_results if r.get('success', False)]
            failed = [r for r in tool_results if 'error' in r or not r.get('success', False)]
            
            print(f'\n   📊 {tool_name} резултати:')
            print(f'      ✅ Успешни: {len(successful)}/{len(tool_results)}')
            print(f'      ❌ Неуспешни: {len(failed)}/{len(tool_results)}')
            
            if successful:
                avg_time = sum(r['response_time'] for r in successful) / len(successful)
                avg_results = sum(r['results_count'] for r in successful) / len(successful)
                print(f'      ⏱️ Средно време: {avg_time:.2f}s')
                print(f'      📈 Средно резултати: {avg_results:.1f}')
            
            results_summary.append({
                'tool': tool_name,
                'successful': len(successful),
                'failed': len(failed),
                'total': len(tool_results),
                'avg_time': sum(r['response_time'] for r in successful) / len(successful) if successful else 0,
                'avg_results': sum(r['results_count'] for r in successful) / len(successful) if successful else 0
            })
    
    return results_summary


async def test_system_health():
    """Test system health and monitoring tools"""
    
    print('\n🏥 ТЕСТ НА СИСТЕМНО ЗДРАВЕ...')
    
    async with aiohttp.ClientSession() as session:

        # Initialize MCP session
        session_id = await initialize_mcp_session(session)

        # Test system health dashboard
        print('   🔍 Тестване на get_system_health_dashboard...')

        result = await call_mcp_tool(session, session_id, "get_system_health_dashboard", {})
        
        if "error" in result:
            print(f'      ❌ Грешка: {result["error"]}')
            return False
        else:
            print('      ✅ Системното здраве е достъпно')
            
            # Try to extract health info
            if "result" in result and "content" in result["result"]:
                content = result["result"]["content"]
                if isinstance(content, str):
                    try:
                        health_data = json.loads(content)
                        if "system_status" in health_data:
                            print(f'      📊 Статус: {health_data["system_status"]}')
                        if "total_documents" in health_data:
                            print(f'      📄 Документи: {health_data["total_documents"]}')
                    except:
                        pass
            
            return True


def analyze_overall_results(results_summary: List[Dict[str, Any]]):
    """Analyze and display overall test results"""
    
    print('\n🎯 ОБЩ АНАЛИЗ НА РЕЗУЛТАТИТЕ')
    print('=' * 50)
    
    total_tests = sum(r['total'] for r in results_summary)
    total_successful = sum(r['successful'] for r in results_summary)
    total_failed = sum(r['failed'] for r in results_summary)
    
    print(f'📊 Общо тестове: {total_tests}')
    print(f'✅ Успешни: {total_successful} ({total_successful/total_tests*100:.1f}%)')
    print(f'❌ Неуспешни: {total_failed} ({total_failed/total_tests*100:.1f}%)')
    
    if total_successful > 0:
        avg_time_overall = sum(r['avg_time'] * r['successful'] for r in results_summary) / total_successful
        avg_results_overall = sum(r['avg_results'] * r['successful'] for r in results_summary) / total_successful
        
        print(f'⏱️ Средно време за отговор: {avg_time_overall:.2f}s')
        print(f'📈 Средно резултати на заявка: {avg_results_overall:.1f}')
    
    print('\n🔧 Резултати по tools:')
    for r in results_summary:
        success_rate = r['successful'] / r['total'] * 100 if r['total'] > 0 else 0
        print(f'   {r["tool"]}: {success_rate:.1f}% успех ({r["successful"]}/{r["total"]})')
    
    # Overall assessment
    if total_successful / total_tests >= 0.8:
        print('\n🌟 ОТЛИЧНА ПРОИЗВОДИТЕЛНОСТ! Системата работи много добре.')
    elif total_successful / total_tests >= 0.6:
        print('\n👍 ДОБРА ПРОИЗВОДИТЕЛНОСТ! Системата работи добре с малки проблеми.')
    elif total_successful / total_tests >= 0.4:
        print('\n⚠️ СРЕДНА ПРОИЗВОДИТЕЛНОСТ! Системата има проблеми които трябва да се решат.')
    else:
        print('\n❌ НИСКА ПРОИЗВОДИТЕЛНОСТ! Системата има сериозни проблеми.')


async def main():
    """Main test function"""
    
    print('🚀 ЗАПОЧВАНЕ НА РЕАЛЕН ТЕСТ НА MCP TOOLS')
    print('=' * 60)
    
    try:
        # Test RAG tools
        results_summary = await test_rag_tools()
        
        # Test system health
        health_ok = await test_system_health()
        
        # Analyze results
        analyze_overall_results(results_summary)
        
        print('\n🏁 ТЕСТЪТ НА MCP TOOLS ЗАВЪРШИ!')
        
    except Exception as e:
        print(f'\n💥 КРИТИЧНА ГРЕШКА: {e}')
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
