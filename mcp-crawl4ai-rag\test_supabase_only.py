#!/usr/bin/env python3
"""
Тест само на Supabase връзката и RPC функцията
"""
import os
from dotenv import load_dotenv
from supabase import create_client, Client

def test_supabase_connection():
    print('🎯 ТЕСТ НА SUPABASE ВРЪЗКАТА И RAG ФУНКЦИОНАЛНОСТ')
    print('=' * 55)
    
    try:
        # Load environment variables
        print('\n1️⃣ Зареждане на environment variables...')
        load_dotenv()
        
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")  # Correct key name from .env

        if not supabase_url or not supabase_key:
            print('❌ Липсват SUPABASE_URL или SUPABASE_SERVICE_KEY')
            print(f'   SUPABASE_URL: {"✅" if supabase_url else "❌"}')
            print(f'   SUPABASE_SERVICE_KEY: {"✅" if supabase_key else "❌"}')
            return False
        
        print('✅ Environment variables заредени')
        
        # Create Supabase client
        print('\n2️⃣ Създаване на Supabase клиент...')
        supabase: Client = create_client(supabase_url, supabase_key)
        print('✅ Supabase клиент създаден')
        
        # Test connection
        print('\n3️⃣ Тест на връзката...')
        result = supabase.table('crawled_pages').select('id').limit(1).execute()
        print(f'✅ Връзката работи - намерени {len(result.data)} записа')
        
        # Check total data
        print('\n4️⃣ Проверка на общите данни...')
        count_result = supabase.table('crawled_pages').select('id', count='exact').execute()
        total_pages = count_result.count
        print(f'📊 Общо страници в базата: {total_pages}')
        
        if total_pages == 0:
            print('❌ Няма данни в базата за тестване')
            return False
        
        # Test data quality and content
        print('\n5️⃣ Анализ на качеството на данните...')

        # Check sample data
        sample_result = supabase.table('crawled_pages').select(
            'id, url, content, metadata, embedding'
        ).limit(3).execute()

        if sample_result.data:
            print(f'✅ Намерени {len(sample_result.data)} примерни записа')

            for i, record in enumerate(sample_result.data, 1):
                print(f'\n   📄 Запис {i}:')
                print(f'      ID: {record.get("id")}')
                print(f'      URL: {record.get("url", "N/A")[:60]}...')
                content = record.get("content", "")
                print(f'      Content length: {len(content)} chars')
                if content:
                    print(f'      Content preview: {content[:100]}...')
                print(f'      Has embedding: {"✅" if record.get("embedding") else "❌"}')

                # Check metadata
                metadata = record.get("metadata", {})
                if metadata:
                    print(f'      Metadata keys: {list(metadata.keys())}')
                    if 'extracted_entities' in metadata:
                        entities = metadata['extracted_entities']
                        if entities:
                            print(f'      Entities count: {len(entities)}')
                            # Show entity types
                            entity_types = [e.get('type') for e in entities if isinstance(e, dict)]
                            unique_types = list(set(entity_types))
                            print(f'      Entity types: {unique_types}')
                        else:
                            print('      No entities extracted')
                    else:
                        print('      No extracted_entities in metadata')
                else:
                    print('      No metadata')
        else:
            print('❌ Няма примерни данни')
            return False
                

        # Test search in content using SQL
        print('\n6️⃣ Тест на търсене в съдържанието...')
        search_terms = ['иновации', 'стартъп', 'дигитализация']

        for term in search_terms:
            try:
                search_result = supabase.table('crawled_pages').select(
                    'id, url, content'
                ).ilike('content', f'%{term}%').limit(3).execute()

                if search_result.data:
                    print(f'   🔍 "{term}": намерени {len(search_result.data)} резултата')
                    for result in search_result.data:
                        content = result.get('content', '')[:60]
                        print(f'      - {content}...')
                else:
                    print(f'   🔍 "{term}": няма резултати')

            except Exception as e:
                print(f'   💥 Грешка при търсене на "{term}": {e}')
        
        print('\n' + '=' * 55)
        print('🎉 SUPABASE И RAG ТЕСТЪТ ЗАВЪРШИ УСПЕШНО!')
        return True
        
    except Exception as e:
        print(f'❌ Грешка при тестването: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = test_supabase_connection()
    print(f'\n🏁 ФИНАЛЕН РЕЗУЛТАТ: {"✅ УСПЕХ" if result else "❌ НЕУСПЕХ"}')
