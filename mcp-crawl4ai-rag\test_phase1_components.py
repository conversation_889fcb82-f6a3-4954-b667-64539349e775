#!/usr/bin/env python3
"""
Тест на Phase 1 компоненти без циркулярни импорти
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_groq_reranker():
    """Тестване на Groq Reranker"""
    print("🤖 ТЕСТВАНЕ НА GROQ RERANKER")
    print("=" * 50)
    
    try:
        from src.groq_reranker import create_groq_reranker
        
        reranker = create_groq_reranker()
        print(f"✅ Groq Reranker създаден: {type(reranker).__name__}")
        
        # Тест на query complexity
        complexity = reranker.analyze_query_complexity("програми за развитие на регионите")
        print(f"✅ Query complexity: {complexity}")
        
        # Тест на document scoring
        test_docs = [
            {"content": "ОПРР програма за развитие на регионите", "title": "ОПРР"},
            {"content": "Interreg програма за трансгранично сътрудничество", "title": "Interreg"},
            {"content": "Horizon Europe иновационни проекти", "title": "Horizon"}
        ]
        
        scored_docs = reranker.stage3_reranking("програми за развитие", test_docs)
        print(f"✅ Reranked documents: {len(scored_docs)}")
        
        for i, doc in enumerate(scored_docs[:2], 1):
            score = doc.get('groq_score', doc.get('fallback_score', 0))
            print(f"  {i}. {doc['title']}: {score:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в Groq Reranker: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_program_extractor():
    """Тестване на Program Name Extractor"""
    print(f"\n🏷️ ТЕСТВАНЕ НА PROGRAM NAME EXTRACTOR")
    print("=" * 50)
    
    try:
        from src.program_name_extractor import BulgarianProgramExtractor
        
        extractor = BulgarianProgramExtractor()
        print(f"✅ Program Extractor създаден: {type(extractor).__name__}")
        
        # Тест текстове
        test_texts = [
            "ОПРР 2021-2027 финансира транспортни проекти в България.",
            "Програма Interreg VI-A IPA България Северна Македония подкрепя трансгранично сътрудничество.",
            "Horizon Europe програмата за иновации и изследвания.",
            "Програма за развитие на селските райони 2021-2027.",
            "Фонд за справедлив преход в регионите."
        ]
        
        total_matches = 0
        for i, text in enumerate(test_texts, 1):
            matches = extractor.extract_program_names(text)
            total_matches += len(matches)
            print(f"  Текст {i}: {len(matches)} програми")
            
            for match in matches[:2]:  # Покажи топ 2
                print(f"    🏷️ {match.exact_name} (confidence: {match.confidence:.2f})")
        
        print(f"✅ Общо извлечени програми: {total_matches}")
        
        # Тест на статистики
        stats = extractor.get_program_statistics()
        print(f"✅ Gazetteer статистики: {stats['total_programs']} програми в {stats['categories']} категории")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в Program Extractor: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_step_rag():
    """Тестване на Multi-Step RAG функция"""
    print(f"\n🚀 ТЕСТВАНЕ НА MULTI-STEP RAG")
    print("=" * 50)
    
    try:
        from src.utils import multi_step_rag_query, MULTI_STEP_PIPELINE_AVAILABLE
        
        print(f"✅ Multi-Step Pipeline Available: {MULTI_STEP_PIPELINE_AVAILABLE}")
        
        if not MULTI_STEP_PIPELINE_AVAILABLE:
            print("⚠️ Multi-Step Pipeline не е наличен - тестът ще използва fallback")
        
        # Тестови заявки
        test_queries = [
            "ОПРР програми за транспорт",
            "Interreg трансгранично сътрудничество",
            "програми за развитие на регионите"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n  🔍 Тест {i}: {query}")
            
            result = multi_step_rag_query(
                query=query,
                max_results=3,
                enable_groq_reranking=True,
                enable_program_extraction=True,
                enable_bulgarian_nlp=False,  # Изключи за по-бърз тест
                debug=False
            )
            
            documents = result.get('documents', [])
            method = result.get('method', 'unknown')
            processing_time = result.get('processing_time', 0)
            
            print(f"    ✅ Резултат: {len(documents)} документа, метод: {method}, време: {processing_time:.2f}s")
            
            # Покажи топ резултат
            if documents:
                top_doc = documents[0]
                title = top_doc.get('title', 'Без заглавие')[:50]
                score = top_doc.get('final_pipeline_score', top_doc.get('similarity_score', 0))
                print(f"    📄 Топ: {title}... (score: {score:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в Multi-Step RAG: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """Тестване на интеграция между компонентите"""
    print(f"\n🔗 ТЕСТВАНЕ НА ИНТЕГРАЦИЯ")
    print("=" * 50)
    
    try:
        from src.groq_reranker import create_groq_reranker
        from src.program_name_extractor import BulgarianProgramExtractor
        
        # Създай компонентите
        reranker = create_groq_reranker()
        extractor = BulgarianProgramExtractor()
        
        # Тестови документи
        test_docs = [
            {
                "content": "ОПРР 2021-2027 е програма за развитие на регионите в България. Тя финансира транспортни проекти и инфраструктура.",
                "title": "ОПРР Програма",
                "similarity_score": 0.85
            },
            {
                "content": "Програма Interreg VI-A IPA България Северна Македония 2021-2027 подкрепя трансгранично сътрудничество между двете страни.",
                "title": "Interreg Програма", 
                "similarity_score": 0.78
            },
            {
                "content": "Horizon Europe е програмата на ЕС за изследвания и иновации за периода 2021-2027.",
                "title": "Horizon Europe",
                "similarity_score": 0.65
            }
        ]
        
        query = "програми за развитие на регионите"
        
        # 1. Извлечи програмни имена
        enhanced_docs = extractor.enhance_rag_results(test_docs, query)
        print(f"✅ Program extraction: {len(enhanced_docs)} документа обогатени")
        
        programs_found = sum(1 for doc in enhanced_docs if doc.get('extracted_programs'))
        print(f"  📊 Документи с програми: {programs_found}/{len(enhanced_docs)}")
        
        # 2. Rerank с Groq
        reranked_docs = reranker.stage3_reranking(query, enhanced_docs)
        print(f"✅ Groq reranking: {len(reranked_docs)} документа преранжирани")
        
        # 3. Покажи финални резултати
        print(f"\n📊 ФИНАЛНИ РЕЗУЛТАТИ:")
        for i, doc in enumerate(reranked_docs, 1):
            title = doc['title']
            similarity = doc.get('similarity_score', 0)
            groq_score = doc.get('groq_score', doc.get('fallback_score', 0))
            programs = doc.get('extracted_programs', [])
            
            print(f"  {i}. {title}")
            print(f"     Similarity: {similarity:.3f} | Groq: {groq_score:.3f}")
            if programs:
                program_names = [p.get('name', p) for p in programs[:2]]
                print(f"     Програми: {', '.join(program_names)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка в интеграцията: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 Започвам Phase 1 Critical Improvements тестове...")
    
    success = True
    
    # Тествай всички компоненти
    if not test_groq_reranker():
        success = False
    
    if not test_program_extractor():
        success = False
    
    if not test_multi_step_rag():
        success = False
    
    if not test_integration():
        success = False
    
    if success:
        print(f"\n🎉 Всички Phase 1 тестове завършени успешно!")
        print(f"✅ Groq Reranker: Работи с fallback логика")
        print(f"✅ Program Name Extractor: Извлича програмни имена с висока точност")
        print(f"✅ Multi-Step RAG: Интегриран в основната система")
        print(f"✅ Integration: Компонентите работят заедно")
    else:
        print(f"\n⚠️ Някои тестове имат проблеми - вижте детайлите по-горе")
