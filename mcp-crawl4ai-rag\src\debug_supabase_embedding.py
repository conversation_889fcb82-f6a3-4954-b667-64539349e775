#!/usr/bin/env python3
"""
Debug script за проверка на Supabase embedding формат
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

def test_supabase_embedding():
    """Тестване на Supabase embedding формат"""
    
    print("=== SUPABASE EMBEDDING DEBUG ===")
    
    # Зареждане на environment variables
    load_dotenv()
    
    # Supabase конфигурация
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not url or not key:
        print("❌ Липсват Supabase credentials")
        return
    
    # Създаване на клиент
    supabase: Client = create_client(url, key)
    
    try:
        # Вземане на един запис
        response = supabase.table('crawled_pages').select(
            'id, embedding'
        ).not_.is_('embedding', 'null').limit(1).execute()
        
        if not response.data:
            print("❌ Няма записи с embeddings")
            return
        
        record = response.data[0]
        embedding = record.get('embedding')
        
        print(f"Record ID: {record.get('id')}")
        print(f"Embedding type: {type(embedding)}")
        print(f"Embedding length: {len(embedding) if embedding else 'None'}")
        
        if embedding:
            print(f"First 50 chars: {embedding[:50]}")
            print(f"Last 50 chars: {embedding[-50:]}")

            # Проверка дали е list
            if isinstance(embedding, list):
                print("✅ Embedding е list")
                print(f"Реална размерност: {len(embedding)}")
            else:
                print(f"⚠️ Embedding не е list: {type(embedding)}")

                # Опит за JSON парсиране
                try:
                    import json
                    embedding_list = json.loads(embedding)
                    print(f"✅ JSON парсиран успешно: {len(embedding_list)} елемента")
                    print(f"Първи 5 стойности: {embedding_list[:5]}")
                    print(f"Последни 5 стойности: {embedding_list[-5:]}")
                except Exception as e:
                    print(f"❌ Грешка при JSON парсиране: {e}")

                    # Опит за конверсия към list
                    try:
                        embedding_list = list(embedding)
                        print(f"Конвертиран към list: {len(embedding_list)}")
                    except Exception as e:
                        print(f"❌ Грешка при конверсия: {e}")
        
        # Тест с повече записи
        print("\n=== MULTIPLE RECORDS TEST ===")
        response = supabase.table('crawled_pages').select(
            'id, embedding'
        ).not_.is_('embedding', 'null').limit(5).execute()
        
        dimension_counts = {}
        for record in response.data:
            embedding = record.get('embedding')
            if embedding:
                dim = len(embedding)
                dimension_counts[dim] = dimension_counts.get(dim, 0) + 1
                print(f"Record {record.get('id')}: {dim} dimensions")
        
        print(f"\nРазпределение на размерности: {dimension_counts}")
        
    except Exception as e:
        print(f"❌ Грешка: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_supabase_embedding()
