#!/usr/bin/env python3
"""
Phase 8.1.2: Hybrid Scoring Algorithm Test
Test optimized weighted fusion algorithm combining BM25 (40%) + Dense vectors (60%)
"""

import asyncio
import json
import os
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_config_optimization():
    """Test that HybridSearchConfig uses optimized weights"""
    print("🧪 Testing HybridSearchConfig optimization...")
    
    try:
        from src.hybrid_search import HybridSearchConfig
        
        # Test default config
        config = HybridSearchConfig()
        
        # Verify external LLM recommendations are applied
        expected_semantic = 0.6  # 60% dense vectors
        expected_keyword = 0.4   # 40% BM25
        
        if abs(config.semantic_weight - expected_semantic) < 0.001:
            print(f"✅ Semantic weight optimized: {config.semantic_weight} (expected {expected_semantic})")
        else:
            print(f"❌ Semantic weight not optimized: {config.semantic_weight} (expected {expected_semantic})")
            return False
            
        if abs(config.keyword_weight - expected_keyword) < 0.001:
            print(f"✅ Keyword weight optimized: {config.keyword_weight} (expected {expected_keyword})")
        else:
            print(f"❌ Keyword weight not optimized: {config.keyword_weight} (expected {expected_keyword})")
            return False
        
        # Test thresholds are lowered for better recall
        if config.min_semantic_threshold <= 0.1:
            print(f"✅ Semantic threshold lowered: {config.min_semantic_threshold}")
        else:
            print(f"❌ Semantic threshold too high: {config.min_semantic_threshold}")
            return False
            
        if config.min_keyword_threshold <= 0.05:
            print(f"✅ Keyword threshold lowered: {config.min_keyword_threshold}")
        else:
            print(f"❌ Keyword threshold too high: {config.min_keyword_threshold}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        return False

def test_utils_optimization():
    """Test that utils.py uses optimized weights"""
    print("\n🧪 Testing utils.py optimization...")
    
    try:
        import inspect
        from src.utils import search_documents_with_text_hybrid
        
        # Get function signature
        sig = inspect.signature(search_documents_with_text_hybrid)
        
        # Check default values
        weight_dense_default = sig.parameters['weight_dense'].default
        weight_sparse_default = sig.parameters['weight_sparse'].default
        
        expected_dense = 0.6
        expected_sparse = 0.4
        
        if abs(weight_dense_default - expected_dense) < 0.001:
            print(f"✅ Utils dense weight optimized: {weight_dense_default} (expected {expected_dense})")
        else:
            print(f"❌ Utils dense weight not optimized: {weight_dense_default} (expected {expected_dense})")
            return False
            
        if abs(weight_sparse_default - expected_sparse) < 0.001:
            print(f"✅ Utils sparse weight optimized: {weight_sparse_default} (expected {expected_sparse})")
        else:
            print(f"❌ Utils sparse weight not optimized: {weight_sparse_default} (expected {expected_sparse})")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Utils test failed: {e}")
        return False

async def test_scoring_algorithm():
    """Test the actual scoring algorithm with real data"""
    print("\n🧪 Testing scoring algorithm with real data...")
    
    try:
        from src.hybrid_search import HybridSearchEngine, HybridSearchConfig
        from supabase import create_client
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        # Create client with optimized config
        supabase = create_client(url, key)
        config = HybridSearchConfig()  # Uses new optimized defaults
        
        # Initialize engine
        engine = HybridSearchEngine(supabase, config)
        
        # Test query
        query = "европейски фондове за малки предприятия"
        results = await engine.hybrid_search(query, limit=5)
        
        if not results:
            print("❌ No results returned")
            return False
        
        print(f"✅ Hybrid search returned {len(results)} results")
        
        # Analyze scoring
        for i, result in enumerate(results[:3]):
            semantic_score = result.semantic_score
            keyword_score = result.keyword_score
            hybrid_score = result.hybrid_score
            
            # Verify hybrid score calculation
            expected_hybrid = (semantic_score * 0.6) + (keyword_score * 0.4)
            
            if abs(hybrid_score - expected_hybrid) < 0.001:
                print(f"✅ Result {i+1}: Hybrid score correct ({hybrid_score:.3f})")
                print(f"   Semantic: {semantic_score:.3f} (60%), Keyword: {keyword_score:.3f} (40%)")
            else:
                print(f"❌ Result {i+1}: Hybrid score incorrect")
                print(f"   Expected: {expected_hybrid:.3f}, Got: {hybrid_score:.3f}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Scoring algorithm test failed: {e}")
        return False

async def test_performance_comparison():
    """Compare old vs new scoring weights"""
    print("\n🧪 Testing performance comparison...")
    
    try:
        from src.hybrid_search import HybridSearchEngine, HybridSearchConfig
        from supabase import create_client
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test query
        query = "програма за конкурентоспособност"
        
        # Old configuration (70% semantic, 30% keyword)
        old_config = HybridSearchConfig(semantic_weight=0.7, keyword_weight=0.3)
        old_engine = HybridSearchEngine(supabase, old_config)
        old_results = await old_engine.hybrid_search(query, limit=5)
        
        # New configuration (60% semantic, 40% keyword)
        new_config = HybridSearchConfig()  # Uses optimized defaults
        new_engine = HybridSearchEngine(supabase, new_config)
        new_results = await new_engine.hybrid_search(query, limit=5)
        
        print(f"✅ Old config (70/30): {len(old_results)} results")
        print(f"✅ New config (60/40): {len(new_results)} results")
        
        if old_results and new_results:
            old_avg_score = sum(r.hybrid_score for r in old_results) / len(old_results)
            new_avg_score = sum(r.hybrid_score for r in new_results) / len(new_results)
            
            print(f"📊 Average hybrid scores:")
            print(f"   Old (70/30): {old_avg_score:.3f}")
            print(f"   New (60/40): {new_avg_score:.3f}")
            
            # Check if keyword scores have more influence in new config
            if new_results[0].keyword_score > 0:
                old_keyword_influence = old_results[0].keyword_score * 0.3
                new_keyword_influence = new_results[0].keyword_score * 0.4
                
                if new_keyword_influence > old_keyword_influence:
                    print("✅ Keyword influence increased as expected")
                else:
                    print("⚠️ Keyword influence not increased (may be query-dependent)")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance comparison failed: {e}")
        return False

def main():
    """Run all Phase 8.1.2 tests"""
    print("🚀 Phase 8.1.2: Hybrid Scoring Algorithm Tests")
    print("=" * 60)
    
    # Test results
    results = []
    
    # Test 1: Config Optimization
    results.append(("Config Optimization", test_config_optimization()))
    
    # Test 2: Utils Optimization
    results.append(("Utils Optimization", test_utils_optimization()))
    
    # Test 3: Scoring Algorithm (async)
    async def run_scoring_test():
        return await test_scoring_algorithm()
    
    results.append(("Scoring Algorithm", asyncio.run(run_scoring_test())))
    
    # Test 4: Performance Comparison (async)
    async def run_performance_test():
        return await test_performance_comparison()
    
    results.append(("Performance Comparison", asyncio.run(run_performance_test())))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PHASE 8.1.2 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 8.1.2 tests PASSED! Scoring algorithm optimized!")
        print("📈 Ready for Phase 8.1.3: Supabase RPC Function Testing")
        return True
    else:
        print("⚠️ Some tests failed. Review issues before proceeding.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
