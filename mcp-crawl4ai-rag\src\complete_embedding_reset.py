#!/usr/bin/env python3
"""
Complete Embedding Reset - Пълно изчистване и regeneration
КРИТИЧНО: Пълно изчистване на всички embeddings и създаване на нови
"""

import asyncio
import time
import json
from typing import List, Dict, Any
from dotenv import load_dotenv
from utils import get_supabase_client, create_embedding, create_embeddings_batch

# Load environment variables
load_dotenv('../.env')

class CompleteEmbeddingReset:
    def __init__(self):
        self.supabase = get_supabase_client()
        self.processed_count = 0
        self.total_count = 0
        self.start_time = time.time()
    
    async def force_clear_all_embeddings(self):
        """ПРИНУДИТЕЛНО изчистване на ВСИЧКИ embeddings"""
        print("🧹 ПРИНУДИТЕЛНО ИЗЧИСТВАНЕ НА ВСИЧКИ EMBEDDINGS...")
        
        try:
            # Първо - проверка колко записи имат embeddings
            with_embeddings = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).not_.is_('embedding', 'null').execute()
            
            before_count = with_embeddings.count if hasattr(with_embeddings, 'count') else 0
            print(f"📊 Записи с embeddings преди изчистване: {before_count}")
            
            # Принудително изчистване на ВСИЧКИ embeddings
            response = self.supabase.table('crawled_pages').update({
                'embedding': None
            }).gte('id', 0).execute()  # Всички записи с id >= 0
            
            print(f"✅ Изпратена команда за изчистване на всички embeddings")
            
            # Проверка че са изчистени
            await asyncio.sleep(2)  # Изчакваме да се приложи промяната
            
            after_check = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).not_.is_('embedding', 'null').execute()
            
            after_count = after_check.count if hasattr(after_check, 'count') else 0
            print(f"📊 Записи с embeddings след изчистване: {after_count}")
            
            if after_count == 0:
                print("✅ ВСИЧКИ embeddings са успешно изчистени!")
                return True
            else:
                print(f"⚠️ Все още има {after_count} записа с embeddings")
                return False
                
        except Exception as e:
            print(f"❌ Грешка при принудително изчистване: {e}")
            return False
    
    async def verify_clean_state(self):
        """Проверка че базата е напълно изчистена"""
        print("\n🔍 ПРОВЕРКА НА ИЗЧИСТЕНО СЪСТОЯНИЕ...")
        
        try:
            # Проверка на sample записи
            sample_response = self.supabase.table('crawled_pages').select(
                'id, embedding'
            ).limit(10).execute()
            
            if sample_response.data:
                embeddings_found = 0
                for record in sample_response.data:
                    if record.get('embedding') is not None:
                        embeddings_found += 1
                        print(f"   ⚠️ Запис {record['id']} все още има embedding!")
                
                if embeddings_found == 0:
                    print("✅ Всички sample записи са изчистени")
                    return True
                else:
                    print(f"❌ {embeddings_found} записа все още имат embeddings")
                    return False
            else:
                print("❌ Няма данни в таблицата")
                return False
                
        except Exception as e:
            print(f"❌ Грешка при проверка: {e}")
            return False
    
    async def get_total_records_count(self):
        """Получаване на общия брой записи"""
        try:
            count_response = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).execute()
            self.total_count = count_response.count if hasattr(count_response, 'count') else 0
            print(f"📊 Общо записи за обработка: {self.total_count}")
            return self.total_count > 0
        except Exception as e:
            print(f"❌ Грешка при получаване на брой записи: {e}")
            return False
    
    async def regenerate_all_embeddings_optimized(self, batch_size: int = 10):
        """Оптимизиран regeneration на всички embeddings"""
        print(f"\n🔄 ОПТИМИЗИРАН REGENERATION НА ВСИЧКИ EMBEDDINGS...")
        print(f"📦 Batch размер: {batch_size}")
        print(f"📊 Общо записи: {self.total_count}")
        
        offset = 0
        total_processed = 0
        batch_number = 1
        
        while offset < self.total_count:
            try:
                print(f"\n📦 Batch {batch_number} (записи {offset+1}-{min(offset+batch_size, self.total_count)})...")
                
                # Вземане на batch записи
                response = self.supabase.table('crawled_pages').select(
                    'id, content, url'
                ).range(offset, offset + batch_size - 1).execute()
                
                if not response.data:
                    print("   ⚠️ Няма повече записи")
                    break
                
                batch_data = response.data
                print(f"   📄 Получени {len(batch_data)} записа")
                
                # Подготовка на текстове за embedding
                texts = []
                record_ids = []
                
                for record in batch_data:
                    content = record.get('content', '')
                    if content and content.strip():
                        # Ограничаваме текста до 1500 символа за по-бързо обработване
                        clean_content = content.strip()[:1500]
                        texts.append(clean_content)
                        record_ids.append(record['id'])
                    else:
                        print(f"      ⚠️ Празно съдържание за запис {record['id']}")
                
                if not texts:
                    print("   ⚠️ Няма валидни текстове в този batch")
                    offset += batch_size
                    batch_number += 1
                    continue
                
                # Създаване на embeddings
                print(f"   🧠 Създаване на {len(texts)} embeddings...")
                start_time = time.time()
                
                try:
                    embeddings = create_embeddings_batch(texts)
                    embedding_time = time.time() - start_time
                    print(f"   ✅ Създадени embeddings за {embedding_time:.2f}s")
                    
                    # Проверка на размерности
                    if embeddings and len(embeddings) > 0:
                        first_dim = len(embeddings[0])
                        print(f"   📏 Embedding размерност: {first_dim}")
                        
                        if first_dim != 1024:
                            print(f"   ⚠️ ВНИМАНИЕ: Неочаквана размерност {first_dim} вместо 1024!")
                    
                except Exception as e:
                    print(f"   ❌ Грешка при създаване на embeddings: {e}")
                    offset += batch_size
                    batch_number += 1
                    continue
                
                # Обновяване на записите в базата
                print(f"   💾 Обновяване на {len(embeddings)} записа...")
                update_start = time.time()
                updated_count = 0
                
                for i, (record_id, embedding) in enumerate(zip(record_ids, embeddings)):
                    try:
                        self.supabase.table('crawled_pages').update({
                            'embedding': embedding
                        }).eq('id', record_id).execute()
                        
                        updated_count += 1
                        total_processed += 1
                        
                    except Exception as e:
                        print(f"      ❌ Грешка при обновяване на запис {record_id}: {e}")
                
                update_time = time.time() - update_start
                print(f"   ✅ Обновени {updated_count}/{len(embeddings)} записа за {update_time:.2f}s")
                
                # Прогрес статистики
                progress = (total_processed / self.total_count) * 100
                elapsed = time.time() - self.start_time
                rate = total_processed / elapsed if elapsed > 0 else 0
                eta = (self.total_count - total_processed) / rate if rate > 0 else 0
                
                print(f"   📊 Прогрес: {total_processed}/{self.total_count} ({progress:.1f}%)")
                print(f"   ⚡ Скорост: {rate:.1f} записа/сек")
                print(f"   ⏱️ ETA: {eta/60:.1f} минути")
                
                offset += batch_size
                batch_number += 1
                
                # Малка пауза между batch-овете
                await asyncio.sleep(0.3)
                
            except Exception as e:
                print(f"❌ Грешка при обработка на batch {batch_number}: {e}")
                offset += batch_size
                batch_number += 1
                continue
        
        print(f"\n✅ REGENERATION ЗАВЪРШЕН!")
        print(f"📊 Общо обработени записи: {total_processed}")
        return total_processed
    
    async def final_verification(self):
        """Финална проверка на резултатите"""
        print("\n🔍 ФИНАЛНА ПРОВЕРКА НА РЕЗУЛТАТИТЕ...")
        
        try:
            # Проверка на общия брой записи с embeddings
            with_embeddings = self.supabase.table('crawled_pages').select(
                'id', count='exact'
            ).not_.is_('embedding', 'null').execute()
            
            embeddings_count = with_embeddings.count if hasattr(with_embeddings, 'count') else 0
            print(f"📊 Записи с embeddings: {embeddings_count}/{self.total_count}")
            
            # Проверка на размерности
            sample_response = self.supabase.table('crawled_pages').select(
                'id, embedding'
            ).not_.is_('embedding', 'null').limit(10).execute()
            
            if sample_response.data:
                dimensions = []
                for record in sample_response.data:
                    if record.get('embedding'):
                        dimensions.append(len(record['embedding']))
                
                print(f"📏 Sample размерности: {dimensions}")
                
                if all(d == 1024 for d in dimensions):
                    print("✅ Всички embeddings имат правилна размерност (1024)")
                    consistent = True
                else:
                    print("❌ Има embeddings с неправилна размерност")
                    consistent = False
            else:
                print("❌ Няма embeddings за проверка")
                consistent = False
            
            # Тест на търсене
            if consistent:
                print("\n🧪 Тест на търсене...")
                test_query = "Програми за иновации в предприятията"
                test_embedding = create_embedding(test_query)
                
                search_start = time.time()
                response = self.supabase.rpc('match_crawled_pages_v4_debug', {
                    'p_query_embedding': test_embedding,
                    'p_match_count': 5,
                    'p_min_similarity_threshold': 0.1,
                    'p_weight_similarity': 1.0,
                    'p_weight_program_name': 0.0,
                    'p_weight_year': 0.0
                }).execute()
                search_time = time.time() - search_start
                
                if response.data:
                    results_count = len(response.data)
                    similarities = [r.get('similarity_score', 0) for r in response.data]
                    avg_similarity = sum(similarities) / results_count if results_count > 0 else 0
                    
                    print(f"   ⏱️ Време: {search_time:.3f}s")
                    print(f"   📊 Резултати: {results_count}")
                    print(f"   🎯 Средна similarity: {avg_similarity:.3f}")
                    print(f"   📈 Similarity range: {min(similarities):.3f} - {max(similarities):.3f}")
                    
                    if avg_similarity > 0.3:
                        print("✅ Търсенето работи добре!")
                        return True
                    else:
                        print("⚠️ Ниска similarity - възможен проблем")
                        return False
                else:
                    print("❌ Търсенето не връща резултати")
                    return False
            else:
                return False
                
        except Exception as e:
            print(f"❌ Грешка при финална проверка: {e}")
            return False

async def main():
    """Главна функция за complete reset"""
    print("🚨 ЗАПОЧВАНЕ НА COMPLETE EMBEDDING RESET")
    print("=" * 60)
    print("⚠️ ВНИМАНИЕ: Това ще изчисти ВСИЧКИ embeddings и ще ги създаде наново!")
    print("=" * 60)
    
    reset = CompleteEmbeddingReset()
    
    # Стъпка 1: Принудително изчистване на всички embeddings
    if not await reset.force_clear_all_embeddings():
        print("❌ Не мога да изчистя embeddings")
        return
    
    # Стъпка 2: Проверка на изчистено състояние
    if not await reset.verify_clean_state():
        print("❌ Базата не е напълно изчистена")
        return
    
    # Стъпка 3: Получаване на общия брой записи
    if not await reset.get_total_records_count():
        print("❌ Не мога да получа брой записи")
        return
    
    # Стъпка 4: Regeneration на всички embeddings
    processed_count = await reset.regenerate_all_embeddings_optimized(batch_size=8)
    
    if processed_count == 0:
        print("❌ Не са обработени записи")
        return
    
    # Стъпка 5: Финална проверка
    success = await reset.final_verification()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ COMPLETE RESET УСПЕШЕН!")
    else:
        print("⚠️ COMPLETE RESET ЗАВЪРШЕН С ПРОБЛЕМИ")
    print("=" * 60)
    
    print(f"\n📋 РЕЗУЛТАТИ:")
    print(f"• Обработени записи: {processed_count}")
    print(f"• Embedding размерност: 1024")
    print(f"• Статус: {'Успешен' if success else 'С проблеми'}")

if __name__ == "__main__":
    asyncio.run(main())
