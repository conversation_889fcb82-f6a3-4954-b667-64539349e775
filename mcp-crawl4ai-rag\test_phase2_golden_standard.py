#!/usr/bin/env python3
"""
🚀 ФАЗА 2.6 ТЕСТ: Golden Standard Testing с Metadata Enhancement
Тестваме подобрението в Golden Standard точността след metadata integration
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils import get_supabase_client
from openai import AsyncOpenAI

# Golden Standard тестови случаи
GOLDEN_STANDARD_TESTS = [
    {
        "query": "процедура за финансиране на проекти за околна среда",
        "expected_urls": [
            "https://eufunds.bg/node/opos/1/2024/bg",
            "https://eufunds.bg/procedure/environment",
            "https://eufunds.bg/node/procedure"
        ]
    },
    {
        "query": "програма за развитие на човешките ресурси 2021-2027",
        "expected_urls": [
            "https://eufunds.bg/program/human-resources",
            "https://eufunds.bg/node/program/hr",
            "https://eufunds.bg/prhr"
        ]
    },
    {
        "query": "европейски фондове за иновации и технологии",
        "expected_urls": [
            "https://eufunds.bg/innovation",
            "https://eufunds.bg/node/innovation",
            "https://eufunds.bg/technology-funding"
        ]
    },
    {
        "query": "процедури за кандидатстване по оперативни програми",
        "expected_urls": [
            "https://eufunds.bg/procedures",
            "https://eufunds.bg/node/procedures",
            "https://eufunds.bg/application-procedures"
        ]
    },
    {
        "query": "финансиране на проекти за транспортна инфраструктура",
        "expected_urls": [
            "https://eufunds.bg/transport",
            "https://eufunds.bg/node/transport",
            "https://eufunds.bg/infrastructure-funding"
        ]
    }
]

async def test_golden_standard_with_metadata():
    """Тестваме Golden Standard точността с новата metadata-enhanced функция"""
    print("🚀 ФАЗА 2.6 ТЕСТ: Golden Standard Testing с Metadata Enhancement")
    print("=" * 80)
    
    # Инициализираме клиенти
    print("📋 Инициализираме клиенти...")
    supabase = get_supabase_client()
    openai_client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    total_tests = len(GOLDEN_STANDARD_TESTS)
    successful_tests = 0
    
    print(f"🎯 Стартираме {total_tests} Golden Standard теста...")
    print("-" * 80)
    
    for i, test_case in enumerate(GOLDEN_STANDARD_TESTS, 1):
        query = test_case["query"]
        expected_urls = test_case["expected_urls"]
        
        print(f"\n🔍 ТЕСТ {i}/{total_tests}: '{query}'")
        print(f"   🎯 Очаквани URLs: {len(expected_urls)}")
        
        try:
            # Генерираме embedding
            embedding_response = await openai_client.embeddings.create(
                model="text-embedding-3-large",
                input=query,
                dimensions=1024
            )
            query_embedding = embedding_response.data[0].embedding
            
            # Тестваме с metadata-enhanced функция
            result = supabase.rpc('match_crawled_pages_v5_metadata', {
                'p_query_embedding': query_embedding,
                'p_weight_similarity': 0.3,
                'p_weight_program_name': 0.2,
                'p_weight_year': 0.1,
                'p_weight_metadata': 0.4,  # Високо тегло за метаданните
                'p_match_count': 10
            }).execute()
            
            if result.data:
                found_urls = [page['url'] for page in result.data[:3]]  # Топ 3 резултата
                matches = [url for url in found_urls if any(expected in url for expected in expected_urls)]
                
                if matches:
                    successful_tests += 1
                    print(f"   ✅ УСПЕХ! Намерени {len(matches)} съвпадения в топ 3")
                    for match in matches:
                        print(f"      🎯 Намерен: {match}")
                else:
                    print(f"   ❌ НЕУСПЕХ! Няма съвпадения в топ 3")
                
                print(f"   📊 Топ 3 резултата:")
                for j, page in enumerate(result.data[:3], 1):
                    metadata = page.get('metadata') or {}
                    doc_type = metadata.get('document_type', 'N/A')
                    quality = metadata.get('quality_score', 'N/A')
                    print(f"      #{j} (Score: {page['final_score']:.3f}) Type: {doc_type} Quality: {quality}")
                    print(f"          {page['url']}")
            else:
                print(f"   ❌ НЕУСПЕХ! Няма намерени резултати")
                
        except Exception as e:
            print(f"   ❌ ГРЕШКА: {e}")
    
    # Изчисляваме точността
    accuracy = (successful_tests / total_tests) * 100
    
    print(f"\n📈 ФИНАЛНИ РЕЗУЛТАТИ")
    print("=" * 80)
    print(f"✅ Успешни тестове: {successful_tests}/{total_tests}")
    print(f"📊 Golden Standard точност: {accuracy:.1f}%")
    
    # Сравняваме с предишните резултати
    baseline_accuracy = 10.0  # Първоначална точност
    phase1_accuracy = 20.0    # След Phase 1 trafilatura
    improvement_from_baseline = ((accuracy - baseline_accuracy) / baseline_accuracy) * 100
    improvement_from_phase1 = ((accuracy - phase1_accuracy) / phase1_accuracy) * 100
    
    print(f"\n🚀 ПОДОБРЕНИЯ:")
    print(f"   📈 От baseline ({baseline_accuracy}%): +{improvement_from_baseline:.0f}%")
    print(f"   📈 От Phase 1 ({phase1_accuracy}%): +{improvement_from_phase1:.0f}%")
    
    if accuracy >= 30:
        print(f"🎉 ОТЛИЧНО! Постигнахме {accuracy:.1f}% точност!")
    elif accuracy >= 25:
        print(f"✅ ДОБРЕ! Постигнахме {accuracy:.1f}% точност!")
    else:
        print(f"⚠️ Нужни са още подобрения. Текуща точност: {accuracy:.1f}%")
    
    print("🚀 ФАЗА 2.6 завършена!")

if __name__ == "__main__":
    asyncio.run(test_golden_standard_with_metadata())
