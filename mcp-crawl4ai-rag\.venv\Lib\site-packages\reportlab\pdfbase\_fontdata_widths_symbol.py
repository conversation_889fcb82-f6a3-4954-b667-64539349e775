widths = {'Alpha': 722,
 'Beta': 667,
 'Chi': 722,
 'Delta': 612,
 'Epsilon': 611,
 'Eta': 722,
 'Euro': 750,
 'Gamma': 603,
 'Ifraktur': 686,
 'Iota': 333,
 '<PERSON>': 722,
 '<PERSON><PERSON>': 686,
 'Mu': 889,
 'Nu': 722,
 'Omega': 768,
 'Omicron': 722,
 'Phi': 763,
 'Pi': 768,
 'Psi': 795,
 'Rfraktur': 795,
 'Rho': 556,
 'Sigma': 592,
 'Tau': 611,
 'Theta': 741,
 'Upsilon': 690,
 'Upsilon1': 620,
 'Xi': 645,
 'Zeta': 611,
 'aleph': 823,
 'alpha': 631,
 'ampersand': 778,
 'angle': 768,
 'angleleft': 329,
 'angleright': 329,
 'apple': 790,
 'approxequal': 549,
 'arrowboth': 1042,
 'arrowdblboth': 1042,
 'arrowdbldown': 603,
 'arrowdblleft': 987,
 'arrowdblright': 987,
 'arrowdblup': 603,
 'arrowdown': 603,
 'arrowhorizex': 1000,
 'arrowleft': 987,
 'arrowright': 987,
 'arrowup': 603,
 'arrowvertex': 603,
 'asteriskmath': 500,
 'bar': 200,
 'beta': 549,
 'braceex': 494,
 'braceleft': 480,
 'braceleftbt': 494,
 'braceleftmid': 494,
 'bracelefttp': 494,
 'braceright': 480,
 'bracerightbt': 494,
 'bracerightmid': 494,
 'bracerighttp': 494,
 'bracketleft': 333,
 'bracketleftbt': 384,
 'bracketleftex': 384,
 'bracketlefttp': 384,
 'bracketright': 333,
 'bracketrightbt': 384,
 'bracketrightex': 384,
 'bracketrighttp': 384,
 'bullet': 460,
 'carriagereturn': 658,
 'chi': 549,
 'circlemultiply': 768,
 'circleplus': 768,
 'club': 753,
 'colon': 278,
 'comma': 250,
 'congruent': 549,
 'copyrightsans': 790,
 'copyrightserif': 790,
 'degree': 400,
 'delta': 494,
 'diamond': 753,
 'divide': 549,
 'dotmath': 250,
 'eight': 500,
 'element': 713,
 'ellipsis': 1000,
 'emptyset': 823,
 'epsilon': 439,
 'equal': 549,
 'equivalence': 549,
 'eta': 603,
 'exclam': 333,
 'existential': 549,
 'five': 500,
 'florin': 500,
 'four': 500,
 'fraction': 167,
 'gamma': 411,
 'gradient': 713,
 'greater': 549,
 'greaterequal': 549,
 'heart': 753,
 'infinity': 713,
 'integral': 274,
 'integralbt': 686,
 'integralex': 686,
 'integraltp': 686,
 'intersection': 768,
 'iota': 329,
 'kappa': 549,
 'lambda': 549,
 'less': 549,
 'lessequal': 549,
 'logicaland': 603,
 'logicalnot': 713,
 'logicalor': 603,
 'lozenge': 494,
 'minus': 549,
 'minute': 247,
 'mu': 576,
 'multiply': 549,
 'nine': 500,
 'notelement': 713,
 'notequal': 549,
 'notsubset': 713,
 'nu': 521,
 'numbersign': 500,
 'omega': 686,
 'omega1': 713,
 'omicron': 549,
 'one': 500,
 'parenleft': 333,
 'parenleftbt': 384,
 'parenleftex': 384,
 'parenlefttp': 384,
 'parenright': 333,
 'parenrightbt': 384,
 'parenrightex': 384,
 'parenrighttp': 384,
 'partialdiff': 494,
 'percent': 833,
 'period': 250,
 'perpendicular': 658,
 'phi': 521,
 'phi1': 603,
 'pi': 549,
 'plus': 549,
 'plusminus': 549,
 'product': 823,
 'propersubset': 713,
 'propersuperset': 713,
 'proportional': 713,
 'psi': 686,
 'question': 444,
 'radical': 549,
 'radicalex': 500,
 'reflexsubset': 713,
 'reflexsuperset': 713,
 'registersans': 790,
 'registerserif': 790,
 'rho': 549,
 'second': 411,
 'semicolon': 278,
 'seven': 500,
 'sigma': 603,
 'sigma1': 439,
 'similar': 549,
 'six': 500,
 'slash': 278,
 'space': 250,
 'spade': 753,
 'suchthat': 439,
 'summation': 713,
 'tau': 439,
 'therefore': 863,
 'theta': 521,
 'theta1': 631,
 'three': 500,
 'trademarksans': 786,
 'trademarkserif': 890,
 'two': 500,
 'underscore': 500,
 'union': 768,
 'universal': 713,
 'upsilon': 576,
 'weierstrass': 987,
 'xi': 493,
 'zero': 500,
 'zeta': 494}
