"""
Groq API Reranker с Fallback Логика
Заменя липсващия OpenAI client за Stage 3 reranking
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import time

# Fallback imports
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("⚠️ requests не е наличен - използвам fallback логика")

@dataclass
class GroqConfig:
    """Конфигурация за Groq API"""
    api_key: Optional[str] = None
    model: str = "llama3-8b-8192"  # Безплатен tier
    base_url: str = "https://api.groq.com/openai/v1"
    max_tokens: int = 1000
    temperature: float = 0.1
    timeout: int = 30

class GroqReranker:
    """
    Groq API Reranker с интелигентен fallback
    Работи с или без API ключ
    """
    
    def __init__(self, config: Optional[GroqConfig] = None):
        self.config = config or GroqConfig()
        self.logger = logging.getLogger(__name__)
        
        # Опитай да намериш API ключ
        self.api_key = (
            self.config.api_key or 
            os.getenv('GROQ_API_KEY') or 
            os.getenv('GROQ_KEY')
        )
        
        self.has_api_access = bool(self.api_key and REQUESTS_AVAILABLE)
        
        if self.has_api_access:
            self.logger.info("✅ Groq API достъп активен")
        else:
            self.logger.info("⚠️ Groq API недостъпен - използвам fallback логика")
    
    def stage3_reranking(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Stage 3 Intelligent Reranking
        Използва Groq API или fallback логика
        """
        if self.has_api_access:
            try:
                return self._groq_api_reranking(query, documents)
            except Exception as e:
                self.logger.warning(f"Groq API грешка: {e} - преминавам към fallback")
                return self._fallback_reranking(query, documents)
        else:
            return self._fallback_reranking(query, documents)
    
    def _groq_api_reranking(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Groq API базирано reranking"""
        
        # Подготви документите за анализ
        docs_text = self._prepare_documents_for_llm(documents)
        
        prompt = f"""
Ти си експерт по европейски фондове в България. Анализирай и ранжирай документите по релевантност към заявката.

ЗАЯВКА: {query}

ДОКУМЕНТИ:
{docs_text}

ЗАДАЧА:
1. Оцени всеки документ по релевантност (1-10)
2. Ранжирай ги по важност
3. Обясни защо всеки документ е релевантен

ОТГОВОР В JSON ФОРМАТ:
{{
    "rankings": [
        {{"doc_id": 0, "score": 9.5, "reason": "Директно отговаря на въпроса за..."}},
        {{"doc_id": 1, "score": 7.2, "reason": "Съдържа релевантна информация за..."}}
    ]
}}
"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.config.model,
            "messages": [
                {
                    "role": "system",
                    "content": "Ти си експерт по европейски фондове. Отговаряй точно и структурирано на български език."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens
        }
        
        response = requests.post(
            f"{self.config.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=self.config.timeout
        )
        
        if response.status_code == 200:
            result = response.json()
            llm_response = result['choices'][0]['message']['content']
            return self._parse_groq_response(llm_response, documents)
        else:
            raise Exception(f"Groq API грешка: {response.status_code} - {response.text}")
    
    def _fallback_reranking(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Fallback reranking без API
        Използва rule-based логика и keyword matching
        """
        self.logger.info("🔄 Използвам fallback reranking логика")
        
        # Извлечи ключови думи от заявката
        query_keywords = self._extract_keywords(query.lower())
        
        # Оцени всеки документ
        scored_docs = []
        for i, doc in enumerate(documents):
            score = self._calculate_fallback_score(query_keywords, doc)
            
            scored_doc = doc.copy()
            scored_doc['groq_fallback_score'] = score
            scored_doc['groq_reasoning'] = self._generate_fallback_reasoning(query_keywords, doc, score)
            
            scored_docs.append((score, scored_doc))
        
        # Сортирай по score (descending)
        scored_docs.sort(key=lambda x: x[0], reverse=True)
        
        return [doc for score, doc in scored_docs]
    
    def _extract_keywords(self, query: str) -> List[str]:
        """Извлича ключови думи от заявката"""
        # Премахни стоп думи
        stop_words = {
            'кои', 'са', 'какви', 'как', 'къде', 'кога', 'защо', 'за', 'на', 'в', 'от', 'до', 'с', 'по',
            'и', 'или', 'но', 'че', 'да', 'се', 'ще', 'би', 'може', 'трябва', 'има', 'няма',
            'този', 'тази', 'това', 'тези', 'един', 'една', 'едно', 'някой', 'някоя', 'нещо'
        }
        
        words = query.split()
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords
    
    def _calculate_fallback_score(self, query_keywords: List[str], doc: Dict[str, Any]) -> float:
        """Изчислява fallback score за документ"""
        content = doc.get('content', '').lower()
        title = doc.get('title', '').lower()
        url = doc.get('url', '').lower()
        
        score = 0.0
        
        # Keyword matching в съдържанието (40% тегло)
        content_matches = sum(1 for keyword in query_keywords if keyword in content)
        if query_keywords:
            score += (content_matches / len(query_keywords)) * 0.4
        
        # Keyword matching в заглавието (30% тегло)
        title_matches = sum(1 for keyword in query_keywords if keyword in title)
        if query_keywords:
            score += (title_matches / len(query_keywords)) * 0.3
        
        # URL релевантност (10% тегло)
        url_matches = sum(1 for keyword in query_keywords if keyword in url)
        if query_keywords:
            score += (url_matches / len(query_keywords)) * 0.1
        
        # Специални бонуси за важни термини (20% тегло)
        important_terms = {
            'програма': 0.05, 'фонд': 0.05, 'финансиране': 0.04,
            'проект': 0.03, 'кандидатстване': 0.03, 'мярка': 0.03,
            'interreg': 0.04, 'horizon': 0.04, 'опрр': 0.04, 'оптти': 0.04
        }
        
        for term, bonus in important_terms.items():
            if term in content:
                score += bonus
        
        return min(score, 1.0)  # Ограничи до максимум 1.0
    
    def _generate_fallback_reasoning(self, query_keywords: List[str], doc: Dict[str, Any], score: float) -> str:
        """Генерира обяснение за fallback scoring"""
        content = doc.get('content', '').lower()
        title = doc.get('title', '').lower()
        
        found_keywords = [kw for kw in query_keywords if kw in content or kw in title]
        
        if score > 0.7:
            relevance = "Високо релевантен"
        elif score > 0.4:
            relevance = "Средно релевантен"
        else:
            relevance = "Ниско релевантен"
        
        reasoning = f"{relevance} документ (score: {score:.2f}). "
        
        if found_keywords:
            reasoning += f"Съдържа ключови думи: {', '.join(found_keywords)}. "
        
        if 'програма' in content:
            reasoning += "Съдържа информация за програми. "
        
        if any(term in content for term in ['финансиране', 'фонд', 'средства']):
            reasoning += "Съдържа финансова информация. "
        
        return reasoning
    
    def _prepare_documents_for_llm(self, documents: List[Dict[str, Any]]) -> str:
        """Подготвя документите за LLM анализ"""
        docs_text = ""
        for i, doc in enumerate(documents[:5]):  # Ограничи до 5 документа за API
            title = doc.get('title', 'Без заглавие')
            content = doc.get('content', '')[:500]  # Ограничи съдържанието
            url = doc.get('url', '')
            
            docs_text += f"""
ДОКУМЕНТ {i}:
Заглавие: {title}
URL: {url}
Съдържание: {content}...
---
"""
        return docs_text
    
    def _parse_groq_response(self, response: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Парсва отговора от Groq API"""
        try:
            # Опитай да извлечеш JSON от отговора
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                parsed = json.loads(json_str)
                
                if 'rankings' in parsed:
                    # Приложи ранжирането
                    ranked_docs = []
                    for ranking in parsed['rankings']:
                        doc_id = ranking.get('doc_id', 0)
                        if 0 <= doc_id < len(documents):
                            doc = documents[doc_id].copy()
                            doc['groq_score'] = ranking.get('score', 5.0)
                            doc['groq_reasoning'] = ranking.get('reason', 'Няма обяснение')
                            ranked_docs.append(doc)
                    
                    # Добави останалите документи
                    used_ids = {r.get('doc_id', 0) for r in parsed['rankings']}
                    for i, doc in enumerate(documents):
                        if i not in used_ids:
                            doc_copy = doc.copy()
                            doc_copy['groq_score'] = 3.0  # Средна оценка
                            doc_copy['groq_reasoning'] = 'Не е анализиран от LLM'
                            ranked_docs.append(doc_copy)
                    
                    return ranked_docs
            
        except Exception as e:
            self.logger.warning(f"Грешка при парсване на Groq отговор: {e}")
        
        # Fallback - върни оригиналните документи
        return documents
    
    def analyze_query_complexity(self, query: str) -> Dict[str, Any]:
        """Анализира сложността на заявката"""
        if self.has_api_access:
            try:
                return self._groq_query_analysis(query)
            except Exception as e:
                self.logger.warning(f"Groq query analysis грешка: {e}")
                return self._fallback_query_analysis(query)
        else:
            return self._fallback_query_analysis(query)
    
    def _fallback_query_analysis(self, query: str) -> Dict[str, Any]:
        """Fallback анализ на заявката"""
        words = query.split()
        
        complexity_score = 1.0
        
        # Дължина на заявката
        if len(words) > 10:
            complexity_score += 1.0
        elif len(words) > 5:
            complexity_score += 0.5
        
        # Специални термини
        complex_terms = ['interreg', 'horizon', 'кандидатстване', 'финансиране', 'критерии']
        if any(term in query.lower() for term in complex_terms):
            complexity_score += 1.0
        
        # Въпросителни думи
        question_words = ['кои', 'какви', 'как', 'къде', 'кога', 'защо']
        if any(word in query.lower() for word in question_words):
            complexity_score += 0.5
        
        return {
            'complexity_score': min(complexity_score, 5.0),
            'word_count': len(words),
            'has_complex_terms': any(term in query.lower() for term in complex_terms),
            'analysis_method': 'fallback'
        }
    
    def _groq_query_analysis(self, query: str) -> Dict[str, Any]:
        """Groq API анализ на заявката"""
        prompt = f"""
Анализирай сложността на тази заявка за българска RAG система:
"{query}"

Оцени по скала 1-5:
- Семантична сложност
- Необходимост от контекст  
- Специфичност на термините

Отговори в JSON формат:
{{
    "complexity_score": 3.5,
    "semantic_complexity": 4,
    "context_need": 3,
    "term_specificity": 4,
    "reasoning": "Обяснение..."
}}
"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.config.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.1,
            "max_tokens": 300
        }
        
        response = requests.post(
            f"{self.config.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            llm_response = result['choices'][0]['message']['content']
            
            try:
                json_start = llm_response.find('{')
                json_end = llm_response.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    return json.loads(llm_response[json_start:json_end])
            except:
                pass
        
        # Fallback при грешка
        return self._fallback_query_analysis(query)


# Utility функции
def create_groq_reranker(api_key: Optional[str] = None) -> GroqReranker:
    """Factory функция за създаване на Groq reranker"""
    config = GroqConfig(api_key=api_key)
    return GroqReranker(config)


def test_groq_reranker():
    """Тест функция за Groq reranker"""
    reranker = create_groq_reranker()
    
    # Тестови документи
    test_docs = [
        {
            "title": "Програма за развитие на регионите",
            "content": "ОПРР 2021-2027 финансира проекти за регионално развитие",
            "url": "https://eufunds.bg/oprd",
            "similarity_score": 0.8
        },
        {
            "title": "Interreg програма",
            "content": "Програма Interreg VI-A IPA България Северна Македония 2021-2027",
            "url": "https://eufunds.bg/interreg",
            "similarity_score": 0.7
        }
    ]
    
    # Тест заявка
    query = "Кои са програмите за развитие на регионите?"
    
    # Тест reranking
    print("🧪 Тестване на Groq reranker...")
    ranked_docs = reranker.stage3_reranking(query, test_docs)
    
    print(f"📊 Резултати за заявка: '{query}'")
    for i, doc in enumerate(ranked_docs):
        score = doc.get('groq_score', doc.get('groq_fallback_score', 'N/A'))
        reasoning = doc.get('groq_reasoning', 'Няма обяснение')
        print(f"{i+1}. {doc['title']} (Score: {score})")
        print(f"   Обяснение: {reasoning}")
        print()
    
    # Тест query analysis
    analysis = reranker.analyze_query_complexity(query)
    print(f"🔍 Анализ на заявката: {analysis}")


if __name__ == "__main__":
    test_groq_reranker()
