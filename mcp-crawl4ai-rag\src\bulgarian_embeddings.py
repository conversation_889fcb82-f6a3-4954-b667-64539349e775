#!/usr/bin/env python3
"""
Phase 8.3: Bulgarian-specific Embeddings
Implementing LaBSE and multilingual models optimized for Bulgarian content
"""

import logging
import time
import asyncio
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from sentence_transformers import SentenceTransformer
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class BulgarianEmbeddingConfig:
    """Configuration for Bulgarian-specific embedding models"""
    
    # Model options (in order of preference for Bulgarian)
    labse_model: str = "sentence-transformers/LaBSE"  # Best for Bulgarian
    multilingual_model: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    fallback_model: str = "BAAI/bge-large-en-v1.5"  # Current model
    
    # Performance settings
    max_length: int = 512
    batch_size: int = 16
    normalize_embeddings: bool = True
    
    # Bulgarian-specific optimizations
    bulgarian_boost: float = 1.1  # Boost for Bulgarian content detection
    cyrillic_weight: float = 1.05  # Extra weight for Cyrillic text

class BulgarianEmbeddingEngine:
    """Advanced embedding engine optimized for Bulgarian content"""
    
    def __init__(self, config: Optional[BulgarianEmbeddingConfig] = None):
        self.config = config or BulgarianEmbeddingConfig()
        self.model = None
        self.model_name = None
        self.embedding_dimension = None
        
        # Model loading priority
        self.model_priority = [
            (self.config.labse_model, "LaBSE (Best for Bulgarian)"),
            (self.config.multilingual_model, "Multilingual MiniLM"),
            (self.config.fallback_model, "BGE Fallback")
        ]
    
    def initialize_model(self) -> bool:
        """Initialize the best available Bulgarian embedding model"""
        logger.info("🇧🇬 Initializing Bulgarian-specific embedding model...")
        
        for model_name, description in self.model_priority:
            try:
                logger.info(f"🔄 Attempting to load {description}: {model_name}")
                start_time = time.time()
                
                self.model = SentenceTransformer(model_name)
                self.model_name = model_name
                self.embedding_dimension = self.model.get_sentence_embedding_dimension()
                
                load_time = time.time() - start_time
                logger.info(f"✅ {description} loaded successfully!")
                logger.info(f"📊 Model: {model_name}")
                logger.info(f"📏 Embedding dimension: {self.embedding_dimension}")
                logger.info(f"⏱️ Load time: {load_time:.2f}s")
                
                return True
                
            except Exception as e:
                logger.warning(f"⚠️ Failed to load {description}: {e}")
                continue
        
        logger.error("❌ Failed to load any Bulgarian embedding model")
        return False
    
    def is_bulgarian_text(self, text: str) -> bool:
        """Detect if text contains Bulgarian/Cyrillic content"""
        if not text:
            return False
        
        # Count Cyrillic characters
        cyrillic_chars = sum(1 for char in text if '\u0400' <= char <= '\u04FF')
        total_chars = len([c for c in text if c.isalpha()])
        
        if total_chars == 0:
            return False
        
        cyrillic_ratio = cyrillic_chars / total_chars
        return cyrillic_ratio > 0.3  # 30% threshold for Bulgarian detection
    
    def preprocess_bulgarian_text(self, text: str) -> str:
        """Preprocess text for better Bulgarian embedding quality"""
        if not text:
            return text
        
        # Basic Bulgarian text normalization
        text = text.strip()
        
        # Remove excessive whitespace
        import re
        text = re.sub(r'\s+', ' ', text)
        
        # Ensure proper encoding for Bulgarian characters
        try:
            text = text.encode('utf-8').decode('utf-8')
        except:
            pass
        
        return text
    
    def create_embeddings(self, texts: Union[str, List[str]]) -> np.ndarray:
        """Create embeddings with Bulgarian-specific optimizations"""
        if not self.model:
            raise RuntimeError("Model not initialized. Call initialize_model() first.")
        
        # Handle single text input
        if isinstance(texts, str):
            texts = [texts]
        
        if not texts:
            return np.array([])
        
        logger.info(f"🔄 Creating embeddings for {len(texts)} texts using {self.model_name}")
        
        # Preprocess texts
        processed_texts = []
        bulgarian_flags = []
        
        for text in texts:
            processed_text = self.preprocess_bulgarian_text(text)
            is_bulgarian = self.is_bulgarian_text(processed_text)
            
            processed_texts.append(processed_text)
            bulgarian_flags.append(is_bulgarian)
        
        bulgarian_count = sum(bulgarian_flags)
        logger.info(f"🇧🇬 Detected {bulgarian_count}/{len(texts)} Bulgarian texts")
        
        try:
            start_time = time.time()
            
            # Create embeddings
            embeddings = self.model.encode(
                processed_texts,
                batch_size=self.config.batch_size,
                normalize_embeddings=self.config.normalize_embeddings,
                show_progress_bar=len(processed_texts) > 10
            )
            
            # Apply Bulgarian-specific boosts
            if bulgarian_count > 0:
                for i, is_bulgarian in enumerate(bulgarian_flags):
                    if is_bulgarian:
                        # Apply Bulgarian boost
                        embeddings[i] *= self.config.bulgarian_boost
                        
                        # Normalize after boost
                        if self.config.normalize_embeddings:
                            norm = np.linalg.norm(embeddings[i])
                            if norm > 0:
                                embeddings[i] /= norm
            
            embedding_time = time.time() - start_time
            logger.info(f"✅ Created {len(embeddings)} embeddings in {embedding_time:.3f}s")
            logger.info(f"📏 Embedding shape: {embeddings.shape}")
            
            return embeddings
            
        except Exception as e:
            logger.error(f"❌ Failed to create embeddings: {e}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            "model_name": self.model_name,
            "embedding_dimension": self.embedding_dimension,
            "is_bulgarian_optimized": "LaBSE" in (self.model_name or ""),
            "is_multilingual": "multilingual" in (self.model_name or "").lower(),
            "config": {
                "max_length": self.config.max_length,
                "batch_size": self.config.batch_size,
                "normalize_embeddings": self.config.normalize_embeddings,
                "bulgarian_boost": self.config.bulgarian_boost,
                "cyrillic_weight": self.config.cyrillic_weight
            }
        }

# Global instance for easy access
_bulgarian_embedding_engine = None

def get_bulgarian_embedding_engine() -> BulgarianEmbeddingEngine:
    """Get or create the global Bulgarian embedding engine"""
    global _bulgarian_embedding_engine
    
    if _bulgarian_embedding_engine is None:
        _bulgarian_embedding_engine = BulgarianEmbeddingEngine()
        if not _bulgarian_embedding_engine.initialize_model():
            raise RuntimeError("Failed to initialize Bulgarian embedding engine")
    
    return _bulgarian_embedding_engine

async def create_bulgarian_embeddings(texts: Union[str, List[str]]) -> np.ndarray:
    """Async wrapper for creating Bulgarian-optimized embeddings"""
    engine = get_bulgarian_embedding_engine()
    
    # Run in thread pool for non-blocking operation
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(None, engine.create_embeddings, texts)

def test_bulgarian_embeddings():
    """Test function for Bulgarian embeddings"""
    print("🧪 Testing Bulgarian Embeddings...")
    
    # Test texts (Bulgarian and English)
    test_texts = [
        "европейски фондове за иновации и развитие",
        "програми за малки и средни предприятия в България",
        "European funding programs for innovation",
        "финансиране на научни изследвания и развойна дейност",
        "digital transformation initiatives"
    ]
    
    try:
        engine = BulgarianEmbeddingEngine()
        
        if not engine.initialize_model():
            print("❌ Failed to initialize model")
            return False
        
        print(f"✅ Model loaded: {engine.model_name}")
        print(f"📏 Embedding dimension: {engine.embedding_dimension}")
        
        # Test embeddings
        embeddings = engine.create_embeddings(test_texts)
        
        print(f"📊 Created embeddings shape: {embeddings.shape}")
        
        # Test Bulgarian detection
        for i, text in enumerate(test_texts):
            is_bg = engine.is_bulgarian_text(text)
            print(f"🇧🇬 '{text[:30]}...' -> Bulgarian: {is_bg}")
        
        print("✅ Bulgarian embeddings test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Bulgarian embeddings test failed: {e}")
        return False

if __name__ == "__main__":
    test_bulgarian_embeddings()
