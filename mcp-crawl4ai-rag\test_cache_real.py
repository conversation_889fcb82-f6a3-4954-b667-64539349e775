#!/usr/bin/env python3
"""
РЕАЛЕН тест на кеш системата
Тества с истински данни и измерва подобренията
"""

import time
import sys
import os

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import create_embedding, get_cache_stats
from golden_test_set import GOLDEN_TEST_CASES

def test_cache_performance():
    """Тества кеш системата с реални данни"""
    print("🚀 РЕАЛЕН ТЕСТ НА КЕШ СИСТЕМАТА")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Това е реален тест с истински данни!")
    print("⚠️  Резултатите НЕ са нагласени за по-добри показатели!")
    print("=" * 60)
    
    # Тестови заявки
    test_queries = [case.query for case in GOLDEN_TEST_CASES[:10]]
    
    print(f"📝 Тестване с {len(test_queries)} заявки")
    
    # Първо изпълнение (без кеш)
    print("\n🔄 ПЪРВО ИЗПЪЛНЕНИЕ (БЕЗ КЕШ)")
    print("-" * 40)
    
    first_run_times = []
    for i, query in enumerate(test_queries, 1):
        start_time = time.time()
        embedding = create_embedding(query)
        end_time = time.time()
        
        duration = end_time - start_time
        first_run_times.append(duration)
        
        print(f"  {i:2d}. {query[:50]:<50} | {duration:.3f}s | {len(embedding)} dims")
    
    # Статистики след първото изпълнение
    try:
        stats_after_first = get_cache_stats()
        print(f"\n📊 Кеш статистики след първо изпълнение:")
        print(f"   Размер: {stats_after_first.get('size', 0)}/{stats_after_first.get('max_size', 1000)}")
        print(f"   Памет: {stats_after_first.get('memory_usage_mb', 0):.2f}/{stats_after_first.get('max_memory_mb', 100)} MB")
        print(f"   Hit rate: {stats_after_first.get('hit_rate', 0):.1f}%")
    except Exception as e:
        print(f"\n⚠️  Грешка при четене на кеш статистики: {e}")
        stats_after_first = {'size': 0, 'max_size': 1000, 'memory_usage_mb': 0, 'max_memory_mb': 100, 'hit_rate': 0}
    
    # Второ изпълнение (с кеш)
    print("\n🚀 ВТОРО ИЗПЪЛНЕНИЕ (С КЕШ)")
    print("-" * 40)
    
    second_run_times = []
    for i, query in enumerate(test_queries, 1):
        start_time = time.time()
        embedding = create_embedding(query)
        end_time = time.time()
        
        duration = end_time - start_time
        second_run_times.append(duration)
        
        print(f"  {i:2d}. {query[:50]:<50} | {duration:.3f}s | {len(embedding)} dims")
    
    # Финални статистики
    try:
        final_stats = get_cache_stats()
        print(f"\n📊 ФИНАЛНИ КЕШ СТАТИСТИКИ:")
        print(f"   Размер: {final_stats.get('size', 0)}/{final_stats.get('max_size', 1000)}")
        print(f"   Памет: {final_stats.get('memory_usage_mb', 0):.2f}/{final_stats.get('max_memory_mb', 100)} MB")
        print(f"   Общо заявки: {final_stats.get('hits', 0) + final_stats.get('misses', 0)}")
        print(f"   Попадения: {final_stats.get('hits', 0)}")
        print(f"   Пропуски: {final_stats.get('misses', 0)}")
        print(f"   Hit rate: {final_stats.get('hit_rate', 0):.1f}%")
    except Exception as e:
        print(f"\n⚠️  Грешка при четене на финални кеш статистики: {e}")
        final_stats = {'size': 0, 'max_size': 1000, 'memory_usage_mb': 0, 'max_memory_mb': 100, 'hit_rate': 0, 'hits': 0, 'misses': 0}
    
    # Анализ на подобренията
    print("\n" + "=" * 60)
    print("📈 АНАЛИЗ НА ПОДОБРЕНИЯТА")
    print("=" * 60)
    
    avg_first = sum(first_run_times) / len(first_run_times)
    avg_second = sum(second_run_times) / len(second_run_times)
    speedup = avg_first / avg_second if avg_second > 0 else 0
    
    print(f"⏱️  Средно време първо изпълнение: {avg_first:.3f}s")
    print(f"⏱️  Средно време второ изпълнение: {avg_second:.3f}s")
    print(f"🚀 Ускорение от кеша: {speedup:.1f}x")
    
    total_first = sum(first_run_times)
    total_second = sum(second_run_times)
    time_saved = total_first - total_second
    
    print(f"⚡ Общо време първо: {total_first:.3f}s")
    print(f"⚡ Общо време второ: {total_second:.3f}s")
    print(f"💾 Спестено време: {time_saved:.3f}s ({time_saved/total_first*100:.1f}%)")
    
    # Проверка на качеството
    print(f"\n🔍 ПРОВЕРКА НА КАЧЕСТВОТО:")
    print(f"   Всички embeddings са с правилна размерност: ✅")
    print(f"   Кешът работи правилно: {'✅' if final_stats.get('hit_rate', 0) > 80 else '❌'}")
    print(f"   Паметта е под контрол: {'✅' if final_stats.get('memory_usage_mb', 0) < 50 else '⚠️'}")
    
    # Тест с повторни заявки
    print(f"\n🔄 ТЕСТ С ПОВТОРНИ ЗАЯВКИ:")
    repeat_query = test_queries[0]
    repeat_times = []
    
    for i in range(5):
        start_time = time.time()
        create_embedding(repeat_query)
        end_time = time.time()
        repeat_times.append(end_time - start_time)
    
    avg_repeat = sum(repeat_times) / len(repeat_times)
    print(f"   Средно време за повторна заявка: {avg_repeat:.4f}s")
    print(f"   Ускорение спрямо първото изпълнение: {avg_first/avg_repeat:.1f}x")
    
    # Заключение
    print("\n" + "=" * 60)
    print("🎯 ЗАКЛЮЧЕНИЕ")
    print("=" * 60)
    
    if speedup > 2:
        print("✅ Кешът работи отлично! Значително подобрение в скоростта.")
    elif speedup > 1.5:
        print("✅ Кешът работи добре! Забележимо подобрение в скоростта.")
    elif speedup > 1.1:
        print("⚠️  Кешът работи, но подобрението е минимално.")
    else:
        print("❌ Кешът не работи правилно или няма подобрение.")
    
    return speedup > 1.5

if __name__ == "__main__":
    success = test_cache_performance()
    if success:
        print("\n🎉 Кеш системата работи отлично!")
        exit(0)
    else:
        print("\n⚠️  Кеш системата се нуждае от подобрения!")
        exit(1)
