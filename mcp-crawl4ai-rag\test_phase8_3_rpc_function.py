#!/usr/bin/env python3
"""
Phase 8.1.3: Supabase RPC Function Testing
Test match_crawled_pages_hybrid RPC function with proper parameter handling
"""

import os
import json
from typing import List, Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_rpc_function_signature():
    """Test RPC function signature and parameter validation"""
    print("🧪 Testing RPC function signature...")
    
    try:
        from supabase import create_client
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test with all required parameters
        test_params = {
            'p_query_text': 'европейски фондове',
            'p_query_embedding': [0.1] * 1024,  # Valid embedding
            'p_match_count': 5,
            'p_weight_dense': 0.6,
            'p_weight_sparse': 0.4,
            'p_min_similarity_threshold': 0.1
        }
        
        result = supabase.rpc('match_crawled_pages_hybrid', test_params).execute()
        
        if result.data is not None:
            print(f"✅ RPC function callable with all parameters")
            print(f"✅ Returned {len(result.data)} results")
            return True
        else:
            print("❌ RPC function returned None")
            return False
            
    except Exception as e:
        print(f"❌ RPC signature test failed: {e}")
        return False

def test_parameter_validation():
    """Test parameter validation and error handling"""
    print("\n🧪 Testing parameter validation...")
    
    try:
        from supabase import create_client
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test 1: Missing required parameter
        try:
            result = supabase.rpc('match_crawled_pages_hybrid', {
                'p_query_text': 'test',
                # Missing p_query_embedding
                'p_match_count': 5,
                'p_weight_dense': 0.6,
                'p_weight_sparse': 0.4,
                'p_min_similarity_threshold': 0.1
            }).execute()
            print("❌ Should have failed with missing parameter")
            return False
        except Exception:
            print("✅ Correctly rejected missing parameter")
        
        # Test 2: Invalid weight values (should sum to 1.0)
        try:
            result = supabase.rpc('match_crawled_pages_hybrid', {
                'p_query_text': 'test',
                'p_query_embedding': [0.1] * 1024,
                'p_match_count': 5,
                'p_weight_dense': 0.8,  # 0.8 + 0.4 = 1.2 > 1.0
                'p_weight_sparse': 0.4,
                'p_min_similarity_threshold': 0.1
            }).execute()
            print("⚠️ RPC accepted weights that don't sum to 1.0 (may be intentional)")
        except Exception:
            print("✅ Correctly rejected invalid weight values")
        
        # Test 3: Invalid embedding dimension
        try:
            result = supabase.rpc('match_crawled_pages_hybrid', {
                'p_query_text': 'test',
                'p_query_embedding': [0.1] * 512,  # Wrong dimension
                'p_match_count': 5,
                'p_weight_dense': 0.6,
                'p_weight_sparse': 0.4,
                'p_min_similarity_threshold': 0.1
            }).execute()
            print("⚠️ RPC accepted wrong embedding dimension (may be flexible)")
        except Exception:
            print("✅ Correctly rejected wrong embedding dimension")
        
        return True
        
    except Exception as e:
        print(f"❌ Parameter validation test failed: {e}")
        return False

def test_hybrid_scoring_output():
    """Test that RPC function returns proper hybrid scoring fields"""
    print("\n🧪 Testing hybrid scoring output...")
    
    try:
        from supabase import create_client
        from src.utils import create_embedding
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Create real embedding for better test
        query_text = "програма за иновации и конкурентоспособност"
        query_embedding = create_embedding(query_text)
        
        if not query_embedding:
            print("❌ Failed to create embedding")
            return False
        
        # Call RPC with real data
        result = supabase.rpc('match_crawled_pages_hybrid', {
            'p_query_text': query_text,
            'p_query_embedding': query_embedding,
            'p_match_count': 3,
            'p_weight_dense': 0.6,
            'p_weight_sparse': 0.4,
            'p_min_similarity_threshold': 0.05
        }).execute()
        
        if not result.data:
            print("❌ No results returned")
            return False
        
        print(f"✅ RPC returned {len(result.data)} results")
        
        # Check required output fields
        required_fields = [
            'id', 'url', 'chunk_number', 'content', 'metadata',
            'dense_similarity', 'sparse_rank', 'hybrid_score', 'search_method'
        ]
        
        first_result = result.data[0]
        missing_fields = []
        
        for field in required_fields:
            if field not in first_result:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing output fields: {missing_fields}")
            return False
        
        print("✅ All required output fields present")
        
        # Verify hybrid score calculation
        for i, row in enumerate(result.data[:3]):
            dense_sim = row.get('dense_similarity', 0.0)
            sparse_rank = row.get('sparse_rank', 0.0)
            hybrid_score = row.get('hybrid_score', 0.0)
            
            # Expected hybrid score: (dense * 0.6) + (sparse * 0.4)
            expected_hybrid = (dense_sim * 0.6) + (sparse_rank * 0.4)
            
            print(f"📊 Result {i+1}:")
            print(f"   Dense: {dense_sim:.3f}, Sparse: {sparse_rank:.3f}")
            print(f"   Hybrid: {hybrid_score:.3f} (expected: {expected_hybrid:.3f})")
            
            # Allow small floating point differences
            if abs(hybrid_score - expected_hybrid) < 0.001:
                print(f"   ✅ Hybrid score calculation correct")
            else:
                print(f"   ⚠️ Hybrid score calculation may use different formula")
        
        return True
        
    except Exception as e:
        print(f"❌ Hybrid scoring output test failed: {e}")
        return False

def test_utils_integration():
    """Test integration with utils.py wrapper function"""
    print("\n🧪 Testing utils.py integration...")
    
    try:
        from supabase import create_client
        from src.utils import search_documents_with_text_hybrid
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test wrapper function
        query = "европейски структурни фондове"
        results = search_documents_with_text_hybrid(
            supabase, 
            query, 
            match_count=5,
            weight_dense=0.6,  # New optimized weights
            weight_sparse=0.4,
            min_similarity_threshold=0.1
        )
        
        if not results:
            print("❌ Utils wrapper returned no results")
            return False
        
        print(f"✅ Utils wrapper returned {len(results)} results")
        
        # Check result format
        first_result = results[0]
        expected_fields = ['id', 'url', 'content', 'metadata', 'similarity']
        
        missing_fields = []
        for field in expected_fields:
            if field not in first_result:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Utils wrapper missing fields: {missing_fields}")
            return False
        
        print("✅ Utils wrapper returns properly formatted results")
        
        # Test that results are sorted by relevance
        if len(results) > 1:
            scores = [r.get('similarity', 0) for r in results]
            if scores == sorted(scores, reverse=True):
                print("✅ Results properly sorted by relevance")
            else:
                print("⚠️ Results may not be sorted by relevance")
        
        return True
        
    except Exception as e:
        print(f"❌ Utils integration test failed: {e}")
        return False

def test_performance_benchmarks():
    """Test RPC function performance with different parameters"""
    print("\n🧪 Testing performance benchmarks...")
    
    try:
        import time
        from supabase import create_client
        from src.utils import create_embedding
        
        # Get credentials
        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_KEY")
        
        if not url or not key:
            print("❌ Supabase credentials not found")
            return False
        
        supabase = create_client(url, key)
        
        # Test query
        query_text = "програма за развитие на селските райони"
        query_embedding = create_embedding(query_text)
        
        if not query_embedding:
            print("❌ Failed to create embedding")
            return False
        
        # Test different match counts
        match_counts = [5, 10, 20]
        
        for match_count in match_counts:
            start_time = time.time()
            
            result = supabase.rpc('match_crawled_pages_hybrid', {
                'p_query_text': query_text,
                'p_query_embedding': query_embedding,
                'p_match_count': match_count,
                'p_weight_dense': 0.6,
                'p_weight_sparse': 0.4,
                'p_min_similarity_threshold': 0.05
            }).execute()
            
            end_time = time.time()
            duration = end_time - start_time
            
            result_count = len(result.data) if result.data else 0
            
            print(f"📊 Match count {match_count}: {result_count} results in {duration:.3f}s")
        
        print("✅ Performance benchmarks completed")
        return True
        
    except Exception as e:
        print(f"❌ Performance benchmark test failed: {e}")
        return False

def main():
    """Run all Phase 8.1.3 tests"""
    print("🚀 Phase 8.1.3: Supabase RPC Function Testing")
    print("=" * 60)
    
    # Test results
    results = []
    
    # Test 1: RPC Function Signature
    results.append(("RPC Function Signature", test_rpc_function_signature()))
    
    # Test 2: Parameter Validation
    results.append(("Parameter Validation", test_parameter_validation()))
    
    # Test 3: Hybrid Scoring Output
    results.append(("Hybrid Scoring Output", test_hybrid_scoring_output()))
    
    # Test 4: Utils Integration
    results.append(("Utils Integration", test_utils_integration()))
    
    # Test 5: Performance Benchmarks
    results.append(("Performance Benchmarks", test_performance_benchmarks()))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PHASE 8.1.3 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 8.1.3 tests PASSED! RPC function working correctly!")
        print("📈 Ready for Phase 8.1.4: Testing and Validation")
        return True
    else:
        print("⚠️ Some tests failed. Review issues before proceeding.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
