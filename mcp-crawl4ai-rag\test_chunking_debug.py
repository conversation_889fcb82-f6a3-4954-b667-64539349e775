#!/usr/bin/env python3
"""
ДЕБЪГ ТЕСТ НА СЕМАНТИЧНОТО РАЗДЕЛЯНЕ
Проследява стъпка по стъпка защо не се създават chunks
"""

import sys
import os

# Добавяме src директорията в path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_semantic_chunking():
    """Дебъгва семантичното разделяне стъпка по стъпка"""
    print("🔍 ДЕБЪГ НА СЕМАНТИЧНО РАЗДЕЛЯНЕ")
    print("=" * 60)
    
    # Тестов текст
    test_text = """Програмата за подкрепа на малките и средните предприятия (МСП) предоставя финансиране за иновативни проекти. Критериите за кандидатстване включват минимум 2 години дейност на предприятието."""
    
    print(f"📝 Тестов текст ({len(test_text)} символа):")
    print(f"   {test_text}")
    print()
    
    try:
        # Импортираме функцията
        from utils import create_semantic_chunks
        print("✅ Функцията create_semantic_chunks е импортирана")
        
        # Проверяваме входните параметри
        print(f"🔧 Параметри по подразбиране:")
        print(f"   max_tokens: 512")
        print(f"   min_tokens: 256") 
        print(f"   overlap_ratio: 0.1")
        print()
        
        # Ръчно симулираме логиката
        print("🧪 СИМУЛАЦИЯ НА ЛОГИКАТА:")
        
        # 1. Проверка на входа
        if not test_text or not isinstance(test_text, str):
            print("❌ Входният текст е невалиден")
            return False
        print("✅ Входният текст е валиден")
        
        # 2. Разделяне на изречения
        bulgarian_sentence_endings = ['.', '!', '?', ':', ';']
        sentences = []
        current_sentence = ""
        
        for char in test_text:
            current_sentence += char
            if char in bulgarian_sentence_endings:
                if current_sentence.strip():
                    sentences.append(current_sentence.strip())
                current_sentence = ""
        
        # Добавяме останалия текст
        if current_sentence.strip():
            sentences.append(current_sentence.strip())
        
        print(f"📄 Намерени изречения: {len(sentences)}")
        for i, sentence in enumerate(sentences):
            tokens = len(sentence.split())
            print(f"   {i+1}. ({tokens} tokens): {sentence}")
        print()
        
        if not sentences:
            print("❌ Няма намерени изречения")
            return False
        
        # 3. Създаване на chunks
        chunks = []
        current_chunk = ""
        current_tokens = 0
        max_tokens = 512
        min_tokens = 256
        overlap_ratio = 0.1
        overlap_tokens = int(max_tokens * overlap_ratio)
        
        print(f"🔨 СЪЗДАВАНЕ НА CHUNKS:")
        print(f"   max_tokens: {max_tokens}")
        print(f"   min_tokens: {min_tokens}")
        print(f"   overlap_tokens: {overlap_tokens}")
        print()
        
        for i, sentence in enumerate(sentences):
            sentence_tokens = len(sentence.split())
            print(f"   Обработвам изречение {i+1} ({sentence_tokens} tokens)")
            print(f"     Текущи tokens в chunk: {current_tokens}")
            print(f"     Ще стане: {current_tokens + sentence_tokens}")
            
            # Проверяваме дали да финализираме chunk
            if current_tokens + sentence_tokens > max_tokens and current_tokens >= min_tokens:
                print(f"     ✅ Финализирам chunk (надвишава max_tokens и >= min_tokens)")
                if current_chunk.strip():
                    chunks.append({
                        'content': current_chunk.strip(),
                        'tokens': current_tokens,
                        'chunk_id': len(chunks)
                    })
                    print(f"     📦 Създаден chunk {len(chunks)} с {current_tokens} tokens")
                
                # Започваме нов chunk
                current_chunk = sentence
                current_tokens = sentence_tokens
                print(f"     🆕 Нов chunk започнат с {sentence_tokens} tokens")
            else:
                # Добавяме към текущия chunk
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence
                current_tokens += sentence_tokens
                print(f"     ➕ Добавено към chunk (общо {current_tokens} tokens)")
            print()
        
        # Финален chunk
        print(f"🏁 ФИНАЛЕН CHUNK:")
        print(f"   Текущи tokens: {current_tokens}")
        print(f"   min_tokens: {min_tokens}")
        print(f"   Условие: current_tokens >= min_tokens = {current_tokens >= min_tokens}")
        
        if current_chunk.strip() and current_tokens >= min_tokens:
            chunks.append({
                'content': current_chunk.strip(),
                'tokens': current_tokens,
                'chunk_id': len(chunks)
            })
            print(f"   ✅ Финален chunk създаден с {current_tokens} tokens")
        else:
            print(f"   ❌ Финален chunk НЕ е създаден (твърде малък)")
        
        print(f"\n📊 РЕЗУЛТАТ:")
        print(f"   Създадени chunks: {len(chunks)}")
        
        for chunk in chunks:
            print(f"   Chunk {chunk['chunk_id'] + 1}: {chunk['tokens']} tokens")
            print(f"     Съдържание: {chunk['content'][:100]}...")
        
        # Сега тестваме реалната функция
        print(f"\n🧪 ТЕСТ НА РЕАЛНАТА ФУНКЦИЯ:")
        real_chunks = create_semantic_chunks(test_text)
        print(f"   Реални chunks: {len(real_chunks)}")
        
        if len(real_chunks) != len(chunks):
            print(f"   ⚠️  Разлика! Очаквани: {len(chunks)}, Реални: {len(real_chunks)}")
        else:
            print(f"   ✅ Съвпадение!")
        
        return len(real_chunks) > 0
        
    except Exception as e:
        print(f"💥 ГРЕШКА: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_params():
    """Тества с различни параметри"""
    print("\n🔧 ТЕСТ С РАЗЛИЧНИ ПАРАМЕТРИ")
    print("-" * 40)
    
    try:
        from utils import create_semantic_chunks
        
        test_text = "Първо изречение. Второ изречение! Трето изречение? Четвърто изречение."
        
        test_cases = [
            (50, 10, "Много малки chunks"),
            (100, 20, "Малки chunks"),
            (200, 50, "Средни chunks"),
            (1000, 100, "Големи chunks")
        ]
        
        for max_tokens, min_tokens, description in test_cases:
            print(f"\n   {description} (max: {max_tokens}, min: {min_tokens}):")
            chunks = create_semantic_chunks(test_text, max_tokens=max_tokens, min_tokens=min_tokens)
            print(f"     Chunks: {len(chunks)}")
            
            if chunks:
                for i, chunk in enumerate(chunks):
                    tokens = chunk.get('tokens', 0)
                    print(f"       Chunk {i+1}: {tokens} tokens")
        
        return True
        
    except Exception as e:
        print(f"❌ Грешка: {e}")
        return False

def main():
    """Главна функция"""
    print("🚀 ДЕБЪГ ТЕСТ НА СЕМАНТИЧНО РАЗДЕЛЯНЕ")
    print("=" * 60)
    
    # Основен дебъг
    basic_success = debug_semantic_chunking()
    
    # Тест с различни параметри
    param_success = test_with_different_params()
    
    # Заключение
    print(f"\n{'='*60}")
    print("🎯 ЗАКЛЮЧЕНИЕ")
    print(f"{'='*60}")
    
    if basic_success:
        print("✅ Семантичното разделяне работи!")
    else:
        print("❌ Семантичното разделяне НЕ работи!")
    
    return basic_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
