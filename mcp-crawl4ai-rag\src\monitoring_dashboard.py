"""
🚀 ФАЗА 5.4: REAL-TIME MONITORING DASHBOARD
Система за мониторинг на производителността и свежестта на данните в реално време

Автор: Augment Agent (Автономно развитие)
Дата: 2025-07-04
Цел: Създаване на мониторинг за системна производителност и качество на данните
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import time
import statistics
from collections import defaultdict, deque

from .utils import get_supabase_client
from openai import AsyncOpenAI
import os

# Настройка на логинг
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Метрика за производителност"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    category: str  # "crawling", "rag", "database", "system"
    status: str    # "good", "warning", "critical"
    threshold_warning: float = None
    threshold_critical: float = None

@dataclass
class DataFreshnessMetric:
    """Метрика за свежест на данните"""
    table_name: str
    last_update: datetime
    record_count: int
    freshness_hours: float
    status: str  # "fresh", "stale", "outdated"
    recommended_action: str

@dataclass
class SystemHealthReport:
    """Доклад за здравето на системата"""
    timestamp: datetime
    overall_status: str  # "healthy", "degraded", "critical"
    performance_metrics: List[PerformanceMetric]
    data_freshness: List[DataFreshnessMetric]
    alerts: List[str]
    recommendations: List[str]
    uptime_hours: float

class RealTimeMonitor:
    """
    📊 Real-Time Monitoring Dashboard за MCP сървъра
    
    Мониторира:
    - Производителност на RAG заявки
    - Свежест на данните в Supabase
    - Системни метрики (памет, CPU, мрежа)
    - Качество на crawling операции
    - Алерти и препоръки
    """
    
    def __init__(self, supabase_client=None, openai_client=None):
        self.supabase_client = supabase_client or get_supabase_client()
        
        # Initialize OpenAI async client
        if openai_client:
            self.openai_client = openai_client
        else:
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            self.openai_client = AsyncOpenAI(api_key=api_key)
        
        # Метрики в памет (последните 1000 записа)
        self.performance_history = deque(maxlen=1000)
        self.rag_query_times = deque(maxlen=100)
        self.crawling_success_rates = deque(maxlen=50)
        self.database_response_times = deque(maxlen=100)
        
        # Стартово време
        self.start_time = datetime.now()
        
        # Прагове за алерти
        self.thresholds = {
            "rag_query_time_warning": 5.0,      # секунди
            "rag_query_time_critical": 10.0,    # секунди
            "data_freshness_warning": 24,       # часове
            "data_freshness_critical": 72,      # часове
            "crawling_success_warning": 0.8,    # 80%
            "crawling_success_critical": 0.6,   # 60%
            "database_response_warning": 2.0,   # секунди
            "database_response_critical": 5.0   # секунди
        }

    async def record_rag_query_performance(self, query_time: float, success: bool, query_type: str = "standard"):
        """📈 Записва производителност на RAG заявка"""
        try:
            self.rag_query_times.append(query_time)
            
            status = "good"
            if query_time > self.thresholds["rag_query_time_critical"]:
                status = "critical"
            elif query_time > self.thresholds["rag_query_time_warning"]:
                status = "warning"
            
            metric = PerformanceMetric(
                name=f"RAG Query Time ({query_type})",
                value=query_time,
                unit="seconds",
                timestamp=datetime.now(),
                category="rag",
                status=status,
                threshold_warning=self.thresholds["rag_query_time_warning"],
                threshold_critical=self.thresholds["rag_query_time_critical"]
            )
            
            self.performance_history.append(metric)
            logger.info(f"📈 RAG query performance recorded: {query_time:.2f}s ({status})")
            
        except Exception as e:
            logger.error(f"Error recording RAG performance: {e}")

    async def record_crawling_performance(self, success_rate: float, pages_crawled: int, errors: int):
        """🕷️ Записва производителност на crawling"""
        try:
            self.crawling_success_rates.append(success_rate)
            
            status = "good"
            if success_rate < self.thresholds["crawling_success_critical"]:
                status = "critical"
            elif success_rate < self.thresholds["crawling_success_warning"]:
                status = "warning"
            
            metric = PerformanceMetric(
                name="Crawling Success Rate",
                value=success_rate,
                unit="percentage",
                timestamp=datetime.now(),
                category="crawling",
                status=status,
                threshold_warning=self.thresholds["crawling_success_warning"],
                threshold_critical=self.thresholds["crawling_success_critical"]
            )
            
            self.performance_history.append(metric)
            logger.info(f"🕷️ Crawling performance recorded: {success_rate:.1%} success rate")
            
        except Exception as e:
            logger.error(f"Error recording crawling performance: {e}")

    async def record_database_performance(self, response_time: float, operation_type: str):
        """💾 Записва производителност на базата данни"""
        try:
            self.database_response_times.append(response_time)
            
            status = "good"
            if response_time > self.thresholds["database_response_critical"]:
                status = "critical"
            elif response_time > self.thresholds["database_response_warning"]:
                status = "warning"
            
            metric = PerformanceMetric(
                name=f"Database Response Time ({operation_type})",
                value=response_time,
                unit="seconds",
                timestamp=datetime.now(),
                category="database",
                status=status,
                threshold_warning=self.thresholds["database_response_warning"],
                threshold_critical=self.thresholds["database_response_critical"]
            )
            
            self.performance_history.append(metric)
            
        except Exception as e:
            logger.error(f"Error recording database performance: {e}")

    async def check_data_freshness(self) -> List[DataFreshnessMetric]:
        """🔍 Проверява свежестта на данните в Supabase"""
        freshness_metrics = []
        
        try:
            # Проверка на основните таблици
            tables_to_check = [
                "crawled_pages_v4",
                "crawled_chunks_v4", 
                "program_gazetteer"
            ]
            
            for table in tables_to_check:
                try:
                    # Получаване на последна актуализация
                    result = self.supabase_client.table(table).select("created_at").order("created_at", desc=True).limit(1).execute()
                    
                    if result.data:
                        last_update = datetime.fromisoformat(result.data[0]["created_at"].replace('Z', '+00:00'))
                        freshness_hours = (datetime.now(last_update.tzinfo) - last_update).total_seconds() / 3600
                    else:
                        last_update = datetime.min
                        freshness_hours = float('inf')
                    
                    # Брой записи
                    count_result = self.supabase_client.table(table).select("id", count="exact").execute()
                    record_count = count_result.count if count_result.count else 0
                    
                    # Определяне на статус
                    if freshness_hours < self.thresholds["data_freshness_warning"]:
                        status = "fresh"
                        recommended_action = "Няма нужда от действие"
                    elif freshness_hours < self.thresholds["data_freshness_critical"]:
                        status = "stale"
                        recommended_action = "Препоръчва се обновяване на данните"
                    else:
                        status = "outdated"
                        recommended_action = "Спешно обновяване на данните необходимо"
                    
                    freshness_metric = DataFreshnessMetric(
                        table_name=table,
                        last_update=last_update,
                        record_count=record_count,
                        freshness_hours=freshness_hours,
                        status=status,
                        recommended_action=recommended_action
                    )
                    
                    freshness_metrics.append(freshness_metric)
                    
                except Exception as e:
                    logger.error(f"Error checking freshness for table {table}: {e}")
                    
                    # Добавяне на error metric
                    freshness_metric = DataFreshnessMetric(
                        table_name=table,
                        last_update=datetime.min,
                        record_count=0,
                        freshness_hours=float('inf'),
                        status="error",
                        recommended_action=f"Грешка при проверка: {str(e)}"
                    )
                    freshness_metrics.append(freshness_metric)
            
            return freshness_metrics
            
        except Exception as e:
            logger.error(f"Error in data freshness check: {e}")
            return []

    async def generate_system_health_report(self) -> SystemHealthReport:
        """📋 Генерира доклад за здравето на системата"""
        try:
            # Събиране на текущи метрики
            current_metrics = []
            alerts = []
            recommendations = []
            
            # RAG Performance
            if self.rag_query_times:
                avg_rag_time = statistics.mean(self.rag_query_times)
                status = "good"
                if avg_rag_time > self.thresholds["rag_query_time_critical"]:
                    status = "critical"
                    alerts.append(f"RAG заявки са много бавни: {avg_rag_time:.2f}s средно")
                    recommendations.append("Оптимизирайте RAG алгоритъма или увеличете ресурсите")
                elif avg_rag_time > self.thresholds["rag_query_time_warning"]:
                    status = "warning"
                    alerts.append(f"RAG заявки са бавни: {avg_rag_time:.2f}s средно")
                    recommendations.append("Разгледайте възможности за оптимизация")
                
                current_metrics.append(PerformanceMetric(
                    name="Average RAG Query Time",
                    value=avg_rag_time,
                    unit="seconds",
                    timestamp=datetime.now(),
                    category="rag",
                    status=status
                ))
            
            # Crawling Performance
            if self.crawling_success_rates:
                avg_success_rate = statistics.mean(self.crawling_success_rates)
                status = "good"
                if avg_success_rate < self.thresholds["crawling_success_critical"]:
                    status = "critical"
                    alerts.append(f"Crawling успеваемост е критично ниска: {avg_success_rate:.1%}")
                    recommendations.append("Проверете мрежовата свързаност и crawling конфигурацията")
                elif avg_success_rate < self.thresholds["crawling_success_warning"]:
                    status = "warning"
                    alerts.append(f"Crawling успеваемост е ниска: {avg_success_rate:.1%}")
                    recommendations.append("Мониторирайте crawling грешките")
                
                current_metrics.append(PerformanceMetric(
                    name="Average Crawling Success Rate",
                    value=avg_success_rate,
                    unit="percentage",
                    timestamp=datetime.now(),
                    category="crawling",
                    status=status
                ))
            
            # Database Performance
            if self.database_response_times:
                avg_db_time = statistics.mean(self.database_response_times)
                status = "good"
                if avg_db_time > self.thresholds["database_response_critical"]:
                    status = "critical"
                    alerts.append(f"База данни отговаря бавно: {avg_db_time:.2f}s средно")
                    recommendations.append("Оптимизирайте заявките или увеличете ресурсите на базата")
                elif avg_db_time > self.thresholds["database_response_warning"]:
                    status = "warning"
                    alerts.append(f"База данни отговаря по-бавно: {avg_db_time:.2f}s средно")
                
                current_metrics.append(PerformanceMetric(
                    name="Average Database Response Time",
                    value=avg_db_time,
                    unit="seconds",
                    timestamp=datetime.now(),
                    category="database",
                    status=status
                ))
            
            # Data Freshness
            freshness_metrics = await self.check_data_freshness()
            for freshness in freshness_metrics:
                if freshness.status == "outdated":
                    alerts.append(f"Таблица {freshness.table_name} е остаряла ({freshness.freshness_hours:.1f}h)")
                    recommendations.append(f"Обновете данните в {freshness.table_name}")
                elif freshness.status == "stale":
                    recommendations.append(f"Планирайте обновяване на {freshness.table_name}")
            
            # Общ статус
            critical_count = sum(1 for m in current_metrics if m.status == "critical")
            warning_count = sum(1 for m in current_metrics if m.status == "warning")
            
            if critical_count > 0:
                overall_status = "critical"
            elif warning_count > 0:
                overall_status = "degraded"
            else:
                overall_status = "healthy"
            
            # Uptime
            uptime_hours = (datetime.now() - self.start_time).total_seconds() / 3600
            
            return SystemHealthReport(
                timestamp=datetime.now(),
                overall_status=overall_status,
                performance_metrics=current_metrics,
                data_freshness=freshness_metrics,
                alerts=alerts,
                recommendations=recommendations,
                uptime_hours=uptime_hours
            )
            
        except Exception as e:
            logger.error(f"Error generating system health report: {e}")
            return SystemHealthReport(
                timestamp=datetime.now(),
                overall_status="error",
                performance_metrics=[],
                data_freshness=[],
                alerts=[f"Грешка при генериране на доклад: {str(e)}"],
                recommendations=["Проверете системните логове"],
                uptime_hours=0
            )

    async def get_performance_trends(self, hours: int = 24) -> Dict[str, Any]:
        """📈 Получава тенденции в производителността за последните часове"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Филтриране на метрики за периода
            recent_metrics = [
                m for m in self.performance_history 
                if m.timestamp >= cutoff_time
            ]
            
            # Групиране по категории
            trends = defaultdict(list)
            for metric in recent_metrics:
                trends[metric.category].append({
                    "timestamp": metric.timestamp.isoformat(),
                    "value": metric.value,
                    "status": metric.status
                })
            
            # Изчисляване на статистики
            stats = {}
            for category, values in trends.items():
                if values:
                    numeric_values = [v["value"] for v in values]
                    stats[category] = {
                        "count": len(values),
                        "average": statistics.mean(numeric_values),
                        "min": min(numeric_values),
                        "max": max(numeric_values),
                        "trend": "improving" if len(numeric_values) > 1 and numeric_values[-1] < numeric_values[0] else "stable"
                    }
            
            return {
                "period_hours": hours,
                "metrics_count": len(recent_metrics),
                "trends": dict(trends),
                "statistics": stats
            }
            
        except Exception as e:
            logger.error(f"Error getting performance trends: {e}")
            return {"error": str(e)}

# Utility функции за интеграция с MCP сървъра

# Глобален мониторинг инстанс
_global_monitor = None

def get_global_monitor() -> RealTimeMonitor:
    """Получава глобалния мониторинг инстанс"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = RealTimeMonitor()
    return _global_monitor

async def get_system_health_dashboard() -> Dict[str, Any]:
    """
    📊 Получава пълен dashboard за здравето на системата
    """
    try:
        monitor = get_global_monitor()
        health_report = await monitor.generate_system_health_report()
        performance_trends = await monitor.get_performance_trends(hours=24)
        
        # Конвертиране за JSON сериализация
        dashboard = {
            "timestamp": health_report.timestamp.isoformat(),
            "overall_status": health_report.overall_status,
            "uptime_hours": round(health_report.uptime_hours, 2),
            "alerts": health_report.alerts,
            "recommendations": health_report.recommendations,
            "performance_metrics": [
                {
                    "name": m.name,
                    "value": m.value,
                    "unit": m.unit,
                    "category": m.category,
                    "status": m.status,
                    "timestamp": m.timestamp.isoformat()
                }
                for m in health_report.performance_metrics
            ],
            "data_freshness": [
                {
                    "table_name": f.table_name,
                    "last_update": f.last_update.isoformat() if f.last_update != datetime.min else "never",
                    "record_count": f.record_count,
                    "freshness_hours": f.freshness_hours if f.freshness_hours != float('inf') else "unknown",
                    "status": f.status,
                    "recommended_action": f.recommended_action
                }
                for f in health_report.data_freshness
            ],
            "performance_trends": performance_trends
        }
        
        return {
            "success": True,
            "dashboard": dashboard
        }
        
    except Exception as e:
        logger.error(f"Error getting system health dashboard: {e}")
        return {
            "success": False,
            "error": str(e)
        }
