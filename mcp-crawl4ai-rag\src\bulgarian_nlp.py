"""
Bulgarian NLP Module с Fallback Логика
Заменя липсващия en_core_web_sm с български NLP възможности
"""

import re
import logging
from typing import List, Dict, Any, Set, Tuple, Optional
from dataclasses import dataclass
import unicodedata

# Опитай да заредиш spaCy
try:
    import spacy
    SPACY_AVAILABLE = True
    
    # Опитай да заредиш български модел
    try:
        nlp_bg = spacy.load("bg_core_news_sm")
        BG_MODEL_AVAILABLE = True
    except OSError:
        BG_MODEL_AVAILABLE = False
        nlp_bg = None
        
except ImportError:
    SPACY_AVAILABLE = False
    BG_MODEL_AVAILABLE = False
    nlp_bg = None

# Опитай да заредиш Stanza
try:
    import stanza
    STANZA_AVAILABLE = True
    
    # Опитай да заредиш български модел
    try:
        stanza_nlp = stanza.Pipeline('bg', processors='tokenize,pos,lemma,ner', verbose=False)
        STANZA_BG_AVAILABLE = True
    except Exception:
        STANZA_BG_AVAILABLE = False
        stanza_nlp = None
        
except ImportError:
    STANZA_AVAILABLE = False
    STANZA_BG_AVAILABLE = False
    stanza_nlp = None

@dataclass
class BulgarianToken:
    """Български токен с NLP информация"""
    text: str
    lemma: str
    pos: str
    is_stop: bool
    is_alpha: bool
    is_digit: bool

@dataclass
class BulgarianEntity:
    """Българска именувана единица"""
    text: str
    label: str
    start: int
    end: int
    confidence: float

class BulgarianNLP:
    """
    Български NLP процесор с множество fallback опции
    spaCy bg_core_news_sm > Stanza > Rule-based fallback
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Определи наличните NLP инструменти
        self.nlp_method = self._determine_nlp_method()
        
        # Зареди български стоп думи
        self.stop_words = self._load_bulgarian_stopwords()
        
        # Зареди морфологични правила
        self.morphology_rules = self._load_morphology_rules()
        
        # Зареди entity patterns
        self.entity_patterns = self._load_entity_patterns()
        
        self.logger.info(f"✅ Български NLP инициализиран с метод: {self.nlp_method}")
    
    def _determine_nlp_method(self) -> str:
        """Определя най-добрия наличен NLP метод"""
        if BG_MODEL_AVAILABLE:
            return "spacy_bg"
        elif STANZA_BG_AVAILABLE:
            return "stanza_bg"
        elif SPACY_AVAILABLE:
            return "spacy_fallback"
        else:
            return "rule_based"
    
    def _load_bulgarian_stopwords(self) -> Set[str]:
        """Зарежда български стоп думи"""
        return {
            # Местоимения
            'аз', 'ти', 'той', 'тя', 'то', 'ние', 'вие', 'те',
            'мен', 'теб', 'него', 'нея', 'нас', 'вас', 'тях',
            'ми', 'ти', 'му', 'й', 'ни', 'ви', 'им',
            'мой', 'твой', 'негов', 'неин', 'наш', 'ваш', 'техен',
            'моя', 'твоя', 'негова', 'нейна', 'наша', 'ваша', 'тяхна',
            'мое', 'твое', 'негово', 'нейно', 'наше', 'ваше', 'тяхно',
            
            # Предлози
            'в', 'на', 'за', 'от', 'до', 'с', 'със', 'по', 'при', 'над', 'под',
            'пред', 'зад', 'между', 'сред', 'около', 'покрай', 'през', 'без',
            'освен', 'въпреки', 'заради', 'поради', 'благодарение',
            
            # Съюзи
            'и', 'или', 'но', 'а', 'ако', 'че', 'когато', 'докато', 'преди',
            'след', 'защото', 'тъй като', 'за да', 'въпреки че', 'макар че',
            
            # Частици
            'не', 'ли', 'да', 'ще', 'би', 'нека', 'май', 'дали',
            
            # Въпросителни думи
            'кой', 'коя', 'кое', 'кои', 'какъв', 'каква', 'какво', 'какви',
            'кога', 'къде', 'как', 'защо', 'колко', 'чий', 'чия', 'чие', 'чии',
            
            # Показателни думи
            'този', 'тази', 'това', 'тези', 'онзи', 'онази', 'онова', 'онези',
            'такъв', 'такава', 'такова', 'такива', 'толкова', 'толкоз',
            
            # Неопределителни думи
            'някой', 'някоя', 'нещо', 'някакъв', 'някаква', 'някакво', 'някакви',
            'всеки', 'всяка', 'всяко', 'всички', 'всичко', 'цял', 'цяла', 'цяло',
            
            # Отрицателни думи
            'никой', 'никоя', 'нищо', 'никакъв', 'никаква', 'никакво', 'никакви',
            'никога', 'никъде', 'никак', 'нито',
            
            # Числителни
            'един', 'една', 'едно', 'първи', 'първа', 'първо', 'втори', 'втора', 'второ',
            
            # Глаголи
            'съм', 'си', 'е', 'сме', 'сте', 'са', 'бях', 'беше', 'бяхме', 'бяхте', 'бяха',
            'ще', 'би', 'бих', 'биш', 'бихме', 'бихте', 'биха',
            'има', 'няма', 'може', 'трябва', 'иска', 'искам', 'искаш', 'искаме', 'искате', 'искат'
        }
    
    def _load_morphology_rules(self) -> Dict[str, List[str]]:
        """Зарежда морфологични правила за български"""
        return {
            # Съществителни имена - множествено число
            'програма': ['програми', 'програмата', 'програмите'],
            'проект': ['проекти', 'проекта', 'проектите'],
            'фонд': ['фондове', 'фонда', 'фондовете'],
            'мярка': ['мерки', 'мярката', 'мерките'],
            'схема': ['схеми', 'схемата', 'схемите'],
            'инициатива': ['инициативи', 'инициативата', 'инициативите'],
            'регион': ['региони', 'региона', 'регионите'],
            'област': ['области', 'областта', 'областите'],
            'община': ['общини', 'общината', 'общините'],
            'предприятие': ['предприятия', 'предприятието', 'предприятията'],
            'организация': ['организации', 'организацията', 'организациите'],
            
            # Прилагателни имена
            'европейски': ['европейска', 'европейско', 'европейски'],
            'български': ['българска', 'българско', 'български'],
            'регионален': ['регионална', 'регионално', 'регионални'],
            'транспортен': ['транспортна', 'транспортно', 'транспортни'],
            'иновационен': ['иновационна', 'иновационно', 'иновационни'],
            'екологичен': ['екологична', 'екологично', 'екологични'],
            
            # Глаголи
            'финансира': ['финансират', 'финансирам', 'финансираш', 'финансираме', 'финансирате'],
            'подкрепя': ['подкрепят', 'подкрепям', 'подкрепяш', 'подкрепяме', 'подкрепяте'],
            'развива': ['развиват', 'развивам', 'развиваш', 'развиваме', 'развивате']
        }
    
    def _load_entity_patterns(self) -> List[Dict[str, Any]]:
        """Зарежда pattern-и за именувани единици"""
        return [
            # Програми
            {
                'label': 'PROGRAM',
                'pattern': r'(?:Програма|Оперативна програма|ОП)\s+[А-Я][А-Яа-я\s\-]+\d{4}[\-\d]*',
                'examples': ['Програма Horizon Europe 2021-2027', 'ОПРР 2021-2027']
            },
            
            # Организации
            {
                'label': 'ORG',
                'pattern': r'(?:Министерство|Агенция|Дирекция|Фонд)\s+[А-Я][А-Яа-я\s]+',
                'examples': ['Министерство на регионалното развитие']
            },
            
            # Места
            {
                'label': 'LOC',
                'pattern': r'(?:България|Европа|ЕС|Европейски съюз|Северна Македония|Сърбия|Румъния|Турция|Гърция)',
                'examples': ['България', 'Европейски съюз']
            },
            
            # Суми
            {
                'label': 'MONEY',
                'pattern': r'\d+(?:\.\d+)?\s*(?:лв\.|лева|евро|EUR|BGN)',
                'examples': ['1000 лв.', '500 евро']
            },
            
            # Дати
            {
                'label': 'DATE',
                'pattern': r'\d{4}[\-\d]*|\d{1,2}\.\d{1,2}\.\d{4}',
                'examples': ['2021-2027', '01.01.2024']
            }
        ]
    
    def process_text(self, text: str) -> Dict[str, Any]:
        """Главна функция за обработка на текст"""
        if self.nlp_method == "spacy_bg":
            return self._process_with_spacy_bg(text)
        elif self.nlp_method == "stanza_bg":
            return self._process_with_stanza(text)
        elif self.nlp_method == "spacy_fallback":
            return self._process_with_spacy_fallback(text)
        else:
            return self._process_with_rules(text)
    
    def _process_with_spacy_bg(self, text: str) -> Dict[str, Any]:
        """Обработка с български spaCy модел"""
        doc = nlp_bg(text)
        
        tokens = []
        for token in doc:
            tokens.append(BulgarianToken(
                text=token.text,
                lemma=token.lemma_,
                pos=token.pos_,
                is_stop=token.is_stop,
                is_alpha=token.is_alpha,
                is_digit=token.is_digit
            ))
        
        entities = []
        for ent in doc.ents:
            entities.append(BulgarianEntity(
                text=ent.text,
                label=ent.label_,
                start=ent.start_char,
                end=ent.end_char,
                confidence=1.0
            ))
        
        return {
            'tokens': tokens,
            'entities': entities,
            'method': 'spacy_bg'
        }
    
    def _process_with_stanza(self, text: str) -> Dict[str, Any]:
        """Обработка със Stanza"""
        doc = stanza_nlp(text)
        
        tokens = []
        entities = []
        
        for sent in doc.sentences:
            for word in sent.words:
                tokens.append(BulgarianToken(
                    text=word.text,
                    lemma=word.lemma,
                    pos=word.upos,
                    is_stop=word.text.lower() in self.stop_words,
                    is_alpha=word.text.isalpha(),
                    is_digit=word.text.isdigit()
                ))
            
            # Извлечи именувани единици
            for ent in sent.ents:
                entities.append(BulgarianEntity(
                    text=ent.text,
                    label=ent.type,
                    start=ent.start_char,
                    end=ent.end_char,
                    confidence=0.9
                ))
        
        return {
            'tokens': tokens,
            'entities': entities,
            'method': 'stanza_bg'
        }
    
    def _process_with_spacy_fallback(self, text: str) -> Dict[str, Any]:
        """Fallback с английски spaCy модел"""
        try:
            nlp_en = spacy.load("en_core_web_sm")
            doc = nlp_en(text)
            
            tokens = []
            for token in doc:
                tokens.append(BulgarianToken(
                    text=token.text,
                    lemma=token.lemma_,
                    pos=token.pos_,
                    is_stop=token.text.lower() in self.stop_words,
                    is_alpha=token.is_alpha,
                    is_digit=token.is_digit
                ))
            
            # Използвай rule-based entity extraction
            entities = self._extract_entities_with_rules(text)
            
            return {
                'tokens': tokens,
                'entities': entities,
                'method': 'spacy_fallback'
            }
            
        except OSError:
            return self._process_with_rules(text)
    
    def _process_with_rules(self, text: str) -> Dict[str, Any]:
        """Rule-based обработка (последен fallback)"""
        # Токенизация
        tokens = self._tokenize_bulgarian(text)
        
        # Entity extraction
        entities = self._extract_entities_with_rules(text)
        
        return {
            'tokens': tokens,
            'entities': entities,
            'method': 'rule_based'
        }
    
    def _tokenize_bulgarian(self, text: str) -> List[BulgarianToken]:
        """Rule-based токенизация за български"""
        # Основна токенизация
        words = re.findall(r'\b\w+\b', text, re.UNICODE)
        
        tokens = []
        for word in words:
            word_lower = word.lower()
            
            # Опитай да намериш лема
            lemma = self._find_lemma(word_lower)
            
            # Определи POS (опростено)
            pos = self._determine_pos(word_lower)
            
            tokens.append(BulgarianToken(
                text=word,
                lemma=lemma,
                pos=pos,
                is_stop=word_lower in self.stop_words,
                is_alpha=word.isalpha(),
                is_digit=word.isdigit()
            ))
        
        return tokens
    
    def _find_lemma(self, word: str) -> str:
        """Намира лема на дума (опростено)"""
        # Търси в морфологичните правила
        for lemma, forms in self.morphology_rules.items():
            if word in forms or word == lemma:
                return lemma
        
        # Опростени правила за лематизация
        if word.endswith('ите'):
            return word[:-3]
        elif word.endswith('ата'):
            return word[:-3] + 'а'
        elif word.endswith('ят'):
            return word[:-2]
        elif word.endswith('те'):
            return word[:-2]
        
        return word
    
    def _determine_pos(self, word: str) -> str:
        """Определя POS tag (опростено)"""
        if word in self.stop_words:
            return 'PART'
        elif word.isdigit():
            return 'NUM'
        elif word.endswith(('ция', 'сия', 'тия')):
            return 'NOUN'
        elif word.endswith(('ски', 'ска', 'ско')):
            return 'ADJ'
        elif word.endswith(('ва', 'ам', 'аш', 'ат')):
            return 'VERB'
        else:
            return 'NOUN'  # По подразбиране
    
    def _extract_entities_with_rules(self, text: str) -> List[BulgarianEntity]:
        """Rule-based извличане на именувани единици"""
        entities = []
        
        for pattern_info in self.entity_patterns:
            pattern = pattern_info['pattern']
            label = pattern_info['label']
            
            matches = re.finditer(pattern, text, re.IGNORECASE)
            
            for match in matches:
                entities.append(BulgarianEntity(
                    text=match.group(0),
                    label=label,
                    start=match.start(),
                    end=match.end(),
                    confidence=0.7
                ))
        
        return entities
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """Извлича ключови думи от текст"""
        result = self.process_text(text)
        tokens = result['tokens']
        
        # Филтрирай токените
        keywords = []
        for token in tokens:
            if (token.is_alpha and 
                not token.is_stop and 
                len(token.text) > 2 and
                token.pos in ['NOUN', 'ADJ', 'VERB']):
                keywords.append(token.lemma.lower())
        
        # Премахни дублирания и върни топ N
        unique_keywords = list(dict.fromkeys(keywords))  # Запази реда
        return unique_keywords[:max_keywords]
    
    def expand_query_with_morphology(self, query: str) -> List[str]:
        """Разширява заявката с морфологични варианти"""
        expanded_terms = []
        words = query.lower().split()
        
        for word in words:
            expanded_terms.append(word)
            
            # Добави морфологични варианти
            if word in self.morphology_rules:
                expanded_terms.extend(self.morphology_rules[word])
        
        return list(set(expanded_terms))
    
    def get_nlp_info(self) -> Dict[str, Any]:
        """Връща информация за NLP възможностите"""
        return {
            'method': self.nlp_method,
            'spacy_available': SPACY_AVAILABLE,
            'spacy_bg_available': BG_MODEL_AVAILABLE,
            'stanza_available': STANZA_AVAILABLE,
            'stanza_bg_available': STANZA_BG_AVAILABLE,
            'stopwords_count': len(self.stop_words),
            'morphology_rules_count': len(self.morphology_rules),
            'entity_patterns_count': len(self.entity_patterns)
        }


def test_bulgarian_nlp():
    """Тест функция за български NLP"""
    nlp = BulgarianNLP()
    
    test_text = """
    Програма Interreg VI-A IPA България Северна Македония 2021-2027 финансира 
    трансгранични проекти за развитие на регионите. ОПРР подкрепя иновационни 
    инициативи в областта на транспорта и околната среда.
    """
    
    print("🧪 Тестване на Bulgarian NLP...")
    print(f"📊 NLP Info: {nlp.get_nlp_info()}")
    
    # Обработи текста
    result = nlp.process_text(test_text)
    
    print(f"\n🔤 Токени ({result['method']}):")
    for token in result['tokens'][:10]:  # Първите 10
        print(f"  {token.text} -> {token.lemma} ({token.pos})")
    
    print(f"\n🏷️ Именувани единици:")
    for entity in result['entities']:
        print(f"  {entity.text} ({entity.label}) - confidence: {entity.confidence}")
    
    # Ключови думи
    keywords = nlp.extract_keywords(test_text)
    print(f"\n🔑 Ключови думи: {keywords}")
    
    # Морфологично разширяване
    query = "програма финансиране"
    expanded = nlp.expand_query_with_morphology(query)
    print(f"\n📈 Разширена заявка '{query}': {expanded}")


if __name__ == "__main__":
    test_bulgarian_nlp()
