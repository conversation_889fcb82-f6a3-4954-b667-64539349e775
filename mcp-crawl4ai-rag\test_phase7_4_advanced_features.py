#!/usr/bin/env python3
"""
🚀 PHASE 7.4: ADVANCED FEATURES IMPLEMENTATION TEST
Тестване на multi-vector search, query classification и context-aware scoring

Очаквано подобрение: +5-8% от текущите 70%
Цел: 75-78% Overall Success Index
"""

import asyncio
import os
import sys
from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import required modules
from supabase import create_client, Client
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv
from query_enhancement import BulgarianQueryEnhancer
from advanced_rag_features import AdvancedRAGProcessor

# Load environment variables
load_dotenv()

def get_supabase_client() -> Client:
    """Get Supabase client"""
    url = os.getenv("SUPABASE_URL")
    key = os.getenv("SUPABASE_SERVICE_KEY")
    if not url or not key:
        raise ValueError(f"Missing Supabase credentials: URL={url is not None}, KEY={key is not None}")
    return create_client(url, key)

# Test queries - same as previous phases for comparison
TEST_QUERIES = [
    {
        "query": "процедури за финансиране на проекти",
        "difficulty": "EASY"
    },
    {
        "query": "образование и професионално обучение", 
        "difficulty": "EASY"
    },
    {
        "query": "европейски фондове за околна среда",
        "difficulty": "MEDIUM"
    },
    {
        "query": "програма за развитие на човешките ресурси",
        "difficulty": "MEDIUM"
    },
    {
        "query": "транспортна инфраструктура и мобилност",
        "difficulty": "MEDIUM"
    },
    {
        "query": "подкрепа за малки и средни предприятия",
        "difficulty": "MEDIUM"
    },
    {
        "query": "иновации и технологии за МСП",
        "difficulty": "HARD"
    },
    {
        "query": "регионално развитие и местни общности", 
        "difficulty": "HARD"
    },
    {
        "query": "цифрова трансформация и дигитализация",
        "difficulty": "HARD"
    },
    {
        "query": "енергийна ефективност и възобновяеми източници",
        "difficulty": "HARD"
    }
]

def calculate_content_relevance(query: str, content: str, threshold: float = 0.3) -> float:
    """Calculate content relevance based on keyword matching"""
    if not content:
        return 0.0
    
    query_words = set(query.lower().split())
    content_words = set(content.lower().split())
    
    # Remove common stopwords
    stopwords = {'и', 'на', 'за', 'в', 'с', 'от', 'до', 'по', 'при', 'към', 'или', 'че', 'да', 'се', 'не'}
    query_words = query_words - stopwords
    content_words = content_words - stopwords
    
    if not query_words:
        return 0.0
    
    matches = len(query_words.intersection(content_words))
    relevance = matches / len(query_words)
    
    return relevance

async def test_advanced_features():
    """Test Phase 7.4 advanced features"""
    print("🚀 PHASE 7.4: ADVANCED FEATURES IMPLEMENTATION TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 Testing: Multi-vector search, query classification, context-aware scoring")
    print(f"📊 Previous Performance (Phase 7.2): 70.0% Overall Success Index")
    print(f"🎯 Target: 75-78% Overall Success Index (+5-8%)")
    print("=" * 60)
    
    # Initialize components
    supabase = get_supabase_client()
    model = SentenceTransformer('BAAI/bge-large-en-v1.5')
    query_enhancer = BulgarianQueryEnhancer()
    advanced_processor = AdvancedRAGProcessor()
    
    results = []
    
    for i, test_case in enumerate(TEST_QUERIES, 1):
        query = test_case["query"]
        difficulty = test_case["difficulty"]
        
        print(f"\n🔍 Test {i}/10: {query}")
        
        try:
            # Step 1: Query classification and strategy optimization
            classification = advanced_processor.classify_query(query)
            strategy = advanced_processor.get_optimal_search_strategy(query)
            
            print(f"   📊 Type: {classification['type'].value}")
            print(f"   📈 Complexity: {classification['complexity'].value}")
            print(f"   ⚙️ Strategy: {strategy['strategy_weights']}")
            
            # Step 2: Enhanced query processing
            enhancement_result = query_enhancer.enhance_query_for_search(query)
            enhanced_query = enhancement_result['expanded_query']
            keywords = enhancement_result['keywords']
            
            # Step 3: Generate embedding
            start_time = time.time()
            query_embedding = model.encode([enhanced_query])[0].tolist()
            embedding_time = time.time() - start_time
            
            # Step 4: Advanced search with adaptive weights
            weights = strategy['strategy_weights']
            match_count = strategy['recommended_match_count']
            
            response = supabase.rpc(
                'match_crawled_pages_v7_4_advanced',
                {
                    'p_query_embedding': query_embedding,
                    'p_keywords': keywords,
                    'p_query_text': query,
                    'p_weight_similarity': weights['semantic_weight'],
                    'p_weight_program_name': weights['program_weight'],
                    'p_weight_year': 0.05,  # Fixed temporal weight
                    'p_weight_metadata': weights['metadata_weight'],
                    'p_weight_keywords': weights['keyword_weight'],
                    'p_match_count': match_count
                }
            ).execute()
            
            if response.data:
                # Analyze top 3 results
                top_3_results = response.data[:3]
                relevance_scores = []
                
                for j, result in enumerate(top_3_results, 1):
                    content_relevance = calculate_content_relevance(query, result.get('content', ''))
                    relevance_scores.append(content_relevance)
                    
                    # Show detailed scoring breakdown for first result
                    if j == 1:
                        print(f"   🔍 Top Result Analysis:")
                        print(f"      Similarity: {result.get('similarity', 0):.3f}")
                        print(f"      Domain Boost: {result.get('domain_boost_score', 0):.3f}")
                        print(f"      Context Score: {result.get('context_aware_score', 0):.3f}")
                        print(f"      Final Score: {result.get('final_score', 0):.3f}")
                
                # Calculate metrics
                success_at_3 = any(score >= 0.3 for score in relevance_scores)
                precision_at_3 = sum(1 for score in relevance_scores if score >= 0.3) / 3
                best_relevance = max(relevance_scores) if relevance_scores else 0
                
                # Calculate MRR
                mrr = 0
                for j, score in enumerate(relevance_scores, 1):
                    if score >= 0.3:
                        mrr = 1.0 / j
                        break
                
                results.append({
                    'query': query,
                    'difficulty': difficulty,
                    'success_at_3': success_at_3,
                    'precision_at_3': precision_at_3,
                    'best_relevance': best_relevance,
                    'mrr': mrr,
                    'embedding_time': embedding_time,
                    'query_classification': classification,
                    'strategy_weights': weights,
                    'match_count': match_count,
                    'top_scores': [r.get('final_score', 0) for r in top_3_results],
                    'detailed_scores': {
                        'similarity': top_3_results[0].get('similarity', 0) if top_3_results else 0,
                        'domain_boost': top_3_results[0].get('domain_boost_score', 0) if top_3_results else 0,
                        'context_aware': top_3_results[0].get('context_aware_score', 0) if top_3_results else 0,
                        'final_score': top_3_results[0].get('final_score', 0) if top_3_results else 0
                    }
                })
                
                print(f"   ✅ Success: {success_at_3} | Precision: {precision_at_3:.1%} | Time: {embedding_time:.3f}s")
                
            else:
                print("   ❌ No results")
                results.append({
                    'query': query,
                    'difficulty': difficulty,
                    'success_at_3': False,
                    'precision_at_3': 0,
                    'best_relevance': 0,
                    'mrr': 0,
                    'embedding_time': embedding_time,
                    'query_classification': classification,
                    'strategy_weights': weights,
                    'match_count': match_count,
                    'top_scores': [],
                    'detailed_scores': {}
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({
                'query': query,
                'difficulty': difficulty,
                'success_at_3': False,
                'precision_at_3': 0,
                'best_relevance': 0,
                'mrr': 0,
                'embedding_time': 0,
                'query_classification': {},
                'strategy_weights': {},
                'match_count': 5,
                'top_scores': [],
                'detailed_scores': {}
            })
    
    # Calculate overall metrics
    total_success_at_3 = sum(1 for r in results if r['success_at_3'])
    avg_precision_at_3 = sum(r['precision_at_3'] for r in results) / len(results)
    avg_mrr = sum(r['mrr'] for r in results) / len(results)
    overall_success_index = (total_success_at_3 / len(results)) * 100
    avg_embedding_time = sum(r['embedding_time'] for r in results) / len(results)
    
    # Calculate improvement from Phase 7.2
    phase_7_2_baseline = 70.0
    improvement = overall_success_index - phase_7_2_baseline
    
    print("\n" + "=" * 60)
    print("📊 PHASE 7.4 ADVANCED FEATURES RESULTS")
    print("=" * 60)
    print(f"🎯 Overall Success Index: {overall_success_index:.1f}%")
    print(f"📈 Improvement from Phase 7.2: {improvement:+.1f}%")
    print(f"📊 Success@3 Rate: {total_success_at_3}/{len(results)}")
    print(f"🎯 Average Precision@3: {avg_precision_at_3:.1%}")
    print(f"📈 Average MRR: {avg_mrr:.3f}")
    print(f"⏱️ Average Embedding Time: {avg_embedding_time:.3f}s")
    
    # Analyze by difficulty
    print(f"\n📊 Performance by Difficulty:")
    for difficulty in ["EASY", "MEDIUM", "HARD"]:
        difficulty_results = [r for r in results if r['difficulty'] == difficulty]
        if difficulty_results:
            success_rate = sum(1 for r in difficulty_results if r['success_at_3']) / len(difficulty_results)
            print(f"   {difficulty}: {success_rate:.1%} ({sum(1 for r in difficulty_results if r['success_at_3'])}/{len(difficulty_results)})")
    
    # Analyze query classification effectiveness
    print(f"\n🧠 Query Classification Analysis:")
    type_counts = {}
    complexity_counts = {}
    
    for result in results:
        if 'query_classification' in result and result['query_classification']:
            qtype = result['query_classification'].get('type', {})
            if hasattr(qtype, 'value'):
                type_name = qtype.value
                if type_name not in type_counts:
                    type_counts[type_name] = {'total': 0, 'success': 0}
                type_counts[type_name]['total'] += 1
                if result['success_at_3']:
                    type_counts[type_name]['success'] += 1
            
            complexity = result['query_classification'].get('complexity', {})
            if hasattr(complexity, 'value'):
                comp_name = complexity.value
                if comp_name not in complexity_counts:
                    complexity_counts[comp_name] = {'total': 0, 'success': 0}
                complexity_counts[comp_name]['total'] += 1
                if result['success_at_3']:
                    complexity_counts[comp_name]['success'] += 1
    
    for qtype, stats in type_counts.items():
        success_rate = stats['success'] / stats['total'] if stats['total'] > 0 else 0
        print(f"   {qtype}: {success_rate:.1%} ({stats['success']}/{stats['total']})")
    
    # Determine success level
    if improvement >= 5:
        print(f"\n✅ PHASE 7.4 SUCCESS! Improvement: {improvement:+.1f}% (target: +5-8%)")
        success_level = "SUCCESS"
    elif improvement >= 2:
        print(f"\n🔄 PARTIAL SUCCESS: {improvement:+.1f}% (target: +5-8%)")
        success_level = "PARTIAL"
    else:
        print(f"\n❌ BELOW TARGET: {improvement:+.1f}% (target: +5-8%)")
        success_level = "BELOW_TARGET"
    
    # Save detailed results
    final_results = {
        'test_date': datetime.now().isoformat(),
        'overall_success_index': overall_success_index,
        'improvement_from_phase_7_2': improvement,
        'success_level': success_level,
        'metrics': {
            'success_at_3_rate': total_success_at_3/len(results),
            'avg_precision_at_3': avg_precision_at_3,
            'avg_mrr': avg_mrr,
            'avg_embedding_time': avg_embedding_time
        },
        'performance_by_difficulty': {
            difficulty: {
                'success_rate': sum(1 for r in [r for r in results if r['difficulty'] == difficulty] if r['success_at_3']) / len([r for r in results if r['difficulty'] == difficulty]) if [r for r in results if r['difficulty'] == difficulty] else 0,
                'count': len([r for r in results if r['difficulty'] == difficulty])
            } for difficulty in ["EASY", "MEDIUM", "HARD"]
        },
        'query_classification_analysis': {
            'by_type': type_counts,
            'by_complexity': complexity_counts
        },
        'detailed_results': results
    }
    
    with open('phase7_4_advanced_features_results.json', 'w', encoding='utf-8') as f:
        json.dump(final_results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: phase7_4_advanced_features_results.json")
    
    return overall_success_index, improvement

if __name__ == "__main__":
    asyncio.run(test_advanced_features())
