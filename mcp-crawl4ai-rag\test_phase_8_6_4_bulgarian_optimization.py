#!/usr/bin/env python3
"""
🇧🇬 Phase 8.6.4: Bulgarian Language Optimization Test
Тестване на българската езикова оптимизация с лематизация и синоними
"""

import asyncio
import sys
import os
import time
from typing import List, Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils import (
    search_crawled_pages_advanced_query,
    extract_bulgarian_keywords,
    expand_query_with_synonyms
)

# Bulgarian-specific test queries with synonyms and morphological variants
BULGARIAN_TEST_QUERIES = [
    {
        "query": "МСП финансиране малки предприятия",
        "expected_synonyms": [
            "микро предприятия", "дребни фирми", "малък бизнес",
            "финансова подкрепа", "безвъзмездна помощ", "грантове"
        ],
        "expected_content": [
            "малки и средни предприятия",
            "МСП",
            "финансиране",
            "безвъзмездна помощ",
            "европейски фондове"
        ],
        "description": "МСП с български синоними"
    },
    {
        "query": "екологични проекти зелена енергия",
        "expected_synonyms": [
            "околна среда", "природозащита", "устойчиво развитие",
            "възобновяеми източници", "чиста енергия", "климат"
        ],
        "expected_content": [
            "екологични",
            "зелена енергия",
            "възобновяеми източници",
            "околна среда",
            "устойчиво развитие"
        ],
        "description": "Екология с български термини"
    },
    {
        "query": "образование обучение квалификация",
        "expected_synonyms": [
            "професионално развитие", "умения", "компетентности",
            "курсове", "сертификация", "учене"
        ],
        "expected_content": [
            "образование",
            "обучение", 
            "квалификация",
            "професионално развитие",
            "умения"
        ],
        "description": "Образование с български варианти"
    },
    {
        "query": "инфраструктура пътища транспорт",
        "expected_synonyms": [
            "магистрали", "железопътен", "летище", "пристанище",
            "мобилност", "логистика", "свързаност"
        ],
        "expected_content": [
            "инфраструктура",
            "пътища",
            "транспорт",
            "строителство",
            "модернизация"
        ],
        "description": "Инфраструктура с транспортни термини"
    }
]

async def test_bulgarian_keyword_extraction():
    """Test Bulgarian keyword extraction with morphological analysis"""
    print("🇧🇬 ТЕСТВАНЕ НА БЪЛГАРСКО ИЗВЛИЧАНЕ НА КЛЮЧОВИ ДУМИ:")
    print("-" * 60)
    
    test_texts = [
        "Програми за финансиране на малки и средни предприятия в България",
        "Екологични проекти за зелена енергия и възобновяеми източници",
        "Образователни програми за професионално обучение и квалификация",
        "Инфраструктурни проекти за пътища и транспортна свързаност"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"{i}. Текст: {text}")
        
        try:
            keywords = extract_bulgarian_keywords(text)
            print(f"   Ключови думи ({len(keywords)}): {', '.join(keywords)}")
            
            # Test keyword quality
            if len(keywords) >= 3:
                print(f"   ✅ Добро извличане на ключови думи")
            else:
                print(f"   ⚠️ Малко ключови думи")
                
        except Exception as e:
            print(f"   ❌ Грешка: {str(e)}")
        
        print()

async def test_bulgarian_query_expansion():
    """Test Bulgarian query expansion with synonyms"""
    print("🇧🇬 ТЕСТВАНЕ НА РАЗШИРЯВАНЕ НА ЗАЯВКИ С БЪЛГАРСКИ СИНОНИМИ:")
    print("-" * 60)
    
    for i, test_case in enumerate(BULGARIAN_TEST_QUERIES, 1):
        query = test_case["query"]
        expected_synonyms = test_case["expected_synonyms"]
        description = test_case["description"]
        
        print(f"{i}. {description}")
        print(f"   Оригинална заявка: {query}")
        
        try:
            expanded_queries = expand_query_with_synonyms(query)
            print(f"   Разширени заявки ({len(expanded_queries)}):")
            
            for j, expanded in enumerate(expanded_queries[:3], 1):
                print(f"      {j}. {expanded}")
            
            # Check if synonyms are found
            all_expanded_text = ' '.join(expanded_queries).lower()
            found_synonyms = []
            for synonym in expected_synonyms:
                if synonym.lower() in all_expanded_text:
                    found_synonyms.append(synonym)
            
            synonym_coverage = len(found_synonyms) / len(expected_synonyms)
            print(f"   📊 Покритие на синоними: {synonym_coverage:.1%} ({len(found_synonyms)}/{len(expected_synonyms)})")
            
            if found_synonyms:
                print(f"   ✅ Намерени синоними: {', '.join(found_synonyms[:3])}...")
            else:
                print(f"   ⚠️ Няма намерени очаквани синоними")
                
        except Exception as e:
            print(f"   ❌ Грешка при разширяване: {str(e)}")
        
        print()

async def test_bulgarian_optimized_search():
    """Test Bulgarian-optimized search with language-specific improvements"""
    print("🇧🇬 ТЕСТВАНЕ НА БЪЛГАРСКО-ОПТИМИЗИРАНО ТЪРСЕНЕ:")
    print("=" * 70)
    
    total_tests = len(BULGARIAN_TEST_QUERIES)
    successful_tests = 0
    all_metrics = []
    
    for i, test_case in enumerate(BULGARIAN_TEST_QUERIES, 1):
        query = test_case["query"]
        expected_content = test_case["expected_content"]
        description = test_case["description"]
        
        print(f"\n🔍 Тест {i}/{total_tests}: {description}")
        print(f"   Заявка: {query}")
        print(f"   Очаквано съдържание: {len(expected_content)} елемента")
        
        start_time = time.time()
        
        try:
            # Search with Bulgarian optimization
            results = await search_crawled_pages_advanced_query(
                query_text=query,
                match_count=10,
                use_bulgarian_embeddings=True,  # Bulgarian-specific embeddings
                rerank_top_k=20,
                enable_query_expansion=True,    # Bulgarian synonym expansion
                enable_hyde=True,
                enable_multi_query=True
            )
            
            search_time = time.time() - start_time
            
            if not results:
                print(f"   ❌ Няма резултати за заявката")
                continue
            
            # Evaluate Bulgarian content relevance
            content_matches = 0
            keyword_density = 0.0
            bulgarian_score = 0.0
            
            for result in results[:5]:  # Check top 5 results
                content = result.get('content', '').lower()
                title = result.get('title', '').lower()
                full_text = f"{title} {content}"
                
                # Count expected content matches
                matches = sum(1 for term in expected_content if term.lower() in full_text)
                content_matches += matches
                
                # Calculate keyword density
                keywords = extract_bulgarian_keywords(full_text)
                if keywords:
                    keyword_density += len(keywords) / max(len(full_text.split()), 1)
                
                # Bulgarian language score (presence of Bulgarian-specific terms)
                bulgarian_indicators = ['програма', 'проект', 'финансиране', 'подкрепа', 'развитие']
                bg_matches = sum(1 for indicator in bulgarian_indicators if indicator in full_text)
                bulgarian_score += bg_matches / len(bulgarian_indicators)
            
            # Calculate metrics
            avg_keyword_density = keyword_density / min(len(results), 5)
            avg_bulgarian_score = bulgarian_score / min(len(results), 5)
            content_coverage = content_matches / (len(expected_content) * min(len(results), 5))
            
            # Enhanced scoring from results
            enhanced_scores = [r.get('enhanced_score', 0.0) for r in results[:3]]
            avg_enhanced_score = sum(enhanced_scores) / len(enhanced_scores) if enhanced_scores else 0.0
            
            # Success criteria for Bulgarian optimization
            is_successful = (
                content_coverage >= 0.4 or      # 40% content coverage
                avg_enhanced_score >= 8.0 or    # High enhanced score
                avg_bulgarian_score >= 0.6 or   # Good Bulgarian language score
                avg_keyword_density >= 0.02     # Good keyword density
            )
            
            if is_successful:
                successful_tests += 1
                print(f"   ✅ УСПЕШЕН български тест")
            else:
                print(f"   ⚠️ Частичен успех")
            
            print(f"   📊 Резултати ({len(results)} намерени за {search_time:.2f}s):")
            print(f"      🇧🇬 Съдържание покритие: {content_coverage:.1%}")
            print(f"      🔑 Средна плътност ключови думи: {avg_keyword_density:.3f}")
            print(f"      🏆 Средна Enhanced Score: {avg_enhanced_score:.3f}")
            print(f"      🇧🇬 Български език оценка: {avg_bulgarian_score:.1%}")
            
            # Store metrics
            metrics = {
                'content_coverage': content_coverage,
                'keyword_density': avg_keyword_density,
                'enhanced_score': avg_enhanced_score,
                'bulgarian_score': avg_bulgarian_score
            }
            all_metrics.append(metrics)
            
            # Show top results
            print(f"   🏆 Топ 3 резултата:")
            for j, result in enumerate(results[:3], 1):
                enhanced_score = result.get('enhanced_score', 0.0)
                title = result.get('title', 'Без заглавие')[:50]
                
                print(f"      {j}. {title}...")
                print(f"         Enhanced Score: {enhanced_score:.3f}")
                
        except Exception as e:
            print(f"   ❌ Грешка при тестване: {str(e)}")
            continue
    
    # Calculate overall Bulgarian optimization statistics
    if all_metrics:
        avg_content_coverage = sum(m['content_coverage'] for m in all_metrics) / len(all_metrics)
        avg_keyword_density = sum(m['keyword_density'] for m in all_metrics) / len(all_metrics)
        avg_enhanced_score = sum(m['enhanced_score'] for m in all_metrics) / len(all_metrics)
        avg_bulgarian_score = sum(m['bulgarian_score'] for m in all_metrics) / len(all_metrics)
        
        success_rate = successful_tests / total_tests
        
        print(f"\n🇧🇬 ОБОБЩЕНИЕ НА ФАЗА 8.6.4:")
        print("=" * 50)
        print(f"📊 Общ успех: {successful_tests}/{total_tests} ({success_rate:.1%})")
        print(f"🇧🇬 Средно съдържание покритие: {avg_content_coverage:.1%}")
        print(f"🔑 Средна плътност ключови думи: {avg_keyword_density:.3f}")
        print(f"🏆 Средна Enhanced Score: {avg_enhanced_score:.3f}")
        print(f"🇧🇬 Средна българска оценка: {avg_bulgarian_score:.1%}")
        
        # Performance evaluation
        if success_rate >= 0.75:
            print(f"🏆 ОТЛИЧЕН резултат! Българската оптимизация работи превъзходно!")
        elif success_rate >= 0.5:
            print(f"✅ ДОБЪР резултат! Българската оптимизация работи добре!")
        else:
            print(f"⚠️ Необходими подобрения в българската езикова оптимизация")
        
        return success_rate >= 0.5
    else:
        print(f"❌ Няма валидни резултати за анализ")
        return False

if __name__ == "__main__":
    async def main():
        print("🇧🇬 СТАРТИРАНЕ НА ТЕСТОВЕ ЗА ФАЗА 8.6.4")
        print("=" * 60)
        
        # Test Bulgarian keyword extraction
        await test_bulgarian_keyword_extraction()
        
        # Test Bulgarian query expansion
        await test_bulgarian_query_expansion()
        
        # Test Bulgarian-optimized search
        success = await test_bulgarian_optimized_search()
        
        if success:
            print(f"\n🎉 ФАЗА 8.6.4 ЗАВЪРШЕНА УСПЕШНО!")
            print(f"   Българската езикова оптимизация работи отлично!")
        else:
            print(f"\n⚠️ Фаза 8.6.4 се нуждае от допълнителни подобрения")
    
    asyncio.run(main())
