#!/usr/bin/env python3
"""
🏆 GOLDEN STANDARD TEST - PHASE 8.5
Тестваме текущата производителност на системата с най-новите оптимизации
"""

import asyncio
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, List, Any

# Добавяме пътя към src
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from src.utils import (
    get_supabase_client, 
    search_documents_with_text_hybrid_reranked,
    search_crawled_pages_advanced_query,
    search_crawled_pages_cascaded
)

# Златен стандарт тестови случаи - реални URL-и от нашата база данни
GOLDEN_STANDARD_TESTS = [
    {
        "id": 1,
        "query": "програми за малки и средни предприятия в България",
        "expected_keywords": ["МСП", "малки", "средни", "предприятия", "бизнес"],
        "category": "SME_PROGRAMS"
    },
    {
        "id": 2, 
        "query": "европейски фондове за иновации и развитие",
        "expected_keywords": ["иновации", "развитие", "технологии", "изследвания"],
        "category": "INNOVATION_FUNDS"
    },
    {
        "id": 3,
        "query": "финансиране на стартъп компании в България", 
        "expected_keywords": ["стартъп", "финансиране", "нови", "компании"],
        "category": "STARTUP_FUNDING"
    },
    {
        "id": 4,
        "query": "процедури за кандидатстване по оперативни програми",
        "expected_keywords": ["процедури", "кандидатстване", "оперативни", "програми"],
        "category": "APPLICATION_PROCEDURES"
    },
    {
        "id": 5,
        "query": "подкрепа за дигитализация на предприятията",
        "expected_keywords": ["дигитализация", "цифрови", "технологии", "трансформация"],
        "category": "DIGITALIZATION"
    },
    {
        "id": 6,
        "query": "програми за околна среда и климат",
        "expected_keywords": ["околна среда", "климат", "екология", "зелени"],
        "category": "ENVIRONMENT"
    },
    {
        "id": 7,
        "query": "европейски средства за образование и обучение",
        "expected_keywords": ["образование", "обучение", "квалификация", "умения"],
        "category": "EDUCATION"
    },
    {
        "id": 8,
        "query": "инфраструктурни проекти с европейско финансиране",
        "expected_keywords": ["инфраструктура", "строителство", "пътища", "транспорт"],
        "category": "INFRASTRUCTURE"
    },
    {
        "id": 9,
        "query": "програми за развитие на селските райони",
        "expected_keywords": ["селски", "райони", "земеделие", "развитие"],
        "category": "RURAL_DEVELOPMENT"
    },
    {
        "id": 10,
        "query": "подкрепа за енергийна ефективност и възобновяеми източници",
        "expected_keywords": ["енергийна", "ефективност", "възобновяеми", "енергия"],
        "category": "ENERGY_EFFICIENCY"
    }
]

class GoldenStandardEvaluator:
    def __init__(self):
        self.supabase = get_supabase_client()
        self.results = []
        
    def evaluate_result_quality(self, results: List[Dict], expected_keywords: List[str]) -> Dict[str, Any]:
        """Оценява качеството на резултатите спрямо очакваните ключови думи"""
        if not results:
            return {
                "relevance_score": 0.0,
                "keyword_matches": 0,
                "content_quality": 0.0,
                "has_relevant_content": False
            }
        
        total_keyword_matches = 0
        total_relevance = 0.0
        relevant_results = 0
        
        for result in results[:3]:  # Проверяваме топ 3 резултата
            content = (result.get('content', '') + ' ' + 
                      result.get('title', '') + ' ' + 
                      result.get('url', '')).lower()
            
            # Броим съвпадения на ключови думи
            keyword_matches = sum(1 for keyword in expected_keywords 
                                if keyword.lower() in content)
            total_keyword_matches += keyword_matches
            
            # Оценяваме релевантността
            if keyword_matches > 0:
                relevance = min(keyword_matches / len(expected_keywords), 1.0)
                total_relevance += relevance
                relevant_results += 1
        
        avg_relevance = total_relevance / len(results[:3]) if results else 0.0
        has_relevant = relevant_results > 0
        
        return {
            "relevance_score": avg_relevance,
            "keyword_matches": total_keyword_matches,
            "content_quality": avg_relevance * 100,
            "has_relevant_content": has_relevant,
            "relevant_results_count": relevant_results
        }
    
    async def test_search_method(self, method_name: str, search_func, query: str, expected_keywords: List[str]) -> Dict[str, Any]:
        """Тества конкретен метод за търсене"""
        start_time = time.time()
        
        try:
            if method_name == "cascaded":
                results = await search_func(query, match_count=10)
            elif method_name == "advanced_query":
                results = await search_func(query, match_count=10)
            else:  # hybrid_reranked
                results = await search_func(self.supabase, query, match_count=10)
            
            execution_time = time.time() - start_time
            quality_metrics = self.evaluate_result_quality(results, expected_keywords)
            
            return {
                "method": method_name,
                "success": len(results) > 0,
                "result_count": len(results),
                "execution_time": execution_time,
                "quality_metrics": quality_metrics
            }
            
        except Exception as e:
            return {
                "method": method_name,
                "success": False,
                "result_count": 0,
                "execution_time": time.time() - start_time,
                "error": str(e),
                "quality_metrics": {
                    "relevance_score": 0.0,
                    "keyword_matches": 0,
                    "content_quality": 0.0,
                    "has_relevant_content": False
                }
            }
    
    async def run_comprehensive_evaluation(self) -> Dict[str, Any]:
        """Пуска цялостна оценка на системата"""
        print("🏆 ЗАПОЧВАМ GOLDEN STANDARD EVALUATION - PHASE 8.5")
        print("=" * 80)
        
        # Методи за тестване
        search_methods = [
            ("hybrid_reranked", search_documents_with_text_hybrid_reranked),
            ("advanced_query", search_crawled_pages_advanced_query),
            ("cascaded", search_crawled_pages_cascaded)
        ]
        
        total_tests = len(GOLDEN_STANDARD_TESTS) * len(search_methods)
        completed_tests = 0
        
        method_results = {method[0]: [] for method in search_methods}
        
        for test_case in GOLDEN_STANDARD_TESTS:
            query = test_case["query"]
            expected_keywords = test_case["expected_keywords"]
            category = test_case["category"]
            
            print(f"\n🔍 ТЕСТ #{test_case['id']}: {query}")
            print(f"   📂 Категория: {category}")
            print(f"   🎯 Ключови думи: {', '.join(expected_keywords)}")
            
            test_results = {}
            
            for method_name, search_func in search_methods:
                print(f"   🧪 Тествам {method_name}...")
                
                result = await self.test_search_method(
                    method_name, search_func, query, expected_keywords
                )
                
                test_results[method_name] = result
                method_results[method_name].append(result)
                completed_tests += 1
                
                # Показваме резултата
                if result["success"]:
                    quality = result["quality_metrics"]
                    print(f"      ✅ {result['result_count']} резултата в {result['execution_time']:.2f}s")
                    print(f"      📊 Качество: {quality['content_quality']:.1f}% | Релевантни: {quality['relevant_results_count']}/3")
                else:
                    print(f"      ❌ Неуспех: {result.get('error', 'Няма резултати')}")
            
            # Записваме резултатите за този тест
            self.results.append({
                "test_case": test_case,
                "results": test_results
            })
            
            print(f"   📈 Прогрес: {completed_tests}/{total_tests} ({completed_tests/total_tests*100:.1f}%)")
        
        return self.calculate_final_metrics(method_results)
    
    def calculate_final_metrics(self, method_results: Dict[str, List]) -> Dict[str, Any]:
        """Изчислява финалните метрики"""
        print(f"\n📊 ИЗЧИСЛЯВАНЕ НА ФИНАЛНИ МЕТРИКИ")
        print("=" * 80)
        
        final_metrics = {}
        
        for method_name, results in method_results.items():
            successful_tests = sum(1 for r in results if r["success"])
            total_tests = len(results)
            success_rate = (successful_tests / total_tests) * 100
            
            avg_execution_time = sum(r["execution_time"] for r in results) / total_tests
            avg_result_count = sum(r["result_count"] for r in results) / total_tests
            
            # Качествени метрики
            quality_scores = [r["quality_metrics"]["content_quality"] for r in results]
            avg_quality = sum(quality_scores) / len(quality_scores)
            
            relevance_scores = [r["quality_metrics"]["relevance_score"] for r in results]
            avg_relevance = sum(relevance_scores) / len(relevance_scores)
            
            relevant_content_rate = sum(1 for r in results 
                                      if r["quality_metrics"]["has_relevant_content"]) / total_tests * 100
            
            final_metrics[method_name] = {
                "success_rate": success_rate,
                "avg_execution_time": avg_execution_time,
                "avg_result_count": avg_result_count,
                "avg_quality_score": avg_quality,
                "avg_relevance_score": avg_relevance,
                "relevant_content_rate": relevant_content_rate,
                "successful_tests": successful_tests,
                "total_tests": total_tests
            }
            
            print(f"\n🔧 {method_name.upper()}:")
            print(f"   ✅ Успеваемост: {success_rate:.1f}% ({successful_tests}/{total_tests})")
            print(f"   ⏱️ Средно време: {avg_execution_time:.2f}s")
            print(f"   📊 Средно резултати: {avg_result_count:.1f}")
            print(f"   🎯 Средно качество: {avg_quality:.1f}%")
            print(f"   📈 Релевантност: {avg_relevance:.3f}")
            print(f"   💯 Релевантно съдържание: {relevant_content_rate:.1f}%")
        
        return final_metrics

async def main():
    """Главна функция"""
    evaluator = GoldenStandardEvaluator()
    
    start_time = datetime.now()
    final_metrics = await evaluator.run_comprehensive_evaluation()
    end_time = datetime.now()
    
    # Записваме резултатите
    results_data = {
        "timestamp": start_time.isoformat(),
        "duration_seconds": (end_time - start_time).total_seconds(),
        "phase": "8.5_PERFORMANCE_OPTIMIZATION",
        "metrics": final_metrics,
        "test_details": evaluator.results
    }
    
    with open("golden_standard_phase8_5_results.json", "w", encoding="utf-8") as f:
        json.dump(results_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n🏆 GOLDEN STANDARD EVALUATION ЗАВЪРШЕНА")
    print("=" * 80)
    print(f"⏱️ Общо време: {(end_time - start_time).total_seconds():.1f}s")
    print(f"💾 Резултатите са записани в: golden_standard_phase8_5_results.json")
    
    # Намираме най-добрия метод
    best_method = max(final_metrics.keys(), 
                     key=lambda x: final_metrics[x]["relevant_content_rate"])
    best_score = final_metrics[best_method]["relevant_content_rate"]
    
    print(f"\n🥇 НАЙ-ДОБЪР МЕТОД: {best_method.upper()}")
    print(f"   📊 Релевантно съдържание: {best_score:.1f}%")
    print(f"   ✅ Успеваемост: {final_metrics[best_method]['success_rate']:.1f}%")
    
    if best_score >= 80:
        print("🎉 ОТЛИЧНО! Системата работи на високо ниво!")
    elif best_score >= 60:
        print("✅ ДОБРЕ! Системата работи задоволително!")
    elif best_score >= 40:
        print("⚠️ СРЕДНО! Нужни са подобрения!")
    else:
        print("❌ СЛАБО! Необходими са значителни оптимизации!")

if __name__ == "__main__":
    asyncio.run(main())
